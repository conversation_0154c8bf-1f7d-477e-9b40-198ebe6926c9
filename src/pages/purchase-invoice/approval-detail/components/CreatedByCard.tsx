// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Avatar from '@mui/material/Avatar'

// Context
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'

const CreatedByCard = () => {
  const { purchaseInvoiceData } = usePurchaseInvoice()

  if (!purchaseInvoiceData) return null

  const createdByUser = purchaseInvoiceData.createdByUser

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Dibuat oleh</Typography>
        
        <div className='flex items-center gap-3'>
          <Avatar
            src={createdByUser?.profilePictureUrl}
            alt={createdByUser?.fullName}
            className='w-12 h-12'
          >
            {createdByUser?.fullName?.charAt(0)?.toUpperCase()}
          </Avatar>
          
          <div className='flex flex-col gap-1'>
            <Typography variant='body1' className='font-medium'>
              {createdByUser?.fullName || 'Unknown User'}
            </Typography>
            <Typography variant='body2' className='text-textSecondary'>
              {createdByUser?.title || 'Admin Purchasing'}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CreatedByCard
