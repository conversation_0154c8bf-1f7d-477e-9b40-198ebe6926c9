// MUI Imports
import Grid from '@mui/material/Grid'
import { <PERSON>readcrum<PERSON>, Chip, Typography, Button } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { usePurchaseInvoice, PurchaseInvoiceContextProvider } from '../context/PurchaseInvoiceContext'

import { useUpdatePurchaseInvoiceApprovalRead } from '@/api/services/purchase-invoice/mutation'

// Components
import ApprovalsCard, { statusChipValue } from './components/ApprovalsCard'
import PurchaseInvoiceDetailCard from './components/PurchaseInvoiceDetailCard'
import DocumentCard from './components/DocumentCard'
import PurchaseOrderCard from './components/PurchaseOrderCard'
import PaymentSummaryCard from './components/PaymentSummaryCard'
import OtherExpenseCard from './components/OtherExpenseCard'
import DiscountCard from './components/DiscountCard'

const PurchaseInvoiceApprovalDetailPage = () => {
  const { userProfile } = useAuth()
  const { purchaseInvoiceData, fetchPurchaseInvoiceList } = usePurchaseInvoice()

  const { mutate: readMutate } = useUpdatePurchaseInvoiceApprovalRead()

  const ownApproval = purchaseInvoiceData?.approvals?.find(approval => approval.userId === userProfile?.id)

  useEffect(() => {
    if (ownApproval && ownApproval.isRead === false) {
      readMutate(
        {
          isRead: true,
          piId: purchaseInvoiceData?.id,
          approvalId: ownApproval.id
        },
        { onSuccess: () => fetchPurchaseInvoiceList() }
      )
    }
  }, [purchaseInvoiceData])

  const handleExport = () => {
    // TODO: Implement export functionality
  }

  const handlePrint = () => {
    // TODO: Implement print functionality
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Persetujuan</Typography>
          </Link>
          <Link to='/purchase-invoice/approval' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Faktur Pembelian</Typography>
          </Link>
          <Typography>Detil Faktur</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>No. Faktur: {purchaseInvoiceData?.number}</Typography>
              {ownApproval && (
                <Chip
                  label={statusChipValue[ownApproval?.status]?.label}
                  color={statusChipValue[ownApproval?.status]?.color}
                  variant='tonal'
                  size='small'
                />
              )}
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(purchaseInvoiceData?.createdAt ?? Date.now(), 'eeee dd/MM/yyyy', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2 is-full sm:is-auto'>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
              onClick={handleExport}
            >
              Ekspor
            </Button>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-printer-line' />}
              className='is-full sm:is-auto'
              onClick={handlePrint}
            >
              Cetak
            </Button>
          </div>
        </div>
      </Grid>

      {/* Left Column */}
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {/* Purchase Invoice Detail Card */}
          <Grid item xs={12}>
            <PurchaseInvoiceDetailCard />
          </Grid>
        </Grid>
      </Grid>
      {/* Right Column */}
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {/* Document Card */}
          <Grid item xs={12}>
            <DocumentCard />
          </Grid>

          {/* Approvals Card */}
          {(purchaseInvoiceData?.approvals?.length ?? 0) > 0 ? (
            <Grid item xs={12}>
              <ApprovalsCard />
            </Grid>
          ) : null}
        </Grid>
      </Grid>

      <Grid item xs={12}>
        <Grid container gap={4}>
          {/* Purchase Order Card */}
          <Grid item xs={12}>
            <PurchaseOrderCard />
          </Grid>

          {/* Discount Card */}
          <Grid item xs={12}>
            <DiscountCard />
          </Grid>

          {/* Other Expense Card */}
          <Grid item xs={12}>
            <OtherExpenseCard />
          </Grid>

          {/* Payment Summary Card */}
          <Grid item xs={12}>
            <PaymentSummaryCard />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

const PurchaseInvoiceApprovalDetailPageWithProvider = () => {
  return (
    <PurchaseInvoiceContextProvider>
      <PurchaseInvoiceApprovalDetailPage />
    </PurchaseInvoiceContextProvider>
  )
}

export default PurchaseInvoiceApprovalDetailPageWithProvider
