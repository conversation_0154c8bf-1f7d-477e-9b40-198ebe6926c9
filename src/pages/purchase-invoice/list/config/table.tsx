import { StatusChipColorType } from '@/types/appTypes'
import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { PurchaseInvoiceApprovalStatus, PurchaseInvoice } from '@/types/purchaseInvoiceTypes'
import { toCurrency } from '@/utils/helper'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [PurchaseInvoiceApprovalStatus.PENDING]: { color: 'warning' },
  [PurchaseInvoiceApprovalStatus.WAITING]: { color: 'warning' },
  [PurchaseInvoiceApprovalStatus.APPROVED]: { color: 'success' },
  [PurchaseInvoiceApprovalStatus.REJECTED]: { color: 'error' }
}

export const statusChipValue: { [key: string]: { label: string; color: StatusChipColorType['color'] } } = {
  [PurchaseInvoiceApprovalStatus.PENDING]: { label: 'Menunggu', color: 'warning' },
  [PurchaseInvoiceApprovalStatus.WAITING]: { label: 'Menunggu', color: 'warning' },
  [PurchaseInvoiceApprovalStatus.APPROVED]: { label: 'Disetujui', color: 'success' },
  [PurchaseInvoiceApprovalStatus.REJECTED]: { label: 'Ditolak', color: 'error' }
}

type PurchaseInvoiceWithAction = PurchaseInvoice & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
}

// Column Definitions
const columnHelper = createColumnHelper<PurchaseInvoiceWithAction>()

export const tableColumns = (rowAction: RowActionType, userId?: string) => [
  columnHelper.accessor('number', {
    header: 'NO. FAKTUR',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('number', {
    header: 'NO. FAKTUR VENDOR',
    cell: ({ row }) => <Typography>{row.original.vendorNumber ?? '-'}</Typography>
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => {
      const ownApproval = row.original.approvals?.find(approval => approval.userId === userId)
      const approvalStatus = ownApproval?.status || row.original?.status

      return (
        <Chip
          label={statusChipValue[approvalStatus]?.label || 'Menunggu'}
          color={statusChipValue[approvalStatus]?.color || 'warning'}
          variant='tonal'
          size='small'
        />
      )
    }
  }),
  columnHelper.accessor('vendor.name', {
    header: 'VENDOR',
    cell: ({ row }) => <Typography>{row.original.vendor?.name || 'N/A'}</Typography>
  }),
  columnHelper.accessor('totalAmount', {
    header: 'TOTAL HARGA',
    cell: ({ row }) => <Typography color={colors.green.A400}>{toCurrency(row.original.totalAmount)}</Typography>
  }),
  columnHelper.accessor('createdAt', {
    header: 'TGL DIBUAT',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'dd/MM/yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('action', {
    header: 'ACTION',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
