// MUI Imports
import Grid from '@mui/material/Grid'

// Type Imports
import { Typography } from '@mui/material'

import PurchaseInvoiceList from './components/PurchaseInvoiceList'
import { PurchaseInvoiceContextProvider } from '../context/PurchaseInvoiceContext'

const PurchaseInvoiceListPage = () => {
  return (
    <PurchaseInvoiceContextProvider>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Faktur Pembelian Terbuat</Typography>
              <Typography>Lihat semua faktur pembelian perusahaan kamu</Typography>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <PurchaseInvoiceList />
        </Grid>
      </Grid>
    </PurchaseInvoiceContextProvider>
  )
}

export default PurchaseInvoiceListPage
