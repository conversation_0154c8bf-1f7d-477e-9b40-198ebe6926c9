// MUI Imports
import Grid from '@mui/material/Grid'

// Type Imports
import { Typography } from '@mui/material'

import PurchaseInvoiceList from './components/PurchaseInvoiceList'
import { PurchaseInvoiceContextProvider } from '../context/PurchaseInvoiceContext'

const PurchaseInvoiceApprovalListPage = () => {
  return (
    <PurchaseInvoiceContextProvider>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Persetujuan Faktur Pembelian</Typography>
              <Typography>Semua Faktur yang harus kamu setujui akan ditampilkan di sini</Typography>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <PurchaseInvoiceList />
        </Grid>
      </Grid>
    </PurchaseInvoiceContextProvider>
  )
}

export default PurchaseInvoiceApprovalListPage
