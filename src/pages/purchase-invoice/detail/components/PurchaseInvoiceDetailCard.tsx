// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

// Context
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'
import { useAuth } from '@/contexts/AuthContext'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ProjectStatus, ProjectType } from '@/types/projectTypes'

const PurchaseInvoiceDetailCard = () => {
  const { allSites, departmentList } = useAuth()
  const { purchaseInvoiceData } = usePurchaseInvoice()

  const {
    data: { items: projects }
  } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY, purchaseInvoiceData?.projectId],
    enabled: !!purchaseInvoiceData?.projectId,
    queryFn: async () => {
      return CompanyQueryMethods.getProjectList({ limit: Number.MAX_SAFE_INTEGER, status: ProjectStatus.ACTIVE })
    },
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  if (!purchaseInvoiceData) return null

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Detil Faktur</Typography>

        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Vendor
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {purchaseInvoiceData.vendor?.name || purchaseInvoiceData.vendorName || '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              No. Faktur Vendor
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {purchaseInvoiceData.vendorNumber || '-'}
            </Typography>
          </div>

          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Tanggal Faktur
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {formatDate(purchaseInvoiceData.invoiceDate, 'dd/MM/yyyy', { locale: id })}
            </Typography>
          </div>

          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Site
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {allSites?.find(site => site.id === purchaseInvoiceData.siteId)?.name || '-'}
            </Typography>
          </div>

          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Departemen
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {departmentList?.find(department => department.id === purchaseInvoiceData.departmentId)?.name || '-'}
            </Typography>
          </div>

          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Proyek
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {projects?.find(project => project.id === purchaseInvoiceData.projectId)?.name || '-'}
            </Typography>
          </div>

          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Metode Pembayaran
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {purchaseInvoiceData.paymentTerms || '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Jatuh Tempo
            </Typography>
            <Typography variant='body1' className='font-medium text-red-500'>
              {purchaseInvoiceData.paymentDueDate
                ? formatDate(purchaseInvoiceData.paymentDueDate, 'dd/MM/yyyy', { locale: id })
                : '-'}
            </Typography>
          </div>

          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Dibuat Oleh
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {purchaseInvoiceData.createdByUser?.fullName || '-'} ({purchaseInvoiceData.createdByUser?.title})
            </Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <Typography variant='body2' className='text-textSecondary'>
              Memo
            </Typography>
            <Typography variant='body1' className='font-medium'>
              {purchaseInvoiceData.note || '-'}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default PurchaseInvoiceDetailCard
