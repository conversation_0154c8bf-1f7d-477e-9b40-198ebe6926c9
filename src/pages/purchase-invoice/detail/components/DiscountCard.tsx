import {
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  FormHelperText
} from '@mui/material'
import { useFormContext, Controller, useWatch } from 'react-hook-form'
import { useMemo } from 'react'

import { PurchaseInvoiceDiscountType } from '@/types/purchaseInvoiceTypes'
import CurrencyField from '@/components/numeric/CurrencyField'
import NumberField from '@/components/numeric/NumberField'
import { toCurrency } from '@/utils/helper'
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'

// Discount type options for purchase invoice
const discountTypeOptions = [
  { value: PurchaseInvoiceDiscountType.PERCENTAGE, label: 'Persentase' },
  { value: PurchaseInvoiceDiscountType.FLAT, label: 'Nominal' }
]

const DiscountCard = () => {
  const { purchaseInvoiceData } = usePurchaseInvoice()

  if (!purchaseInvoiceData || !purchaseInvoiceData.otherExpense?.length || !purchaseInvoiceData.discountValue)
    return null

  const isPercentage = purchaseInvoiceData?.discountType === PurchaseInvoiceDiscountType.PERCENTAGE

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-start'>
            <Typography variant='h5'>Diskon Faktur (Opsional)</Typography>
          </div>

          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Tipe Diskon</InputLabel>
                <Select
                  label='Tipe Diskon'
                  value={purchaseInvoiceData?.discountType}
                  readOnly
                  IconComponent={() => null}
                >
                  {discountTypeOptions.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label='Jumlah Diskon'
                value={purchaseInvoiceData?.discountValue}
                InputProps={{
                  endAdornment: isPercentage ? '%' : '',
                  inputComponent: (isPercentage ? NumberField : CurrencyField) as any,
                  inputProps: {
                    prefix: isPercentage ? '' : 'Rp',
                    name: 'discountValue',
                    value: purchaseInvoiceData?.discountValue,
                    allowLeadingZeros: false
                  },
                  readOnly: true
                }}
              />
            </Grid>
          </Grid>

          {/* Discount Summary Section */}
          <div className='bg-[rgba(75,216,139,0.25)] rounded-lg p-4'>
            <div className='flex justify-center gap-4 items-center'>
              <Typography variant='body1' className='font-medium text-[#4C4E64]'>
                Diskon Faktur
              </Typography>
              <Typography variant='body1' className='font-bold text-[#4C4E64]'>
                {toCurrency(purchaseInvoiceData?.discountAmount)}
              </Typography>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default DiscountCard
