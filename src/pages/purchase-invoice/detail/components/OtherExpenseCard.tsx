// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Table from '@mui/material/Table'
import TableBody from '@mui/material/TableBody'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'

// Utils
import { toCurrency } from '@/utils/helper'

// Context
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'

const OtherExpenseCard = () => {
  const { purchaseInvoiceData } = usePurchaseInvoice()

  if (!purchaseInvoiceData || !purchaseInvoiceData.otherExpense?.length) return null

  const otherExpenses = purchaseInvoiceData.otherExpense

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Biaya Lain-Lain</Typography>

        <TableContainer>
          <Table>
            <TableHead>
              <TableRow className='bg-green-50'>
                <TableCell className='font-medium text-gray-700'>AKUN PERKIRAAN</TableCell>
                <TableCell className='font-medium text-gray-700'>NOMINAL</TableCell>
                <TableCell className='font-medium text-gray-700'>MEMO</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {otherExpenses.map((expense, index) => (
                <TableRow key={index} className='hover:bg-gray-50'>
                  <TableCell className='text-gray-600'>
                    [{expense.account?.code || '-'}] {expense.account?.name || 'Unknown Account'}
                  </TableCell>
                  <TableCell className='text-gray-600'>{toCurrency(expense.amount || 0)}</TableCell>
                  <TableCell className='text-gray-600'>{expense.note || '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  )
}

export default OtherExpenseCard
