// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'

// Context
import { usePurchaseInvoice } from '../../context/PurchaseInvoiceContext'
import { FileMimeType } from '@/utils/mimeType'

const DocumentCard = () => {
  const { purchaseInvoiceData } = usePurchaseInvoice()

  if (!purchaseInvoiceData) return null

  const handleDownload = () => {
    if (purchaseInvoiceData.documentUrl) {
      window.open(purchaseInvoiceData.documentUrl, '_blank')
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Dokumen Penyerta</Typography>

        {purchaseInvoiceData.documentUrl ? (
          <div className='flex justify-between items-center p-3 bg-gray-50 rounded-lg'>
            <div className='flex flex-col gap-1'>
              <Typography variant='body1' className='font-medium'>
                Dokumen_Penyerta.{FileMimeType[purchaseInvoiceData?.documentMimeType]}
              </Typography>
            </div>
            <Button
              variant='text'
              color='primary'
              onClick={handleDownload}
              className='text-green-600 hover:text-green-700'
            >
              Unduh
            </Button>
          </div>
        ) : (
          <Typography variant='body2' className='text-textSecondary text-center py-4'>
            Tidak ada dokumen yang dilampirkan
          </Typography>
        )}
      </CardContent>
    </Card>
  )
}

export default DocumentCard
