import React from 'react'
import PurchaseOrderDetailCard from './PurchaseOrderDetailCard'

// Example usage of PurchaseOrderDetailCard component
const ExampleUsage: React.FC = () => {
  // Sample data that matches the component's expected structure
  const sampleData = {
    poNumber: 'PO-2024-001',
    createdAt: '2024-01-15T10:30:00Z',
    totalPurchase: 15000000,
    items: [
      {
        id: 1,
        itemCode: 'ITM-001',
        itemName: 'Steel Pipe 2 inch',
        pricePerUnit: 150000,
        quantity: 50,
        remainingQuantity: 20,
        total: 7500000
      },
      {
        id: 2,
        itemCode: 'ITM-002',
        itemName: 'Steel Pipe 3 inch',
        pricePerUnit: 200000,
        quantity: 25,
        remainingQuantity: 10,
        total: 5000000
      },
      {
        id: 3,
        itemCode: 'ITM-003',
        itemName: 'Steel Pipe 4 inch',
        pricePerUnit: 250000,
        quantity: 10,
        remainingQuantity: 5,
        total: 2500000
      }
    ],
    advancePayments: [
      {
        id: 'AP-001',
        invoiceNumber: 'INV-2024-001',
        invoiceDate: '2024-01-10T00:00:00Z',
        amount: 3000000
      },
      {
        id: 'AP-002',
        invoiceNumber: 'INV-2024-002',
        invoiceDate: '2024-01-12T00:00:00Z',
        amount: 2000000
      }
    ],
    subTotal: 15000000,
    totalAdvancePayment: 5000000,
    totalPayable: 10000000
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>Purchase Order Detail Card Example</h2>
      <PurchaseOrderDetailCard data={sampleData} />
    </div>
  )
}

export default ExampleUsage
