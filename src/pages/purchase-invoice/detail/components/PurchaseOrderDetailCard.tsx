import React from 'react'
import { Box, Typography, Table, TableHead, TableBody, TableRow, TableCell, Grid, IconButton } from '@mui/material'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { toCurrency } from '@/utils/helper'

// Types for the component props
export interface PurchaseOrderItem {
  id: number
  itemCode: string
  itemName: string
  pricePerUnit: number
  quantity: number
  remainingQuantity: number
  total: number
}

export interface AdvancePayment {
  id: string
  invoiceNumber: string
  invoiceDate: string
  amount: number
}

export interface PurchaseOrderDetailData {
  poNumber: string
  createdAt: string
  totalPurchase: number
  items: PurchaseOrderItem[]
  advancePayments: AdvancePayment[]
  subTotal: number
  totalAdvancePayment: number
  totalPayable: number
}

export interface PurchaseOrderDetailCardProps {
  data: PurchaseOrderDetailData
}

const PurchaseOrderDetailCard: React.FC<PurchaseOrderDetailCardProps> = ({ data }) => {
  return (
    <Box
      sx={{
        backgroundColor: 'rgba(76, 78, 100, 0.05)',
        borderRadius: '8px',
        padding: '16px',
        display: 'flex',
        flexDirection: 'column',
        gap: '24px'
      }}
    >
      {/* Header Section */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Typography
          sx={{
            fontWeight: 500,
            fontSize: '20px',
            lineHeight: '1.334em',
            color: 'rgba(76, 78, 100, 0.87)'
          }}
        >
          No. PO: {data.poNumber}
        </Typography>
        <Typography
          sx={{
            fontWeight: 400,
            fontSize: '14px',
            lineHeight: '1.5em',
            color: 'rgba(76, 78, 100, 0.6)'
          }}
        >
          {format(new Date(data.createdAt), 'eeee dd/MM/yyyy, HH:mm', { locale: id })}
        </Typography>
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: '16px',
            lineHeight: '1.334em',
            color: '#4BD88B'
          }}
        >
          Total Purchase {toCurrency(data.totalPurchase)}
        </Typography>
      </Box>

      {/* Barang Section */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: '14px',
            lineHeight: '1.5em',
            color: 'rgba(76, 78, 100, 0.6)'
          }}
        >
          Barang
        </Typography>

        {/* Barang Table */}
        <Box
          sx={{
            backgroundColor: 'white',
            borderRadius: '10px',
            boxShadow: '0px 2px 10px 0px rgba(76, 78, 100, 0.22)',
            overflow: 'hidden'
          }}
        >
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#DBF7E8' }}>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    width: '160px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Kode Barang
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Nama Item
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Harga Satuan
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Qty
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    width: '128px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Sisa Qty
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Total
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    width: '128px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)',
                    textAlign: 'center'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Action
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.items.map(item => (
                <TableRow key={item.id} sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                  {/* Kode Barang */}
                  <TableCell
                    sx={{
                      padding: '15px 16px 15px 20px',
                      width: '160px'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.5em',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      {item.itemCode}
                    </Typography>
                  </TableCell>

                  {/* Nama Item */}
                  <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.5em',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      {item.itemName}
                    </Typography>
                  </TableCell>

                  {/* Harga Satuan */}
                  <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.5em',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      {toCurrency(item.pricePerUnit)}
                    </Typography>
                  </TableCell>

                  {/* Qty */}
                  <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.5em',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      {item.quantity} pcs
                    </Typography>
                  </TableCell>

                  {/* Sisa Qty */}
                  <TableCell
                    sx={{
                      padding: '15px 16px 15px 20px',
                      width: '128px'
                    }}
                  >
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.5em',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      {item.remainingQuantity} pcs
                    </Typography>
                  </TableCell>

                  {/* Total */}
                  <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.5em',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      {toCurrency(item.total)}
                    </Typography>
                  </TableCell>

                  {/* Action */}
                  <TableCell
                    sx={{
                      padding: '16px',
                      width: '128px',
                      textAlign: 'center'
                    }}
                  >
                    <IconButton>
                      <i className='ri-eye-line' />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Box>
      </Box>

      {/* Uang Muka Section */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: '14px',
            lineHeight: '1.5em',
            color: 'rgba(76, 78, 100, 0.6)'
          }}
        >
          Uang Muka
        </Typography>

        {/* Uang Muka Table */}
        <Box
          sx={{
            backgroundColor: 'white',
            borderRadius: '10px',
            boxShadow: '0px 2px 10px 0px rgba(76, 78, 100, 0.22)',
            overflow: 'hidden'
          }}
        >
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#DBF7E8' }}>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    No. Faktur
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Tanggal Faktur
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Jumlah Uang Muka
                  </Typography>
                </TableCell>
                <TableCell
                  sx={{
                    padding: '16px 16px 16px 20px',
                    width: '128px',
                    borderBottom: '1px solid rgba(76, 78, 100, 0.12)',
                    textAlign: 'center'
                  }}
                >
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '12px',
                      lineHeight: '2em',
                      letterSpacing: '1.42%',
                      textTransform: 'uppercase',
                      color: 'rgba(76, 78, 100, 0.87)'
                    }}
                  >
                    Action
                  </Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.advancePayments.map(payment => (
                <TableRow key={payment.id} sx={{ borderBottom: '1px solid rgba(76, 78, 100, 0.12)' }}>
                  {/* No. Faktur */}
                  <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.5em',
                        color: '#4BD88B'
                      }}
                    >
                      {payment.invoiceNumber}
                    </Typography>
                  </TableCell>

                  {/* Tanggal Faktur */}
                  <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.5em',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      {format(new Date(payment.invoiceDate), 'dd/MM/yyyy')}
                    </Typography>
                  </TableCell>

                  {/* Jumlah Uang Muka */}
                  <TableCell sx={{ padding: '15px 16px 15px 20px' }}>
                    <Typography
                      sx={{
                        fontWeight: 400,
                        fontSize: '14px',
                        lineHeight: '1.5em',
                        color: 'rgba(76, 78, 100, 0.87)'
                      }}
                    >
                      {toCurrency(payment.amount)}
                    </Typography>
                  </TableCell>

                  {/* Action */}
                  <TableCell
                    sx={{
                      padding: '16px',
                      width: '128px',
                      textAlign: 'center'
                    }}
                  >
                    <IconButton>
                      <i className='ri-eye-line' />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Box>
      </Box>

      {/* Summary Section */}
      <Grid container spacing={2}>
        {/* Sub Total */}
        <Grid item xs={12} md={4}>
          <Box
            sx={{
              backgroundColor: 'rgba(76, 78, 100, 0.05)',
              borderRadius: '8px',
              padding: '8px 16px',
              display: 'flex',
              flexDirection: 'column',
              gap: '4px'
            }}
          >
            <Typography
              sx={{
                fontWeight: 400,
                fontSize: '12px',
                lineHeight: '1em',
                letterSpacing: '1.25%',
                color: 'rgba(76, 78, 100, 0.6)'
              }}
            >
              Sub Total
            </Typography>
            <Typography
              sx={{
                fontWeight: 600,
                fontSize: '16px',
                lineHeight: '1.5em',
                letterSpacing: '0.94%',
                color: 'rgba(76, 78, 100, 0.87)'
              }}
            >
              {toCurrency(data.subTotal)}
            </Typography>
          </Box>
        </Grid>

        {/* Total Uang Muka */}
        <Grid item xs={12} md={4}>
          <Box
            sx={{
              backgroundColor: 'rgba(76, 78, 100, 0.05)',
              borderRadius: '8px',
              padding: '8px 16px',
              display: 'flex',
              flexDirection: 'column',
              gap: '4px'
            }}
          >
            <Typography
              sx={{
                fontWeight: 400,
                fontSize: '12px',
                lineHeight: '1em',
                letterSpacing: '1.25%',
                color: 'rgba(76, 78, 100, 0.6)'
              }}
            >
              Total Uang Muka
            </Typography>
            <Typography
              sx={{
                fontWeight: 600,
                fontSize: '16px',
                lineHeight: '1.5em',
                letterSpacing: '0.94%',
                color: 'rgba(76, 78, 100, 0.87)'
              }}
            >
              {toCurrency(data.totalAdvancePayment)}
            </Typography>
          </Box>
        </Grid>

        {/* Total Bayar */}
        <Grid item xs={12} md={4}>
          <Box
            sx={{
              backgroundColor: '#DBF7E8',
              borderRadius: '8px',
              padding: '8px 16px',
              display: 'flex',
              flexDirection: 'column',
              gap: '4px'
            }}
          >
            <Typography
              sx={{
                fontWeight: 400,
                fontSize: '12px',
                lineHeight: '1em',
                letterSpacing: '1.25%',
                color: 'rgba(76, 78, 100, 0.6)'
              }}
            >
              Total Bayar
            </Typography>
            <Typography
              sx={{
                fontWeight: 600,
                fontSize: '16px',
                lineHeight: '1.5em',
                letterSpacing: '0.94%',
                color: '#4BD88B'
              }}
            >
              {toCurrency(data.totalPayable)}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Box>
  )
}

export default PurchaseOrderDetailCard
