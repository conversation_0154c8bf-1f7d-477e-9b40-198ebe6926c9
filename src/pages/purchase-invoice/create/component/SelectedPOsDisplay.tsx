import { Box, Button, TextField, Typography } from '@mui/material'
import { Controller, useFormContext, useFieldArray } from 'react-hook-form'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'

import { PurchaseInvoiceDtoType } from '../config/schema'
import { PoType } from '@/pages/purchase-order/config/types'
import { formatThousandSeparator } from '@/utils/helper'
import Currency<PERSON>ield from '@/components/numeric/CurrencyField'

interface SelectedPOsDisplayProps {
  selectedPOs: PoType[]
  onRemovePO: (poId: string) => void
  fields: any[]
}

const SelectedPOsDisplay = ({ selectedPOs, onRemovePO, fields }: SelectedPOsDisplayProps) => {
  const { control } = useFormContext<PurchaseInvoiceDtoType>()

  return (
    <>
      {selectedPOs.map(po => (
        <Box
          key={po.id}
          sx={{
            backgroundColor: 'rgba(76, 78, 100, 0.05)',
            borderRadius: '8px',
            padding: '16px',
            display: 'flex',
            flexDirection: 'column',
            gap: '24px'
          }}
        >
          {/* Header with PO info and delete button */}
          <div className='flex justify-between items-start'>
            <div className='flex flex-col gap-2'>
              <Typography variant='h6' sx={{ fontWeight: 500, fontSize: '20px', lineHeight: '1.334em' }}>
                No. PO: {po.number}
              </Typography>
              <Typography variant='body2' sx={{ color: 'rgba(76, 78, 100, 0.6)', fontSize: '14px' }}>
                {format(new Date(po.createdAt), 'eeee dd/MM/yyyy, HH:mm', { locale: id })}
              </Typography>
              <Typography
                variant='body1'
                sx={{
                  fontWeight: 700,
                  fontSize: '16px',
                  color: '#4BD88B',
                  lineHeight: '1.334em'
                }}
              >
                Total Purchase Rp {formatThousandSeparator(po.grandTotal)}
              </Typography>
            </div>
            <div className='flex items-center' style={{ height: '38px' }}>
              <Button
                variant='outlined'
                color='error'
                size='medium'
                onClick={() => onRemovePO(po.id)}
                sx={{
                  borderRadius: '8px',
                  borderColor: 'rgba(255, 77, 73, 0.5)',
                  color: '#FF4D49',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 77, 73, 0.04)'
                  }
                }}
              >
                Hapus PO
              </Button>
            </div>
          </div>

          {/* Down payment amount input */}
          <div className='flex items-center'>
            <Controller
              control={control}
              name={`orders.${fields.findIndex(field => {
                return field.purchaseOrderId === po.id
              })}.downPaymentAmount`}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  label='Jumlah Uang Muka'
                  error={!!error}
                  helperText={error?.message}
                  value={field.value || ''}
                  InputProps={{
                    inputComponent: CurrencyField as any,
                    inputProps: {
                      prefix: 'Rp',
                      name: `orders.${fields.findIndex(field => field.purchaseOrderId === po.id)}.downPaymentAmount`,
                      onChange: (e: any) => {
                        field.onChange(e.target.value)
                      },
                      value: field.value,
                      allowLeadingZeros: false,
                      isAllowed: ({ floatValue }) => floatValue <= (po.grandTotal ?? 0) || floatValue === undefined
                    },
                    className: 'bg-white'
                  }}
                />
              )}
            />
          </div>
        </Box>
      ))}
    </>
  )
}

export default SelectedPOsDisplay
