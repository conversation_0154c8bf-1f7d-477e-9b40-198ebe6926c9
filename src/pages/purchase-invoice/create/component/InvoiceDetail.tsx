import {
  <PERSON>rid,
  <PERSON><PERSON>ield,
  Card,
  CardContent,
  Typography,
  Autocomplete,
  CircularProgress,
  Checkbox,
  FormControlLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useState, useCallback } from 'react'
import { useQuery } from '@tanstack/react-query'

import { PurchaseInvoiceDtoType } from '../config/schema'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { VendorType } from '@/types/companyTypes'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY, VENDOR_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import { PurchaseOrderPaymentMethod } from '@/pages/purchase-order/config/enum'
import { addDays, differenceInDays, formatISO, startOfDay, toDate } from 'date-fns'
import { ProjectStatus, ProjectType } from '@/types/projectTypes'
import { useAuth } from '@/contexts/AuthContext'
import { useSearchParams } from 'react-router-dom'

// Simple debounce function
const debounce = (func: Function, delay: number) => {
  let timeoutId: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(null, args), delay)
  }
}

const InvoiceDetail = () => {
  const { control } = useFormContext<PurchaseInvoiceDtoType>()
  const { userProfile, departmentList } = useAuth()
  const [searchParams] = useSearchParams()
  const [vendorSearchQuery, setVendorSearchQuery] = useState('')
  const [selectedVendor, setSelectedVendor] = useState<VendorType | null>(null)

  // Watch form values
  const paymentMethodWatch = useWatch({
    control,
    name: 'paymentTerms',
    defaultValue: ''
  })

  const isGeneralPurchaseWatch = useWatch({
    control,
    name: 'isGeneralPurchase',
    defaultValue: false
  })

  // Fetch vendor list
  const { data: vendorListResponse = defaultListData as ListResponse<VendorType>, isLoading: fetchVendorsLoading } =
    useQuery({
      enabled: !!vendorSearchQuery,
      queryKey: [VENDOR_LIST_QUERY_KEY, vendorSearchQuery],
      queryFn: () => {
        return CompanyQueryMethods.getVendorList({
          ...(vendorSearchQuery && { search: vendorSearchQuery }),
          limit: 100
        })
      },
      placeholderData: defaultListData as ListResponse<VendorType>
    })

  const vendorList = vendorListResponse.items || []

  // Debounced search handler
  const handleVendorInputChange = useCallback(
    debounce((_event: any, newValue: string, reason: string) => {
      if (reason === 'input') {
        setVendorSearchQuery(newValue)
      }
    }, 700),
    []
  )

  const {
    data: { items: projects }
  } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY],
    queryFn: async () => {
      return CompanyQueryMethods.getProjectList({ limit: Number.MAX_SAFE_INTEGER, status: ProjectStatus.ACTIVE })
    },
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <Typography variant='h5'>Detil Faktur</Typography>

        <Grid container spacing={4}>
          {/* Pembelian di Vendor Umum Checkbox */}
          {/* <Grid item xs={12}>
            <Controller
              control={control}
              name='isGeneralPurchase'
              render={({ field: { value, onChange } }) => (
                <FormControlLabel
                  control={<Checkbox checked={value} onChange={e => onChange(e.target.checked)} />}
                  label='Pembelian di Vendor Umum'
                />
              )}
            />
          </Grid> */}

          {/* Vendor Selection */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='isGeneralPurchase'
              render={({ field: { value, onChange } }) => (
                <FormControlLabel
                  label='Pembelian di Vendor Umum'
                  control={<Checkbox checked={value} onChange={onChange} />}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {!!isGeneralPurchaseWatch ? (
              <Controller
                control={control}
                name='vendorName'
                render={({ field, fieldState: { error } }) => (
                  <TextField {...field} label='Nama Vendor' error={!!error} />
                )}
              />
            ) : (
              <Controller
                control={control}
                name='vendorId'
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <Autocomplete
                    key={JSON.stringify(selectedVendor)}
                    filterOptions={x => x}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    onInputChange={handleVendorInputChange}
                    options={vendorList}
                    freeSolo
                    onChange={(_e, newValue: VendorType | null) => {
                      if (newValue) {
                        setSelectedVendor(newValue)
                        onChange(newValue.id)
                      } else {
                        setSelectedVendor(null)
                        onChange('')
                      }
                    }}
                    value={selectedVendor || vendorList.find(v => v.id === value) || null}
                    noOptionsText='Vendor tidak ditemukan'
                    loading={fetchVendorsLoading}
                    renderInput={params => (
                      <TextField
                        {...params}
                        label='Vendor'
                        placeholder='Cari Vendor'
                        variant='outlined'
                        error={!!error}
                        helperText={error?.message}
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: (
                            <>
                              {fetchVendorsLoading ? <CircularProgress size={20} /> : null}
                              {params.InputProps.endAdornment}
                            </>
                          ),
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                      />
                    )}
                    getOptionLabel={(option: VendorType) => option?.name || ''}
                    renderOption={(props, option) => {
                      const { key, ...optionProps } = props
                      return (
                        <li key={key} {...optionProps}>
                          <Typography>
                            {option.code} - {option.name}
                          </Typography>
                        </li>
                      )
                    }}
                  />
                )}
              />
            )}
          </Grid>

          {/* Invoice Number */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='vendorNumber'
              rules={{ required: 'No. Faktur Vendor wajib diisi' }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='No. Faktur Vendor'
                  placeholder='Isi nomor faktur dari vendor'
                  error={!!error}
                  helperText={error?.message}
                />
              )}
            />
          </Grid>

          {/* Payment Method */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='paymentTerms'
              rules={{ required: 'Metode Bayar wajib dipilih' }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel>Metode Bayar</InputLabel>
                  <Select value={value} onChange={onChange} label='Metode Bayar'>
                    <MenuItem value=''>
                      <em>Pilih</em>
                    </MenuItem>
                    {paymentMethodOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {error && <FormHelperText>{error.message}</FormHelperText>}
                </FormControl>
              )}
            />
          </Grid>

          {/* Payment Due Date - only show if payment method is NET */}
          {paymentMethodWatch === PurchaseOrderPaymentMethod.NET && (
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='paymentDueDays'
                rules={{ required: 'Tanggal Jatuh Tempo wajib diisi' }}
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={addDays(startOfDay(new Date()), value)}
                    onChange={(date: Date) => {
                      onChange(differenceInDays(date, startOfDay(new Date())))
                    }}
                    dateFormat='dd/MM/yyyy'
                    minDate={startOfDay(new Date())}
                    customInput={
                      <TextField
                        fullWidth
                        label='Tanggal Jatuh Tempo'
                        error={!!error}
                        helperText={error?.message}
                        InputProps={{
                          readOnly: true
                        }}
                      />
                    }
                  />
                )}
              />
            </Grid>
          )}

          {/* Invoice Date */}
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='invoiceDate'
              rules={{ required: 'Tanggal Faktur wajib diisi' }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(formatISO(date))}
                  dateFormat='dd/MM/yyyy'
                  customInput={
                    <TextField
                      fullWidth
                      label='Tanggal Faktur'
                      placeholder='19/05/2025'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>

          {/* Down Payment Checkbox */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='isDownPayment'
              render={({ field: { value, onChange } }) => (
                <FormControlLabel
                  control={<Checkbox checked={value} onChange={e => onChange(e.target.checked)} />}
                  label='Pembayaran Untuk Uang Muka'
                />
              )}
            />
          </Grid>

          {/* Memo */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Memo'
                  placeholder='Tambahkan catatan (opsional)'
                  multiline
                  rows={2}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <Controller
              name='siteId'
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel id='role-select'>Site</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-siteId'
                    value={value}
                    onChange={e => onChange((e.target as HTMLInputElement).value)}
                    label='Site'
                    size='medium'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Pilih Site' }}
                    defaultValue=''
                  >
                    {userProfile?.sites?.map(site => (
                      <MenuItem key={site.id} value={site.id}>
                        {site.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <Controller
              name='departmentId'
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel id='role-select'>Departemen</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-departmentId'
                    value={value}
                    onChange={e => onChange((e.target as HTMLInputElement).value)}
                    label='Departemen'
                    size='medium'
                    labelId='departmentId-select'
                    inputProps={{ placeholder: 'Pilih Departemen' }}
                    defaultValue=''
                  >
                    {departmentList?.map(department => (
                      <MenuItem key={department.id} value={department.id}>
                        {department.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12} md={5}>
            <Controller
              control={control}
              name='projectId'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth error={!!error}>
                  <InputLabel id='select-projectId'>Proyek</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-projectId'
                    value={value}
                    readOnly={!!searchParams.get('projectId')}
                    onChange={e => onChange((e.target as HTMLInputElement).value)}
                    label='Proyek'
                    labelId='select-projectId'
                    inputProps={{ placeholder: 'Pilih Proyek' }}
                  >
                    {projects?.map(project => (
                      <MenuItem key={project.id} value={project.id}>
                        {project.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default InvoiceDetail
