import { <PERSON><PERSON>, <PERSON>, CardContent, Typo<PERSON>, IconButton } from '@mui/material'
import { useFormContext, useFieldArray } from 'react-hook-form'
import { useState } from 'react'

import { PurchaseInvoiceDtoType } from '../config/schema'
import { PurchaseInvoiceExpense } from '@/types/purchaseInvoiceTypes'
import AddExpenseDialog from '@/components/dialogs/add-expense-dialog'
import { toCurrency } from '@/utils/helper'

const OtherExpenses = () => {
  const { control } = useFormContext<PurchaseInvoiceDtoType>()
  const [dialogOpen, setDialogOpen] = useState(false)
  const [selectedExpenses, setSelectedExpenses] = useState<PurchaseInvoiceExpense[]>([])

  // Use useFieldArray to manage the otherExpenses array in the form
  const { append, remove } = useFieldArray({
    control,
    name: 'otherExpenses'
  })

  const handleDeleteExpense = (index: number) => {
    // Remove from local state
    setSelectedExpenses(current => current.filter((_, i) => i !== index))
    // Remove from form state
    remove(index)
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-start'>
            <Typography variant='h5'>Biaya Lain-Lain (Opsional)</Typography>
            <Button variant='contained' onClick={() => setDialogOpen(true)}>
              Tambah
            </Button>
          </div>

          {selectedExpenses.length === 0 ? (
            <div className='text-center py-8'>
              <Typography color='textSecondary'>Belum ada Biaya Lain-Lain yang dipilih</Typography>
              <Typography variant='body2' color='textSecondary' className='mt-2'>
                Klik "Tambah" untuk memilih Biaya Lain-Lain
              </Typography>
            </div>
          ) : (
            <div className='overflow-x-auto'>
              <table className='w-full border-collapse'>
                <thead>
                  <tr className='bg-[#DBF7E8]'>
                    <th className='text-left px-5 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F]'>
                      AKUN PERKIRAAN
                    </th>
                    <th className='text-left px-4 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F]'>
                      NOMINAL
                    </th>
                    <th className='text-left px-4 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F]'>
                      MEMO
                    </th>
                    <th className='text-center px-4 py-4 text-xs font-medium text-[#4C4E64] uppercase tracking-wider border-b border-[#4C4E641F] w-24'>
                      ACTION
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {selectedExpenses.map((expense, index) => (
                    <tr key={index} className='border-b border-[#4C4E641F]'>
                      <td className='px-5 py-4 text-sm text-[#4C4E64]'>
                        [{expense.account?.code}] {expense.account?.name}
                      </td>
                      <td className='px-4 py-4 text-sm text-[#4C4E64]'>{toCurrency(expense.amount)}</td>
                      <td className='px-4 py-4 text-sm text-[#4C4E64]'>{expense.note}</td>
                      <td className='px-4 py-4 text-center'>
                        <IconButton
                          size='small'
                          onClick={() => handleDeleteExpense(index)}
                          className='text-[#4C4E648A] hover:text-[#4C4E64]'
                        >
                          <i className='ri-delete-bin-line text-lg' />
                        </IconButton>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
      {dialogOpen && (
        <AddExpenseDialog
          open={dialogOpen}
          setOpen={setDialogOpen}
          onSubmit={expense => {
            // Add to local state for display
            setSelectedExpenses(current => [...current, expense])
            // Add to form state
            append({
              accountId: expense.accountId,
              amount: expense.amount,
              note: expense.note
            })
            setDialogOpen(false)
          }}
        />
      )}
    </>
  )
}

export default OtherExpenses
