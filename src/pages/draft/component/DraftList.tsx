import { MouseEvent, useEffect, useMemo, useState } from 'react'
import { Box, IconButton, Menu, MenuItem, Typography } from '@mui/material'
import Card from '@mui/material/Card'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useRouter } from '@/routes/hooks'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { useDraft } from '../context/DraftContext'
import { tableColumns } from '../config/table'

const DraftList = () => {
  const router = useRouter()
  const {
    isMobile,
    draftParams,
    setDraftParams,
    setPartialDraftParams,
    draftListResponse,
    refetchDraft,
    handleDeleteDraft,
    redirect
  } = useDraft()

  const { search, limit, page } = draftParams
  const { totalItems, totalPages, limit: limitPages, page: pagePages } = draftListResponse

  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  const [actionBtn, setBtn] = useState<boolean>(false)
  const [searchExtend, setSearchExtend] = useState<boolean>(false)

  const tableOptions = useMemo(
    () => ({
      data: draftListResponse?.items ?? [],
      columns: tableColumns({
        onLoadingDraft: async row => {
          const redirectUrl = await redirect(row)
          const url = new URL(redirectUrl, window.location.origin)
          const params = new URLSearchParams(url.search)

          params.set('draft', row.id)

          url.search = params.toString()
          router.push(`${url.pathname}${url.search}`)
        },
        onDeleteDraft: id => handleDeleteDraft(id)
      }),
      initialState: {
        pagination: {
          pageSize: limit,
          pageIndex: page - 1
        }
      },
      state: {
        pagination: {
          pageSize: limitPages,
          pageIndex: pagePages - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [draftListResponse, draftParams]
  )

  const table = useReactTable<any>(tableOptions)

  const actionButton = (
    <></>
    // <Button
    //   color='secondary'
    //   variant='outlined'
    //   startIcon={<i className='ri-upload-2-line' />}
    //   className='is-full sm:is-auto'
    // >
    //   Ekspor
    // </Button>
  )

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setDraftParams(prev => ({ ...prev, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setDraftParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'></div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Draft</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua draft yang telah kamu buat akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setDraftParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialDraftParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setDraftParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialDraftParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default DraftList
