// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Typography } from '@mui/material'
import DraftList from './component/DraftList'
import { useDraft } from './context/DraftContext'

const DraftListPage = () => {
  const { pageName } = useDraft()
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>List Draft {pageName ?? ''}</Typography>
            <Typography>Semua draft {pageName ?? ''} yang sudah terdaftar akan ditampilkan di sini</Typography>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <DraftList />
      </Grid>
    </Grid>
  )
}

export default DraftListPage
