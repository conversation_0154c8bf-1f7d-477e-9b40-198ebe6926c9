import ValidateQueryMethods, { VALIDATE_QUERY_KEY } from '@/api/services/validate-doc/query'
import { PoValidationDoc } from '@/types/validationDocTypes'
import { Box, Skeleton, Table, TableBody, TableCell, TableHead, TableRow, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { useLocation, useParams } from 'react-router-dom'
import { Fragment } from 'react/jsx-runtime'

const formatDate = (input: string) => {
  try {
    return format(new Date(input), 'dd MMMM yyyy', { locale: id })
  } catch (error) {
    return '-'
  }
}

const ServiceOrderDoc = () => {
  const params = useParams()
  const location = useLocation()
  const query = new URLSearchParams(location.search)
  const signId = query.get('signature')

  const {
    data: validationData,
    isLoading,
    error
  } = useQuery<PoValidationDoc, { message: string }>({
    queryKey: [VALIDATE_QUERY_KEY, params.id, signId],
    enabled: !!signId && !!params.id,
    queryFn: () => ValidateQueryMethods.getSoDetail(params.id, signId)
  })

  return (
    <Box className='container px-4 md:px-0 text-[#000] flex py-0 md:py-2 flex-col gap-4 max-w-lg justify-self-center'>
      {!error ? (
        <Fragment>
          {isLoading ? (
            <Skeleton height={300} variant='rectangular' />
          ) : (
            <Fragment>
              <Box className='flex justify-center items-center p-5 -mx-4 md:p-6 bg-[#4cb050] text-slate-50'>
                <Typography color='currentcolor' variant='h4' className='font-bold'>
                  Validasi Dokumen
                </Typography>
              </Box>
              <Typography className='font-bold' variant='h5'>
                Detail Dokumen
              </Typography>
              <Box className='grid grid-cols-3'>
                <Typography className='border p-1 px-2 odd:bg-[var(--mui-palette-grey-200)] odd:font-bold'>
                  Nama Dokumen
                </Typography>
                <Typography className='border p-1 px-2 text-black text-wrap break-words col-span-2'>
                  Service Order
                </Typography>
                <Typography className='border p-1 px-2 odd:bg-[var(--mui-palette-grey-200)] odd:font-bold'>
                  Nomor Dokumen
                </Typography>
                <Typography className='border p-1 px-2 text-black text-wrap break-words col-span-2'>
                  {validationData?.number ?? '-'}
                </Typography>
                <Typography className='border p-1 px-2 odd:bg-[var(--mui-palette-grey-200)] odd:font-bold'>
                  Nama Pembuat
                </Typography>
                <Typography className='border p-1 px-2 text-black text-wrap break-words col-span-2'>
                  {validationData?.createdByUser?.fullName} ({validationData?.createdByUser?.title})
                </Typography>
                <Typography className='border p-1 px-2 odd:bg-[var(--mui-palette-grey-200)] odd:font-bold'>
                  Nama Vendor
                </Typography>
                <Typography className='border p-1 px-2 text-black text-wrap break-words col-span-2'>
                  {validationData?.vendor?.name ?? '-'}
                </Typography>
                <Typography className='border p-1 px-2 odd:bg-[var(--mui-palette-grey-200)] odd:font-bold'>
                  Tanggal Dokumen
                </Typography>
                <Typography className='border p-1 px-2 text-black text-wrap break-words col-span-2'>
                  {formatDate(validationData?.createdAt) ?? '-'}
                </Typography>
              </Box>
              <Typography className='font-bold' variant='h5'>
                Riwayat Persetujuan
              </Typography>
              <Table>
                <TableHead sx={{ backgroundColor: 'var(--mui-palette-grey-200)' }}>
                  <TableRow>
                    <TableCell>No</TableCell>
                    <TableCell>Nama</TableCell>
                    <TableCell>Jabatan</TableCell>
                    <TableCell>Tanggal</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {isLoading ? (
                    <Skeleton />
                  ) : (
                    validationData?.approvals?.map((app, idx) => (
                      <TableRow key={idx}>
                        <TableCell>{idx + 1}</TableCell>
                        <TableCell>{app.user?.fullName}</TableCell>
                        <TableCell>{app.user?.title}</TableCell>
                        <TableCell>{app.respondedAt ? formatDate(app.respondedAt) : '-'}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </Fragment>
          )}
        </Fragment>
      ) : (
        <Box className='flex justify-center items-center p-5 -mx-4 md:p-6 bg-red-500 text-slate-50'>
          <Typography color='currentcolor' variant='h4' className='font-bold'>
            Dokumen Tidak Valid
          </Typography>
        </Box>
      )}
    </Box>
  )
}

export default ServiceOrderDoc
