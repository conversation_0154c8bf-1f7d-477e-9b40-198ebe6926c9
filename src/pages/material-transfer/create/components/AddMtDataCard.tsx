import {
  Card,
  CardContent,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { useAuth } from '@/contexts/AuthContext'
import { CompanySiteType, SiteType } from '@/types/companyTypes'
import { useMt } from '../../context/MtContext'
import { MtPayload } from '../../config/types'
import { mtTypeOptions } from '../../config/options'
import { MaterialTransferType } from '../../config/enum'
import NumberField from '@/components/numeric/NumberField'

const AddMtDataCard = ({ siteList }: { siteList: SiteType[] }) => {
  const { userProfile } = useAuth()
  const { mrData, showLoading } = useMt()
  const {
    control,
    setValue,
    resetField,
    formState: { errors }
  } = useFormContext<MtPayload>()

  const sites = userProfile?.sites ?? []
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Data Pindah Barang</Typography>
        </div>
        <Controller
          name='type'
          control={control}
          rules={{ required: true }}
          render={({ field, fieldState: { error } }) => (
            <>
              <FormControl error={Boolean(error)}>
                <InputLabel id='siteId-select'>Tipe Material Transfer</InputLabel>
                <Select
                  key={field.value}
                  {...field}
                  fullWidth
                  id='select-type'
                  value={field.value}
                  onChange={e => {
                    const value = e.target.value
                    field.onChange(value)
                    if (value === MaterialTransferType.IN_SITE) {
                      setValue('requestedSiteId', mrData?.siteId)
                    } else {
                      resetField('requestedSiteId')
                    }
                  }}
                  label='Pilih Tipe Material Transfer'
                  labelId='siteId-select'
                  inputProps={{ placeholder: 'Pilih Tipe Material Transfer' }}
                  defaultValue=''
                >
                  {mtTypeOptions?.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
                {!!error?.message && (
                  <FormHelperText className='ml-0' error>
                    Tipe Material Transfer wajib dipilih
                  </FormHelperText>
                )}
              </FormControl>
              {!!mrData?.siteId && (
                <FormControl>
                  <InputLabel id='siteId-select'>Gudang Tujuan</InputLabel>
                  <Select
                    fullWidth
                    id='select-siteId'
                    value={mrData?.siteId}
                    label='Pilih Site Tujuan'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Pilih Site' }}
                    defaultValue={mrData?.siteId}
                    disabled
                  >
                    {sites.map(site => (
                      <MenuItem key={site.id} value={site.id}>
                        {site.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
              {!!field.value && field.value !== MaterialTransferType.IN_SITE && (
                <Controller
                  name='requestedSiteId'
                  control={control}
                  rules={{ required: true }}
                  render={({ field: requestedSiteIdField, fieldState: { error } }) => (
                    <FormControl error={Boolean(error)}>
                      <InputLabel id='requestedSiteId-select'>Minta ke Gudang</InputLabel>
                      <Select
                        key={requestedSiteIdField.value}
                        {...requestedSiteIdField}
                        fullWidth
                        id='select-requestedSiteId'
                        value={requestedSiteIdField.value}
                        label='Minta ke Gudang'
                        labelId='requestedSiteId-select'
                        inputProps={{ placeholder: 'Pilih Gudang' }}
                        defaultValue=''
                      >
                        {siteList
                          ?.filter(site => site.id !== mrData?.siteId)
                          ?.filter(site => site.type === CompanySiteType.WAREHOUSE)
                          .map(site => (
                            <MenuItem key={site.id} value={site.id}>
                              {site.name}
                            </MenuItem>
                          ))}
                      </Select>
                      {!!error?.message && (
                        <FormHelperText className='ml-0' error>
                          Gudang wajib dipilih
                        </FormHelperText>
                      )}
                    </FormControl>
                  )}
                />
              )}
              {!!field.value && field.value === MaterialTransferType.S2S_BORROW && (
                <Controller
                  name='borrowDuration'
                  control={control}
                  rules={{ required: true }}
                  render={({ field: borrowDurationField, fieldState: { error } }) => (
                    <TextField
                      {...borrowDurationField}
                      disabled={showLoading}
                      fullWidth
                      label='Durasi Peminjaman'
                      variant='outlined'
                      placeholder='Masukkan durasi peminjaman'
                      InputProps={{
                        inputComponent: NumberField as any,
                        endAdornment: 'Hari'
                      }}
                      InputLabelProps={{
                        shrink: !!borrowDurationField.value
                      }}
                      {...(error?.message && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              )}
            </>
          )}
        />
        <Controller
          name='note'
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              disabled={showLoading}
              fullWidth
              label='Catatan (optional)'
              variant='outlined'
              placeholder='Contoh: Surplus stok'
              className='mbe-5'
              {...(errors.note && { error: true, helperText: errors.note.message })}
              InputLabelProps={{
                shrink: !!field.value
              }}
            />
          )}
        />
      </CardContent>
    </Card>
  )
}

export default AddMtDataCard
