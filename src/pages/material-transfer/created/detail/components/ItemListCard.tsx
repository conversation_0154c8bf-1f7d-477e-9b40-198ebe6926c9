// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useMenu } from '@/components/menu/contexts/menuContext'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useUploadImage } from '@/api/services/file/mutation'
import { useAuth } from '@/contexts/AuthContext'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import { useMt } from '@/pages/material-transfer/context/MtContext'
import { useDeleteMtItem, useUpdateMtItem } from '@/api/services/mt/mutation'
import { MtItemType } from '@/pages/material-transfer/config/types'
import { tableColumns } from '../table'
import { WarehouseItemType } from '@/types/appTypes'
import { isNullOrUndefined } from '@/utils/helper'

const ItemListCard = () => {
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { mtData, fetchMtData, fetchLogList, canRemove } = useMt()
  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: deleteItemMutate, isLoading: deleteItemLoading } = useDeleteMtItem()
  const { mutate: updateItemMutate, isLoading: updateItemLoading } = useUpdateMtItem()

  const [mtItems, setMtItems] = useState<MtItemType[]>([])

  const isLoading = uploadLoading || deleteItemLoading || updateItemLoading

  const [{ open: addItemOpen, selectedItem }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as MtItemType
  })

  const handleDeleteItem = (mtItem: MtItemType) => {
    setConfirmState({
      open: true,
      title: 'Hapus Barang',
      content: 'Apakah kamu yakin ingin menghapus barang ini?',
      onConfirm: () => {
        updateItemMutate(
          {
            mtId: mtData.id,
            payload: {
              id: mtItem.id,
              itemId: mtItem.itemId,
              quantity: 0,
              quantityUnit: mtItem.quantityUnit,
              note: mtItem.note,
              largeUnitQuantity: mtItem.largeUnitQuantity,
              isLargeUnit: mtItem.isLargeUnit,
              ...(mtItem.unitId && { unitId: mtItem.unitId }),
              ...(!isNullOrUndefined(mtItem?.unitKm) && { unitKm: mtItem.unitKm }),
              ...(!isNullOrUndefined(mtItem?.unitHm) && { unitHm: mtItem.unitHm })
              // images: [
              //   ...(mtItem.images ?? [])
              //     .filter(image => !image.fileName && !!image.uploadId)
              //     .map(image => ({
              //       uploadId: image.uploadId
              //     }))
              // ]
            }
          },
          {
            onSuccess: () => {
              toast.success('Barang berhasil dihapus')
              fetchMtData()
              fetchLogList()
            }
          }
        )
      },
      confirmText: 'Hapus',
      confirmColor: 'error'
    })
  }

  const handleUpdateItem = (itemData: WarehouseItemType) => {
    Promise.all(
      (itemData.images ?? [])
        .filter(item => !!item.fileName)
        .map(item =>
          uploadMutate({
            fieldName: `item_image_${itemData.itemId}`,
            file: item.content,
            scope: 'public-image',
            fileName: item.fileName
          })
        )
    )
      .then(values => {
        const uploadIds = values.map(val => ({
          uploadId: val.data?.id ?? ''
        }))
        updateItemMutate(
          {
            mtId: mtData.id,
            payload: {
              id: selectedItem?.id,
              itemId: itemData.itemId,
              quantity: itemData.quantity,
              quantityUnit: itemData.quantityUnit,
              note: itemData.note,
              isLargeUnit: itemData.isLargeUnit,
              largeUnitQuantity: itemData.largeUnitQuantity,
              ...(itemData.unitId && { unitId: itemData.unitId }),
              ...(!isNullOrUndefined(itemData?.unitKm) && { unitKm: itemData.unitKm }),
              ...(!isNullOrUndefined(itemData?.unitHm) && { unitHm: itemData.unitHm })
              // images: [
              //   ...(itemData.images ?? [])
              //     .filter(image => !image.fileName && !!image.uploadId)
              //     .map(image => ({
              //       uploadId: image.uploadId
              //     })),
              //   ...uploadIds
              // ]
            }
          },
          {
            onSuccess: () => {
              toast.success('Data barang berhasil diubah')
              fetchMtData()
              fetchLogList()
              setAddItemModalState({
                open: false,
                selectedItem: undefined
              })
            }
          }
        )
      })
      .catch(error => {
        const message = error.response?.data?.message
        if (message) {
          toast.error(message)
        } else {
          toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
        }
      })
  }

  const table = useReactTable({
    data: mtItems,
    columns: tableColumns(
      {
        onEdit: itemData => {
          setAddItemModalState({ open: true, selectedItem: itemData })
        },
        onRemove: handleDeleteItem
      },
      mtData?.requesterSiteId,
      canRemove
    ),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    setMtItems(mtData?.items ?? [])
  }, [mtData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Barang</Typography>
          </div>
          <div>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan barang yang ingin dimasukkan dalam Material Transfer ini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? undefined : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          withoutUnit
          canUpdateAll={false}
          isLoading={isLoading}
          currentItem={selectedItem}
          onSubmit={handleUpdateItem}
          siteId={mtData?.siteId}
        />
      )}
    </>
  )
}

export default ItemListCard
