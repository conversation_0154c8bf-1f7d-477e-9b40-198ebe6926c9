import { useRouter } from '@/routes/hooks'
import { ProjectType } from '@/types/projectTypes'
import { toCurrency } from '@/utils/helper'
import { <PERSON><PERSON>, Card, CardContent, IconButton, Typography } from '@mui/material'

type Props = {
  projectData: ProjectType
}

const RequirementsCard = ({ projectData }: Props) => {
  const router = useRouter()

  const handleCreateMr = () => {
    router.push(`/mr/create?projectId=${projectData?.id}`)
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Ke<PERSON><PERSON><PERSON></Typography>
          <Button onClick={handleCreateMr} size='small' color='primary' variant='contained'>
            Buat MR
          </Button>
        </div>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col gap-2 p-4 rounded-md bg-[#4C4E640D]'>
            <small>Total Kebutuhan <PERSON></small>
            <Typography variant='h6' color='primary'>
              {toCurrency(projectData?.inboundAmount)}
            </Typography>
          </div>
          <div className='flex justify-between gap-2'>
            <div className='flex flex-1 justify-between items-center p-4 rounded-md bg-[#4C4E640D]'>
              <div className='flex flex-col gap-2'>
                <small>Material Request Terbuat</small>
                <Typography variant='h6' color='secondary'>
                  {projectData?.materialRequestsCount} Dokumen
                </Typography>
              </div>
              <IconButton onClick={() => router.push('/mr/list', { state: { projectId: projectData?.id } })}>
                <i className='ri-arrow-right-s-line' />
              </IconButton>
            </div>
            <div className='flex flex-1 justify-between items-center p-4 rounded-md bg-[#4C4E640D]'>
              <div className='flex flex-col gap-2'>
                <small>Purchase Order Terbuat</small>
                <Typography variant='h6' color='secondary'>
                  {projectData?.purchaseOrdersCount} Dokumen
                </Typography>
              </div>
              <IconButton onClick={() => router.push('/po/list', { state: { projectId: projectData?.id } })}>
                <i className='ri-arrow-right-s-line' />
              </IconButton>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
export default RequirementsCard
