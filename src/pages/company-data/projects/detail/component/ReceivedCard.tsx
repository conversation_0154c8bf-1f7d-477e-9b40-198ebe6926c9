import { useRouter } from '@/routes/hooks'
import { ProjectType } from '@/types/projectTypes'
import { toCurrency } from '@/utils/helper'
import { Card, CardContent, IconButton, Typography } from '@mui/material'

type Props = {
  projectData: ProjectType
}

const ReceivedCard = ({ projectData }: Props) => {
  const router = useRouter()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Diterima</Typography>
        </div>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col gap-2 p-4 rounded-md bg-[#4C4E640D]'>
            <small>Total Diterima</small>
            <Typography variant='h6' color='primary'>
              {toCurrency(projectData?.inboundAmount)}
            </Typography>
          </div>
          <div className='flex justify-between items-center p-4 rounded-md bg-[#4C4E640D]'>
            <div className='flex flex-col gap-2'>
              <small>Barang Masuk</small>
              <Typography variant='h6' color='secondary'>
                {projectData?.inboundDocumentsCount} Dokumen
              </Typography>
            </div>
            <IconButton onClick={() => router.push('/mg/in', { state: { projectId: projectData?.id } })}>
              <i className='ri-arrow-right-s-line' />
            </IconButton>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
export default ReceivedCard
