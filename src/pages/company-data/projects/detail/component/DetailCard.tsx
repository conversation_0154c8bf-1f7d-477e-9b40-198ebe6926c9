import { ProjectType } from '@/types/projectTypes'
import { Card, CardContent, Typography } from '@mui/material'

type Props = {
  projectData: ProjectType
}

const ProjectDetailCard = ({ projectData }: Props) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Proyek</Typography>
        </div>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col gap-2'>
            <small>Kode Proyek</small>
            <Typography>{projectData?.code}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Nama Proyek</small>
            <Typography>{projectData?.name}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Site</small>
            <Typography>{projectData?.site?.name}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default ProjectDetailCard
