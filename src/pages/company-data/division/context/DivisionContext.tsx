import { defaultListData } from '@/api/queryClient'
import { useDeleteDivision } from '@/api/services/account/mutation'
import AccountsQueryMethods, { DIVISION_LIST_QUERY_KEY } from '@/api/services/account/query'
import CompanyQueryMethods, { DEPARTMENT_LIST_QUERY_KEY } from '@/api/services/company/query'
import AddDivisionDialog from '@/components/dialogs/add-division-dialog'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { useRouter } from '@/routes/hooks'
import { DivisionType } from '@/types/accountTypes'
import { ListResponse } from '@/types/api'
import { DepartmentType } from '@/types/companyTypes'
import { ListParams } from '@/types/payload'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import * as React from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

type DivisionContextProps = {
  isMobile: boolean
  divisionParams: ListParams
  setPartialDivisionParams: (fieldName: keyof ListParams, value: any) => void
  setDivisionParams: React.Dispatch<React.SetStateAction<ListParams>>
  divisionListResponse: ListResponse<DivisionType>
  selectedDivisionId: string | undefined
  setSelectedDivisionId: React.Dispatch<React.SetStateAction<string | undefined>>
  setDivisionState: React.Dispatch<React.SetStateAction<{ open: boolean; data: DivisionType | null }>>
  handleDeleteDivision: (id: string) => void
  departmentList: DepartmentType[]
  fetchDivisionList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<DivisionType>, unknown>>
  fetchDivisionData: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<DivisionType, unknown>>
}

const DivisionContext = React.createContext<DivisionContextProps>({} as DivisionContextProps)

export const useDivision = () => {
  const context = React.useContext(DivisionContext)
  if (context === undefined) {
    throw new Error('useProject must be used within a ProjectContextProvider')
  }
  return context
}

export const DivisionContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { divisionId } = useParams()
  const { setConfirmState } = useMenu()
  const router = useRouter()

  const [divisionParams, setPartialDivisionParams, setDivisionParams] = usePartialState<ListParams>({
    limit: 10,
    page: 1
  })
  const [selectedDivisionId, setSelectedDivisionId] = React.useState<string | undefined>(undefined)
  const [{ open, data }, setDivisionState] = React.useState<{ open: boolean; data: DivisionType | null }>({
    data: null,
    open: false
  })

  const { mutate: deleteMutate } = useDeleteDivision()

  const { data: departmentListresponse } = useQuery({
    queryKey: [DEPARTMENT_LIST_QUERY_KEY],
    queryFn: () => {
      return CompanyQueryMethods.getDepartmentList({ limit: Number.MAX_SAFE_INTEGER })
    },
    placeholderData: defaultListData as ListResponse<DepartmentType>
  })

  const { data: divisionListResponse, refetch: fetchDivisionList } = useQuery({
    queryKey: [DIVISION_LIST_QUERY_KEY, JSON.stringify(divisionParams)],
    queryFn: () => {
      return AccountsQueryMethods.getDivisionList(divisionParams)
    },
    placeholderData: defaultListData as ListResponse<DivisionType>
  })

  const { data: divisionData, refetch: fetchDivisionData } = useQuery({
    enabled: !!divisionId,
    queryKey: [DIVISION_LIST_QUERY_KEY, 'DETAIL', divisionId],
    queryFn: () => AccountsQueryMethods.getDivision(divisionId)
  })

  const handleDeleteDivision = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Proyek',
      content: 'Apakah Anda yakin ingin menghapus proyek ini? Action ini tidak dapat dikembalikan',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        deleteMutate(id, {
          onSuccess: () => {
            toast.success('Proyek berhasil dihapus')
            router.push('/company-data/projects')
            fetchDivisionList()
          }
        })
      }
    })
  }

  React.useEffect(() => {
    if (divisionId) {
      setSelectedDivisionId(divisionId)
    }
  }, [divisionId])

  const value = {
    isMobile,
    divisionParams,
    setPartialDivisionParams,
    setDivisionParams,
    divisionListResponse,
    fetchDivisionList,
    fetchDivisionData,
    selectedDivisionId,
    setSelectedDivisionId,
    setDivisionState,
    divisionData,
    handleDeleteDivision,
    departmentList: departmentListresponse?.items ?? []
  }
  return (
    <DivisionContext.Provider value={value}>
      {open && (
        <AddDivisionDialog
          open={open}
          setOpen={open => {
            setDivisionState(prev => ({ ...prev, ...(!open && { open, data: null }) }))
          }}
          data={data}
          readonly={false}
        />
      )}
      {children}
    </DivisionContext.Provider>
  )
}
