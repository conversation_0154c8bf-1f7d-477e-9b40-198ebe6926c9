import { ProjectType } from '@/types/projectTypes'
import { toTitleCase } from '@/utils/helper'
import { Chip, Grid, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { projectStatusConfig } from './utils'
import { DivisionType } from '@/types/accountTypes'
import { DepartmentType } from '@/types/companyTypes'

const columnHelper = createColumnHelper<DivisionType>()

type RowAction = {
  detail: (row: DivisionType) => void
  departments: DepartmentType[]
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('code', {
    header: 'Kode Divisi',
    cell: ({ row }) => (
      <Typography
        sx={{ cursor: 'pointer' }}
        color='primary'
        role='button'
        onClick={() => rowAction.detail(row.original)}
      >
        {row.original?.code}
      </Typography>
    )
  }),
  columnHelper.accessor('name', {
    header: 'Nama Divisi'
  }),
  columnHelper.accessor('departmentId', {
    header: 'Departemen',
    cell: ({ row }) => {
      const department = rowAction.departments?.find(department => department.id === row.original?.departmentId)
      return department?.name ? <Typography>{department.name}</Typography> : '-'
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Dibuat',
    cell: ({ row }) => formatDate(new Date(row.original?.createdAt), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })
  }),
  columnHelper.display({
    id: 'view',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <Grid container>
          <Grid item xs={12}>
            <IconButton onClick={() => rowAction.detail(row.original)}>
              <i className='ri-eye-line' />
            </IconButton>
          </Grid>
        </Grid>
      )
    }
  })
]
