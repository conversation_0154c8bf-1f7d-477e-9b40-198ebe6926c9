import { Avatar, Card, CardContent, Typography } from '@mui/material'
import { useCategory } from '../../../context/CategoryContext'

const CreatedByCard = () => {
  const { categoryData } = useCategory()

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Dibuat Oleh</Typography>
        </div>
        <div className='flex gap-3 items-center w-full px-6'>
          <Avatar>J</Avatar>
          <div className='flex flex-col flex-1 gap-0 items-start relative bg-transparent'>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <p className='tracking-[0.2px] leading-6 text-base text-black dark:text-inherit'>John <PERSON></p>
            </div>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <Typography variant='caption'>CEO</Typography>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CreatedByCard
