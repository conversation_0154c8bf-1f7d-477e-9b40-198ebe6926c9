import React, { createContext, useEffect, useState } from 'react'
import { useUnit } from './UnitContext'
import { useQuery } from '@tanstack/react-query'
import { array, enum as enum_, number, object, string, TypeOf, ZodIssueCode } from 'zod'
import { toast } from 'react-toastify'

import CompanyQueryMethods, { CATEGORY_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useAddUnit, useUpdateUnit } from '@/api/services/company/mutation'
import { FormProvider, SubmitHandler, useForm, useFormContext, useWatch } from 'react-hook-form'
import { CategoryType, ImageItemType, UnitType } from '@/types/companyTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import { useUploadDocument, useUploadImage } from '@/api/services/file/mutation'
import { NavigateFunction, useNavigate, useParams } from 'react-router-dom'
import { defaultImageList } from '@/components/dialogs/add-item-dialog'
import { DocumentPayload } from '@/types/payload'
import { useMenu } from '@/components/menu/contexts/menuContext'

type FormUnitContextType = {
  subCategoryList: CategoryType[]
  isLoading: boolean
  onSubmitHandler: (inputValues: AddUnitInput) => void
  imageList: ImageItemType[]
  setImageList: React.Dispatch<React.SetStateAction<ImageItemType[]>>
  docsList: AddUnitInput['documents']
  setDocsList: React.Dispatch<React.SetStateAction<AddUnitInput['documents']>>
  insuranceList: AddUnitInput['documents']
  setInsuranceList: React.Dispatch<React.SetStateAction<AddUnitInput['documents']>>
  navigate: NavigateFunction
}

const FormUnitContext = createContext<FormUnitContextType>({} as FormUnitContextType)
const docObjectSchema = object({
  content: string().optional().nullable(),
  uploadId: string().optional().nullable(),
  type: enum_(['BPKB', 'STNK', 'KIR', 'SIO', 'INSURANCE_FIRST', 'INSURANCE_SECOND', 'INSURANCE_THIRD'])
    .optional()
    .nullable(),
  name: string().optional().nullable(),
  effectiveDate: string().optional().nullable(),
  expirationDate: string().optional().nullable(),
  renewalDate: string().optional().nullable()
}).superRefine((values, ctx) => {
  if (!!values.name && !values.effectiveDate) {
    ctx.addIssue({
      code: ZodIssueCode.custom,
      message: 'Wajib diisi',
      path: ['effectiveDate']
    })
  }
  if (!!values.name && !values.expirationDate) {
    ctx.addIssue({
      code: ZodIssueCode.custom,
      message: 'Wajib diisi',
      path: ['expirationDate']
    })
  }
})
const addUnitSchema = object({
  number: string({ message: 'Wajib diisi' }),
  assetCode: string({ message: 'Wajib diisi' }),
  description: string().optional().nullable(),
  ownerName: string({ message: 'Wajib diisi' }),
  brandName: string({ message: 'Wajib diisi' }),
  type: string({ message: 'Wajib diisi' }),
  hullNumber: string({ message: 'Wajib diisi' }),
  categoryId: string({ message: 'Wajib dipilih' }),
  subCategoryId: string().optional().nullable(),
  status: enum_(['ACTIVE', 'IN_REPAIR']),
  engineNumber: string({ message: 'Wajib diisi' }),
  plateNumber: string({ message: 'Wajib diisi' }),
  equipmentType: string({ message: 'Wajib diisi' }),
  km: number().optional().nullable(),
  hm: number().optional().nullable(),
  specification: string().optional().nullable(),
  condition: enum_(['BREAKDOWN', 'READY_FOR_USE']),
  smType: enum_(['SM_250', 'SM_500', 'SM_1000', 'SM_2000']),
  note: string().optional().nullable(),
  purchaseDate: string({ message: 'Wajib diisi' }),
  siteId: string({ message: 'Wajib dipilih' }),
  images: array(
    object({
      uploadId: string().optional().nullable(),
      fileName: string().optional().nullable(),
      content: string().optional().nullable(),
      url: string().optional().nullable()
    })
  )
    .optional()
    .nullable(),
  documents: array(docObjectSchema).optional()
})

export type AddUnitInput = Required<TypeOf<typeof addUnitSchema>>
export type DocObjectType = TypeOf<typeof docObjectSchema>

export const useFormUnit = () => {
  const context = React.useContext(FormUnitContext)
  if (context === undefined) {
    throw new Error('useFormUnit must be used within a FormUnitContextProvider')
  }
  return context
}

export const defaultDocsList = [
  {
    type: 'BPKB',
    name: 'BPKB'
  },
  {
    type: 'STNK',
    name: 'STNK'
  },
  {
    type: 'KIR',
    name: 'KIR'
  },
  {
    type: 'SIO',
    name: 'SIO'
  }
] as AddUnitInput['documents']

export const defaultInsurance = [
  {
    type: 'INSURANCE_FIRST',
    name: 'ASURANSI KE-1'
  }
] as AddUnitInput['documents']

export const FormUnitContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { unitId } = useParams()
  const navigate = useNavigate()
  const { unitData, fetchUnitData, fetchUnitList } = useUnit()
  const { setConfirmState } = useMenu()
  const methods = useForm<AddUnitInput>({
    resolver: zodResolver(addUnitSchema),
    defaultValues: {
      km: 0,
      hm: 0
    }
  })
  const { control } = methods
  const [imageList, setImageList] = useState<ImageItemType[]>(defaultImageList)
  const [docsList, setDocsList] = useState<AddUnitInput['documents']>(defaultDocsList)
  const [insuranceList, setInsuranceList] = useState<AddUnitInput['documents']>(defaultInsurance)

  const categoryIdWatch = useWatch({
    control,
    name: 'categoryId',
    defaultValue: unitData?.categoryId ?? ''
  })

  const { data: subCategoryList } = useQuery({
    enabled: !!categoryIdWatch,
    queryKey: [CATEGORY_LIST_QUERY_KEY, 'UNIT', categoryIdWatch],
    queryFn: () => CompanyQueryMethods.getCategoryList({ limit: 1000, type: 'UNIT', parentId: categoryIdWatch }),
    placeholderData: []
  })

  const { mutate: addMutate, isLoading: addLoading } = useAddUnit()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateUnit()
  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutateAsync: uploadDocumentMutate, isLoading: uploadDocumentLoading } = useUploadDocument()

  const isLoading = addLoading || updateLoading || uploadLoading || uploadDocumentLoading

  const confirmDialogSubmit = (unitData: UnitType, cb: () => void) => {
    setConfirmState({
      open: true,
      title: unitData ? 'Simpan Unit' : 'Tambah Unit',
      content: `Apakah kamu yakin akan ${unitData ? 'mengubah' : 'menambahkan'} unit ini? Pastikan semua data yang kamu masukkan sudah benar`,
      onConfirm: () => {
        cb()
      },
      confirmText: unitData ? 'Ubah Unit' : 'Tambah Unit',
      confirmColor: 'primary'
    })
  }

  const onSubmitHandler: SubmitHandler<AddUnitInput> = (inputValues: AddUnitInput) => {
    confirmDialogSubmit(unitData, () => {
      const documents = [...docsList, ...insuranceList] as DocumentPayload[]

      for (const doc of documents) {
        if (!!doc.content && (!doc.effectiveDate || !doc.expirationDate)) {
          let errorMsg = 'Wajib diisi'
          if (!doc.effectiveDate) {
            errorMsg = `Tanggal Mulai Berlaku dokumen ${doc.type} wajib diisi`
          } else if (!doc.expirationDate) {
            errorMsg = `Tanggal Berakhir dokumen ${doc.type} wajib diisi`
          }
          toast.error(errorMsg)
          return
        }
      }

      Promise.all(
        imageList
          .filter(item => !!item.fileName)
          .map(item =>
            uploadMutate({
              fieldName: `item_image_${inputValues.number}`,
              file: item.content,
              scope: 'public-image',
              fileName: item.fileName
            })
          )
      )
        .then(values => {
          const documentFiltered = documents.filter(
            doc => !!doc.content && /^data:[a-zA-Z0-9+/.-]+;base64,[A-Za-z0-9+/]+={0,2}$/.test(doc.content)
          )
          Promise.all(
            documentFiltered.map(doc =>
              uploadDocumentMutate({
                fieldName: `unit_document_${inputValues.number}`,
                file: doc.content,
                scope: 'public-document',
                fileName: doc.name
              })
            )
          ).then(docsValues => {
            const documentsPayload: DocumentPayload[] = docsValues.map((val, idx) => ({
              uploadId: val.data?.id ?? '',
              type: documentFiltered[idx].type,
              name: documentFiltered[idx].name,
              effectiveDate: documentFiltered[idx]?.effectiveDate ?? '',
              expirationDate: documentFiltered[idx]?.expirationDate ?? '',
              renewalDate: documentFiltered[idx]?.renewalDate ?? undefined
            }))

            const uploadIds = values.map(val => ({
              uploadId: val.data?.id ?? ''
            }))

            if (unitData) {
              updateMutate(
                {
                  unitId: unitData.id,
                  ...{
                    ...inputValues,
                    documents: [
                      ...(documents ?? []).filter(doc => !!doc.uploadId && !!doc.name).map(doc => ({ ...doc })),
                      ...documentsPayload
                    ],
                    images: [
                      ...(imageList ?? [])
                        .filter(image => !image.fileName && !!image.uploadId)
                        .map(image => ({
                          uploadId: image.uploadId
                        })),
                      ...uploadIds
                    ]
                  }
                },
                {
                  onSuccess: () => {
                    toast.success('Data unit berhasil diubah')
                    fetchUnitData()
                    fetchUnitList()
                    navigate(`/company-data/assets/unit/${unitId}`)
                  }
                }
              )
            } else {
              addMutate(
                {
                  ...inputValues,
                  documents: [
                    ...(documents ?? []).filter(doc => !!doc.uploadId && !!doc.name).map(doc => ({ ...doc })),
                    ...documentsPayload
                  ],
                  images: [
                    ...(imageList ?? [])
                      .filter(image => !image.fileName && !!image.uploadId)
                      .map(image => ({
                        uploadId: image.uploadId
                      })),
                    ...uploadIds
                  ]
                },
                {
                  onSuccess: () => {
                    toast.success('Data unit berhasil ditambahkan')
                    fetchUnitList()
                    navigate(`/company-data/assets/unit`)
                  }
                }
              )
            }
          })
        })
        .catch(error => {
          const message = error.response?.data?.message
          if (message) {
            toast.error(message)
          } else {
            toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
          }
        })
    })
  }

  const value = {
    subCategoryList,
    isLoading,
    onSubmitHandler,
    imageList,
    setImageList,
    docsList,
    setDocsList,
    insuranceList,
    setInsuranceList,
    navigate
  }
  return (
    <FormUnitContext.Provider value={value}>
      <FormProvider {...methods}>{children}</FormProvider>
    </FormUnitContext.Provider>
  )
}
