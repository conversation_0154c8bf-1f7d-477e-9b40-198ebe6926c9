import { UnitType } from '@/types/companyTypes'
import { Card, CardContent, Typography } from '@mui/material'

const ImagesCard = ({ unitData }: { unitData: UnitType }) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Foto Unit</Typography>
        </div>
        {unitData?.images?.length > 0 ? (
          <div className='grid md:grid-cols-5'>
            {unitData?.images?.map(img => (
              <img alt={img.uploadId} src={img.url} className='rounded-[8px] size-[80px]' />
            ))}
          </div>
        ) : (
          <div>
            <Typography>-</Typography>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default ImagesCard
