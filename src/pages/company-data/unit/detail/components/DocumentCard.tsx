import { DocumentUnitType } from '@/types/companyTypes'
import { extractNameFromUrl } from '@/utils/string'
import { Button, Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { useMemo, useState } from 'react'
import { defaultDocsList, defaultInsurance } from '../../context/FormUnitContext'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { insuranceColumns } from '../config/table'
import Table from '@/components/table'
import { downloadFile } from '@/utils/downloadFile'
import HistoryDocumentUnit from '@/components/dialogs/history-document-unit'
import { useUnit } from '../../context/UnitContext'

type DocumentProps = {
  documents?: DocumentUnitType[]
}

const DocumentCard = ({
  document,
  onClickHistory
}: {
  document: DocumentUnitType
  onClickHistory: (type: string) => void
}) => {
  return (
    <div key={document.id} className='flex flex-col gap-4 bg-[#4C4E640D] rounded-[8px] p-4'>
      <div className='flex gap-2 items-center justify-between'>
        <Typography>{document.type}</Typography>
        <Button size='small' variant='outlined' onClick={() => onClickHistory(document.type)}>
          Lihat Riwayat
        </Button>
      </div>
      <div className='flex flex-col gap-2'>
        <Typography variant='caption' color='text-secondary'>
          Dokumen {document.type}
        </Typography>
        <div className='flex justify-between items-center'>
          <Typography>{extractNameFromUrl(document.url)}</Typography>
          <Typography
            role='button'
            onClick={() =>
              downloadFile(`${document.url}.${document.mimeType.split('/')[1]}`, extractNameFromUrl(document.url))
            }
            color={document?.url ? 'primary' : 'text-secondary'}
            sx={{ textDecoration: 'underline', cursor: document?.url ? 'pointer' : 'default' }}
          >
            Unduh
          </Typography>
        </div>
      </div>
      <div className='grid grid-cols-1 md:grid-cols-2'>
        <div className='flex flex-col gap-2'>
          <Typography variant='caption' color='text-secondary'>
            Tanggal Mulai Berlaku
          </Typography>
          <Typography>{formatDate(new Date(document.effectiveDate), 'dd/MM/yyyy')}</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <Typography variant='caption' color='text-secondary'>
            Tanggal Habis Berlaku
          </Typography>
          <Typography>{formatDate(new Date(document.expirationDate), 'dd/MM/yyyy')}</Typography>
        </div>
      </div>
    </div>
  )
}

const DocumentCards = (props: DocumentProps) => {
  const { documents } = props
  const { selectedUnitId } = useUnit()

  const [dialogHistoryDocumentUnit, setDialogHistoryDocumentUnit] = useState<{ state: boolean; type: string }>({
    state: false,
    type: ''
  })

  const documentTypes = useMemo(() => defaultDocsList.map(doc => doc.type), [])

  const docList = useMemo(() => {
    return documents?.filter(doc => documentTypes.includes(doc.type as any))
  }, [documents])

  const insuranceList = useMemo(() => {
    return documents?.filter(doc => !documentTypes.includes(doc.type as any))
  }, [documents])

  const tableOptions = useMemo(
    () => ({
      data: insuranceList ?? [],
      columns: insuranceColumns({
        onHistory: doc => {
          setDialogHistoryDocumentUnit({ state: true, type: doc.type })
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [insuranceList]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Dokumen Unit</Typography>
          </div>
          <div>
            {docList?.length > 0 ? (
              docList.map((document, index) => (
                <DocumentCard
                  onClickHistory={type => setDialogHistoryDocumentUnit({ state: true, type })}
                  document={document}
                  key={index}
                />
              ))
            ) : (
              <Typography>Belum ada dokumen</Typography>
            )}
          </div>
          <div className='flex flex-col bg-[#4C4E640D] rounded-[8px] p-4 gap-4'>
            <Typography>Asuransi</Typography>
            <div className='rounded-[8px] shadow-md'>
              <Table
                headerColor='green'
                table={table}
                emptyLabel={
                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                    <Typography>Belum ada asuransi</Typography>
                  </td>
                }
                disablePagination
              />
            </div>
          </div>
        </CardContent>
      </Card>
      <HistoryDocumentUnit
        unitId={selectedUnitId}
        type={dialogHistoryDocumentUnit.type}
        open={dialogHistoryDocumentUnit.state}
        setOpen={bool => setDialogHistoryDocumentUnit(prev => ({ ...prev, state: bool }))}
      />
    </>
  )
}

export default DocumentCards
