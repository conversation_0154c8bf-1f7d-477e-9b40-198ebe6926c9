import { DocumentUnitType } from '@/types/companyTypes'
import { Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'

const columnHelper = createColumnHelper<DocumentUnitType>()

type RowActionType = {
  onHistory: (doc: DocumentUnitType) => void
}

export const insuranceColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('name', {
    header: 'Nama Asura<PERSON>i'
  }),
  columnHelper.accessor('effectiveDate', {
    header: 'Tanggal Mulai',
    cell: ({ row }) =>
      !!row.original?.effectiveDate ? formatDate(new Date(row.original.effectiveDate), 'dd/MM/yyyy') : '-'
  }),
  columnHelper.accessor('expirationDate', {
    header: 'Tanggal Habis',
    cell: ({ row }) =>
      !!row.original?.expirationDate ? formatDate(new Date(row.original.expirationDate), 'dd/MM/yyyy') : '-'
  }),
  columnHelper.accessor('id', {
    header: '',
    cell: ({ row }) => (
      <Typography
        color={'primary'}
        onClick={() => rowAction.onHistory(row.original)}
        role='button'
        sx={{ cursor: 'pointer', textDecoration: 'underline' }}
      >
        Lihat Riwayat
      </Typography>
    ),
    enableSorting: false
  })
]
