import { AddUnitInput, DocObjectType } from '../context/FormUnitContext'

export const getUnitCondition = (status: string) => {
  switch (status) {
    case 'BREAKDOWN':
      return {
        label: 'Break Down',
        color: 'error'
      }
    case 'READY_FOR_USE':
      return {
        label: 'Ready For Use',
        color: 'success'
      }
    default:
      return {
        label: 'Unknown',
        color: 'default'
      }
  }
}

export const addInsuranceItem = (insurances: AddUnitInput['documents']): DocObjectType | null => {
  const regex = /^INSURANCE_(FIRST|SECOND|THIRD|FOURTH|FIFTH|SIXTH|SEVENTH|EIGHTH|NINTH|TENTH)$/

  let maxNumber = 0

  insurances.forEach(insurance => {
    const match = insurance.type.match(regex)
    if (match) {
      const numberPart = match[1]
      const numberIndex = [
        'FIRST',
        'SECOND',
        'THIRD',
        'FOURTH',
        'FIFTH',
        'SIXTH',
        'SEVENTH',
        'EIGHTH',
        'NINTH',
        'TENTH'
      ].indexOf(numberPart)
      if (numberIndex > maxNumber) {
        maxNumber = numberIndex
      }
    }
  })

  const newInsuranceType = `INSURANCE_${['FIRST', 'SECOND', 'THIRD', 'FOURTH', 'FIFTH', 'SIXTH', 'SEVENTH', 'EIGHTH', 'NINTH', 'TENTH'][maxNumber + 1]}`
  const newInsuranceName = `ASURANSI KE-${maxNumber + 2}`

  const newInsuranceItem: DocObjectType = {
    type: newInsuranceType as DocObjectType['type'],
    name: newInsuranceName
  }

  insurances.push(newInsuranceItem)
  return newInsuranceItem
}
