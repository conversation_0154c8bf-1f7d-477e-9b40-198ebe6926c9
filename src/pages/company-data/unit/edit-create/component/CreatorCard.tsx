// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useUnit } from '../../context/UnitContext'
import { Avatar } from '@mui/material'

const CreatorCard = () => {
  const { unitData } = useUnit()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Dibuat oleh</Typography>
        </div>
        <div className='flex gap-3 items-center w-full'>
          <Avatar src={unitData?.createdByUser?.profilePictureUrl} />
          <div className='flex flex-col flex-1 gap-0 items-start relative bg-transparent'>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <p className='tracking-[0.2px] leading-6 text-base text-black dark:text-inherit'>
                {unitData?.createdByUser?.fullName}
              </p>
            </div>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <Typography variant='caption'>{unitData?.createdByUser?.title}</Typography>
            </div>
          </div>
          <Typography>
            {formatDate(unitData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
          </Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default CreatorCard
