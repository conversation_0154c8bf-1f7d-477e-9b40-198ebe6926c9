import { useEffect, useState } from 'react'
import { ImageItemType, UnitType } from '@/types/companyTypes'
import { Card, CardContent, Typography } from '@mui/material'
import PhotoPicker from '@/components/PhotoPicker'
import { AddUnitInput, useFormUnit } from '../../context/FormUnitContext'
import { useFormContext } from 'react-hook-form'

const ImagesCard = ({ unitData }: { unitData: UnitType }) => {
  const { isLoading, imageList, setImageList } = useFormUnit()

  const { reset, getValues } = useFormContext<AddUnitInput>()

  useEffect(() => {
    if (unitData?.images) {
      setImageList([
        ...unitData?.images?.map(image => ({
          content: image.url,
          uploadId: image.uploadId
        })),
        ...(new Array(5 - (unitData?.images?.length ?? 0)).fill({
          content: '',
          fileName: ''
        }) as ImageItemType[])
      ])
    }
  }, [unitData])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Foto Unit</Typography>
        </div>
        <div className='flex gap-5 overflow-y-hidden max-sm:px-2'>
          {imageList?.map((item, index) => (
            <PhotoPicker
              key={`${item.content}_${index}`}
              content={item.content}
              disabled={isLoading}
              onPicked={(content, fileName) => {
                setImageList(current => {
                  const tempCurrent = [...current]
                  tempCurrent[index] = { content, fileName }
                  return tempCurrent
                })
              }}
              onRemoved={() => {
                setImageList(current => {
                  const tempCurrent = [...current]
                  tempCurrent[index] = { content: '', fileName: '' }
                  return tempCurrent
                })
              }}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

export default ImagesCard
