// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useVendor } from '../../context/VendorContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Button } from '@mui/material'
import { useState } from 'react'
import { VendorAdressesType } from '@/types/companyTypes'
import { useUpdateVendorAddress } from '@/api/services/company/mutation'
import { toast } from 'react-toastify'
import AddVendorAddress from '@/components/dialogs/add-vendor-address-dialog'

const VendorAddressesCard = () => {
  const { vendorData, fetchVendorData } = useVendor()

  const [{ open, vendorAddres }, setSelectedAdress] = useState<{ open: boolean; vendorAddres?: VendorAdressesType }>({
    open: false,
    vendorAddres: null
  })

  const { mutate: updateVendor, isLoading: loadingUpdate } = useUpdateVendorAddress()

  const handleDefaultAddr = (address: VendorAdressesType) => {
    updateVendor(
      { ...address, isDefault: true, addressId: address.id, vendorId: vendorData.id },
      {
        onSuccess: () => {
          toast.success('Alamat utama berhasil diupdate')
          fetchVendorData()
        }
      }
    )
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Alamat Vendor</Typography>
          <Button
            size='small'
            onClick={() => setSelectedAdress({ open: true, vendorAddres: null })}
            color='primary'
            disabled={loadingUpdate}
            variant='outlined'
          >
            Tambah Alamat
          </Button>
        </div>
        <div className='grid grid-cols-1 gap-4'>
          {vendorData?.addresses?.map((addr, idx) => (
            <div className='rounded-[8px] bg-[#4C4E640D] flex flex-col gap-3 items-center w-full p-3'>
              <div className='flex justify-between w-full'>
                {addr.isDefault ? <Typography>Alamat Utama</Typography> : <Typography>Alamat {idx + 1}</Typography>}
                <div className='flex gap-2'>
                  <Button
                    size='small'
                    variant='outlined'
                    disabled={loadingUpdate}
                    onClick={() => setSelectedAdress({ open: true, vendorAddres: addr })}
                  >
                    Edit
                  </Button>
                  <Button
                    disabled={addr.isDefault || loadingUpdate}
                    onClick={() => handleDefaultAddr(addr)}
                    variant='contained'
                    size='small'
                  >
                    Jadikan Alamat Utama
                  </Button>
                </div>
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 w-full gap-2'>
                <div className='col-span-2 flex flex-col gap-1'>
                  <small>Alamat</small>
                  <Typography>{addr.address ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-1'>
                  <small>Nomor Telepon Vendor</small>
                  <Typography>{addr.phoneNumber ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-1'>
                  <small>Nomor Fax Vendor</small>
                  <Typography>{addr.faxNumber ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-1'>
                  <small>Nama PIC</small>
                  <Typography>{addr.picName ?? '-'}</Typography>
                </div>
                <div className='flex flex-col gap-1'>
                  <small>Nomor Telepon PIC</small>
                  <Typography>{addr.picPhoneNumber ?? '-'}</Typography>
                </div>
              </div>
            </div>
          ))}
        </div>
        {open && (
          <AddVendorAddress
            open={open}
            setOpen={bool => setSelectedAdress({ open: bool, ...(!bool && { vendorAddres: null }) })}
            vendorAdress={vendorAddres}
          />
        )}
      </CardContent>
    </Card>
  )
}

export default VendorAddressesCard
