// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Breadcrumbs, Chip, Typography } from '@mui/material'

import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import ItemListDetailCard from '@/pages/material-request/detail/components/ItemListDetailCard'
import AdditionalInfoCard from '@/pages/material-request/detail/components/AdditionalInfoCard'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import ActivityLogCard from './components/ActivityLogCard'
import PurchaseDetailCard from './components/PurchaseDetailCard'
import { useRma } from '../context/RmaContext'
import RmaReqDetailCard from './components/RmaReqDetailCard'
import ApprovalsCard from './components/ApprovalsCard'
import { useReadRmaApproval } from '@/api/services/rma/mutation'
import { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import { rmaStatusOptions } from '../../list/config/utils'

const RmaAppDetailPage = () => {
  const { userProfile } = useAuth()
  const { poData, logList, rmaData, refreshData, isApproval } = useRma()

  const { mutate: readMutate } = useReadRmaApproval()

  useEffect(() => {
    if (rmaData) {
      const ownApproval = rmaData?.approvals?.find(approval => approval.userId === userProfile?.id)
      if (ownApproval && ownApproval.isRead === false) {
        readMutate(
          {
            isRead: true,
            rmaId: rmaData?.id,
            approvalId: ownApproval.id
          },
          { onSuccess: () => refreshData() }
        )
      }
    }
  }, [rmaData])

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>RMA</Typography>
            </Link>
            <Link to={isApproval ? '/rma/approval' : '/rma/list'} replace>
              <Typography color='var(--mui-palette-text-disabled)'>
                {isApproval ? 'Persetujuan RMA' : 'RMA Terbuat'}
              </Typography>
            </Link>
            <Typography>Detil RMA</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>NO. PO: {poData?.number}</Typography>
                {isApproval ? (
                  <Chip
                    label={poData?.closedAt ? 'Semua barang sudah diterima' : 'Barang belum diterima semua'}
                    color={poData?.closedAt ? 'success' : 'secondary'}
                    variant='tonal'
                    size='small'
                  />
                ) : (
                  <Chip
                    label={rmaStatusOptions.find(option => option.value === rmaData?.status)?.label}
                    color={rmaStatusOptions.find(option => option.value === rmaData?.status)?.color as any}
                    variant='tonal'
                    size='small'
                  />
                )}
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(poData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
              </Typography>
            </div>
            <div className='flex gap-2 is-full sm:is-auto'>
              {/* <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button> */}
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {!!poData?.references && (
              <>
                {poData.references.map(reference => (
                  <Grid item xs={12}>
                    <DocNumberCard
                      warehouseDoc={reference.purchaseRequisitionChild ?? reference.purchaseRequisition}
                      docType='PR'
                    />
                  </Grid>
                ))}
              </>
            )}
            <Grid item xs={12}>
              <ItemListDetailCard warehouseData={poData} />
            </Grid>
            <Grid item xs={12}>
              <AdditionalInfoCard warehouseData={poData} />
            </Grid>
            <Grid item xs={12}>
              <ActivityLogCard logList={logList} />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {rmaData ? (
              <>
                <Grid item xs={12}>
                  <RmaReqDetailCard rmaData={rmaData} />
                </Grid>
                {isApproval ? (
                  <Grid item xs={12}>
                    <ApprovalsCard />
                  </Grid>
                ) : (
                  <Grid item xs={12}>
                    <ApprovalDetailCard approvalList={rmaData?.approvals ?? []} />
                  </Grid>
                )}
                <Grid item xs={12}>
                  <OwnerCard user={rmaData?.createdByUser} />
                </Grid>
                <Grid item xs={12}>
                  <PurchaseDetailCard purchaseData={poData as any} />
                </Grid>
              </>
            ) : null}
          </Grid>
        </Grid>
      </Grid>
    </>
  )
}

export default RmaAppDetailPage
