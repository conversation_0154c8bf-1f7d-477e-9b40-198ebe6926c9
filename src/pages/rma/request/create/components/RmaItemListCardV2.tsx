import {
  <PERSON>ton,
  Card,
  CardContent,
  FormControl,
  FormHelperText,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useRma } from '../../context/RmaContext'
import { Controller, useFieldArray, useFormContext, useWatch } from 'react-hook-form'
import { RmaPayload } from '@/pages/rma/config/types'
import Separator from '@/components/Separator'
import NumberField from '@/components/numeric/NumberField'
import { isNullOrUndefined } from '@/utils/helper'
import {
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  getPaginationRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import AddGoodsDialog from './add-goods-dialog'
import { useMemo, useState } from 'react'
import { WarehouseItemType } from '@/types/appTypes'

const RmaItemListCardV2 = () => {
  const { poData } = useRma()
  const { control, setValue } = useFormContext<RmaPayload>()

  const [openDialog, setOpenDialog] = useState(false)

  const poItems = poData?.items ?? []
  const { fields, remove } = useFieldArray({ control, name: 'items' })

  const handleAddGoods = (data: WarehouseItemType[]) => {
    setValue(
      'items',
      data.map(item => ({
        purchaseOrderItemId: item.id,
        largeUnitQuantity: item.largeUnitQuantity,
        note: item?.note ?? '',
        isActive: true,
        hasBeenOut: null,
        quantity: null,
        quantityUnit: null
      }))
    )
    setOpenDialog(false)
  }

  const tableOptions = useMemo(
    () => ({
      data: fields ?? [],
      columns: tableColumns({ control, items: poItems, setValue, remove }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues()
    }),
    [fields, poItems]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Material/Barang</Typography>
          <Button onClick={() => setOpenDialog(true)} variant='outlined'>
            Pilih Barang
          </Button>
        </div>
        <div className='flex flex-col gap-4 rounded-md shadow-xs'>
          <Table
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography>Belum ada Barang</Typography>
                <Typography className='text-sm text-gray-400'>
                  Semua barang yang dipilih akan ditampilkan di sini
                </Typography>
              </td>
            }
            disablePagination
          />
        </div>
      </CardContent>
      {openDialog && <AddGoodsDialog open={openDialog} setOpen={setOpenDialog} setData={handleAddGoods} />}
    </Card>
  )
}

export default RmaItemListCardV2
