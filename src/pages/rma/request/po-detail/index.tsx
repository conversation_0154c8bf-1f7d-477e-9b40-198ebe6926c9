// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { B<PERSON>c<PERSON><PERSON>, <PERSON><PERSON>, Card, CardContent, Chip, Typography } from '@mui/material'

import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import ItemListDetailCard from '@/pages/material-request/detail/components/ItemListDetailCard'
import AdditionalInfoCard from '@/pages/material-request/detail/components/AdditionalInfoCard'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import ApprovalDetailCard from '@/pages/material-request/detail/components/ApprovalDetailCard'
import ActivityLogCard from './components/ActivityLogCard'
import PurchaseDetailCard from './components/PurchaseDetailCard'
import PrQueryMethods, { PR_QUERY_KEY } from '@/api/services/pr/query'
import { useQuery } from '@tanstack/react-query'
import { useRma } from '../context/RmaContext'
import RmaReqDetailCard from './components/RmaReqDetailCard'
import Separator from '@/components/Separator'
import { useRouter } from '@/routes/hooks'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'

const PoDetailPage = () => {
  const router = useRouter()
  const { poData, logList, rmaList, imList } = useRma()

  const { data: prData } = useQuery({
    enabled: !!poData?.purchaseRequisitionId,
    queryKey: [PR_QUERY_KEY, poData?.purchaseRequisitionId],
    queryFn: () => PrQueryMethods.getPr(poData?.purchaseRequisitionId)
  })

  const isCanDoRma = (imList?.length ?? 0) > 0 && (rmaList?.length ?? 0) === 0

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>RMA</Typography>
            </Link>
            <Link to='/rma/request' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Pengajuan RMA</Typography>
            </Link>
            <Typography>Detil PO</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>NO. PO: {poData?.number}</Typography>
                <Chip
                  label={poData?.closedAt ? 'Semua barang sudah diterima' : 'Barang belum diterima semua'}
                  color={poData?.closedAt ? 'success' : 'secondary'}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(poData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
              </Typography>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
              <div className='flex gap-2'>
                {/* <Button
                  color='secondary'
                  variant='outlined'
                  startIcon={<i className='ri-upload-2-line' />}
                  className='is-full sm:is-auto'
                >
                  Ekspor
                </Button>
                <Button
                  color='secondary'
                  variant='outlined'
                  startIcon={<i className='ic-outline-local-printshop' />}
                  className='is-full sm:is-auto'
                >
                  Cetak
                </Button> */}
              </div>
              <Button
                variant='contained'
                disabled={!isCanDoRma}
                color={!isCanDoRma ? 'secondary' : 'primary'}
                className='is-full sm:is-auto'
                onClick={() => router.push(`/rma/request/${poData?.id}/create-rma`)}
              >
                Ajukan RMA
              </Button>
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <DocNumberCard warehouseDoc={prData} docType='PR' />
            </Grid>
            <Grid item xs={12}>
              <ItemListDetailCard warehouseData={poData} />
            </Grid>
            <Grid item xs={12}>
              <AdditionalInfoCard warehouseData={poData} />
            </Grid>
            <Grid item xs={12}>
              <ActivityLogCard logList={logList} />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {(rmaList.length ?? 0) > 0 ? (
              <>
                {rmaList.map(rma => (
                  <>
                    <Grid item xs={12}>
                      <RmaReqDetailCard rmaData={rma} />
                    </Grid>
                    <Grid item xs={12}>
                      <ApprovalDetailCard title='Persetujuan Pengajuan RMA' approvalList={rma?.approvals ?? []} />
                    </Grid>
                    <Grid item xs={12}>
                      <OwnerCard user={rma?.createdByUser} />
                    </Grid>
                    <Grid item xs={12}>
                      <Separator containerClassName='mt-0' />
                    </Grid>
                  </>
                ))}
              </>
            ) : (
              <Grid item xs={12}>
                <RmaReqDetailCard />
              </Grid>
            )}
            <Grid item xs={12}>
              <PurchaseDetailCard purchaseData={poData as any} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  )
}

export default PoDetailPage
