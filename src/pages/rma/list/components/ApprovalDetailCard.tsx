// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { Button, Chip } from '@mui/material'
import { ThemeColor } from '@/core/types'
import { id } from 'date-fns/locale'
import { formatDate } from 'date-fns'
import { ApproverType } from '@/types/userTypes'
import { useState } from 'react'
import { useUpdateMrApprover } from '@/api/services/mr/mutation'
import EditApproverDialog, { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useAuth } from '@/contexts/AuthContext'
import { MaterialTransferApprovalStatus } from '@/pages/material-transfer/config/enum'

type Props = {
  title?: string
  approvalList?: ApproverType[]
  handleUpdateApprover?: (formData: EditApproverInput) => void
  updateApproverLoading?: boolean
}

type StatusChipType = {
  label: string
  color: ThemeColor
}

// Vars
export const statusChipValue: { [key: string]: StatusChipType } = {
  WAITING: { label: 'Menunggu', color: 'secondary' },
  PENDING: { label: 'Menunggu', color: 'secondary' },
  APPROVED: { label: 'Disetujui', color: 'success' },
  REJECTED: { label: 'Ditolak', color: 'error' }
}

const ApprovalDetailCard = ({ title, approvalList, handleUpdateApprover, updateApproverLoading }: Props) => {
  const { accountPermissions } = useAuth()
  const [{ open: editApproverOpen, selectedApproval }, setEditApproverModalState] = useState<{
    open: boolean
    selectedApproval?: ApproverType
  }>({
    open: false
  })

  const canUpdateApprover = accountPermissions.includes('default-approval.write')

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>{title ?? 'Pengajuan Persetujuan'}</Typography>
          </div>
          <div className='flex flex-col gap-4'>
            {approvalList?.map(approval => {
              const statusValue = statusChipValue[approval.status]
              return (
                <div
                  key={approval.id}
                  className='rounded-lg border border-[#4c4e64]/22 p-4 flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'
                >
                  <div className='flex justify-between items-start self-stretch relative w-full bg-transparent'>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {approval.user?.fullName}
                    </p>
                    <Chip label={statusValue?.label} color={statusValue?.color} variant='tonal' size='small' />
                  </div>
                  <div className='flex justify-between w-full'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                        {approval.user?.title}
                      </small>
                    </label>
                    {(approval.status === MaterialTransferApprovalStatus.PENDING ||
                      approval.status === MaterialTransferApprovalStatus.WAITING) &&
                      canUpdateApprover &&
                      !!handleUpdateApprover && (
                        <Button
                          variant='outlined'
                          color='error'
                          size='small'
                          className='mt-2'
                          onClick={() => setEditApproverModalState({ open: true, selectedApproval: approval })}
                        >
                          Ganti
                        </Button>
                      )}
                    {approval.respondedAt ? (
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                          {formatDate(approval.respondedAt, 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
                        </small>
                      </label>
                    ) : null}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
      {editApproverOpen && (
        <EditApproverDialog
          open={editApproverOpen}
          setOpen={open =>
            setEditApproverModalState(current => ({
              open: open,
              selectedApproval: open ? current.selectedApproval : undefined
            }))
          }
          selectedApproval={selectedApproval}
          scope={DefaultApprovalScope.MaterialRequest}
          onSubmit={value => {
            handleUpdateApprover?.({
              ...value,
              approvalId: selectedApproval?.id
            })
            setEditApproverModalState({ open: false })
          }}
          isLoading={updateApproverLoading}
          approvalList={approvalList}
        />
      )}
    </>
  )
}

export default ApprovalDetailCard
