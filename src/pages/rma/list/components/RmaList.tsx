import { FormControl, InputLabel, MenuItem, Select, Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import Table from '@/components/table'
import { useRouter } from '@/routes/hooks'
import DateRangePicker from '@/components/DateRangePicker'
import { mgStatusOptions } from '../../approval/config/utils'
import { tableColumns } from '../../approval/config/table'
import { useRma } from '../../approval/context/RmaContext'
import { useAuth } from '@/contexts/AuthContext'
import { mgUserStatusOptions } from '@/pages/material-goods/list-out-approval/config/utils'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { MrUserStatus } from '@/types/mrTypes'
import DebouncedInput from '@/components/DebounceInput'
import { useEffect } from 'react'
import { rmaStatusOptions } from '../config/utils'

const RmaList = () => {
  const router = useRouter()
  const { userProfile } = useAuth()
  const {
    rmaParams: { page, userStatus, startDate, endDate, limit, search, status },
    setRmaParams,
    rmaListResponse: { items: rmaList, totalItems, totalPages, limit: limitItems, page: pageItems },
    setPartialRmaParams,
    setSelectedId
  } = useRma()

  // TODO: MOVE THIS SHIT
  const table = useReactTable({
    data: rmaList.map(rma => ({ ...rma, isRead: rma.approvals?.[0].isRead })),
    columns: tableColumns(
      {
        showDetail: rmaData => {
          setSelectedId({
            rmaId: rmaData.id,
            poId: rmaData.purchaseOrder?.id
          })
          router.push(`/rma/list/${rmaData.id}`)
        }
      },
      userProfile?.id
    ),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const filterGroupConfig: FilterGroupConfig = {
    date: {
      options: [],
      values: [startDate, endDate]
    },
    status: {
      options: rmaStatusOptions,
      values: status ? [status] : []
    }
  }

  const onFilterChanged = ({ date, status, priority, site, department }: FilterValues) => {
    setRmaParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        status: status.length > 0 ? (status[0] as MrUserStatus) : undefined,
        priority: priority.length > 0 ? priority[0] : undefined,
        siteIds: site.length > 0 ? site[0] : undefined,
        departmentId: department.length > 0 ? department[0] : undefined
      }
    })
  }

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setPartialRmaParams('search', value)}
            placeholder='Cari'
            className='is-full sm:is-auto'
          />
          <FilterGroupDialog
            onRemoveFilter={onFilterChanged}
            config={filterGroupConfig}
            onFilterApplied={onFilterChanged}
          />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
          <Button variant='contained' onClick={() => router.push('/rma/list/create-rma')}>
            Ajukan RMA
          </Button>
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada RMA</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua RMA yang perlu kamu setujui akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setRmaParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialRmaParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setRmaParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialRmaParams('page', pageIndex)}
      />
    </Card>
  )
}

export default RmaList
