// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useMenu } from '@/components/menu/contexts/menuContext'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useUploadImage } from '@/api/services/file/mutation'
import { useAuth } from '@/contexts/AuthContext'
import { CreateMrInput } from '@/pages/material-request/create/config/schema'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import { usePo } from '../../context/PoContext'
import { useDeletePoItem, useUpdatePoItem } from '@/api/services/po/mutation'
import { PoItemType } from '../../config/types'
import { CreatePoInput } from '../../create/config/schema'
import { isNullOrUndefined } from '@/utils/helper'
import { tableColumns } from '../table'

const ItemListCard = () => {
  const { setConfirmState } = useMenu()
  const { poData, fetchPoData, fetchLogList, canRemove } = usePo()
  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: deleteItemMutate, isLoading: deleteItemLoading } = useDeletePoItem()
  const { mutate: updateItemMutate, isLoading: updateItemLoading } = useUpdatePoItem()

  const [poItems, setPoItems] = useState<PoItemType[]>([])

  const isLoading = uploadLoading || deleteItemLoading || updateItemLoading

  const [{ open: addItemOpen, selectedItem }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as PoItemType
  })

  const handleDeleteItem = (poItem: PoItemType) => {
    setConfirmState({
      open: true,
      title: 'Hapus Barang',
      content: 'Apakah kamu yakin ingin menghapus barang ini?',
      onConfirm: () => {
        updateItemMutate(
          {
            poId: poData.id,
            payload: {
              id: poItem.id,
              itemId: poItem.itemId,
              quantity: 0,
              quantityUnit: poItem.quantityUnit,
              note: poItem.note,
              largeUnitQuantity: poItem.largeUnitQuantity,
              portions: [
                {
                  purchaseRequisitionId: poData?.references?.[0].purchaseRequisitionId,
                  quantity: 0
                }
              ],
              images: [
                ...(poItem.images ?? [])
                  .filter(image => !image.fileName && !!image.uploadId)
                  .map(image => ({
                    uploadId: image.uploadId
                  }))
              ]
            }
          },
          {
            onSuccess: () => {
              toast.success('Barang berhasil dihapus')
              fetchPoData()
              fetchLogList()
            }
          }
        )
      },
      confirmText: 'Hapus',
      confirmColor: 'error'
    })
  }

  const handleUpdateItem = (itemData: CreateMrInput['items'][0]) => {
    Promise.all(
      (itemData.images ?? [])
        .filter(item => !!item.fileName)
        .map(item =>
          uploadMutate({
            fieldName: `item_image_${itemData.itemId}`,
            file: item.content,
            scope: 'public-image',
            fileName: item.fileName
          })
        )
    )
      .then(values => {
        const uploadIds = values.map(val => ({
          uploadId: val.data?.id ?? ''
        }))
        updateItemMutate(
          {
            poId: poData.id,
            payload: {
              id: selectedItem.id,
              itemId: itemData.itemId,
              quantity: itemData.quantity,
              quantityUnit: itemData.quantityUnit,
              note: itemData.note,
              largeUnitQuantity: itemData.largeUnitQuantity,
              portions: [
                {
                  purchaseRequisitionId: poData?.references?.[0].purchaseRequisitionId,
                  quantity: itemData.quantity
                }
              ],
              images: [
                ...(itemData.images ?? [])
                  .filter(image => !image.fileName && !!image.uploadId)
                  .map(image => ({
                    uploadId: image.uploadId
                  })),
                ...uploadIds
              ]
            }
          },
          {
            onSuccess: () => {
              toast.success('Data barang berhasil diubah')
              fetchPoData()
              fetchLogList()
              setAddItemModalState({
                open: false,
                selectedItem: {}
              })
            }
          }
        )
      })
      .catch(error => {
        const message = error.response?.data?.message
        if (message) {
          toast.error(message)
        } else {
          toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
        }
      })
  }

  const table = useReactTable({
    data: poItems,
    columns: tableColumns(
      {
        onEdit: itemData => {
          setAddItemModalState({ open: true, selectedItem: itemData as PoItemType })
        },
        onRemove: handleDeleteItem
      },
      canRemove
    ),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    setPoItems(poData?.items ?? [])
  }, [poData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Barang</Typography>
          </div>
          <div>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan barang yang ingin dimasukkan dalam Purchase Order ini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? undefined : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          canUpdateAll={false}
          isLoading={isLoading}
          currentItem={selectedItem}
          onSubmit={handleUpdateItem}
          siteId={poData?.siteId}
        />
      )}
    </>
  )
}

export default ItemListCard
