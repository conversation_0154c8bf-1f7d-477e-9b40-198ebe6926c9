import React from 'react'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { PoType, PurchasePayload } from '../../config/types'
import InfoSection from '../../create/components/puchase-info/InfoSection'
import PaymentDetails from '../../create/components/puchase-info/PaymentDetails'
import CompanyQueryMethods, { VENDOR_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { PriceBreakdown } from './PriceBreakdown'
import { Button } from '@mui/material'
import usePartialState from '@/core/hooks/usePartialState'
import AddPurchaseDialog from '@/components/dialogs/add-purchase'
import { toast } from 'react-toastify'
import { TAX_PERCENTAGE } from '@/utils/constants'
import { useUpdatePo, useUpdatePoItem } from '@/api/services/po/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useUploadDocument } from '@/api/services/file/mutation'
import { format } from 'date-fns'
import { usePo } from '../../context/PoContext'

type Props = {
  poData: PoType
}

const PurchaseDetailCard: React.FC<Props> = ({ poData }) => {
  const { fetchPoData, fetchPoList, fetchLogList } = usePo()
  const { setConfirmState } = useMenu()
  const {
    vendorPicName,
    vendorPicPhoneNumber,
    estimatedDeliveryTime,
    paymentMethod,
    paymentDueDate,
    paymentTerms,
    tenderDocumentUrl,
    isEditable,
    paymentDueDays
  } = poData

  const [{ open: addPurchaseDialogOpen, purchaseData }, setPartialState, setAddPurchaseState] = usePartialState<{
    open: boolean
    purchaseData?: PurchasePayload
  }>({
    open: false,
    purchaseData: {
      items: []
    }
  })

  const { data: vendorData } = useQuery({
    enabled: !!poData?.vendorId,
    queryKey: [VENDOR_QUERY_KEY, poData?.vendorId],
    queryFn: () => CompanyQueryMethods.getVendor(poData?.vendorId)
  })

  const { mutateAsync: uploadDocumentMutate, isLoading: uploadDocumentLoading } = useUploadDocument()
  const { mutate: updatePoMutate, isLoading: updatePoLoading } = useUpdatePo()
  const { mutateAsync: updateItemMutate, isLoading: updateItemLoading } = useUpdatePoItem()

  const onUpdateSubmit = ({
    items,
    discountValue,
    shippingCost,
    tenderDocumentContent,
    tenderDocumentName,
    ...formInput
  }: PurchasePayload) => {
    setConfirmState({
      open: true,
      title: 'Ubah Data PO',
      content:
        'Apakah kamu yakin akan mengubah data PO ini? Pastikan semua detil yang kamu masukkan untuk PO ini sudah benar',
      confirmText: 'Ubah PO',
      onConfirm: () => {
        if (tenderDocumentContent) {
          uploadDocumentMutate({
            fieldName: `tender_document_${format(new Date(), 'yyyyMMddHHmmss')}`,
            file: tenderDocumentContent,
            scope: 'public-document',
            fileName: tenderDocumentName
          })
            .then(uploadResponse => {
              updatePoMutate(
                {
                  ...formInput,
                  poId: poData?.id,
                  items: items
                    .filter(val => !!val.itemId)
                    .map(val => ({
                      id: val.id,
                      itemId: val.itemId,
                      originalItemId: val.originalItemId,
                      quantity: val.quantity,
                      quantityUnit: val.quantityUnit,
                      largeUnitQuantity: val.largeUnitQuantity,
                      pricePerUnit: val.pricePerUnit,
                      taxType: val.taxType,
                      taxPercentage: TAX_PERCENTAGE,
                      discountType: val.discountType,
                      isDiscountAfterTax: val.isDiscountAfterTax,
                      note: val.note,
                      images: val.images,
                      ...(val.discountValue && { discountValue: val.discountValue }),
                      portions: val.portions ?? [
                        {
                          purchaseRequisitionId: poData?.references?.[0]?.purchaseRequisitionId,
                          quantity: val.quantity
                        }
                      ]
                    })),
                  ...(shippingCost && { shippingCost }),
                  ...(discountValue && { discountValue }),
                  tenderDocumentUploadId: uploadResponse?.data?.id
                },
                {
                  onSuccess: () => {
                    fetchPoData()
                    fetchPoList()
                    fetchLogList()
                    toast.success('PO berhasil diubah')
                    setAddPurchaseState({
                      open: false,
                      purchaseData: {
                        items: []
                      }
                    })
                  }
                }
              )
            })
            .catch(error => {
              const message = error.response?.data?.message
              if (message) {
                toast.error(message)
              } else {
                toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
              }
            })
        } else {
          updatePoMutate(
            {
              ...formInput,
              poId: poData?.id,
              items: items
                .filter(val => !!val.itemId)
                .map(val => ({
                  id: val.id,
                  itemId: val.itemId,
                  originalItemId: val.originalItemId,
                  quantity: val.quantity,
                  quantityUnit: val.quantityUnit,
                  largeUnitQuantity: val.largeUnitQuantity,
                  pricePerUnit: val.pricePerUnit,
                  taxType: val.taxType,
                  taxPercentage: TAX_PERCENTAGE,
                  discountType: val.discountType,
                  isDiscountAfterTax: val.isDiscountAfterTax,
                  note: val.note,
                  images: val.images,
                  ...(val.discountValue && { discountValue: val.discountValue }),
                  portions: val.portions ?? [
                    {
                      purchaseRequisitionId: poData?.references?.[0]?.purchaseRequisitionId,
                      quantity: val.quantity
                    }
                  ]
                })),
              ...(shippingCost && { shippingCost }),
              ...(discountValue && { discountValue })
            },
            {
              onSuccess: () => {
                fetchPoData()
                fetchPoList()
                fetchLogList()
                toast.success('PO berhasil diubah')
                setAddPurchaseState({
                  open: false,
                  purchaseData: {
                    items: []
                  }
                })
              }
            }
          )
        }
      }
    })
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Data Purchase</Typography>
            {isEditable && (
              <Button
                variant='outlined'
                onClick={() => {
                  setAddPurchaseState({
                    open: true,
                    purchaseData: poData as unknown as PurchasePayload
                  })
                }}
              >
                Ubah
              </Button>
            )}
          </div>

          <section className='flex flex-col gap-3'>
            {vendorData ? (
              <InfoSection
                vendorName={vendorData?.name}
                contactPerson={vendorPicName}
                contactNumber={vendorPicPhoneNumber}
                tenderDocumentUrl={tenderDocumentUrl}
              />
            ) : null}
            <PriceBreakdown poData={poData} />
            <PaymentDetails
              paymentDueDays={!!paymentDueDays ? String(paymentDueDays) : undefined}
              paymentMethod={paymentTerms}
              estimatedArrival={estimatedDeliveryTime}
              paymentDueDate={paymentDueDate}
            />
          </section>
        </CardContent>
      </Card>
      {addPurchaseDialogOpen && (
        <AddPurchaseDialog
          open={addPurchaseDialogOpen}
          setOpen={isOpen => setPartialState('open', isOpen)}
          purchaseData={purchaseData}
          onSubmit={submittedData => {
            onUpdateSubmit(submittedData)
          }}
        />
      )}
    </>
  )
}

export default PurchaseDetailCard
