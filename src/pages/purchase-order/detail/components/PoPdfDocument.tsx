import { Document, Page, View, Text, StyleSheet, Font, Image } from '@react-pdf/renderer'
import { Table, TableHeader, TableCell, TableRow } from '@ag-media/react-pdf-table'
import { ReactNode, useEffect, useState } from 'react'
import { PoType } from '../../config/types'
import { format, formatDate } from 'date-fns'
import { formatThousandSeparator, toCurrency } from '@/utils/helper'
import { VendorType } from '@/types/companyTypes'
import qrGenerator from '@/utils/qrGenerator'
import { AuthContextProps, useAuth } from '@/contexts/AuthContext'
import { PurchaseOrderPaymentMethod } from '../../config/enum'
import { id } from 'date-fns/locale'
import { colors } from '@mui/material'

// Styles
export const pdfStyles = StyleSheet.create({
  page: {
    padding: 30,
    color: '#323639'
  },
  textNormal: {
    fontSize: 9
  },
  textHeading: {
    fontSize: 20,
    fontWeight: 700,
    lineHeight: 1.2
  },
  textBold: {
    fontFamily: 'Helvetica-Bold',
    fontWeight: 700,
    fontSize: 9
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 5,
    marginBottom: 20
  },
  section: {
    display: 'flex',
    flexDirection: 'column',
    gap: 3,
    alignItems: 'flex-start',
    justifyContent: 'space-between'
  },
  flexRowBetween: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  flexRow: {
    display: 'flex',
    flexDirection: 'row'
  },
  flexCol: {
    display: 'flex',
    flexDirection: 'column'
  },
  border: {
    border: '1px solid #000',
    borderRadius: 4,
    padding: 4
  },
  textAddress: {
    fontSize: 8,
    width: '100%'
  },
  poInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15
  },
  table: {
    width: '100%',
    marginBottom: 15,
    border: '1px solid #000'
  },
  tableRow: {
    flexDirection: 'row',
    alignItems: 'center',
    borderTop: '1px solid black'
  },
  tableHeader: {
    fontWeight: 'bold',
    fontSize: 8,
    borderTop: '1px solid black'
  },
  tableCell: {
    fontSize: 8,
    padding: 6,
    border: 'none'
  },
  textRight: {
    textAlign: 'right',
    width: '100%'
  },
  textCenter: {
    textAlign: 'center',
    width: '100%'
  },
  totals: {
    fontSize: 10,
    marginLeft: 'auto',
    width: '30%',
    display: 'flex',
    flexDirection: 'column',
    gap: 4
  },
  signature: {
    marginTop: 20,
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap'
  },
  signatureItem: {
    fontSize: 10,
    minHeight: 65,
    maxWidth: 120,
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  signatureName: {
    paddingTop: 4,
    fontSize: 9
  }
})

const headerProduct = ['Kode Ext', 'Nama Barang', 'Qty', 'Harga', 'Tax', 'Diskon', 'Total']

export const TextItemFlex = ({ children, end, style }: { children: ReactNode; end?: boolean; style?: any }) => (
  <Text
    style={{
      width: '50%',
      fontSize: 9,
      ...(end ? { textAlign: 'right' } : {}),
      ...style
    }}
  >
    {children}
  </Text>
)

const PurchaseOrderPdfDocument = ({
  poData,
  vendorData,
  qrCode,
  isPreview = false
}: {
  poData: PoType
  vendorData: VendorType
  qrCode?: any
  isPreview?: boolean
}) => {
  return (
    <Document>
      <Page size='A4' style={pdfStyles.page}>
        {/* Header Perusahaan */}
        <View style={[pdfStyles.flexRow, { justifyContent: 'space-between', gap: 4 }]}>
          <View style={[pdfStyles.border, pdfStyles.flexRow, { gap: 8 }]}>
            <Image src='/rpu-logo.png' style={{ width: 32, height: 32 }} />
            <View style={[pdfStyles.flexCol]}>
              <Text style={[pdfStyles.textBold, { color: '#000' }]}>PT. RIMBA PERKASA UTAMA</Text>
              <View style={{ marginVertical: 2 }} />
              <Text style={pdfStyles.textAddress}>Jl. Danau Toba No. 148</Text>
              <Text style={[pdfStyles.textAddress, { marginTop: 2 }]}>Samarinda</Text>
            </View>
          </View>
          <Text style={[pdfStyles.textBold, { fontSize: 14, color: '#000', paddingHorizontal: 8 }]}>
            Purchase Order
          </Text>
        </View>

        {isPreview && (
          <View fixed style={{ top: '30%', bottom: '50%', left: '20%', right: '50%' }}>
            <Text style={{ color: colors.blueGrey[200], fontWeight: 800, fontSize: 54 }}>P R E V I E W</Text>
          </View>
        )}

        <View style={{ marginVertical: 10 }} />

        <View style={[pdfStyles.flexCol, { gap: 4 }]}>
          <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
            <TextItemFlex style={{ color: '#000' }}>Nomor PO</TextItemFlex>
            <Text style={{ fontSize: 9 }}>:</Text>
            <TextItemFlex>{poData?.number}</TextItemFlex>
          </View>
          <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
            <TextItemFlex style={{ color: '#000' }}>Tanggal PO</TextItemFlex>
            <Text style={{ fontSize: 9 }}>:</Text>
            <TextItemFlex>
              {formatDate(poData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}
            </TextItemFlex>
          </View>
          <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
            <TextItemFlex style={{ color: '#000' }}>Departemen</TextItemFlex>
            <Text style={{ fontSize: 9 }}>:</Text>
            <TextItemFlex>{poData?.department?.name ?? '-'}</TextItemFlex>
          </View>
          <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
            <TextItemFlex style={{ color: '#000' }}>Metode Bayar</TextItemFlex>
            <Text style={{ fontSize: 9 }}>:</Text>
            <TextItemFlex>{poData?.paymentMethod === PurchaseOrderPaymentMethod.TERM ? 'Tempo' : 'Tunai'}</TextItemFlex>
          </View>
          {poData?.paymentMethod === PurchaseOrderPaymentMethod.TERM && (
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Jatuh Tempo Bayar</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>
                {formatDate(poData?.paymentDueDate ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}
              </TextItemFlex>
            </View>
          )}
          {poData?.estimatedDeliveryTime && (
            <View style={[pdfStyles.flexRow, { width: '60%', gap: 4 }]}>
              <TextItemFlex style={{ color: '#000' }}>Perkiraan Tiba</TextItemFlex>
              <Text style={{ fontSize: 9 }}>:</Text>
              <TextItemFlex>
                {formatDate(poData?.estimatedDeliveryTime ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}
              </TextItemFlex>
            </View>
          )}
        </View>

        <View style={{ marginVertical: 10, borderTop: '1px solid #323639' }} />

        <View style={[pdfStyles.flexRow, { fontSize: 10, gap: 8 }]}>
          <View style={[pdfStyles.flexCol, { flexGrow: 1 }]}>
            <Text style={pdfStyles.textNormal}>Vendor:</Text>
            <Text style={[pdfStyles.textBold, { marginTop: 4 }]}>{vendorData?.name}</Text>
            <View style={{ marginVertical: 2 }} />
            <Text style={pdfStyles.textNormal}>{poData?.vendorAddress ?? '-'}</Text>
            <Text style={{ marginTop: 2, ...pdfStyles.textNormal }}>
              {poData?.vendorPicPhoneNumber} ({poData?.vendorPicName})
            </Text>
          </View>
          <View style={[pdfStyles.flexCol, { flexGrow: 1 }]}>
            <Text style={pdfStyles.textNormal}>Kirim Ke:</Text>
            <Text style={[pdfStyles.textBold, { marginTop: 4 }]}>{poData?.site?.name}</Text>
            <View style={{ marginVertical: 2 }} />
            <Text style={pdfStyles.textNormal}>{poData?.site?.address}</Text>
            <Text style={{ marginTop: 2 }}>{poData?.site?.phoneNumber ?? ''}</Text>
          </View>
        </View>

        <View style={{ marginVertical: 10 }} />

        {/* Tabel Items */}
        <Table
          tdStyle={{ padding: '10px', textAlign: 'center' }}
          style={pdfStyles.table}
          weightings={[0.8, 1.1, 0.5, 0.8, 0.3, 0.7, 1.1]}
        >
          <TableHeader fixed style={pdfStyles.tableHeader}>
            {headerProduct.map(header => (
              <TableCell key={`$indexHeader}`} style={[pdfStyles.tableCell, pdfStyles.textBold]}>
                <Text style={['Qty', 'Harga', 'Diskon', 'Total'].includes(header) ? pdfStyles.textCenter : {}}>
                  {header}
                </Text>
              </TableCell>
            ))}
          </TableHeader>

          {poData?.items.map((item, index) => (
            <TableRow
              key={index}
              style={[
                pdfStyles.tableRow,
                { borderBottom: index === poData?.items?.length - 1 ? '1px solid #000' : 'none' }
              ]}
            >
              <TableCell style={[pdfStyles.tableCell]}>
                <Text>{item?.item?.vendorNumber}</Text>
              </TableCell>
              <TableCell style={[pdfStyles.tableCell]}>
                <Text style={{ textAlign: 'left' }}>{item?.item?.name}</Text>
              </TableCell>
              <TableCell style={[pdfStyles.tableCell]}>
                <Text style={pdfStyles.textRight}>
                  {formatThousandSeparator(item?.quantity)} {item?.quantityUnit}
                </Text>
              </TableCell>
              <TableCell style={[pdfStyles.tableCell]}>
                <Text style={pdfStyles.textRight}>{toCurrency(item?.pricePerUnit, false, poData?.currency?.code)}</Text>
              </TableCell>
              <TableCell style={[pdfStyles.tableCell]}>
                <Text>{item?.taxPercentage ?? 0}%</Text>
              </TableCell>
              <TableCell style={[pdfStyles.tableCell]}>
                <Text style={pdfStyles.textRight}>
                  {toCurrency(item?.subtotalDiscount, false, poData?.currency?.code)}
                </Text>
              </TableCell>
              <TableCell style={[pdfStyles.tableCell]}>
                <Text style={pdfStyles.textRight}>{toCurrency(item?.totalPrice, true, poData?.currency?.code)}</Text>
              </TableCell>
            </TableRow>
          ))}
        </Table>

        <View style={[pdfStyles.flexRowBetween, { gap: 4 }]}>
          <View style={{ flexGrow: 3 }}>
            {/* Deskripsi dan Total */}
            <View
              style={{
                fontSize: 10,
                display: 'flex',
                alignItems: 'flex-start',
                gap: 4,
                flexDirection: 'row',
                maxWidth: '50%'
              }}
            >
              <Text style={pdfStyles.textNormal}>Catatan:</Text>
              <Text style={pdfStyles.textNormal}>{poData?.note ?? '-'}</Text>
            </View>
          </View>
          <View style={[pdfStyles.totals, { flexGrow: 1 }]}>
            <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
              <TextItemFlex>Sub Total</TextItemFlex>
              <Text style={pdfStyles.textNormal}>:</Text>
              <TextItemFlex end>
                {toCurrency(
                  poData?.items?.reduce((acc, item) => acc + item?.totalPrice, 0) || 0,
                  false,
                  poData?.currency?.code
                )}
              </TextItemFlex>
            </View>
            <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
              <TextItemFlex>Diskon Pembelian</TextItemFlex>
              <Text style={pdfStyles.textNormal}>:</Text>
              <TextItemFlex end>{toCurrency(poData?.discountAmount ?? 0, false, poData?.currency?.code)}</TextItemFlex>
            </View>
            <View>
              <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
                <TextItemFlex>Ongkos Kirim</TextItemFlex>
                <Text style={pdfStyles.textNormal}>:</Text>
                <TextItemFlex end>{toCurrency(poData?.shippingCost, false, poData?.currency?.code)}</TextItemFlex>
              </View>
            </View>
            <View>
              <View style={[pdfStyles.flexRowBetween, { width: '100%' }]}>
                <TextItemFlex
                  style={{
                    ...pdfStyles.textBold,
                    fontSize: 10
                  }}
                >
                  Total Purchase
                </TextItemFlex>
                <Text
                  style={{
                    ...pdfStyles.textBold,
                    fontSize: 10
                  }}
                >
                  :
                </Text>
                <TextItemFlex
                  style={{
                    ...pdfStyles.textBold,
                    fontSize: 10
                  }}
                  end
                >
                  {toCurrency(poData?.grandTotal, false, poData?.currency?.code)}
                </TextItemFlex>
              </View>
            </View>
          </View>
        </View>
        {/* Tanda Tangan */}
        <View style={{ marginVertical: 5 }} />
        {/* <Text style={{ fontSize: 11, paddingLeft: 10, marginBottom: 10 }}>Disetujui Oleh:</Text> */}
        <View style={[pdfStyles.signature, { justifyContent: 'flex-start' }]}>
          <View style={pdfStyles.signatureItem}>
            <Text style={{ flexGrow: 3, marginBottom: 5, fontSize: 9 }}>Dibuat oleh</Text>
            {/* <Image src={poData?.createdByUser?.qr} style={{ width: 40, height: 40 }} /> */}
            <View style={{ borderBottom: '1px solid #000', width: '60%', marginTop: 5 }} />
            <Text style={pdfStyles.signatureName}>{poData?.createdByUser?.fullName}</Text>
          </View>
          {!isPreview &&
            poData?.approvals?.map((app, index) => (
              <View key={index} style={pdfStyles.signatureItem}>
                <Text style={{ flexGrow: 3, marginBottom: 5, fontSize: 9 }}>{index === 0 ? 'Disetujui oleh' : ''}</Text>
                <View style={{ borderBottom: '1px solid #000', width: '60%', marginTop: 5 }} />
                <Text style={pdfStyles.signatureName}>{app?.user?.fullName}</Text>
              </View>
            ))}
        </View>
        <View
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'flex-end',
            padding: 16,
            width: '100%'
          }}
        >
          {qrCode && <Image src={qrCode} style={{ width: 64, height: 64 }} />}
        </View>
      </Page>
    </Document>
  )
}

export default PurchaseOrderPdfDocument
