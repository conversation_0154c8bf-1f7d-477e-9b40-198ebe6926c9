import { DepartmentType, ImageType, ItemType, SiteType, UnitType, VendorType } from '@/types/companyTypes'
import { ImagePayload, ListParams, UserIdPayload } from '@/types/payload'
import { PrType } from '@/types/prTypes'
import { ApproverType, UserType } from '@/types/userTypes'
import { PurchaseOrderApprovalStatus, PurchaseOrderCancelationType, PurchaseOrderStatus } from './enum'
import { WarehouseDataType, WarehouseItemType } from '@/types/appTypes'

export type PoPayload = {
  poId?: string
  purchaseRequisitionId?: string
  purchaseRequisitionIds?: string[]
  approvals?: UserIdPayload[]
  note?: string
} & PurchasePayload

export type PurchasePayload = {
  id?: string
  vendorId?: string
  vendor?: VendorType
  vendorName?: string
  vendorPicName?: string
  vendorPicPhoneNumber?: string
  vendorAddressId?: string
  items?: PoItemType[]
  discountType?: string
  discountValue?: number
  shippingCost?: number
  paymentTerms?: string
  paymentMethod?: string
  paymentDueDate?: string
  paymentDueDays?: string
  estimatedDeliveryTime?: string
  currencyId?: string
  exchangeRate?: number
  totalTax?: number
  totalPrice?: number
  discountAmount?: number
  subTotalItems?: number
  totalDiscount?: number
  grandTotal?: number
  tenderNumber?: string
  tenderDocumentContent?: string
  tenderDocumentName?: string
  tenderDocumentUrl?: string
  tenderDocumentMimeType?: string
  tenderDocumentUploadId?: string
}

export type PoItemType = WarehouseItemType

export type PoType = {
  id: string
  number: string
  itemsCount: number
  approvalsCount: number
  note: string
  cancelationType: string
  cancelationProofUrl: string
  cancelationProofMimeType: string
  cancelationNote: string
  vendorAddress: string
  vendorAddressId: string
  vendorId: string
  vendorPicName: string
  vendorPicPhoneNumber: string
  totalPrice: number
  discountType: string
  discountValue: number
  discountAmount: number
  totalDiscount: number
  shippingCost: number
  grandTotal: number
  paymentMethod: string
  paymentDueDate: string
  paymentTerms: string
  status: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  purchaseRequisitionId: string
  purchaseRequisitionChildId: string
  isClosed: boolean
  createdAt: string
  updatedAt: string
  createdBy: string
  items: PoItemType[]
  approvals: ApproverType[]
  department: DepartmentType
  site: SiteType
  purchaseRequisition: PrType
  purchaseRequisitionChild: PrType
  vendor: VendorType
  createdByUser: UserType
  tenderDocumentUrl?: string
  tenderDocumentMimeType?: string
  signature?: string
  references?: PoReferenceType[]
} & WarehouseDataType

export type PoReferenceType = {
  id: number
  purchaseRequisitionId?: string
  purchaseRequisitionChildId?: string
  purchaseRequisition?: PrType
  purchaseRequisitionChild?: PrType
}

export type PoApprovalPayload = {
  poId?: string
  approvalId?: number
  userId?: string
  note?: string
  status?: PurchaseOrderApprovalStatus
  isRead?: boolean
}

export type PoLogType = {
  id: number
  type: string
  status: string
  changes: string
  attachmentUrl: string
  attachmentMimeType: string
  purchaseOrderId: string
  userId: string
  purchaseOrderItemId: number
  createdAt: string
  user: UserType
  purchaseOrderItem: PoItemType
}

export type PoParams = {
  endDate?: string
  startDate?: string
  isClosed?: boolean
  status?: PurchaseOrderStatus
  materialRequestId?: string
  projectId?: string
  purchaseRequisitionId?: string
} & ListParams

export type CancelPoPayload = {
  poId: string
  cancelationType: string
  approvals: UserIdPayload[]
  cancelationProofUploadId: string
  cancelationNote: string
}
