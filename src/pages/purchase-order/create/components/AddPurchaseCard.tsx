// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { Button, IconButton } from '@mui/material'

import { useFormContext, useWatch } from 'react-hook-form'

// Component Imports
import usePartialState from '@/core/hooks/usePartialState'
import AddPurchaseDialog from '@/components/dialogs/add-purchase'
import PurchaseDataSection from './puchase-info/PurchaseDataSection'
import { useEffect } from 'react'
import { PoPayload, PurchasePayload } from '../../config/types'
import { usePo } from '../../context/PoContext'
import { isNullOrUndefined } from '@/utils/helper'
import { usePrList } from '../../context/PrListContext'

const EmptyState: React.FC = () => {
  return (
    <div className='flex flex-col items-center py-12 mt-4 w-full text-center max-md:max-w-full'>
      <h3 className='text-xl font-medium leading-relaxed text-gray-600 text-opacity-90'>Belum ada data</h3>
      <p className='text-sm leading-5 text-gray-600 text-opacity-60 max-md:max-w-full'>
        Masukkan dan lengkapi data pendukung purchase order
      </p>
    </div>
  )
}

const AddPurchaseCard = () => {
  const { showLoading } = usePo()
  const { prData } = usePrList()
  const { control, reset, watch, getValues } = useFormContext<PoPayload>()

  const itemsWatch = useWatch({
    control,
    name: `items`,
    defaultValue: []
  })

  const [{ open: addPurchaseDialogOpen, purchaseData }, setPartialState, setAddPurchaseState] = usePartialState<{
    open: boolean
    purchaseData?: PurchasePayload
  }>({
    open: false,
    purchaseData: {
      items: []
    }
  })

  useEffect(() => {
    setPartialState('purchaseData', {
      ...getValues(),
      items: itemsWatch,
      vendor: getValues().vendor ?? itemsWatch?.[0]?.item?.vendor,
      vendorId: getValues().vendorId ?? itemsWatch?.[0]?.item?.vendor?.id
    })
  }, [itemsWatch])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Data Purchase</Typography>
            {!isNullOrUndefined(purchaseData.totalPrice) && (
              <div className='flex gap-1 items-center'>
                <Typography>Edit</Typography>
                <IconButton size='small' onClick={() => setPartialState('open', true)}>
                  <i className='ic-baseline-edit text-secondary' />
                </IconButton>
              </div>
            )}
          </div>
          {!isNullOrUndefined(purchaseData.totalPrice) ? (
            <PurchaseDataSection purchaseData={purchaseData} />
          ) : (
            <>
              <EmptyState />
              <Button
                disabled={showLoading}
                color='primary'
                variant='outlined'
                onClick={() => setPartialState('open', true)}
                className='flex overflow-hidden flex-col justify-center items-center mt-4 w-full text-base font-medium tracking-wide leading-7 text-green-400 rounded-lg border border-solid border-indigo-500 border-opacity-50 max-md:max-w-full'
              >
                Masukkan Data
              </Button>
            </>
          )}
        </CardContent>
      </Card>
      {addPurchaseDialogOpen && (
        <AddPurchaseDialog
          open={addPurchaseDialogOpen}
          setOpen={isOpen => setPartialState('open', isOpen)}
          purchaseData={purchaseData}
          prData={prData}
          onSubmit={submittedData => {
            setAddPurchaseState({
              open: false,
              purchaseData: submittedData
            })
            reset({
              ...getValues(),
              ...submittedData
            })
          }}
        />
      )}
    </>
  )
}

export default AddPurchaseCard
