// MUI Imports
import { useEffect, useState } from 'react'

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports

// Component Imports
import { useFieldArray, useFormContext } from 'react-hook-form'

import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import ItemCard from './ItemCard'
import { usePrList } from '../../context/PrListContext'
import { WarehouseItemType } from '@/types/appTypes'
import { PoItemType, PoPayload } from '../../config/types'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useParams, useSearchParams } from 'react-router-dom'
import Table from '@/components/table'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../../multi-pr-create/config/table'
import { ItemMrInput } from '@/pages/material-request/create/config/schema'

const AddItemCard = () => {
  const [searchParams] = useSearchParams()
  const { prData } = usePrList()
  const { control, trigger, reset, getValues } = useFormContext<PoPayload>()

  const { fields, update, replace, remove } = useFieldArray({
    control,
    name: 'items',
    keyName: '_id'
  })

  const filledItems = (fields ?? []).filter(item => item.itemId)

  const [{ open: addItemOpen, selectedItem, selectedIndex }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as WarehouseItemType,
    selectedIndex: 0
  })

  const table = useReactTable({
    data: fields as ItemMrInput[],
    columns: tableColumns({
      disabled: !!prData?.isGeneralPurchase,
      onEdit: itemData => {
        setAddItemModalState({
          open: true,
          selectedItem: itemData as unknown as PoItemType,
          selectedIndex: fields.findIndex(item => item.itemId === itemData.itemId)
        })
      },
      onRemove: itemData => {
        remove(fields.findIndex(item => item.itemId === itemData.itemId))
        trigger()
      }
    }),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    replace(
      (prData?.items ?? [])
        .filter(item => {
          const selectedItemIds = searchParams?.get('items')?.split(',') ?? []
          return !item.isClosed && selectedItemIds.includes(String(item.id))
        })
        .map(item => {
          const remainingQty = item.isLargeUnit
            ? item.remainingQuantity / item.largeUnitQuantity
            : item.remainingQuantity
          return { ...item, quantity: remainingQty, originalItemId: item.itemId }
        })
    )
  }, [prData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Barang</Typography>
          </div>
          <div>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan barang yang ingin dimasukkan dalam Purchase Order ini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          canUpdateAll={false}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? {} : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          currentItem={selectedItem as WarehouseItemType}
          onSubmit={itemData => {
            update(selectedIndex, itemData)
            trigger()
            setAddItemModalState({
              open: false,
              selectedItem: {} as WarehouseItemType,
              selectedIndex: undefined
            })
          }}
          siteId={prData?.siteId}
        />
      )}
    </>
  )
}

export default AddItemCard
