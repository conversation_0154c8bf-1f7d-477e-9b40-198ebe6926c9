import React from 'react'

import { Button, Typography } from '@mui/material'
import { fileNameFromUrl } from '@/utils/helper'

interface InfoSectionProps {
  vendorName: string
  contactPerson: string
  contactNumber: string
  tenderDocumentName?: string
  tenderDocumentContent?: string
  tenderDocumentUrl?: string
  isGeneralPurchase?: boolean
}

const InfoSection: React.FC<InfoSectionProps> = ({
  vendorName,
  contactPerson,
  contactNumber,
  tenderDocumentName,
  tenderDocumentContent,
  tenderDocumentUrl,
  isGeneralPurchase
}) => {
  return (
    <div className='flex overflow-hidden flex-col justify-center p-4 w-full text-base font-medium tracking-normal leading-none rounded-lg bg-gray-400 bg-opacity-10 text-gray-600 text-opacity-60 max-md:max-w-full'>
      <div className='flex justify-between items-start w-full max-md:max-w-full'>
        <div className='flex flex-col flex-1 shrink w-full max-md:max-w-full'>
          <Typography className='text-sm'>Informasi Vendor</Typography>
          <Typography className='text-base'>{vendorName}</Typography>
          {!isGeneralPurchase ? (
            <Typography className='mt-2'>{`${contactPerson} - ${contactNumber}`}</Typography>
          ) : null}
          {(!!tenderDocumentName || !!tenderDocumentUrl) && (
            <>
              <Typography className='mt-3 text-sm '>Dokumen Penawaran Vendor</Typography>
              <div className='flex justify-between'>
                <Typography className='mt-2 text-opacity-90'>
                  {tenderDocumentName ?? fileNameFromUrl(tenderDocumentUrl)}
                </Typography>
                <Button
                  href={tenderDocumentUrl}
                  download={!!tenderDocumentUrl}
                  target='_blank'
                  onClick={() => {
                    if (tenderDocumentName) {
                      var image = new Image()
                      image.src = tenderDocumentContent

                      var w = window.open('')
                      w.document.write(image.outerHTML)
                    }
                  }}
                >
                  Lihat
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default InfoSection
