import React, { useEffect } from 'react'

import InfoSection from './InfoSection'
import PaymentDetails from './PaymentDetails'
import { toDate } from 'date-fns'
import { PurchasePayload } from '@/pages/purchase-order/config/types'
import { PriceBreakdown } from './PriceBreakdown'

type Props = {
  purchaseData: PurchasePayload
}

const PurchaseDataSection: React.FC<Props> = ({ purchaseData }: Props) => {
  const {
    vendor,
    vendorPicName,
    vendorPicPhoneNumber,
    estimatedDeliveryTime,
    paymentMethod,
    paymentDueDate,
    paymentTerms,
    tenderDocumentName,
    tenderDocumentUrl,
    tenderDocumentContent,
    paymentDueDays,
    isGeneralPurchase,
    vendorName
  } = purchaseData
  return (
    <section className='flex flex-col gap-3'>
      <InfoSection
        isGeneralPurchase={isGeneralPurchase}
        vendorName={!isGeneralPurchase ? vendor?.name : vendorName}
        contactPerson={vendorPicName}
        contactNumber={vendorPicPhoneNumber}
        tenderDocumentName={tenderDocumentName}
        tenderDocumentContent={tenderDocumentContent}
        tenderDocumentUrl={tenderDocumentUrl}
      />
      <PriceBreakdown purchaseData={purchaseData} />
      <PaymentDetails
        paymentDueDays={paymentDueDays}
        paymentMethod={paymentTerms}
        estimatedArrival={estimatedDeliveryTime}
        paymentDueDate={paymentDueDate}
      />
    </section>
  )
}

export default PurchaseDataSection
