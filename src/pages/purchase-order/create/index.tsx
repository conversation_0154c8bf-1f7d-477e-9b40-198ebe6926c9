// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Card, CardContent, Typography } from '@mui/material'

import { FormProvider, SubmitHandler, useForm, useWatch } from 'react-hook-form'

import { useRouter } from '@/routes/hooks'
import { useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import LoadingButton from '@mui/lab/LoadingButton'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { usePrList } from '../context/PrListContext'
import { format, formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import ApprovalListCard from '@/pages/material-request/create/components/ApprovalListCard'
import { Link, useLocation } from 'react-router-dom'
import AddPurchaseCard from './components/AddPurchaseCard'
import AdditionalInfoCard from './components/AdditionalInfoCard'
import { PoPayload } from '../config/types'
import { toast } from 'react-toastify'
import { usePo } from '../context/PoContext'
import { WarehouseItemType } from '@/types/appTypes'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { TAX_PERCENTAGE } from '@/utils/constants'
import { useAuth } from '@/contexts/AuthContext'
import { useUploadDocument } from '@/api/services/file/mutation'
import AddItemCard from './components/AddItemCard'
import { useDraft } from '@/pages/draft/context/DraftContext'
import { mergeArrays } from '@/utils/helper'
import { DRAFT_QUERY_KEY } from '@/api/services/draft/service'
import DraftQueryMethods from '@/api/services/draft/query'
import { DraftScope } from '@/types/draftsTypes'
import { ItemType } from '@/types/companyTypes'

const CreatePoPage = () => {
  const router = useRouter()
  const { search } = useLocation()
  const searchParams = new URLSearchParams(search)
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { prData, fetchPrData, fetchPoList } = usePrList()
  const { showLoading, uploadMutate, createPoMutate } = usePo()
  const { updateDraft, createDraft: saveAsDraft, loadingDraft, deleteDraft } = useDraft()

  const isLoading = showLoading || loadingDraft

  const { mutateAsync: uploadDocumentMutate, isLoading: uploadDocumentLoading } = useUploadDocument()

  const method = useForm<PoPayload>({
    // resolver: zodResolver(poPayloadSchema),
    mode: 'onChange'
  })

  const { control, handleSubmit, setValue, watch, getValues, reset } = method

  const scope = DefaultApprovalScope.PurchaseOrder

  const vendorIdWatch = useWatch({
    control,
    name: 'vendorId',
    defaultValue: undefined
  })

  const isGeneralPurchaseWatch = useWatch({
    control,
    name: 'isGeneralPurchase',
    defaultValue: false
  })

  const grandTotalWatch = useWatch({
    control,
    name: 'grandTotal',
    defaultValue: 0
  })

  const { data: draftData, refetch: refetchDraft } = useQuery({
    enabled: !!searchParams.get('draft'),
    queryKey: [DRAFT_QUERY_KEY, searchParams.get('draft')],
    queryFn: () => DraftQueryMethods.getOneDraft(searchParams.get('draft')),
    cacheTime: 0
  })

  const { data: approverList } = useQuery({
    enabled: !!prData?.siteId && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, prData?.siteId, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope,
        siteId: prData?.siteId,
        departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  const onSaveDraft = () => {
    const { items, ...formInput } = getValues()
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Simpan Draft Purchase Order',
      content:
        'Apakah kamu yakin akan menyimpan draft Purchase Order ini? Pastikan semua detil yang kamu masukkan untuk Material Request ini sudah benar',
      confirmText: 'Simpan',
      onConfirm: () => {
        Promise.all(
          (items ?? [])
            ?.filter(item => (item.images?.length ?? 0) > 0)
            ?.map(item =>
              Promise.all(
                item.images
                  .filter(image => !!image.content)
                  .map(image =>
                    uploadMutate({
                      fieldName: `item_image_${item.itemId}`,
                      file: image.content,
                      scope: 'public-image',
                      fileName: image.fileName
                    })
                  )
              )
                .then(values => {
                  const uploadIds = values.map(val => ({
                    uploadId: val.data?.id ?? ''
                  }))
                  return {
                    ...item,
                    images: [
                      ...(item.images ?? [])
                        .filter(image => !image.fileName && !!image.uploadId)
                        .map(image => ({
                          uploadId: image.uploadId
                        })),
                      ...uploadIds
                    ]
                  }
                })
                .catch(error => {
                  const message = error.response?.data?.message
                  if (message) {
                    toast.error(message)
                  } else {
                    toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
                  }
                  return {} as WarehouseItemType
                })
            )
        )
          .then(values => {
            const itemsValues = values
              .filter(val => !!val.itemId)
              .map(val => ({
                itemId: val.itemId,
                originalItemId: val.originalItemId,
                quantity: val.quantity,
                quantityUnit: val.quantityUnit,
                largeUnitQuantity: val.largeUnitQuantity,
                pricePerUnit: val.pricePerUnit,
                taxType: val.taxType,
                taxPercentage: TAX_PERCENTAGE,
                discountType: val.discountType,
                isDiscountAfterTax: val.isDiscountAfterTax,
                note: val.note,
                images: val.images,
                ...(val.discountValue && { discountValue: val.discountValue }),
                portions: [
                  {
                    purchaseRequisitionId: prData?.id ?? '',
                    quantity: val.quantity
                  }
                ]
              }))
            const stringifyPayload = JSON.stringify({
              ...formInput,
              approvals: approverList.map(approver => ({
                userId: approver.user?.id
              })),
              items: mergeArrays(items, itemsValues, 'itemId') as PoPayload['items'][],
              ...(formInput.shippingCost && { shippingCost: formInput.shippingCost }),
              ...(formInput.discountValue && { discountValue: formInput.discountValue })
            })
            if (draftData) {
              updateDraft(
                {
                  draftId: draftData.id,
                  siteId: prData.siteId,
                  payload: stringifyPayload
                },
                {
                  onSuccess: () => {
                    toast.success('PO disimpan sebagai draft.')
                    router.replace('/po/draft')
                  }
                }
              )
            } else {
              saveAsDraft(
                {
                  payload: stringifyPayload,
                  scope: DraftScope['PURCHASE-ORDER'],
                  siteId: prData.siteId
                },
                {
                  onSuccess: () => {
                    toast.success('PO disimpan sebagai draft.')
                    router.replace('/po/draft')
                  }
                }
              )
            }
            // }
          })
          .catch(error => {
            const message = error.response?.data?.message
            if (message) {
              toast.error(message)
            } else {
              toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
            }
          })
      }
    })
  }

  const onSubmitHandler: SubmitHandler<PoPayload> = ({
    items,
    discountValue,
    shippingCost,
    tenderDocumentContent,
    tenderDocumentName,
    ...formInput
  }: PoPayload) => {
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Buat PO',
      content:
        'Apakah kamu yakin akan membuat PO ini? Pastikan semua detil yang kamu masukkan untuk PO ini sudah benar',
      confirmText: 'Buat PO',
      onConfirm: () => {
        if (tenderDocumentContent) {
          uploadDocumentMutate({
            fieldName: `tender_document_${format(new Date(), 'yyyyMMddHHmmss')}`,
            file: tenderDocumentContent,
            scope: 'public-document',
            fileName: tenderDocumentName
          })
            .then(uploadResponse => {
              Promise.all(
                items.map(item => {
                  return (item.images?.length ?? 0) > 0
                    ? Promise.all(
                        item.images
                          .filter(image => !!image.content && !image.uploadId)
                          .map(image =>
                            uploadMutate({
                              fieldName: `item_image_${item.itemId}`,
                              file: image.content,
                              scope: 'public-image',
                              fileName: image.fileName
                            })
                          )
                      )
                        .then(values => {
                          const uploadIds = values.map(val => ({
                            uploadId: val.data?.id ?? ''
                          }))
                          return {
                            ...item,
                            images: [
                              ...(item.images ?? [])
                                .filter(image => !image.fileName && !!image.uploadId)
                                .map(image => ({
                                  uploadId: image.uploadId
                                })),
                              ...uploadIds
                            ]
                          }
                        })
                        .catch(error => {
                          const message = error.response?.data?.message
                          if (message) {
                            toast.error(message)
                          } else {
                            toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
                          }
                          return {} as WarehouseItemType
                        })
                    : item
                })
              )
                .then(values => {
                  createPoMutate(
                    {
                      ...formInput,
                      approvals: approverList
                        .filter(approver => approver.threshold <= grandTotalWatch)
                        .map(approver => ({
                          userId: approver.user?.id
                        })),
                      items: values
                        .filter(val => !!val.itemId)
                        .map(val => ({
                          itemId: val.itemId,
                          originalItemId: val.originalItemId,
                          quantity: val.quantity,
                          quantityUnit: val.quantityUnit,
                          largeUnitQuantity: val.largeUnitQuantity,
                          pricePerUnit: val.pricePerUnit,
                          taxType: val.taxType,
                          taxPercentage: TAX_PERCENTAGE,
                          discountType: val.discountType,
                          isDiscountAfterTax: val.isDiscountAfterTax,
                          note: val.note,
                          images: val.images,
                          ...(val.discountValue && { discountValue: val.discountValue }),
                          portions: [
                            {
                              purchaseRequisitionId: prData?.id ?? '',
                              quantity: val.quantity
                            }
                          ]
                        })),
                      ...(shippingCost && { shippingCost }),
                      ...(discountValue && { discountValue }),
                      tenderDocumentUploadId: uploadResponse?.data?.id
                    },
                    {
                      onSuccess: () => {
                        if (draftData) deleteDraft(draftData.id)
                        fetchPrData()
                        fetchPoList()
                        toast.success('PO berhasil dibuat')
                        router.back()
                      }
                    }
                  )
                })
                .catch(error => {
                  const message = error.response?.data?.message
                  if (message) {
                    toast.error(message)
                  } else {
                    toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
                  }
                })
            })
            .catch(error => {
              const message = error.response?.data?.message
              if (message) {
                toast.error(message)
              } else {
                toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
              }
            })
        } else {
          Promise.all(
            items.map(item => {
              return (item.images?.length ?? 0) > 0
                ? Promise.all(
                    item.images
                      .filter(image => !!image.content && !image.uploadId)
                      .map(image =>
                        uploadMutate({
                          fieldName: `item_image_${item.itemId}`,
                          file: image.content,
                          scope: 'public-image',
                          fileName: image.fileName
                        })
                      )
                  )
                    .then(values => {
                      const uploadIds = values.map(val => ({
                        uploadId: val.data?.id ?? ''
                      }))
                      return {
                        ...item,
                        images: [
                          ...(item.images ?? [])
                            .filter(image => !image.fileName && !!image.uploadId)
                            .map(image => ({
                              uploadId: image.uploadId
                            })),
                          ...uploadIds
                        ]
                      }
                    })
                    .catch(error => {
                      const message = error.response?.data?.message
                      if (message) {
                        toast.error(message)
                      } else {
                        toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
                      }
                      return {} as WarehouseItemType
                    })
                : item
            })
          )
            .then(values => {
              createPoMutate(
                {
                  ...formInput,
                  approvals: approverList
                    .filter(approver => approver.threshold <= grandTotalWatch)
                    .map(approver => ({
                      userId: approver.user?.id
                    })),
                  items: values
                    .filter(val => !!val.itemId)
                    .map(val => ({
                      itemId: val.itemId,
                      originalItemId: val.originalItemId,
                      quantity: val.quantity,
                      quantityUnit: val.quantityUnit,
                      largeUnitQuantity: val.largeUnitQuantity,
                      pricePerUnit: val.pricePerUnit,
                      taxType: val.taxType,
                      taxPercentage: TAX_PERCENTAGE,
                      discountType: val.discountType,
                      isDiscountAfterTax: val.isDiscountAfterTax,
                      note: val.note,
                      images: val.images,
                      ...(val.discountValue && { discountValue: val.discountValue }),
                      portions: [
                        {
                          purchaseRequisitionId: prData?.id ?? '',
                          quantity: val.quantity
                        }
                      ]
                    })),
                  ...(shippingCost && { shippingCost }),
                  ...(discountValue && { discountValue })
                },
                {
                  onSuccess: () => {
                    fetchPrData()
                    fetchPoList()
                    toast.success('PO berhasil dibuat')
                    router.back()
                  }
                }
              )
            })
            .catch(error => {
              const message = error.response?.data?.message
              if (message) {
                toast.error(message)
              } else {
                toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
              }
            })
        }
      }
    })
  }

  useEffect(() => {
    setValue('purchaseRequisitionIds', [prData?.id ?? ''] as string[])
  }, [prData])

  useEffect(() => {
    if (draftData) {
      const parsedPayload: PoPayload = JSON.parse(draftData.payload)
      const itemsArray = mergeArrays(parsedPayload.items, getValues('items'), 'itemId') as WarehouseItemType[]

      reset({
        ...parsedPayload,
        items: itemsArray,
        note: !!getValues('note') ? getValues('note') : parsedPayload.note
      })
    }
  }, [draftData])

  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>PO</Typography>
            </Link>
            <Link to='/po/pr-list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>PR Masuk</Typography>
            </Link>
            <Link to={'../..'} replace>
              <Typography color='var(--mui-palette-text-disabled)'>Detil PR</Typography>
            </Link>
            <Typography>Buat PO</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col max-sm:text-center'>
              <Typography variant='h4'>Buat PO</Typography>
              <Typography>Buat PO untuk diajukan kepada Role terkait</Typography>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
              <Button
                color='secondary'
                variant='outlined'
                disabled={isLoading || uploadDocumentLoading}
                className='is-full sm:is-auto'
                onClick={() => router.back()}
              >
                Batalkan
              </Button>
              <LoadingButton startIcon={<></>} loading={isLoading} variant='outlined' onClick={onSaveDraft}>
                Simpan Draft
              </LoadingButton>
              <LoadingButton
                startIcon={<></>}
                disabled={(!vendorIdWatch && !isGeneralPurchaseWatch) || isLoading || uploadDocumentLoading}
                loading={isLoading}
                variant='contained'
                onClick={handleSubmit(onSubmitHandler)}
              >
                Buat PO
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <AddItemCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Card>
                <CardContent className='space-y-1'>
                  <Typography variant='h5'>No. PR: {prData?.number}</Typography>
                  <Typography variant='subtitle1'>
                    {formatDate(prData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12}>
              <AdditionalInfoCard warehouseData={prData} />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <AddPurchaseCard />
            </Grid>
            <Grid item xs={12}>
              <ApprovalListCard
                approverList={
                  approverList.map(approver => ({ ...approver.user, threshold: approver.threshold ?? 0 })) ?? []
                }
                scope={DefaultApprovalScope.PurchaseOrder}
                amount={grandTotalWatch}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default CreatePoPage
