// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useEffect, useState } from 'react'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import { usePrList } from '../../context/PrListContext'
import { prItemTableColumns } from './config/table'
import { WarehouseItemType } from '@/types/appTypes'

const ItemListCard = ({ items }: { items: WarehouseItemType[] }) => {
  const table = useReactTable({
    data: items,
    columns: prItemTableColumns(),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  return (
    <>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Barang</Typography>
            <Typography className='text-sm text-gray-400'>
              Tambahkan barang yang ingin dimasukkan dalam Purchase Order ini
            </Typography>
          </td>
        }
      />
    </>
  )
}

export default ItemListCard
