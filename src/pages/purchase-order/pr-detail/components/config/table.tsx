import { StatusChipColorType } from '@/types/appTypes'
import { Mr<PERSON>r<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, MrType } from '@/types/mrTypes'
import { Button, Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { PoType } from '../../../config/types'
import { mrStatusOptions } from '@/pages/material-request/list/config/utils'
import { PurchaseOrderStatus } from '@/pages/purchase-order/config/enum'
import { PrType } from '@/types/prTypes'
import { Link } from 'react-router-dom'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [MrStatus.PROCESSED]: { color: 'warning' },
  [MrStatus.APPROVED]: { color: 'success' },
  [MrStatus.CANCELED]: { color: 'error' },
  [MrStatus.REJECTED]: { color: 'error' },
  [MrStatus.CLOSED]: { color: 'info' },
  [MrStatus.CANCEL_REQUESTED]: { color: 'error' }
}

type PoTypeWithAction = PoType & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string, isCancelation?: boolean) => void
}

// Column Definitions
const columnHelper = createColumnHelper<PoTypeWithAction>()

export const tableColumns = (rowAction: RowActionType, prData?: PrType) => [
  columnHelper.accessor('number', {
    header: 'No. PO',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() =>
          rowAction.showDetail(row.original.id, row.original.status === PurchaseOrderStatus.CANCEL_REQUESTED)
        }
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('purchaseRequisitionChild.number', {
    header: 'No. PR',
    cell: ({ row }) => {
      console.warn({ data: row.original })
      return (
        <Link
          to={`/po/pr-list/${row.original?.references?.[0]?.purchaseRequisitionId}/pr/${row.original?.references?.[0]?.purchaseRequisitionChild?.id}`}
        >
          <Button variant='text' className='px-0'>
            {row.original.purchaseRequisitionChild?.number ?? prData?.number}
          </Button>
        </Link>
      )
    }
  }),
  columnHelper.accessor('itemsCount', {
    header: 'Item',
    cell: ({ row }) => <Typography>{row.original.itemsCount} Item</Typography>
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        label={mrStatusOptions.find(option => option.value === row.original.status)?.label ?? '-'}
        color={statusChipColor[row.original.status]?.color ?? 'default'}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton
          size='small'
          onClick={() =>
            rowAction.showDetail(row.original.id, row.original.status === PurchaseOrderStatus.CANCEL_REQUESTED)
          }
        >
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
