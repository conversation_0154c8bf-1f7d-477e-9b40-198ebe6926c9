import { defaultListData } from '@/api/queryClient'
import { useDeleteCurrency } from '@/api/services/company/mutation'
import CompanyQueryMethods, { CURRENCIES_LIST_QUERY_KEY } from '@/api/services/company/query'
import AddCurrenciesDialog from '@/components/dialogs/add-currency-dialog'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { CurrenciesType } from '@/types/currenciesTypes'
import { ListParams } from '@/types/payload'
import { QueryObserverResult, useQuery } from '@tanstack/react-query'
import React, { useState } from 'react'
import { toast } from 'react-toastify'

type CurrenciesContextType = {
  isMobile: boolean
  currenciesParams: ListParams
  setCurrenciesParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialCurrenciesParams: (fieldName: keyof ListParams, value: any) => void
  handleAddClick: () => void
  handleDetail: (data: CurrenciesType) => void
  handleCloseDialog: () => void
  handleDeleteRow: (id: string) => void
  handleDetailEdit: () => void
  currenciesListResponse: ListResponse<CurrenciesType>
  fetchCurrenciesList: () => Promise<QueryObserverResult<ListResponse<CurrenciesType>, unknown>>
}

const CurrenciesContext = React.createContext<CurrenciesContextType>({} as CurrenciesContextType)

export const useCurrencies = () => {
  const context = React.useContext(CurrenciesContext)
  if (context === undefined) {
    throw new Error('useTax must be used within a PayrollProvider')
  }
  return context
}

export const CurrenciesProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { setConfirmState } = useMenu()
  const [{ state, readonly, currencies }, setCurrenciesState] = useState<{
    state: boolean
    readonly?: boolean
    currencies?: CurrenciesType
  }>({ state: false })
  const [currenciesParams, setPartialCurrenciesParams, setCurrenciesParams] = usePartialState<ListParams>({
    page: 1,
    limit: 10
  })

  const { mutate: removeCurrenciesMutate } = useDeleteCurrency()

  const { data: currenciesListResponse, refetch: fetchCurrenciesList } = useQuery({
    queryKey: [CURRENCIES_LIST_QUERY_KEY, JSON.stringify(currenciesParams)],
    queryFn: () => CompanyQueryMethods.getCurrenciesList(currenciesParams),
    placeholderData: defaultListData as ListResponse<CurrenciesType>
  })

  const handleAddClick = () => {
    setCurrenciesState(prev => ({ ...prev, state: true }))
  }

  const handleDetail = (data: CurrenciesType) => {
    setCurrenciesState(prev => ({ ...prev, currencies: data, state: true, readonly: true }))
  }

  const handleDetailEdit = () => {
    setCurrenciesState(prev => ({ ...prev, readonly: false }))
  }

  const handleCloseDialog = () => {
    setCurrenciesState(prev => ({ ...prev, state: false, currencies: undefined, readonly: undefined }))
  }

  const handleDeleteRow = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Mata Uang',
      content: 'Apakah kamu yakin ingin menghapus Mata Uang ini? Action ini tidak dapat diubah',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        removeCurrenciesMutate(id, {
          onSuccess: () => {
            setCurrenciesState(prev => ({ ...prev, state: false, currencies: undefined, readonly: undefined }))
            toast.success('Akun berhasil dihapus')
            fetchCurrenciesList()
          }
        })
      }
    })
  }

  const value = {
    isMobile,
    currenciesParams,
    setCurrenciesParams,
    setPartialCurrenciesParams,
    handleAddClick,
    handleDetail,
    handleCloseDialog,
    handleDeleteRow,
    handleDetailEdit,
    currenciesListResponse,
    fetchCurrenciesList
  }

  return (
    <CurrenciesContext.Provider value={value}>
      <AddCurrenciesDialog
        open={state}
        readonly={readonly}
        setOpen={bool =>
          setCurrenciesState(prev => ({
            ...prev,
            state: bool,
            ...(!bool && { currencies: undefined, readonly: undefined })
          }))
        }
        currenciesData={currencies}
      />
      {children}
    </CurrenciesContext.Provider>
  )
}
