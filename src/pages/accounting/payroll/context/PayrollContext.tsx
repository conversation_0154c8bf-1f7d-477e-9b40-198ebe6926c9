import { defaultListData } from '@/api/queryClient'
import { useDeleteSalary } from '@/api/services/salaries/mutation'
import SalaryQueryMethods, { SALARY_LIST_QUERY_KEY } from '@/api/services/salaries/query'
import AddSalaryDialog from '@/components/dialogs/add-salary-dialog'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { SalaryParams, SalaryType, SalaryTypeMaster } from '@/types/salaryTypes'
import { QueryObserverResult, useQuery } from '@tanstack/react-query'
import React, { useState } from 'react'
import { toast } from 'react-toastify'

type PayrollContextType = {
  isMobile: boolean
  payrollParams: SalaryParams
  setPayrollParams: React.Dispatch<React.SetStateAction<SalaryParams>>
  setPartialPayrollParams: (fieldName: keyof SalaryParams, value: any) => void
  handleAddClick: () => void
  handleDetail: (data: SalaryType) => void
  handleCloseDialog: () => void
  handleDeleteRow: (id: string) => void
  handleDetailEdit: () => void
  salaryListResponse: ListResponse<SalaryType>
  salaryTypeList: SalaryTypeMaster[]
  fetchSalaryList: () => Promise<QueryObserverResult<ListResponse<SalaryType>, unknown>>
}

const PayrollContext = React.createContext<PayrollContextType>({} as PayrollContextType)

export const usePayrollContext = () => {
  const context = React.useContext(PayrollContext)
  if (context === undefined) {
    throw new Error('usePayrollContext must be used within a PayrollProvider')
  }
  return context
}

export const PayrollProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { setConfirmState } = useMenu()
  const [{ state, readonly, salary }, setSalaryState] = useState<{
    state: boolean
    readonly?: boolean
    salary?: SalaryType
  }>({ state: false })
  const [payrollParams, setPartialPayrollParams, setPayrollParams] = usePartialState<SalaryParams>({
    page: 1,
    limit: 10
  })

  const { mutate: removeSalaryMutate } = useDeleteSalary()

  const { data: salaryListResponse, refetch: fetchSalaryList } = useQuery({
    queryKey: [SALARY_LIST_QUERY_KEY, JSON.stringify(payrollParams)],
    queryFn: () => SalaryQueryMethods.getSalaryList(payrollParams),
    placeholderData: defaultListData as ListResponse<SalaryType>
  })

  const { data: salaryTypeList } = useQuery({
    queryKey: ['SALARY_TYPE_QUERY_KEY'],
    queryFn: async () => (await SalaryQueryMethods.getSalaryTypeList()).items,
    placeholderData: []
  })

  const handleAddClick = () => {
    setSalaryState(prev => ({ ...prev, state: true }))
  }

  const handleDetail = (data: SalaryType) => {
    setSalaryState(prev => ({ ...prev, salary: data, state: true, readonly: true }))
  }

  const handleDetailEdit = () => {
    setSalaryState(prev => ({ ...prev, readonly: false }))
  }

  const handleCloseDialog = () => {
    setSalaryState(prev => ({ ...prev, state: false, salary: undefined, readonly: undefined }))
  }

  const handleDeleteRow = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Gaji / Tunjangan',
      content: 'Apakah kamu yakin ingin menghapus gaji / tunjangan ini? Action ini tidak dapat diubah',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        removeSalaryMutate(id, {
          onSuccess: () => {
            toast.success('Gaji / Tunjangan berhasil dihapus')
            fetchSalaryList()
          }
        })
      }
    })
  }

  const value = {
    isMobile,
    payrollParams,
    setPayrollParams,
    setPartialPayrollParams,
    handleAddClick,
    handleDetail,
    handleCloseDialog,
    handleDeleteRow,
    handleDetailEdit,
    salaryListResponse,
    salaryTypeList,
    fetchSalaryList
  }

  return (
    <PayrollContext.Provider value={value}>
      <AddSalaryDialog
        open={state}
        readonly={readonly}
        setOpen={bool =>
          setSalaryState(prev => ({ ...prev, state: bool, ...(!bool && { salary: undefined, readonly: undefined }) }))
        }
        salaryData={salary}
      />
      {children}
    </PayrollContext.Provider>
  )
}
