import truncateString from '@/core/utils/truncate'
import { SalaryType } from '@/types/salaryTypes'
import { toTitleCase } from '@/utils/helper'
import { Chip, Grid, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<SalaryType>()

type RowAction = {
  detail: (row: SalaryType) => void
  delete: (row: SalaryType) => void
}

export const tableColumns = (rowActions: RowAction) => [
  columnHelper.accessor('account.name', {
    header: 'Akun <PERSON>ban',
    cell: ({ row }) => (
      <Typography>
        {!!row.original?.account
          ? truncateString(`[${row.original.account.code}] ${row.original.account.name}`, 20)
          : '-'}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        variant='tonal'
        size='small'
        label={toTitleCase(row.original.status)}
        color={row.original.status === 'ACTIVE' ? 'success' : 'error'}
      />
    )
  }),
  columnHelper.accessor('name', {
    header: 'Nama'
  }),
  columnHelper.accessor('salaryType.name', {
    header: 'Tipe Gaji / Tunjangan'
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <Grid item>
          <IconButton onClick={() => rowActions.delete(row.original)}>
            <i className='ri-delete-bin-7-line text-textSecondary' />
          </IconButton>
        </Grid>
        <Grid item>
          <IconButton onClick={() => rowActions.detail(row.original)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
