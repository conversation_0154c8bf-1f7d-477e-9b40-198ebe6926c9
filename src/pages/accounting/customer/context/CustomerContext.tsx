import { defaultListData } from '@/api/queryClient'
import { useDeleteCustomer } from '@/api/services/company/mutation'
import CompanyQueryMethods, { CUSTOMER_LIST_QUERY_KEY } from '@/api/services/company/query'
import AddCustomerDialog from '@/components/dialogs/add-customer-dialog'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { CustomerType } from '@/types/customerTypes'
import { ListParams } from '@/types/payload'
import { QueryObserverResult, useQuery } from '@tanstack/react-query'
import React, { useState } from 'react'
import { toast } from 'react-toastify'

type CustomerContextType = {
  isMobile: boolean
  customerParams: ListParams
  setCustomerParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialCustomerParams: (fieldName: keyof ListParams, value: any) => void
  handleAddClick: () => void
  handleDetail: (data: CustomerType) => void
  handleCloseDialog: () => void
  handleDeleteRow: (id: string) => void
  handleDetailEdit: () => void
  customerListResponse: ListResponse<CustomerType>
  fetchCustomerList: () => Promise<QueryObserverResult<ListResponse<CustomerType>, unknown>>
}

const CustomerContext = React.createContext<CustomerContextType>({} as CustomerContextType)

export const useCustomer = () => {
  const context = React.useContext(CustomerContext)
  if (context === undefined) {
    throw new Error('useTax must be used within a PayrollProvider')
  }
  return context
}

export const CustomerProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { setConfirmState } = useMenu()
  const [{ state, readonly, customer }, setCustomerState] = useState<{
    state: boolean
    readonly?: boolean
    customer?: CustomerType
  }>({ state: false })
  const [customerParams, setPartialCustomerParams, setCustomerParams] = usePartialState<ListParams>({
    page: 1,
    limit: 10
  })

  const { mutate: removeCustomerMutate } = useDeleteCustomer()

  const { data: customerListResponse, refetch: fetchCustomerList } = useQuery({
    queryKey: [CUSTOMER_LIST_QUERY_KEY, JSON.stringify(customerParams)],
    queryFn: () => CompanyQueryMethods.getCustomerList(customerParams),
    placeholderData: defaultListData as ListResponse<CustomerType>
  })

  const handleAddClick = () => {
    setCustomerState(prev => ({ ...prev, state: true }))
  }

  const handleDetail = (data: CustomerType) => {
    setCustomerState(prev => ({ ...prev, customer: data, state: true, readonly: true }))
  }

  const handleDetailEdit = () => {
    setCustomerState(prev => ({ ...prev, readonly: false }))
  }

  const handleCloseDialog = () => {
    setCustomerState(prev => ({ ...prev, state: false, customer: undefined, readonly: undefined }))
  }

  const handleDeleteRow = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Pelanggan',
      content: 'Apakah kamu yakin ingin menghapus Pelanggan ini? Action ini tidak dapat diubah',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        removeCustomerMutate(id, {
          onSuccess: () => {
            setCustomerState(prev => ({ ...prev, state: false, customer: undefined, readonly: undefined }))
            toast.success('Pelanggan berhasil dihapus')
            fetchCustomerList()
          }
        })
      }
    })
  }

  const value = {
    isMobile,
    customerParams,
    setCustomerParams,
    setPartialCustomerParams,
    handleAddClick,
    handleDetail,
    handleCloseDialog,
    handleDeleteRow,
    handleDetailEdit,
    customerListResponse,
    fetchCustomerList
  }

  return (
    <CustomerContext.Provider value={value}>
      {state && (
        <AddCustomerDialog
          open={state}
          readonly={readonly}
          setOpen={bool =>
            setCustomerState(prev => ({
              ...prev,
              state: bool,
              ...(!bool && { customer: undefined, readonly: undefined })
            }))
          }
          customerData={customer}
        />
      )}
      {children}
    </CustomerContext.Provider>
  )
}
