import { CustomerType } from '@/types/customerTypes'
import { Grid, IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<CustomerType>()

type RowAction = {
  detail: (row: CustomerType) => void
}

export const tableColumns = (rowActions: RowAction) => [
  columnHelper.accessor('name', {
    header: 'Nama Customer/Pelanggan'
  }),
  columnHelper.accessor('address', {
    header: 'Alamat'
  }),
  columnHelper.accessor('picName', {
    header: 'Nama PIC',
    cell: ({ row }) => row.original?.picName ?? '-'
  }),
  columnHelper.accessor('picPhoneNumber', {
    header: 'No. Telepon PIC',
    cell: ({ row }) => row.original?.picPhoneNumber ?? '-'
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <Grid item>
          <IconButton onClick={() => rowActions.detail(row.original)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
