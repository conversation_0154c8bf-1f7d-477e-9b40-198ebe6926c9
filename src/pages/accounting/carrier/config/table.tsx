import { CarrierStatus, CarrierType } from '@/types/companyTypes'
import { CurrenciesType } from '@/types/currenciesTypes'
import { Button, Chip, Grid, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { getStatusConfig } from './utils'

const columnHelper = createColumnHelper<CarrierType>()

type RowAction = {
  detail: (row: CarrierType) => void
  onToggle: (row: CarrierType) => void
  loading?: boolean
}

export const tableColumns = (rowActions: RowAction) => [
  columnHelper.accessor('name', {
    header: 'Nama'
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        size='small'
        variant='tonal'
        label={getStatusConfig[row.original.status].label}
        color={getStatusConfig[row.original.status].color as any}
      />
    )
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <Grid item>
          <Button
            size='small'
            variant='outlined'
            color={row.original.status === CarrierStatus.ACTIVE ? 'error' : 'success'}
            onClick={() => rowActions?.onToggle(row.original)}
          >
            {row.original.status === CarrierStatus.ACTIVE ? 'Nonaktifkan' : 'Aktifkan'}
          </Button>
        </Grid>
        <Grid item>
          <IconButton onClick={() => rowActions.detail(row.original)} disabled={rowActions?.loading}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
