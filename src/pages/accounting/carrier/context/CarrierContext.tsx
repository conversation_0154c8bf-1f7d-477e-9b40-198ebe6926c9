import { defaultListData } from '@/api/queryClient'
import { useDeleteCarrier, useDeleteCurrency, useUpdateCarrier } from '@/api/services/company/mutation'
import CompanyQueryMethods, { CARRIER_LIST_QUERY_KEY, CURRENCIES_LIST_QUERY_KEY } from '@/api/services/company/query'
import AddCarrierDialog from '@/components/dialogs/add-carrier-dialog'
import AddCurrenciesDialog from '@/components/dialogs/add-currency-dialog'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { CarrierStatus, CarrierType } from '@/types/companyTypes'
import { CurrenciesType } from '@/types/currenciesTypes'
import { ListParams } from '@/types/payload'
import { QueryObserverResult, useQuery } from '@tanstack/react-query'
import React, { useState } from 'react'
import { toast } from 'react-toastify'

type CarrierContextType = {
  isMobile: boolean
  carrierParams: ListParams
  setCarrierParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialCarrierParams: (fieldName: keyof ListParams, value: any) => void
  handleAddClick: () => void
  handleDetail: (data: CarrierType) => void
  handleCloseDialog: () => void
  handleDeleteRow: (id: string) => void
  handleDetailEdit: () => void
  carrierListResponse: ListResponse<CarrierType>
  fetchCarrierList: () => Promise<QueryObserverResult<ListResponse<CarrierType>, unknown>>
  toggleStatus: (carrier: CarrierType) => void
}

const CarrierContext = React.createContext<CarrierContextType>({} as CarrierContextType)

export const useCarrier = () => {
  const context = React.useContext(CarrierContext)
  if (context === undefined) {
    throw new Error('useTax must be used within a PayrollProvider')
  }
  return context
}

export const CarrierProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { setConfirmState } = useMenu()
  const [{ state, readonly, carrier }, setCurrenciesState] = useState<{
    state: boolean
    readonly?: boolean
    carrier?: CarrierType
  }>({ state: false })
  const [carrierParams, setPartialCarrierParams, setCarrierParams] = usePartialState<ListParams>({
    page: 1,
    limit: 10
  })

  const { mutate: removeMutate } = useDeleteCarrier()
  const { mutate: updateMutate } = useUpdateCarrier()

  const { data: carrierListResponse, refetch: fetchCarrierList } = useQuery({
    queryKey: [CARRIER_LIST_QUERY_KEY, JSON.stringify(carrierParams)],
    queryFn: () => CompanyQueryMethods.getCarrierList(carrierParams),
    placeholderData: defaultListData as ListResponse<CarrierType>
  })

  const handleAddClick = () => {
    setCurrenciesState(prev => ({ ...prev, state: true, carrier: undefined }))
  }

  const handleDetail = (data: CarrierType) => {
    setCurrenciesState(prev => ({ ...prev, carrier: data, state: true, readonly: true }))
  }

  const handleDetailEdit = () => {
    setCurrenciesState(prev => ({ ...prev, readonly: false }))
  }

  const handleCloseDialog = () => {
    setCurrenciesState(prev => ({ ...prev, state: false, carrier: undefined, readonly: undefined }))
  }

  const handleDeleteRow = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Mata Uang',
      content: 'Apakah kamu yakin ingin menghapus Mata Uang ini? Action ini tidak dapat diubah',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        removeMutate(id, {
          onSuccess: () => {
            setCurrenciesState(prev => ({ ...prev, state: false, carrier: undefined, readonly: undefined }))
            toast.success('Pengiriman berhasil dihapus')
            fetchCarrierList()
          }
        })
      }
    })
  }

  const toggleStatus = (carrier: CarrierType) => {
    updateMutate(
      {
        ...carrier,
        id: carrier.id,
        status: carrier.status === CarrierStatus.ACTIVE ? CarrierStatus.INACTIVE : CarrierStatus.ACTIVE
      },
      {
        onSuccess: () => {
          toast.success('Status berhasil diubah')
          fetchCarrierList()
        }
      }
    )
  }

  const value = {
    isMobile,
    carrierParams,
    setCarrierParams,
    setPartialCarrierParams,
    handleAddClick,
    handleDetail,
    handleCloseDialog,
    handleDeleteRow,
    handleDetailEdit,
    carrierListResponse,
    fetchCarrierList,
    toggleStatus
  }

  return (
    <CarrierContext.Provider value={value}>
      <AddCarrierDialog
        open={state}
        readonly={readonly}
        setOpen={bool =>
          setCurrenciesState(prev => ({
            ...prev,
            state: bool,
            ...(!bool && { carrier: undefined, readonly: undefined })
          }))
        }
        carrierData={carrier}
      />
      {children}
    </CarrierContext.Provider>
  )
}
