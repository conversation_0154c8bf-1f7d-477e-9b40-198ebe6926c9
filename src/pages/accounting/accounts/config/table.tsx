import truncateString from '@/core/utils/truncate'
import { AccountMasterType, AccountType } from '@/types/accountTypes'
import { SalaryType } from '@/types/salaryTypes'
import { toCurrency, toTitleCase } from '@/utils/helper'
import { Chip, Grid, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<AccountType>()

type RowAction = {
  detail: (row: AccountType) => void
  delete: (row: AccountType) => void
  accountType?: AccountMasterType[]
}

export const tableColumns = (rowActions: RowAction) => [
  columnHelper.accessor('code', {
    header: 'Ko<PERSON>'
  }),
  columnHelper.accessor('name', {
    header: 'Nama'
  }),
  columnHelper.accessor('accountTypeId', {
    header: 'Tipe Akun',
    cell: ({ row }) => {
      const accountType = rowActions.accountType?.find(a => a.id === row.original?.accountTypeId)
      return accountType?.name
    }
  }),
  columnHelper.accessor('balance', {
    header: 'Saldo',
    cell: ({ row }) => (row.original?.balance ? toCurrency(row.original.balance) : '-')
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <Grid item>
          <IconButton onClick={() => rowActions.delete(row.original)}>
            <i className='ri-delete-bin-7-line text-textSecondary' />
          </IconButton>
        </Grid>
        <Grid item>
          <IconButton onClick={() => rowActions.detail(row.original)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
