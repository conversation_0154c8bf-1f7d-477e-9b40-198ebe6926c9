import { defaultListData } from '@/api/queryClient'
import { useDeleteAccount } from '@/api/services/account/mutation'
import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY, ACCOUNT_TYPE_QUERY_KEY } from '@/api/services/account/query'
import { useDeleteSalary } from '@/api/services/salaries/mutation'
import SalaryQueryMethods, { SALARY_LIST_QUERY_KEY } from '@/api/services/salaries/query'
import AddAccountsDialog from '@/components/dialogs/add-accounts-dialog'
import AddSalaryDialog from '@/components/dialogs/add-salary-dialog'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { AccountMasterType, AccountParams, AccountType } from '@/types/accountTypes'
import { ListResponse } from '@/types/api'
import { SalaryParams, SalaryType, SalaryTypeMaster } from '@/types/salaryTypes'
import { QueryObserverResult, useQuery } from '@tanstack/react-query'
import React, { useState } from 'react'
import { toast } from 'react-toastify'

type AccountsContextType = {
  isMobile: boolean
  accountParams: AccountParams
  setAccountsParams: React.Dispatch<React.SetStateAction<AccountParams>>
  setPartialAccountsParams: (fieldName: keyof AccountParams, value: any) => void
  handleAddClick: () => void
  handleDetail: (data: AccountType) => void
  handleCloseDialog: () => void
  handleDeleteRow: (id: string) => void
  handleDetailEdit: () => void
  accountTypeList: AccountMasterType[]
  accountListResponse: ListResponse<AccountType>
  fetchAccountList: () => Promise<QueryObserverResult<ListResponse<AccountType>, unknown>>
}

const AccountsContext = React.createContext<AccountsContextType>({} as AccountsContextType)

export const useAccount = () => {
  const context = React.useContext(AccountsContext)
  if (context === undefined) {
    throw new Error('usePayrollContext must be used within a PayrollProvider')
  }
  return context
}

export const AccountProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { setConfirmState } = useMenu()
  const [{ state, readonly, account }, setAccountState] = useState<{
    state: boolean
    readonly?: boolean
    account?: AccountType
  }>({ state: false })
  const [accountParams, setPartialAccountsParams, setAccountsParams] = usePartialState<AccountParams>({
    page: 1,
    limit: 10
  })

  const { mutate: removeAccountMutate } = useDeleteAccount()

  const { data: accountListResponse, refetch: fetchAccountList } = useQuery({
    queryKey: [ACCOUNT_LIST_QUERY_KEY, JSON.stringify(accountParams)],
    queryFn: () => AccountsQueryMethods.getAccountList(accountParams),
    placeholderData: defaultListData as ListResponse<AccountType>
  })

  const { data: accountTypeList } = useQuery({
    queryKey: [ACCOUNT_TYPE_QUERY_KEY],
    queryFn: async () =>
      (await AccountsQueryMethods.getAccountTypeList({ page: 1, limit: Number.MAX_SAFE_INTEGER })).items,
    placeholderData: []
  })

  const handleAddClick = () => {
    setAccountState(prev => ({ ...prev, state: true }))
  }

  const handleDetail = (data: AccountType) => {
    setAccountState(prev => ({ ...prev, account: data, state: true, readonly: true }))
  }

  const handleDetailEdit = () => {
    setAccountState(prev => ({ ...prev, readonly: false }))
  }

  const handleCloseDialog = () => {
    setAccountState(prev => ({ ...prev, state: false, account: undefined, readonly: undefined }))
  }

  const handleDeleteRow = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Akun',
      content: 'Apakah kamu yakin ingin menghapus Akun ini? Action ini tidak dapat diubah',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        removeAccountMutate(id, {
          onSuccess: () => {
            toast.success('Akun berhasil dihapus')
            fetchAccountList()
          }
        })
      }
    })
  }

  const value = {
    isMobile,
    accountParams,
    setAccountsParams,
    setPartialAccountsParams,
    handleAddClick,
    handleDetail,
    handleCloseDialog,
    handleDeleteRow,
    handleDetailEdit,
    accountTypeList,
    accountListResponse,
    fetchAccountList
  }

  return (
    <AccountsContext.Provider value={value}>
      {state && (
        <AddAccountsDialog
          open={state}
          readonly={readonly}
          setOpen={bool =>
            setAccountState(prev => ({
              ...prev,
              state: bool,
              ...(!bool && { account: undefined, readonly: undefined })
            }))
          }
          accountData={account}
        />
      )}
      {children}
    </AccountsContext.Provider>
  )
}
