import { defaultListData } from '@/api/queryClient'
import AccountsQueryMethods, {
  GENERAL_LEDGER_LIST_QUERY_KEY,
  GENERAL_LEDGER_QUERY_KEY
} from '@/api/services/account/query'
import usePartialState from '@/core/hooks/usePartialState'
import { useRouter } from '@/routes/hooks'
import { GeneralLedgerType } from '@/types/accountTypes'
import { QueryFn } from '@/types/alias'
import { ListResponse } from '@/types/api'
import { GeneralLedgerPayload, ListParams } from '@/types/payload'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQuery } from '@tanstack/react-query'
import React, { createContext, useEffect, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useParams } from 'react-router-dom'
import z, { array, number, object, string } from 'zod'

type GeneralLedgerContextProps = {
  generalLedgerParams: ListParams
  setPartialGeneralLedgerParams: (key: keyof ListParams, value: any) => void
  setGeneralLedgerParams: React.Dispatch<React.SetStateAction<ListParams>>
  generalLedgerList: ListResponse<GeneralLedgerType>
  fetchGeneralLedgerList: QueryFn<ListResponse<GeneralLedgerType>>
  generalLedgerData: GeneralLedgerType
  router: ReturnType<typeof useRouter>
}

const GeneralLedgerContext = createContext<GeneralLedgerContextProps>({} as GeneralLedgerContextProps)

export const useGeneralLedger = () => {
  const context = React.useContext(GeneralLedgerContext)
  if (context === undefined) {
    throw new Error('useGeneralLedger must be used within a GeneralLedgerProvider')
  }
  return context
}

const GeneralLedgerSchema = object({
  type: z.enum([
    'GENERAL',
    'SALES_TRANSACTION',
    'PURCHASE_TRANSACTION',
    'CASH_RECEIPT',
    'CASH_PAYMENT',
    'ADJUSTING_ENTRIES',
    'OTHER_TRANSACTION'
  ]),
  description: string().optional().nullable(),
  transactionDate: string(),
  lines: array(
    object({
      accountId: string(),
      type: z.enum(['DEBIT', 'CREDIT']),
      amount: number(),
      description: string().optional().nullable()
    })
  ).min(1),
  siteId: string().optional().nullable(),
  projectId: string().optional().nullable()
})

export const GeneralLedgerProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter()
  const params = useParams()
  const methods = useForm<GeneralLedgerPayload>({
    resolver: zodResolver(GeneralLedgerSchema)
  })
  const [selectedLedgerId, setSelectedLedgerId] = useState<string>()
  const [generalLedgerParams, setPartialGeneralLedgerParams, setGeneralLedgerParams] = usePartialState<ListParams>({
    limit: 10,
    page: 1
  })

  const { data: generalLedgerList, refetch: fetchGeneralLedgerList } = useQuery({
    queryKey: [GENERAL_LEDGER_LIST_QUERY_KEY, JSON.stringify(generalLedgerParams)],
    queryFn: () => AccountsQueryMethods.getGeneralLedgerList(generalLedgerParams),
    placeholderData: defaultListData as ListResponse<GeneralLedgerType>
  })

  const { data: generalLedgerData } = useQuery({
    enabled: !!selectedLedgerId,
    queryKey: [GENERAL_LEDGER_QUERY_KEY, selectedLedgerId],
    queryFn: () => AccountsQueryMethods.getGeneralLedger(selectedLedgerId)
  })

  useEffect(() => {
    if (params?.ledgerId) {
      setSelectedLedgerId(params.ledgerId)
    }
  }, [params])

  const value = {
    generalLedgerParams,
    setGeneralLedgerParams,
    setPartialGeneralLedgerParams,
    generalLedgerList,
    fetchGeneralLedgerList,
    generalLedgerData,
    router
  }
  return (
    <GeneralLedgerContext.Provider value={value}>
      <FormProvider {...methods}>{children}</FormProvider>
    </GeneralLedgerContext.Provider>
  )
}
