import { mergeArrays, toCurrency } from '@/utils/helper'
import { <PERSON><PERSON>, Card, CardContent, FormHelperText, Typography } from '@mui/material'
import {
  getFacetedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFacetedUniqueValues,
  useReactTable,
  getCoreRowModel,
  getFacetedMinMaxValues
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import { useMemo, useState } from 'react'
import Table from '@/components/table'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { GeneralLedgerPayload } from '@/types/payload'
import { useGeneralLedger } from '../../context/GeneralLedgerContext'

const DettailListLedgerCard = () => {
  const { generalLedgerData } = useGeneralLedger()

  const tableOptions: any = useMemo(
    () => ({
      data: generalLedgerData?.lines ?? [],
      columns: tableColumns({}),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [generalLedgerData]
  )

  const table = useReactTable(tableOptions)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Transaksi</Typography>
          </div>
          <div className='flex flex-col md:flex-row justify-between items-center gap-2'>
            <div className='rounded-[8px] bg-[#4C4E640D] p-4 py-3 flex flex-col flex-1'>
              <small>Total Debit</small>
              <Typography className='font-semibold'>{toCurrency(generalLedgerData?.totalDebit ?? 0)}</Typography>
            </div>
            <div className='rounded-[8px] bg-[#4C4E640D] p-4 py-3 flex flex-col flex-1'>
              <small>Total Kredit</small>
              <Typography className='font-semibold'>{toCurrency(generalLedgerData?.totalCredit)}</Typography>
            </div>
          </div>
          <div className='shadow-md rounded-[8px]'>
            <Table
              headerColor='green'
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada data transaksi</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan data transaksi dengan tombol di atas
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default DettailListLedgerCard
