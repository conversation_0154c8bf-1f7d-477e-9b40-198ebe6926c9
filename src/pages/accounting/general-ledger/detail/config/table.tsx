import { GeneralLedgerLineType, GeneralLedgerType } from '@/types/accountTypes'
import { GeneralLedgerPayload } from '@/types/payload'
import { toCurrency } from '@/utils/helper'
import { IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

type GeneralLedgerTypeWithAction = GeneralLedgerLineType

const columnHelper = createColumnHelper<GeneralLedgerTypeWithAction>()

type RowActionType = {
  delete?: (row: GeneralLedgerTypeWithAction) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('account.code', {
    header: 'Kode <PERSON>kun'
  }),
  columnHelper.accessor('account.name', {
    header: '<PERSON>a <PERSON>kun'
  }),
  columnHelper.accessor('debit', {
    header: 'Debit',
    cell: ({ row }) => toCurrency(row.original.debit)
  }),
  columnHelper.display({
    id: 'credit',
    header: 'Kredit',
    cell: ({ row }) => toCurrency(row.original.credit)
  }),
  columnHelper.accessor('description', {
    header: 'Keterangan',
    cell: ({ row }) => row.original.description
  }),
  ...(!!rowAction?.delete
    ? [
        columnHelper.display({
          id: 'delete',
          header: 'Action',
          cell: ({ row }) => {
            return (
              <IconButton onClick={() => rowAction.delete(row.original)}>
                <i className='ri-delete-bin-line text-error' />
              </IconButton>
            )
          }
        })
      ]
    : [])
]
