import { GeneralLedgerPayload } from '@/types/payload'
import { toCurrency } from '@/utils/helper'
import { IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

type GeneralLedgerTypeWithAction = GeneralLedgerPayload['lines'][0] & {
  code: string
  name: string
}

const columnHelper = createColumnHelper<GeneralLedgerTypeWithAction>()

type RowActionType = {
  delete: (row: GeneralLedgerPayload['lines'][0]) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('code', {
    header: 'Kode Akun'
  }),
  columnHelper.accessor('name', {
    header: '<PERSON><PERSON>kun'
  }),
  columnHelper.accessor('type', {
    header: 'Debit',
    cell: ({ row }) => (row.original.type === 'DEBIT' ? toCurrency(row.original.amount) : toCurrency(0))
  }),
  columnHelper.display({
    id: 'credit',
    header: 'Kredit',
    cell: ({ row }) => (row.original.type === 'CREDIT' ? toCurrency(row.original.amount) : toCurrency(0))
  }),
  columnHelper.accessor('description', {
    header: 'Keterangan',
    cell: ({ row }) => row.original.description
  }),
  columnHelper.display({
    id: 'delete',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.delete(row.original)}>
          <i className='ri-delete-bin-line text-error' />
        </IconButton>
      )
    }
  })
]
