import { mergeArrays, toCurrency } from '@/utils/helper'
import { <PERSON><PERSON>, Card, CardContent, FormHelperText, Typography } from '@mui/material'
import {
  getFacetedRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFacetedUniqueValues,
  useReactTable,
  getCoreRowModel,
  getFacetedMinMaxValues
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import { useMemo, useState } from 'react'
import Table from '@/components/table'
import DialogAddLinesTransaction, { LineType } from './dialog-transaction-lines'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { GeneralLedgerPayload } from '@/types/payload'

const DettailListLedgerCard = () => {
  const [dialogTransaction, setDialogTransaction] = useState(false)
  const [lines, setLines] = useState<LineType[]>([])
  const { control, reset, getValues } = useFormContext<GeneralLedgerPayload>()

  const tableOptions: any = useMemo(
    () => ({
      data: lines,
      columns: tableColumns({
        delete: row => {
          reset({
            ...getValues(),
            lines: getValues('lines').filter(l => l.accountId !== row.accountId)
          })
          setLines(lines.filter(l => l.accountId !== row.accountId))
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [lines]
  )

  const table = useReactTable(tableOptions)

  const handleSubmitLine = (line: LineType) => {
    reset({
      ...getValues(),
      lines: mergeArrays(
        getValues('lines'),
        [{ accountId: line.accountId, amount: line.amount, description: line.description, type: line.type }],
        'accountId'
      )
    })
    setLines(curr => mergeArrays(curr, [line], 'accountId'))
    setDialogTransaction(false)
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Transaksi</Typography>
            <Button size='small' onClick={() => setDialogTransaction(true)} variant='outlined'>
              Tambah Transaksi
            </Button>
          </div>
          <div className='flex flex-col md:flex-row justify-between items-center gap-2'>
            <div className='rounded-[8px] bg-[#4C4E640D] p-4 py-3 flex flex-col flex-1'>
              <small>Total Debit</small>
              <Typography className='font-semibold'>
                {toCurrency(
                  lines.filter(line => line.type === 'DEBIT').reduce((sum, line) => sum + (Number(line.amount) || 0), 0)
                )}
              </Typography>
            </div>
            <div className='rounded-[8px] bg-[#4C4E640D] p-4 py-3 flex flex-col flex-1'>
              <small>Total Kredit</small>
              <Typography className='font-semibold'>
                {toCurrency(
                  lines
                    .filter(line => line.type === 'CREDIT')
                    .reduce((sum, line) => sum + (Number(line.amount) || 0), 0)
                )}
              </Typography>
            </div>
          </div>
          <Controller
            control={control}
            name='lines'
            render={({ fieldState: { error } }) => (
              <>
                <div className='rounded-[8px] shadow-md'>
                  <Table
                    headerColor='green'
                    table={table}
                    emptyLabel={
                      <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                        <Typography>Belum ada data transaksi</Typography>
                        <Typography className='text-sm text-gray-400'>
                          Tambahkan data transaksi dengan tombol di atas
                        </Typography>
                      </td>
                    }
                  />
                </div>
                {!!error && <FormHelperText error>Wajib diisi</FormHelperText>}
              </>
            )}
          />
        </CardContent>
      </Card>
      {dialogTransaction && (
        <DialogAddLinesTransaction
          onSubmitLine={handleSubmitLine}
          open={dialogTransaction}
          setOpen={setDialogTransaction}
        />
      )}
    </>
  )
}

export default DettailListLedgerCard
