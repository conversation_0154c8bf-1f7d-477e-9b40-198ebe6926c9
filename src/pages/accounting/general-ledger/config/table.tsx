import { GeneralLedgerType } from '@/types/accountTypes'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { getGeneralLedgerType } from './utils'
import { formatDate } from 'date-fns'
import truncateString from '@/core/utils/truncate'
import { toCurrency } from '@/utils/helper'

const columnHelper = createColumnHelper<GeneralLedgerType>()

type RowActionType = {
  detail: (row: GeneralLedgerType) => void
}

export const tableColumns = ({ detail }: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No. Pencatatan',
    cell: ({ row }) => (
      <Typography onClick={() => detail(row.original)} color='primary' sx={{ cursor: 'pointer' }}>
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('type', {
    header: 'Tipe Transaksi',
    cell: ({ row }) => getGeneralLedgerType(row.original.type)
  }),
  columnHelper.accessor('description', {
    size: 250,
    header: 'Keterangan',
    cell: ({ row }) => <Typography>{truncateString(row.original?.description ?? '-', 15)}</Typography>
  }),
  columnHelper.display({
    id: 'total',
    header: 'Total Debit',
    cell: ({ row }) => toCurrency(row.original.totalDebit)
  }),
  columnHelper.display({
    id: 'total-credit',
    header: 'Total Kredit',
    cell: ({ row }) => toCurrency(row.original.totalCredit)
  }),
  columnHelper.accessor('transactionDate', {
    header: 'Tgl Transaksi',
    cell: ({ row }) => formatDate(row.original.transactionDate, 'dd/MM/yyyy')
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Pencatatan',
    cell: ({ row }) => formatDate(row.original.createdAt, 'dd/MM/yyyy')
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => detail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
