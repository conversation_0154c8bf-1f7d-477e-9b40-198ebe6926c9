import { defaultListData } from '@/api/queryClient'
import CashBankQueryMethods from '@/api/services/cashbank/query'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import { ChildrenType } from '@/core/types'
import { CashReceiptParams, CashReceiptType } from '@/pages/cash-bank/receipt/config/types'
import { QueryFn } from '@/types/alias'
import { ListResponse } from '@/types/api'
import { CustomerType } from '@/types/customerTypes'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import React from 'react'
import { useLocation } from 'react-router-dom'

type SellingReceiptContextProps = {
  isMobile: boolean
  sellingReceiptParams: ListParams
  setSellingReceiptParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialSellingReceiptParams: (key: keyof ListParams, value: any) => void
  cashReceiptListResponse: ListResponse<CashReceiptType>
  fetchCashReceiptList: QueryFn<ListResponse<CashReceiptType>>
}

const SellingReceiptContext = React.createContext<SellingReceiptContextProps>({} as SellingReceiptContextProps)

export const useSellingReceipt = () => {
  const context = React.useContext(SellingReceiptContext)

  if (!context) {
    throw new Error('useSellingReceipt must be used within SellingReceiptContext')
  }

  return context
}

export const SellingReceiptProvider = ({ children }: ChildrenType) => {
  const { isMobile } = useMobileScreen()
  const { pathname } = useLocation()
  const [sellingReceiptParams, setPartialSellingReceiptParams, setSellingReceiptParams] =
    usePartialState<CashReceiptParams>({
      page: 1,
      limit: 10,
      type: 'SALES'
    })
  const isApprovals = pathname.includes('approval')

  const { data: cashReceiptListResponse, refetch: fetchCashReceiptList } = useQuery({
    queryKey: ['SALES_RECEIPT_LIST_QUERY', JSON.stringify(sellingReceiptParams)],
    queryFn: () => {
      if (isApprovals) {
        return CashBankQueryMethods.getCashReceiptToMe(sellingReceiptParams)
      } else {
        return CashBankQueryMethods.getCashReceiptList(sellingReceiptParams)
      }
    },
    placeholderData: defaultListData as ListResponse<CashReceiptType>
  })

  const value = {
    isMobile,
    sellingReceiptParams,
    setSellingReceiptParams,
    setPartialSellingReceiptParams,
    cashReceiptListResponse,
    fetchCashReceiptList
  }

  return <SellingReceiptContext.Provider value={value}>{children}</SellingReceiptContext.Provider>
}
