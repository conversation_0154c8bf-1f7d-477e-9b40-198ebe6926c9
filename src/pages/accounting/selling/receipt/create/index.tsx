import { Grid, Typo<PERSON>, <PERSON>readcrum<PERSON>, But<PERSON> } from '@mui/material'
import { Link, useSearchParams } from 'react-router-dom'
import { useRouter } from '@/routes/hooks'
import { FormProvider, useForm } from 'react-hook-form'
import ReceiptDetailCard from './components/PaymentDetailCard'
import ApprovalListCard from './components/ApprovalListCard'
import InvoiceCard from './components/InvoiceCard'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import SellingInvoiceQueryMethods, { SELLING_INVOICE_LIST_QUERY_KEY } from '@/api/services/selling-invoice/query'
import { CreateCashReceiptPayload } from '@/pages/cash-bank/receipt/config/types'
import { useAuth } from '@/contexts/AuthContext'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { useCreateCashReceipt } from '@/api/services/cashbank/mutation'
import { toast } from 'react-toastify'
import { useSellingReceipt } from '../context/SellingReceiptContext'

const CreateSellingReceipt = () => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const { ownSiteList, userProfile } = useAuth()
  const [searchParams] = useSearchParams()

  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string>()

  const methods = useForm<CreateCashReceiptPayload>()
  const { handleSubmit, reset, getValues } = methods

  const { fetchCashReceiptList } = useSellingReceipt()
  const { mutate: createMutate, isLoading: loadingMutate } = useCreateCashReceipt()

  const { data: salesInvoiceData } = useQuery({
    enabled: !!selectedInvoiceId,
    queryKey: [SELLING_INVOICE_LIST_QUERY_KEY, selectedInvoiceId],
    queryFn: () => SellingInvoiceQueryMethods.getSellingInvoice(selectedInvoiceId)
  })

  const scope = `cash-receipt`

  const { data: approverList } = useQuery({
    enabled: !!ownSiteList?.[0]?.id && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, ownSiteList?.[0]?.id, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope,
        siteId: ownSiteList?.[0]?.id,
        departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  const onSubmit = (data: CreateCashReceiptPayload) => {
    setConfirmState({
      open: true,
      title: 'Buat Penerimaan Penjualan',
      content:
        'Apakah kamu yakin akan membuat penerimaan penjualan untuk faktur ini? Pastikan semua data sudah benar, action ini tidak bisa diubah',
      confirmText: 'Buat Penerimaan Penjualan',
      onConfirm: () => {
        createMutate(
          { ...data, approvals: approverList.map(approver => ({ userId: approver.user?.id })) },
          {
            onSuccess: () => {
              toast.success('Pencatatan berhasil dibuat')
              fetchCashReceiptList()
              router.push('/selling/receipts')
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    if (searchParams.get('invoiceId')) {
      reset({
        ...getValues(),
        type: 'SALES',
        salesInvoiceId: searchParams.get('invoiceId')
      })
      setSelectedInvoiceId(searchParams.get('invoiceId'))
    }
  }, [searchParams])

  return (
    <FormProvider {...methods}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Penjualan</Typography>
            </Link>
            <Link to='/selling/invoice' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Faktur</Typography>
            </Link>
            <Link to='/selling/invoice/123' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Detil Faktur</Typography>
            </Link>
            <Typography>Buat Penerimaan</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Tambah Penerimaan</Typography>
              <Typography>Lengkapi data dan tambahkan pencatatan Penerimaan</Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              <Button
                // disabled={loadingMutate}
                onClick={() => router.back()}
                color='secondary'
                variant='outlined'
                className='is-full sm:is-auto'
              >
                Batalkan
              </Button>
              <Button
                variant='contained'
                // disabled={loadingMutate}
                onClick={handleSubmit(onSubmit)}
                className='is-full sm:is-auto'
              >
                Buat Penerimaan
              </Button>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <InvoiceCard invoiceData={salesInvoiceData} />
        </Grid>
        <Grid item xs={12} md={6}>
          <ReceiptDetailCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default CreateSellingReceipt
