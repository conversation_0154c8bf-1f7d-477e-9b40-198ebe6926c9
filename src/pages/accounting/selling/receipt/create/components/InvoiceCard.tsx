import { toCurrency } from '@/utils/helper'
import { Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { SalesInvoice } from '../../../invoice/config/types'

type Props = {
  invoiceData: SalesInvoice
}

const InvoiceCard = ({ invoiceData }: Props) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Penerima<PERSON> dari <PERSON></Typography>
        </div>
        <div className='flex p-4 flex-col gap-3 w-full rounded-md bg-[#4C4E640D]'>
          <Typography variant='h4'>No. Faktur {invoiceData?.number}</Typography>
          <Typography>
            {invoiceData?.createdAt ? formatDate(invoiceData?.createdAt, 'eeee, dd/MM/yyyy', { locale: id }) : '-'}
          </Typography>
        </div>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-4 w-full'>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#4C4E640D]'>
            <small>Sub Total Faktur</small>
            <Typography>{toCurrency(invoiceData?.subTotalAmount)}</Typography>
          </div>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#4C4E640D]'>
            <small>Biaya Lain lain</small>
            <Typography>
              {toCurrency(invoiceData?.otherExpense?.reduce((acc, current) => acc + current.amount, 0) ?? 0)}
            </Typography>
          </div>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#4C4E640D]'>
            <small>Diskon Faktur</small>
            <Typography>{toCurrency(invoiceData?.discountAmount)}</Typography>
          </div>
          <div className='p-3 flex flex-col gap-2 w-full rounded-md bg-[#DBF7E8]'>
            <small>Total Faktur</small>
            <Typography variant='h5' color='primary' className='font-semibold'>
              {toCurrency(invoiceData?.totalAmount)}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default InvoiceCard
