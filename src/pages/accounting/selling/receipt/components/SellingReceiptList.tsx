import { But<PERSON>, <PERSON>, Typography } from '@mui/material'
import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useSellingReceipt } from '../context/SellingReceiptContext'
import { defaultListData } from '@/api/queryClient'
import { useRouter } from '@/routes/hooks'
import { tableColumns } from '../config/table'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useMemo } from 'react'

const SellingReceiptList = () => {
  const router = useRouter()
  const { sellingReceiptParams, setSellingReceiptParams, setPartialSellingReceiptParams, cashReceiptListResponse } =
    useSellingReceipt()
  const { search, page, limit } = sellingReceiptParams
  const { totalItems } = defaultListData

  const tableOptions = useMemo(
    () => ({
      data: cashReceiptListResponse?.items ?? [],
      columns: tableColumns({
        onDetail: row => {
          router.push(`/cash-bank/receipt/${row.id}`)
        }
      }),
      initialState: {
        pagination: {
          pageSize: limit ?? 10,
          pageIndex: page - 1
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [limit, page, cashReceiptListResponse]
  )

  const table = useReactTable(tableOptions as any)

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setPartialSellingReceiptParams('search', value)}
            placeholder='Cari'
            className='is-full sm:is-auto'
          />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
          {/* <Button variant='contained' onClick={() => {}}>
            Tambah Penerimaan
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Penerimaan</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua penerimaan yang dibuat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setSellingReceiptParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialSellingReceiptParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setSellingReceiptParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialSellingReceiptParams('page', pageIndex)}
      />
    </Card>
  )
}

export default SellingReceiptList
