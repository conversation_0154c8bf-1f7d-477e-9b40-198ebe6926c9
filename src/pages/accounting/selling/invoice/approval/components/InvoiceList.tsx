import { FormControl, InputLabel, MenuItem, Select, Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import Table from '@/components/table'
import { useRouter } from '@/routes/hooks'
import DateRangePicker from '@/components/DateRangePicker'
import { useAuth } from '@/contexts/AuthContext'
import { mgUserStatusOptions } from '@/pages/material-goods/list-out-approval/config/utils'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { MrUserStatus } from '@/types/mrTypes'
import DebouncedInput from '@/components/DebounceInput'
import { useEffect } from 'react'
import { useSellingInvoice } from '../../context/SellingInvoiceContext'
import { tableColumns } from '../../config/table'
import { defaultListData } from '@/api/queryClient'
import { useLocation } from 'react-router-dom'
import { SalesInvoiceStatus } from '../../config/types'

const InvoiceList = () => {
  const router = useRouter()
  const { pathname } = useLocation()
  const { userProfile } = useAuth()
  const {
    setSelectedId,
    invoicesParams: { page, userStatus, startDate, endDate, limit, search, status },
    setInvoicesParams,
    setPartialInvoicesParams,
    salesInvoiceList: { items: invoiceList, totalItems, totalPages, limit: limitItems, page: pageItems }
  } = useSellingInvoice()

  // TODO: MOVE THIS SHIT
  const table = useReactTable({
    data: invoiceList ?? [],
    columns: tableColumns({
      showDetail: invoice => {
        router.push(`/selling/approval/${invoice.id}`)
      }
    }),
    initialState: {
      pagination: {
        pageSize: limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const filterGroupConfig: FilterGroupConfig = {
    date: {
      options: [],
      values: [startDate, endDate]
    },
    status: {
      options: [],
      values: status ? [status] : []
    }
  }

  const onFilterChanged = ({ date, status, priority, site, department }: FilterValues) => {
    setInvoicesParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        status: status.length > 0 ? (status[0] as SalesInvoiceStatus) : undefined,
        priority: priority.length > 0 ? priority[0] : undefined,
        siteIds: site.length > 0 ? site[0] : undefined,
        departmentId: department.length > 0 ? department[0] : undefined
      }
    })
  }

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setPartialInvoicesParams('search', value)}
            placeholder='Cari'
            className='is-full sm:is-auto'
          />
          <FilterGroupDialog
            onRemoveFilter={onFilterChanged}
            config={filterGroupConfig}
            onFilterApplied={onFilterChanged}
          />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
          <Button variant='contained' onClick={() => router.push(`${pathname}/create`)}>
            Buat Faktur
          </Button>
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Data</Typography>
            <Typography className='text-sm text-gray-400'>Semua faktur Penjualan akan ditampilkan disini</Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setInvoicesParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialInvoicesParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setInvoicesParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialInvoicesParams('page', pageIndex)}
      />
    </Card>
  )
}

export default InvoiceList
