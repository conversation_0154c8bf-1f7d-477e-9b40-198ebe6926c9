import { createColumnHelper } from '@tanstack/react-table'
import { SellingInvoicesType } from '../context/SellingInvoiceContext'
import { Chip, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { SalesInvoice, SalesInvoiceStatus } from './types'
import { toCurrency } from '@/utils/helper'
import truncateString from '@/core/utils/truncate'

const columnHelper = createColumnHelper<SalesInvoice>()

type RowAction = {
  showDetail: (invoice: SalesInvoice) => void
}

export const statusConfig = (status: SalesInvoiceStatus) => {
  switch (status) {
    case 'PROCESSED':
      return { label: 'Diproses', color: 'warning' }
    case 'APPROVED':
      return { label: 'Disetujui', color: 'success' }
    case 'REJECTED':
      return { label: 'Ditolak', color: 'error' }
    case 'CLOSED':
      return { label: 'Ditutup', color: 'info' }
    case 'PAID':
      return { label: 'Sudah Dibayar', color: 'success' }
    default:
      return { label: status, color: 'default' }
  }
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('number', {
    header: 'No. Faktur',
    cell: ({ row }) => (
      <Typography onClick={() => rowAction.showDetail(row.original)} className='cursor-pointer' color='primary'>
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        size='small'
        variant='tonal'
        label={statusConfig(row.original.status as SalesInvoiceStatus).label}
        color={statusConfig(row.original.status as SalesInvoiceStatus).color as any}
      />
    )
  }),
  columnHelper.accessor('invoiceDate', {
    header: 'Tanggal Faktur',
    cell: ({ row }) => formatDate(new Date(row.original.invoiceDate), 'dd/MM/yyyy')
  }),
  columnHelper.accessor('customer.id', {
    header: 'Customer/Pelanggan',
    cell: ({ row }) => row.original.customer?.name
  }),
  columnHelper.accessor('note', {
    header: 'Memo',
    cell: ({ row }) => truncateString(row.original?.note ?? '-', 25)
  }),
  columnHelper.accessor('totalAmount', {
    header: 'Total Faktur',
    cell: ({ row }) => (
      <Typography align='right' color='primary'>
        {toCurrency(row.original.totalAmount)}
      </Typography>
    )
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.showDetail(row.original)}>
          <i className='ri-eye-line' />
        </IconButton>
      )
    }
  })
]
