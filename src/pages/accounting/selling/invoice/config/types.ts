import { PurchaseOrderPaymentMethod } from '@/pages/purchase-order/config/enum'
import { CurrenciesType } from '@/types/currenciesTypes'
import { CustomerType } from '@/types/customerTypes'
import { ListParams } from '@/types/payload'
import { ApproverType, UserOutlineType } from '@/types/userTypes'

export type SalesInvoiceStatus = 'PROCESSED' | 'APPROVED' | 'REJECTED' | 'CLOSED' | 'PAID'

export type SalesInvoiceParams = {
  status?: SalesInvoiceStatus
  projectId?: string
  customerId?: string
} & ListParams

export type CreateSalesInvoiceItem = {
  itemId: string
  quantity: number
  quantityUnit: string
  largeUnitQuantity: number
  pricePerUnit: number
  taxType: string
  taxPercentage: number
  discountType: string
  discountValue: number
  isDiscountAfterTax: boolean
  totalAmount?: number
}

export type CreateSalesOtherExpense = {
  accountId: string
  amount: number
  note: string
}

export type CreateSalesInvoicePayload = {
  customerId: string
  invoiceDate: string
  note: string
  currencyId: string
  exchangeRate: number
  documentUploadId: string
  approvals: ApproverType[]
  items: CreateSalesInvoiceItem[]
  otherExpenses: CreateSalesOtherExpense[]
  discountType: string
  discountValue: number
  paymentTerms: PurchaseOrderPaymentMethod
  paymentDueDays: number
  departmentId: string
  siteId: string
  projectId: string
}

export type SalesInvoice = {
  id: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  projectId: string
  customerId: string
  currencyId: string
  number: string
  subTotalAmount: number
  otherAmount: number
  discountType: string
  discountValue: number
  discountAmount: number
  totalAmount: number
  lcTotalAmount: number
  status: string
  note: string
  exchangeRate: number
  approvalsCount: number
  documentUrl: string
  documentMimeType: string
  paymentTerms: string
  paymentDueDays: number
  paymentDueDate: string
  invoiceDate: string
  createdAt: string
  updatedAt: string
  approvedAt: string
  approvals: ApproverType[]
  items: {
    id: number
    pricePerUnit: number
    quantity: number
    quantityUnit: string
    subTotalDiscount: number
    totalAmount: number
    item: {
      id: string
      number: string
      name: string
    }
  }[]
  otherExpense: CreateSalesOtherExpense[]
  customer: CustomerType
  currency: CurrenciesType
  createdByUser: UserOutlineType
}

export type SalesInvoiceLog = {
  id: number
  salesInvoiceId: string
  userId: string
  type: 'ACTIVITY'
  status: SalesInvoiceStatus | 'CREATED'
  changes: string
  createdAt: string
  user: UserOutlineType
}
