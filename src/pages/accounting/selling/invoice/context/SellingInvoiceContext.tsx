import usePartialState from '@/core/hooks/usePartialState'
import { ChildrenType } from '@/core/types'
import { CustomerType } from '@/types/customerTypes'
import { ListParams } from '@/types/payload'
import React, { createContext, useEffect, useState } from 'react'
import { SalesInvoice, SalesInvoiceParams } from '../config/types'
import { useQuery } from '@tanstack/react-query'
import SellingInvoiceQueryMethods, {
  SELLING_INVOICE_LIST_QUERY_KEY,
  SELLING_INVOICE_QUERY_KEY
} from '@/api/services/selling-invoice/query'
import { ListResponse } from '@/types/api'
import { QueryFn } from '@/types/alias'
import { useLocation, useParams } from 'react-router-dom'
import { defaultListData } from '@/api/queryClient'

type SellingInvoiceContextProps = {
  invoicesParams: SalesInvoiceParams
  setInvoicesParams: React.Dispatch<React.SetStateAction<SalesInvoiceParams>>
  setPartialInvoicesParams: (key: keyof SalesInvoiceParams, value: any) => void
  selectedId: string
  setSelectedId: React.Dispatch<React.SetStateAction<string>>
  salesInvoiceList: ListResponse<SalesInvoice>
  salesInvoiceData: SalesInvoice
  fetchSalesInvoiceList: QueryFn<ListResponse<SalesInvoice>>
  fetchSalesInvoiceData: QueryFn<SalesInvoice>
  salesInvoiceLogs: ListResponse<any>
  fetchSalesInvoiceLogs: QueryFn<ListResponse<any>>
  isApproval: boolean
}

export type SellingInvoicesType = {
  number: string
  status: string
  invoiceDate: string
  customer: CustomerType
  memo: string
  totalAmount: number
  id: string
  createdAt: string
}

const SellingInvoiceContext = createContext<SellingInvoiceContextProps>({} as SellingInvoiceContextProps)

export const useSellingInvoice = () => {
  const context = React.useContext(SellingInvoiceContext)
  if (context === undefined) {
    throw new Error('useSellingInvoice must be used within SellingInvoiceContext')
  }
  return context
}

export const SellingInvoiceProvider = ({ children }: ChildrenType) => {
  const { invoiceId } = useParams()
  const { pathname } = useLocation()
  const [invoicesParams, setPartialInvoicesParams, setInvoicesParams] = usePartialState<SalesInvoiceParams>({
    page: 1,
    limit: 10
  })
  const [selectedId, setSelectedId] = useState<string>()

  const isApproval = pathname.includes('approval')

  const { data: salesInvoiceList, refetch: fetchSalesInvoiceList } = useQuery({
    queryKey: [SELLING_INVOICE_LIST_QUERY_KEY, JSON.stringify(invoicesParams), isApproval],
    queryFn: () => {
      if (isApproval) {
        return SellingInvoiceQueryMethods.getSellingInvoiceToMe(invoicesParams)
      } else {
        return SellingInvoiceQueryMethods.getSellingInvoiceList(invoicesParams)
      }
    },
    placeholderData: defaultListData as ListResponse<SalesInvoice>
  })

  const { data: salesInvoiceData, refetch: fetchSalesInvoiceData } = useQuery({
    enabled: !!selectedId,
    queryKey: [SELLING_INVOICE_QUERY_KEY, selectedId],
    queryFn: () => SellingInvoiceQueryMethods.getSellingInvoice(selectedId)
  })

  const { data: salesInvoiceLogs, refetch: fetchSalesInvoiceLogs } = useQuery({
    enabled: !!selectedId,
    queryKey: [SELLING_INVOICE_QUERY_KEY, 'LOGS', selectedId],
    queryFn: () => SellingInvoiceQueryMethods.getSellingInvoiceLogs(selectedId)
  })

  useEffect(() => {
    if (!!invoiceId) {
      setSelectedId(invoiceId)
    }
  }, [invoiceId])

  const value = {
    invoicesParams,
    setInvoicesParams,
    setPartialInvoicesParams,
    selectedId,
    setSelectedId,
    salesInvoiceList,
    salesInvoiceData,
    fetchSalesInvoiceList,
    fetchSalesInvoiceData,
    salesInvoiceLogs,
    fetchSalesInvoiceLogs,
    isApproval
  }
  return <SellingInvoiceContext.Provider value={value}>{children}</SellingInvoiceContext.Provider>
}
