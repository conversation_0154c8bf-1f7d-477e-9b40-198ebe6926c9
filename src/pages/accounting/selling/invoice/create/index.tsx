import { useRouter } from '@/routes/hooks'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import DetailInvoiceCard from './components/DetailInvoiceCard'
import DocumentInvoiceCard from './components/DocumentInvoiceCard'
import ItemListCard from './components/ItemListCard'
import AdditionalExpenses from './components/AdditionalExpenseCard'
import DiscountCard from './components/DiscountCard'
import SummaryCard from './components/SummaryCard'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { FormProvider, useForm } from 'react-hook-form'
import { CreateSalesInvoicePayload } from '../config/types'
import { zodResolver } from '@hookform/resolvers/zod'
import z from 'zod'
import { useCreateSellingInvoice } from '@/api/services/selling-invoice/mutation'
import { toast } from 'react-toastify'
import { useSellingInvoice } from '../context/SellingInvoiceContext'
import { useAuth } from '@/contexts/AuthContext'
import { useQuery } from '@tanstack/react-query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'

const SellingInvoiceCreate = () => {
  const router = useRouter()
  const { fetchSalesInvoiceList } = useSellingInvoice()
  const { setConfirmState } = useMenu()
  const {
    userProfile: { departmentId, sites }
  } = useAuth()

  const method = useForm<CreateSalesInvoicePayload>()
  const { handleSubmit } = method

  const { mutate: createSellingInvoice } = useCreateSellingInvoice()

  const { data: approverList } = useQuery({
    enabled: !!sites?.[0]?.id && !!departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, DefaultApprovalScope.SalesInvoice, sites?.[0]?.id, departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope: DefaultApprovalScope.PurchaseInvoice,
        siteId: sites?.[0]?.id,
        departmentId: departmentId
      }),
    placeholderData: []
  })

  const onSubmitInvoice = (dto: CreateSalesInvoicePayload) => {
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Buat Faktur Pembelian',
      content:
        'Apakah kamu yakin akan membuat faktur pembelian ini? Pastikan semua data sudah benar, action ini tidak bisa diubah',
      confirmText: 'Buat Faktur Pembelian',
      onConfirm: () => {
        createSellingInvoice(
          {
            ...dto,
            items: dto?.items?.map(item => ({
              itemId: item.itemId,
              quantity: item.quantity,
              quantityUnit: item.quantityUnit,
              largeUnitQuantity: item.largeUnitQuantity,
              pricePerUnit: item.pricePerUnit,
              taxType: item.taxType,
              taxPercentage: item.taxPercentage,
              discountType: item.discountType,
              discountValue: item.discountValue,
              isDiscountAfterTax: item.isDiscountAfterTax
            })),
            otherExpenses: dto?.otherExpenses?.map(item => ({
              accountId: item.accountId,
              amount: item.amount,
              note: item.note
            })),
            approvals: approverList?.map(approver => ({
              userId: approver.user?.id
            })),
            documentUploadId: dto?.documentUploadId,
            departmentId: departmentId
          },
          {
            onSuccess: () => {
              toast.success('Faktur pembelian berhasil dibuat')
              fetchSalesInvoiceList()
              router.push('/selling/invoice')
            }
          }
        )
      }
    })
  }
  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Link to='#' replace>
              <Typography color='var(--color-text-disabled)'>Penjualan</Typography>
            </Link>
            <Link to='/selling/invoice' replace>
              <Typography color='var(--color-text-disabled)'>Faktur</Typography>
            </Link>
            <Typography>Buat Faktur</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Buat Faktur Penjualan</Typography>
              <Typography>Buat Faktur penjualan Baru</Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              <Button color='secondary' onClick={() => router.back()} variant='outlined'>
                Batalkan
              </Button>
              <Button onClick={handleSubmit(onSubmitInvoice)} variant='contained'>
                Buat Faktur
              </Button>
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <DetailInvoiceCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <DocumentInvoiceCard />
        </Grid>
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
        <Grid item xs={12}>
          <AdditionalExpenses />
        </Grid>
        <Grid item xs={12}>
          <DiscountCard />
        </Grid>
        <Grid item xs={12}>
          <SummaryCard />
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default SellingInvoiceCreate
