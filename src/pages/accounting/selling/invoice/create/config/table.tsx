import { AccountType } from '@/types/accountTypes'
import { ItemType } from '@/types/companyTypes'
import { toCurrency } from '@/utils/helper'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { CreateSalesInvoiceItem, CreateSalesOtherExpense } from '../../config/types'

type ItemWithQuantity = ItemType & CreateSalesInvoiceItem

type AccountWithMemo = CreateSalesOtherExpense & AccountType

const columnHelper = createColumnHelper<ItemWithQuantity>()
const addColumnHelper = createColumnHelper<AccountWithMemo>()

type RowAction = {
  delete: (id: string) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('number', {
    header: 'Kode Barang'
  }),
  columnHelper.accessor('name', {
    header: '<PERSON><PERSON>'
  }),
  columnHelper.accessor('quantity', {
    header: 'Kuantitas',
    cell: ({ row }) => `${row.original.quantity} ${row.original.smallUnit}`
  }),
  columnHelper.accessor('pricePerUnit', {
    header: 'Harga Satuan',
    cell: ({ row }) => toCurrency(row.original.pricePerUnit)
  }),
  columnHelper.accessor('discountValue', {
    header: 'Diskon',
    cell: ({ row }) => (
      <Typography color='error'>
        {row.original.discountType === 'PERCENTAGE'
          ? `${row.original.discountValue}%`
          : toCurrency(row.original.discountValue)}
      </Typography>
    )
  }),
  columnHelper.accessor('totalAmount', {
    header: 'Total Harga',
    cell: ({ row }) => <Typography color='primary'>{toCurrency(row.original.totalAmount)}</Typography>
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.delete(row.original.id)}>
          <i className='ri-delete-bin-line' />
        </IconButton>
      )
    }
  })
]

export const additionalTableColumns = (rowAction: RowAction) => [
  addColumnHelper.accessor('accountId', {
    header: 'Akun Perkiraan',
    cell: ({ row }) => `[${row.original.code}] ${row.original.name}`
  }),
  addColumnHelper.accessor('amount', {
    header: 'Nominal',
    cell: ({ row }) => <Typography>{toCurrency(row.original.amount)}</Typography>
  }),
  addColumnHelper.accessor('note', {
    header: 'Memo',
    cell: ({ row }) => <Typography>{row.original?.note ?? '-'}</Typography>
  }),
  addColumnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.delete(row.original.id)}>
          <i className='ri-delete-bin-line' />
        </IconButton>
      )
    }
  })
]
