import CompanyQueryMethods, { CUSTOMER_LIST_QUERY_KEY } from '@/api/services/company/query'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import {
  Autocomplete,
  Card,
  CardContent,
  CircularProgress,
  debounce,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { CreateSalesInvoicePayload } from '../../config/types'
import { formatISO, toDate } from 'date-fns'
import { CustomerType } from '@/types/customerTypes'

const DetailInvoiceCard = () => {
  const [searchQuery, setSearchQuery] = useState('')
  const { control } = useFormContext<CreateSalesInvoicePayload>()

  const {
    data: customerList,
    remove: removeCustomer,
    isFetching: loadingCustomer
  } = useQuery({
    enabled: !!searchQuery,
    queryKey: [CUSTOMER_LIST_QUERY_KEY, searchQuery],
    queryFn: () => CompanyQueryMethods.getCustomerList({ limit: Number.MAX_SAFE_INTEGER, search: searchQuery })
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-end'>
          <Typography variant='h5'>Detil Faktur</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={12} md={6}>
                <Controller
                  control={control}
                  name='customerId'
                  rules={{ required: true }}
                  render={({ field, fieldState: { error } }) => (
                    <Autocomplete
                      filterOptions={x => x}
                      isOptionEqualToValue={(option, value) => option.id === value.id}
                      onInputChange={debounce((e, newValue, reason) => {
                        if (reason === 'input') {
                          setSearchQuery(newValue)
                        }
                      }, 700)}
                      options={customerList?.items ?? []}
                      value={customerList?.items?.find(customer => customer.id === field.value) ?? null}
                      freeSolo={!searchQuery}
                      onChange={(e, newValue: CustomerType) => {
                        if (newValue) {
                          field.onChange(newValue.id)
                          removeCustomer()
                        }
                      }}
                      noOptionsText='Pelanggan tidak ditemukan'
                      loading={loadingCustomer}
                      renderInput={params => (
                        <TextField
                          {...params}
                          label='Customer/Pelanggan'
                          placeholder='Masukkan Pelanggan'
                          variant='outlined'
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: <>{loadingCustomer ? <CircularProgress /> : null}</>,
                            onKeyDown: e => {
                              if (e.key === 'Enter') {
                                e.stopPropagation()
                              }
                            }
                          }}
                          {...(!!error && { error: true })}
                        />
                      )}
                      getOptionLabel={(option: CustomerType) => option?.name}
                      className='flex-1'
                      renderOption={(props, option) => {
                        const { key, ...optionProps } = props
                        return (
                          <li key={key} {...optionProps}>
                            <Typography>
                              {option.name}, {option.address}
                            </Typography>
                          </li>
                        )
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  control={control}
                  name='paymentTerms'
                  rules={{ required: true }}
                  render={({ field, fieldState: { error } }) => (
                    <FormControl fullWidth>
                      <InputLabel id='payment-terms'>Syarat Pembayaran</InputLabel>
                      <Select
                        {...field}
                        error={!!error}
                        label='Syarat Pembayaran'
                        placeholder='Pilih Syarat Pembayaran'
                        className='bg-white'
                      >
                        {paymentMethodOptions.map(opt => (
                          <MenuItem key={opt.value} value={opt.value}>
                            {opt.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='note'
                  render={({ field }) => <TextField {...field} label='Memo' fullWidth />}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='invoiceDate'
                  rules={{ required: true }}
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <AppReactDatepicker
                      boxProps={{ className: 'is-full' }}
                      selected={value ? toDate(value) : undefined}
                      onChange={(date: Date) => onChange(formatISO(date))}
                      dateFormat='eeee dd/MM/yyyy'
                      customInput={
                        <TextField
                          fullWidth
                          label='Tanggal Jatuh Tempo'
                          placeholder='Pilih Tanggal'
                          className='flex-1'
                          InputProps={{
                            readOnly: true
                          }}
                          {...(!!error && { error: true })}
                        />
                      }
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default DetailInvoiceCard
