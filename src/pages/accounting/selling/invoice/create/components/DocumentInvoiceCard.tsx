import { <PERSON><PERSON>, <PERSON>, CardContent, <PERSON><PERSON>ield, Typography } from '@mui/material'
import { useFormContext } from 'react-hook-form'
import { useFilePicker } from 'use-file-picker'
import { CreateSalesInvoicePayload } from '../../config/types'
import { useEffect } from 'react'
import { useUploadDocument } from '@/api/services/file/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { uuidv7 } from 'uuidv7'

const DocumentInvoiceCard = () => {
  const { reset, getValues } = useFormContext<CreateSalesInvoicePayload>()

  const { filesContent, openFilePicker } = useFilePicker({
    readAs: 'DataURL',
    multiple: false,
    accept: 'image/png, image/jpeg, image/jpg'
  })

  const { mutateAsync: uploadDocument, isLoading: uploadLoading } = useUploadDocument()

  useEffect(() => {
    if (filesContent?.length > 0) {
      uploadDocument({
        fieldName: `invoice_document_${uuidv7()}`,
        file: filesContent[0].content,
        fileName: filesContent[0].name,
        scope: 'public-document'
      }).then(res => {
        if (res.data) {
          reset({
            ...getValues(),
            documentUploadId: res.data.id
          })
        }
      })
    }
  }, [filesContent])

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-end'>
          <Typography variant='h5'>Dokumen Penyerta (Opsional)</Typography>
        </div>
        <div className='flex flex-col gap-3 flex-1'>
          <Typography className='font-semibold'>Unggah Dokumen</Typography>
          <div className='flex items-center gap-4'>
            <TextField
              key={JSON.stringify(filesContent)}
              size='small'
              fullWidth
              value={filesContent?.[0]?.name}
              placeholder='Tidak ada file dipilih'
              aria-readonly
              className='flex-1'
            />
            <LoadingButton loading={uploadLoading} variant='contained' onClick={() => openFilePicker()}>
              Pilih File
            </LoadingButton>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default DocumentInvoiceCard
