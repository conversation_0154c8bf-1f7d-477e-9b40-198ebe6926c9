import { But<PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import { useMemo, useState } from 'react'
import Table from '@/components/table'
import { toCurrency } from '@/utils/helper'
import { ItemType } from '@/types/companyTypes'
import AddItemInvoiceDialog from './dialogs/dialog-add-item'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { CreateSalesInvoiceItem, CreateSalesInvoicePayload } from '../../invoice/config/types'
import { useSellingInvoice } from '../../invoice/context/SellingInvoiceContext'

const ItemListCard = () => {
  const { salesInvoiceData } = useSellingInvoice()
  const [itemState, setItemState] = useState<{ open: boolean; item?: ItemType }>({ open: false })

  const { control, reset, getValues } = useFormContext<CreateSalesInvoicePayload>()

  const itemsList = useWatch({ control, name: 'items' })

  const tableOptions: any = useMemo(
    () => ({
      data: salesInvoiceData?.items ?? [],
      columns: tableColumns({
        delete: id => {}
      }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: 0
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [salesInvoiceData]
  )
  const table = useReactTable(tableOptions)

  const handleAddItem = (item: CreateSalesInvoiceItem) => {
    reset({
      ...getValues(),
      items: [...(itemsList ?? []), item]
    })
    setItemState(curr => ({ ...curr, open: false }))
  }

  return (
    <>
      <Card>
        <Controller
          control={control}
          name='items'
          rules={{ required: true }}
          render={({ fieldState: { error } }) => (
            <CardContent className='flex flex-col gap-4'>
              <div className='flex justify-between items-center'>
                <Typography variant='h5' color={!!error ? 'error' : 'text-secondary'}>
                  List Barang
                </Typography>
              </div>
              <div className='rounded-[8px] shadow-md'>
                <Table
                  headerColor='green'
                  table={table}
                  emptyLabel={
                    <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography>Belum ada Barang</Typography>
                      <Typography className='text-sm text-gray-400'>
                        Semua Barang yang telah kamu buat akan ditampilkan di sini
                      </Typography>
                    </td>
                  }
                />
              </div>
              <div className='flex flex-col p-4 bg-[#DBF7E8] rounded-[8px]'>
                <small>Total Harga</small>
                <Typography color='primary'>
                  {toCurrency(salesInvoiceData?.items?.reduce((acc, item) => acc + item?.totalAmount, 0))}
                </Typography>
              </div>
            </CardContent>
          )}
        />
      </Card>
      {itemState?.open && (
        <AddItemInvoiceDialog
          onSubmitItem={handleAddItem}
          open={itemState.open}
          setOpen={open => setItemState({ open, ...(!open && { item: undefined }) })}
        />
      )}
    </>
  )
}

export default ItemListCard
