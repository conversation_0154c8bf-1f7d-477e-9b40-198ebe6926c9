import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import NumberField from '@/components/numeric/NumberField'
import { AccountParams, AccountType } from '@/types/accountTypes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Button,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { CreateSalesOtherExpense } from '../../../invoice/config/types'
import CurrencyField from '@/components/numeric/CurrencyField'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  onSaveExpense: (data: CreateSalesOtherExpense & AccountType) => void
}

const BASE_ACCOUNT_PARAMS: AccountParams = {
  limit: Number.MAX_SAFE_INTEGER,
  level: 1
}

const DialogAddExpenses = (props: Props) => {
  const { open, setOpen, onSaveExpense } = props
  const [selectedAccount, setSelectedAccount] = useState<AccountType>()
  const [accountQuery, setAccountQuery] = useState<string>('')

  const { control, handleSubmit } = useForm<CreateSalesOtherExpense>()

  const { data: accountList, remove: removeAccounts } = useQuery({
    enabled: !!accountQuery,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, accountQuery],
    queryFn: () => AccountsQueryMethods.getAccountList({ ...BASE_ACCOUNT_PARAMS, search: accountQuery })
  })

  const handleClose = () => {
    setOpen(false)
  }

  const handleSubmitExpense = (data: CreateSalesOtherExpense) => {
    onSaveExpense({ ...selectedAccount, ...data })
  }

  return (
    <Dialog fullWidth maxWidth='sm' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Tambah Biaya Lain Lain
        <Typography component='span' className='flex flex-col text-center'>
          Tambahkan Kode Perkiraan untuk faktur ini
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
        <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='accountId'
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <Autocomplete
                  value={selectedAccount}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setAccountQuery(newValue as string)
                    }
                  }, 700)}
                  options={accountList?.items ?? []}
                  getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                  freeSolo={!accountQuery}
                  noOptionsText='Akun tidak ditemukan'
                  onChange={(e, newValue: AccountType) => {
                    if (newValue) {
                      onChange(newValue.id)
                      setSelectedAccount(newValue)
                      removeAccounts()
                    }
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      InputProps={{
                        ...params.InputProps,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                      {...(!!error && { error: true })}
                      placeholder='Cari akun perkiraan'
                      label='Kode Perkiraan'
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='amount'
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  error={!!error}
                  label='Nominal'
                  fullWidth
                  InputProps={{ inputComponent: CurrencyField as any }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field }) => <TextField {...field} label='Memo' fullWidth />}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='justify-center pbs-0 sm:pbe-16 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton onClick={handleSubmit(handleSubmitExpense)} variant='contained' color='primary'>
          Simpan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogAddExpenses
