import { <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { additionalTableColumns } from '../config/table'
import Table from '@/components/table'
import DialogAddExpenses from './dialogs/dialog-add-additional-expense'
import { CreateSalesInvoicePayload, CreateSalesOtherExpense } from '../../invoice/config/types'
import { useFormContext, useWatch } from 'react-hook-form'
import { AccountType } from '@/types/accountTypes'
import { useSellingInvoice } from '../../invoice/context/SellingInvoiceContext'
import SalaryService from '@/api/services/salaries/service'

const AdditionalExpenses = () => {
  const { salesInvoiceData } = useSellingInvoice()
  const [dialogExpenses, setDialogExpenses] = useState(false)
  const { reset, getValues, control } = useFormContext<CreateSalesInvoicePayload>()

  const otherExpenses = useWatch({ control, name: 'otherExpenses' })

  const handleSaveExpense = (data: CreateSalesOtherExpense & AccountType) => {
    reset({
      ...getValues(),
      otherExpenses: [...(otherExpenses ?? []), data]
    })
    setDialogExpenses(false)
  }

  const tableOptions: any = useMemo(
    () => ({
      data: otherExpenses ?? [],
      columns: additionalTableColumns({
        delete: id => {
          reset({
            ...getValues(),
            otherExpenses: otherExpenses?.filter(expense => expense.accountId !== id)
          })
        }
      }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: 0
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [otherExpenses]
  )
  const table = useReactTable(tableOptions)
  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Biaya Lain-lain (Opsional)</Typography>
          </div>
          <div className='rounded-[8px] shadow-md'>
            <Table
              headerColor='green'
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Data</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Semua Biaya Lain-lain akan ditampilkan di sini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {dialogExpenses && (
        <DialogAddExpenses onSaveExpense={handleSaveExpense} open={dialogExpenses} setOpen={setDialogExpenses} />
      )}
    </>
  )
}

export default AdditionalExpenses
