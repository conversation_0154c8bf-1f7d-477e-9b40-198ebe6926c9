import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ield, Typography } from '@mui/material'
import { useFormContext } from 'react-hook-form'
import { useFilePicker } from 'use-file-picker'
import { CreateSalesInvoicePayload } from '../../invoice/config/types'
import { useEffect } from 'react'
import { useUploadDocument } from '@/api/services/file/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { uuidv7 } from 'uuidv7'
import { useSellingInvoice } from '../../invoice/context/SellingInvoiceContext'
import { FileMimeType } from '@/utils/mimeType'

const DocumentInvoiceCard = () => {
  const { salesInvoiceData } = useSellingInvoice()
  const { reset, getValues } = useFormContext<CreateSalesInvoicePayload>()

  const { filesContent, openFilePicker } = useFilePicker({
    readAs: 'DataURL',
    multiple: false,
    accept: 'image/png, image/jpeg, image/jpg'
  })

  const { mutateAsync: uploadDocument, isLoading: uploadLoading } = useUploadDocument()

  useEffect(() => {
    if (filesContent?.length > 0) {
      uploadDocument({
        fieldName: `invoice_document_${uuidv7()}`,
        file: filesContent[0].content,
        fileName: filesContent[0].name,
        scope: 'public-document'
      }).then(res => {
        if (res.data) {
          reset({
            ...getValues(),
            documentUploadId: res.data.id
          })
        }
      })
    }
  }, [filesContent])

  const handleDownload = () => {
    if (salesInvoiceData.documentUrl) {
      window.open(salesInvoiceData.documentUrl, '_blank')
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-end'>
          <Typography variant='h5'>Dokumen Penyerta</Typography>
        </div>
        {salesInvoiceData?.documentUrl ? (
          <div className='flex justify-between items-center p-3 bg-gray-50 rounded-lg'>
            <div className='flex flex-col gap-1'>
              <Typography variant='body1' className='font-medium'>
                Dokumen_Penyerta.{FileMimeType[salesInvoiceData?.documentMimeType]}
              </Typography>
            </div>
            <Button
              variant='text'
              color='primary'
              onClick={handleDownload}
              className='text-green-600 hover:text-green-700'
            >
              Unduh
            </Button>
          </div>
        ) : (
          <div className='flex flex-col gap-2 items-center'>
            <Typography variant='h5'>Tidak ada dokumen penyerta</Typography>
            <Typography>Dokumen Penyerta faktur ditampilkan disini</Typography>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default DocumentInvoiceCard
