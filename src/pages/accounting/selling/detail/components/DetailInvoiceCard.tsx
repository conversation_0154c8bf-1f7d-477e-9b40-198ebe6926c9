import CompanyQueryMethods, { CUSTOMER_LIST_QUERY_KEY } from '@/api/services/company/query'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import {
  Autocomplete,
  Card,
  CardContent,
  CircularProgress,
  debounce,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import { CreateSalesInvoicePayload } from '../../invoice/config/types'
import { formatDate, formatISO, toDate } from 'date-fns'
import { CustomerType } from '@/types/customerTypes'
import { useSellingInvoice } from '../../invoice/context/SellingInvoiceContext'
import { id } from 'date-fns/locale'

const DetailInvoiceCard = () => {
  const { salesInvoiceData } = useSellingInvoice()
  const [searchQuery, setSearchQuery] = useState('')
  const { control } = useFormContext<CreateSalesInvoicePayload>()

  const {
    data: customerList,
    remove: removeCustomer,
    isFetching: loadingCustomer
  } = useQuery({
    enabled: !!searchQuery,
    queryKey: [CUSTOMER_LIST_QUERY_KEY, searchQuery],
    queryFn: () => CompanyQueryMethods.getCustomerList({ limit: Number.MAX_SAFE_INTEGER, search: searchQuery })
  })

  // Make detail mode based on value in salesInvoiceData
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-end'>
          <Typography variant='h5'>Detil Faktur</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Grid container spacing={4}>
              <Grid item xs={12} md={6}>
                <div className='flex flex-col gap-2'>
                  <small>Customer/Pelanggan</small>
                  <Typography>{salesInvoiceData?.customer?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12} md={6}>
                <div className='flex flex-col gap-2'>
                  <small>Syarat Pembayaran</small>
                  <Typography>
                    {paymentMethodOptions.find(opt => opt.value === salesInvoiceData?.paymentTerms)?.label}
                  </Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-2'>
                  <small>Memo</small>
                  <Typography>{salesInvoiceData?.note ?? '-'}</Typography>
                </div>
              </Grid>
              <Grid item xs={12} md={6}>
                <div className='flex flex-col gap-2'>
                  <small>Tanggal Faktur</small>
                  <Typography>
                    {salesInvoiceData?.invoiceDate
                      ? formatDate(salesInvoiceData?.invoiceDate, 'eeee, dd/MM/yyyy', { locale: id })
                      : '-'}
                  </Typography>
                </div>
              </Grid>
              <Grid item xs={12} md={6}>
                {' '}
                <div className='flex flex-col gap-2'>
                  <small>Tanggal Jatuh Tempo</small>
                  <Typography color='error'>
                    {salesInvoiceData?.paymentDueDate
                      ? formatDate(salesInvoiceData?.paymentDueDate, 'eeee, dd/MM/yyyy')
                      : '-'}
                  </Typography>
                </div>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default DetailInvoiceCard
