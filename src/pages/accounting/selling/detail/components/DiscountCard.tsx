import NumberField from '@/components/numeric/NumberField'
import { discountTypeOptions } from '@/pages/purchase-order/config/options'
import { isNullOrUndefined, toCurrency } from '@/utils/helper'
import { Card, CardContent, FormControl, InputLabel, MenuItem, Select, TextField, Typography } from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { CreateSalesInvoicePayload } from '../../invoice/config/types'
import CurrencyField from '@/components/numeric/CurrencyField'

const DiscountCard = () => {
  const { control } = useFormContext<CreateSalesInvoicePayload>()

  const discountTypeWatch = useWatch({ control, name: 'discountType' })

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Diskon Faktur</Typography>
        </div>
        <div className='flex flex-col md:flex-row justify-between gap-4'>
          <Controller
            control={control}
            name='discountType'
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <FormControl fullWidth className='is-full'>
                <InputLabel id='discount-type'>Tipe Diskon</InputLabel>
                <Select
                  value={value}
                  onChange={onChange}
                  labelId='discount-type'
                  id='discount-type'
                  label='Tipe Diskon'
                  fullWidth
                >
                  {discountTypeOptions.map(disc => (
                    <MenuItem key={disc.value} value={disc.value}>
                      {disc.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
          <Controller
            control={control}
            name='discountValue'
            rules={{
              validate: value => {
                if (isNullOrUndefined(value) && !!discountTypeWatch) {
                  return 'Silahkan isi diskon'
                }
                return true
              }
            }}
            render={({ field, fieldState: { error } }) => (
              <TextField
                {...field}
                fullWidth
                label='Jumlah Diskon'
                InputProps={{
                  inputComponent: discountTypeWatch !== 'PERCENTAGE' ? (CurrencyField as any) : (NumberField as any),
                  endAdornment: discountTypeWatch === 'PERCENTAGE' && '%',
                  inputProps: {
                    isAllowed: ({ floatValue }) =>
                      floatValue <= (discountTypeWatch === 'PERCENTAGE' ? 100 : Number.MAX_SAFE_INTEGER) ||
                      isNullOrUndefined(floatValue)
                  }
                }}
                {...(!!error && { error: true })}
              />
            )}
          />
        </div>
        <div className='flex flex-col p-4 bg-[#DBF7E8] rounded-[8px]'>
          <small>Diskon Faktur</small>
          <Typography color='primary'>{toCurrency(0)}</Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default DiscountCard
