// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { Button, Chip } from '@mui/material'
import { ThemeColor } from '@/core/types'
import { MrStatus, MrUserStatus } from '@/types/mrTypes'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useUpdateApprovalPaymentStatus, useUpdateCashReceiptStatus } from '@/api/services/cashbank/mutation'
import { useSellingInvoice } from '../../invoice/context/SellingInvoiceContext'
import { useUpdateSellingInvoiceApprovalStatus } from '@/api/services/selling-invoice/mutation'

type StatusChipType = {
  label: string
  color: ThemeColor
}

// Vars
export const statusChipValue: { [key: string]: StatusChipType } = {
  WAITING: { label: 'Menunggu', color: 'secondary' },
  PENDING: { label: 'Menunggu', color: 'secondary' },
  APPROVED: { label: 'Disetujui', color: 'success' },
  REJECTED: { label: 'Ditolak', color: 'error' },
  PROCESSED: { label: 'Diproses', color: 'warning' }
}

const ApprovalsCard = () => {
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { salesInvoiceData, fetchSalesInvoiceData, fetchSalesInvoiceList } = useSellingInvoice()
  const { mutate: updateStatusMutate } = useUpdateSellingInvoiceApprovalStatus()

  const approvalList = salesInvoiceData?.approvals ?? []

  let ownApprovalIndex = -1
  const ownApproval = approvalList?.find((approval, index) => {
    ownApprovalIndex = index
    return approval.userId === userProfile?.id
  })

  const handleApprove = (id: number) => {
    setConfirmState({
      open: true,
      title: 'Setujui Faktur Penjualan',
      content: 'Apakah kamu yakin akan menyetujui Faktur Penjualan ini? Action ini tidak bisa diubah ',
      confirmText: 'Setujui Penerimaan',
      onConfirm: () => {
        updateStatusMutate(
          {
            id: salesInvoiceData?.id,
            approvalId: id,
            status: MrUserStatus.APPROVED
          },
          {
            onSuccess: () => {
              toast.success('Faktur Penjualan berhasil disetujui')
              fetchSalesInvoiceData()
              fetchSalesInvoiceList()
            }
          }
        )
      }
    })
  }

  const handleReject = (id: number) => {
    setConfirmState({
      open: true,
      title: 'Tolak Faktur Penjualan',
      content: 'Apakah kamu yakin akan menolak Faktur Penjualan ini? Action ini tidak bisa diubah ',
      confirmText: 'Tolak',
      onConfirm: () => {
        updateStatusMutate(
          {
            id: salesInvoiceData?.id,
            approvalId: id,
            status: MrUserStatus.REJECTED
          },
          {
            onSuccess: () => {
              toast.success('Faktur Penjualan berhasil ditolak')
              fetchSalesInvoiceData()
              fetchSalesInvoiceList()
            }
          }
        )
      },
      confirmColor: 'error'
    })
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Pengajuan Persetujuan</Typography>
          </div>
          <div className='flex flex-col gap-4'>
            {approvalList.map((approval, index) => {
              const statusValue = statusChipValue[approval.status]
              return (
                <div
                  key={approval.id}
                  className='rounded-lg border border-[#4c4e64]/22 p-4 flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'
                >
                  <div className='flex justify-between items-start self-stretch relative w-full bg-transparent'>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {approval.user?.fullName}
                    </p>
                    {ownApproval?.status === MrUserStatus.WAITING &&
                    approval.status === MrUserStatus.WAITING &&
                    salesInvoiceData?.status !== MrStatus.CANCELED ? (
                      <div className='flex gap-2 items-center self-center'>
                        <Button
                          variant='contained'
                          size='small'
                          color='error'
                          onClick={() => handleReject(approval.id)}
                        >
                          Tolak
                        </Button>
                        <Button variant='contained' size='small' onClick={() => handleApprove(approval.id)}>
                          Setujui
                        </Button>
                      </div>
                    ) : (
                      <Chip label={statusValue?.label} color={statusValue?.color} variant='tonal' size='small' />
                    )}
                  </div>
                  <div className='flex justify-between items-start w-full'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>{approval.user?.title}</small>
                    </label>
                    {approval.respondedAt ? (
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>
                          {formatDate(approval.respondedAt, 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
                        </small>
                      </label>
                    ) : null}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </>
  )
}

export default ApprovalsCard
