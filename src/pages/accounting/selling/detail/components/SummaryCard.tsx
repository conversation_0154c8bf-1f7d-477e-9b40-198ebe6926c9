import { toCurrency } from '@/utils/helper'
import { Card, CardContent, Typography } from '@mui/material'
import { useFormContext, useWatch } from 'react-hook-form'
import { CreateSalesInvoicePayload } from '../../invoice/config/types'
import { useSellingInvoice } from '../../invoice/context/SellingInvoiceContext'

const SummaryCard = () => {
  const { salesInvoiceData } = useSellingInvoice()
  const { control } = useFormContext<CreateSalesInvoicePayload>()

  const [items, otherExpense, discountType, discountValue] = useWatch({
    control,
    name: ['items', 'otherExpenses', 'discountType', 'discountValue'],
    defaultValue: {}
  })

  const subTotalAmount = items?.reduce((acc, item) => acc + item?.totalAmount, 0) ?? 0
  const otherAmount = otherExpense?.reduce((acc, item) => acc + item?.amount, 0) ?? 0
  const discountAmount =
    discountType === 'PERCENTAGE' ? ((subTotalAmount + otherAmount) * discountValue) / 100 : discountValue ?? 0

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Ringkasan Pembayaran</Typography>
        </div>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
          <div className='flex flex-col p-4 bg-[#4C4E640D] rounded-[8px]'>
            <small>Sub Total Faktur</small>
            <Typography color='primary'>{toCurrency(salesInvoiceData?.subTotalAmount)}</Typography>
          </div>
          <div className='flex flex-col p-4 bg-[#4C4E640D] rounded-[8px]'>
            <small>Biaya Lain-lain</small>
            <Typography color='primary'>{toCurrency(salesInvoiceData?.otherAmount)}</Typography>
          </div>
          <div className='flex flex-col p-4 bg-[#4C4E640D] rounded-[8px]'>
            <small>Diskon Faktur</small>
            <Typography color='error'>{toCurrency(salesInvoiceData?.discountAmount)}</Typography>
          </div>
          <div className='flex flex-col p-4 bg-[#DBF7E8] rounded-[8px]'>
            <small>Total Faktur</small>
            <Typography color='primary'>{toCurrency(salesInvoiceData?.totalAmount)}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default SummaryCard
