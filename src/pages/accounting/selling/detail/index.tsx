import { useRouter } from '@/routes/hooks'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Chip, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import DetailInvoiceCard from './components/DetailInvoiceCard'
import DocumentInvoiceCard from './components/DocumentInvoiceCard'
import ItemListCard from './components/ItemListCard'
import AdditionalExpenses from './components/AdditionalExpenseCard'
import DiscountCard from './components/DiscountCard'
import SummaryCard from './components/SummaryCard'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { FormProvider, useForm } from 'react-hook-form'
import { CreateSalesInvoicePayload, SalesInvoiceStatus } from '../invoice/config/types'
import { zodResolver } from '@hookform/resolvers/zod'
import z from 'zod'
import { useCreateSellingInvoice, useReadSellingInvoice } from '@/api/services/selling-invoice/mutation'
import { toast } from 'react-toastify'
import { useSellingInvoice } from '../invoice/context/SellingInvoiceContext'
import { useAuth } from '@/contexts/AuthContext'
import { useQuery } from '@tanstack/react-query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { statusConfig } from '../invoice/config/table'
import CreatedByCard from './components/CreatedByCard'
import ActivityLogCard from './components/ActivityLogCard'
import { useEffect } from 'react'
import ApprovalsCard from './components/ApprovalsCard'

const SalesInvoiceDetil = () => {
  const router = useRouter()
  const { fetchSalesInvoiceList, salesInvoiceData, fetchSalesInvoiceData, salesInvoiceLogs, isApproval } =
    useSellingInvoice()
  const { setConfirmState } = useMenu()
  const {
    userProfile: { departmentId, sites, id: userId }
  } = useAuth()

  const method = useForm<CreateSalesInvoicePayload>()
  const { handleSubmit } = method

  const { mutate: createSellingInvoice } = useCreateSellingInvoice()
  const { mutate: readMutate } = useReadSellingInvoice()

  const { data: approverList } = useQuery({
    enabled: !!sites?.[0]?.id && !!departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, DefaultApprovalScope.SalesInvoice, sites?.[0]?.id, departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope: DefaultApprovalScope.PurchaseInvoice,
        siteId: sites?.[0]?.id,
        departmentId: departmentId
      }),
    placeholderData: []
  })

  const onSubmitInvoice = (dto: CreateSalesInvoicePayload) => {
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Buat Faktur Pembelian',
      content:
        'Apakah kamu yakin akan membuat faktur pembelian ini? Pastikan semua data sudah benar, action ini tidak bisa diubah',
      confirmText: 'Buat Faktur Pembelian',
      onConfirm: () => {
        createSellingInvoice(
          {
            ...dto,
            items: dto.items.map(item => ({
              itemId: item.itemId,
              quantity: item.quantity,
              quantityUnit: item.quantityUnit,
              largeUnitQuantity: item.largeUnitQuantity,
              pricePerUnit: item.pricePerUnit,
              taxType: item.taxType,
              taxPercentage: item.taxPercentage,
              discountType: item.discountType,
              discountValue: item.discountValue,
              isDiscountAfterTax: item.isDiscountAfterTax
            })),
            approvals: approverList.map(approver => ({
              userId: approver.user?.id
            })),
            departmentId: departmentId
          },
          {
            onSuccess: () => {
              toast.success('Faktur pembelian berhasil dibuat')
              fetchSalesInvoiceList()
              router.push('/selling/invoice')
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    const ownApproval = salesInvoiceData?.approvals?.find(approval => approval.userId === userId)
    if (ownApproval && ownApproval.isRead === false && isApproval) {
      readMutate(
        {
          isRead: true,
          approvalId: ownApproval.id,
          id: salesInvoiceData?.id
        },
        { onSuccess: () => fetchSalesInvoiceData() }
      )
    }
  }, [salesInvoiceData])

  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Link to='#' replace>
              <Typography color='var(--color-text-disabled)'>Penjualan</Typography>
            </Link>
            <Link to='/selling/invoice' replace>
              <Typography color='var(--color-text-disabled)'>Faktur</Typography>
            </Link>
            <Typography>Detil Faktur</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2'>
                <Typography variant='h4'>No. Faktur {salesInvoiceData?.number}</Typography>
                <Chip
                  size='small'
                  variant='tonal'
                  label={statusConfig(salesInvoiceData?.status as SalesInvoiceStatus).label}
                  color={statusConfig(salesInvoiceData?.status as SalesInvoiceStatus).color as any}
                />
              </div>
              <Typography>
                {salesInvoiceData?.createdAt
                  ? formatDate(salesInvoiceData?.createdAt, 'eeee, dd/MM/yyyy', { locale: id })
                  : '-'}
              </Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              {/* <Button color='error' onClick={() => router.back()} variant='outlined'>
                Hapus Faktur
              </Button> */}
              {!isApproval && (
                <Button
                  disabled={salesInvoiceData?.status !== 'APPROVED'}
                  onClick={() => router.push(`/selling/receipts/create?invoiceId=${salesInvoiceData?.id}`)}
                  variant='contained'
                >
                  Buat Penerimaan
                </Button>
              )}
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <DetailInvoiceCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <DocumentInvoiceCard />
            </Grid>
            {isApproval && (
              <Grid item xs={12}>
                <ApprovalsCard />
              </Grid>
            )}
          </Grid>
        </Grid>
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
        <Grid item xs={12}>
          <AdditionalExpenses />
        </Grid>
        <Grid item xs={12}>
          <SummaryCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <CreatedByCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <ActivityLogCard logList={salesInvoiceLogs?.items ?? []} />
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default SalesInvoiceDetil
