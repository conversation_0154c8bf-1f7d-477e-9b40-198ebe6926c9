import { AccountType } from '@/types/accountTypes'
import { ItemType } from '@/types/companyTypes'
import { toCurrency } from '@/utils/helper'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { CreateSalesInvoiceItem, CreateSalesOtherExpense, SalesInvoice } from '../../invoice/config/types'

type ItemWithQuantity = ItemType & CreateSalesInvoiceItem

type AccountWithMemo = CreateSalesOtherExpense & AccountType

const columnHelper = createColumnHelper<SalesInvoice['items'][0]>()
const addColumnHelper = createColumnHelper<AccountWithMemo>()

type RowAction = {
  delete: (id: string) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('item.number', {
    header: '<PERSON><PERSON>'
  }),
  columnHelper.accessor('item.name', {
    header: '<PERSON><PERSON>'
  }),
  columnHelper.accessor('quantity', {
    header: 'Kuantitas',
    cell: ({ row }) => `${row.original.quantity} ${row.original.quantityUnit}`
  }),
  columnHelper.accessor('pricePerUnit', {
    header: 'Harga Satuan',
    cell: ({ row }) => toCurrency(row.original.pricePerUnit)
  }),
  columnHelper.accessor('subTotalDiscount', {
    header: 'Diskon',
    cell: ({ row }) => <Typography color='error'>{toCurrency(row.original.subTotalDiscount)}</Typography>
  }),
  columnHelper.accessor('totalAmount', {
    header: 'Total Harga',
    cell: ({ row }) => <Typography color='primary'>{toCurrency(row.original.totalAmount)}</Typography>
  })
]

export const additionalTableColumns = (rowAction: RowAction) => [
  addColumnHelper.accessor('accountId', {
    header: 'Akun Perkiraan',
    cell: ({ row }) => `[${row.original.code}] ${row.original.name}`
  }),
  addColumnHelper.accessor('amount', {
    header: 'Nominal',
    cell: ({ row }) => <Typography>{toCurrency(row.original.amount)}</Typography>
  }),
  addColumnHelper.accessor('note', {
    header: 'Memo',
    cell: ({ row }) => <Typography>{row.original?.note ?? '-'}</Typography>
  }),
  addColumnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.delete(row.original.id)}>
          <i className='ri-delete-bin-line' />
        </IconButton>
      )
    }
  })
]
