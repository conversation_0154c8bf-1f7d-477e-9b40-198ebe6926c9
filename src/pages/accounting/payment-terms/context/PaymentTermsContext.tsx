import { defaultListData } from '@/api/queryClient'
import { useDeletePaymentTerms } from '@/api/services/account/mutation'
import AccountsQueryMethods, { PAYMENT_TERMS_LIST_QUERY_KEY } from '@/api/services/account/query'
import AddPaymentTermsDialog from '@/components/dialogs/add-payment-terms-dialog'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { PaymentTermsType } from '@/types/accountTypes'
import { QueryFn } from '@/types/alias'
import { ListResponse } from '@/types/api'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import React, { useState } from 'react'
import { toast } from 'react-toastify'

type PaymentTermsContextType = {
  isMobile: boolean
  paymentTermsParams: ListParams
  setPartialPaymentTermsParams: (fieldName: keyof ListParams, value: any) => void
  setPaymentTermsParams: React.Dispatch<React.SetStateAction<ListParams>>
  handleAddClick: () => void
  handleDetail: (data: PaymentTermsType) => void
  handleCloseDialog: () => void
  handleDeleteRow: (id: string) => void
  handleDetailEdit: () => void
  paymentTermsListResponse: ListResponse<PaymentTermsType>
  fetchPaymentTermsList: QueryFn<ListResponse<PaymentTermsType>>
}

const PaymentTermsContext = React.createContext<PaymentTermsContextType>({} as PaymentTermsContextType)

export const usePaymentTerms = () => {
  const context = React.useContext(PaymentTermsContext)
  if (context === undefined) {
    throw new Error('useTax must be used within a PayrollProvider')
  }
  return context
}

export const PaymentTermsProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { setConfirmState } = useMenu()
  const [{ state, readonly, paymentTerms: paymentTerms }, setPaymentTermsState] = useState<{
    state: boolean
    readonly?: boolean
    paymentTerms?: PaymentTermsType
  }>({ state: false })
  const [paymentTermsParams, setPartialPaymentTermsParams, setPaymentTermsParams] = usePartialState<ListParams>({
    page: 1,
    limit: 10
  })

  const { mutate: removePaymentTermsMutate } = useDeletePaymentTerms()

  const { data: paymentTermsListResponse, refetch: fetchPaymentTermsList } = useQuery({
    queryKey: [PAYMENT_TERMS_LIST_QUERY_KEY, JSON.stringify(paymentTermsParams)],
    queryFn: () => AccountsQueryMethods.getPaymentTermsList(paymentTermsParams),
    placeholderData: defaultListData as ListResponse<PaymentTermsType>
  })

  const handleAddClick = () => {
    setPaymentTermsState(prev => ({ ...prev, state: true }))
  }

  const handleDetail = (data: PaymentTermsType) => {
    setPaymentTermsState(prev => ({ ...prev, paymentTerms: data, state: true, readonly: true }))
  }

  const handleDetailEdit = () => {
    setPaymentTermsState(prev => ({ ...prev, readonly: false }))
  }

  const handleCloseDialog = () => {
    setPaymentTermsState(prev => ({ ...prev, state: false, paymentTerms: undefined, readonly: undefined }))
  }

  const handleDeleteRow = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Syarat Pembayaran',
      content: 'Apakah kamu yakin ingin menghapus Syarat Pembayaran ini? Action ini tidak dapat diubah',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        removePaymentTermsMutate(id, {
          onSuccess: () => {
            setPaymentTermsState(prev => ({ ...prev, state: false, paymentTerms: undefined, readonly: undefined }))
            toast.success('Syarat Pembayaran berhasil dihapus')
            fetchPaymentTermsList()
          }
        })
      }
    })
  }

  const value = {
    isMobile,
    paymentTermsParams,
    setPaymentTermsParams,
    setPartialPaymentTermsParams,
    handleAddClick,
    handleDetail,
    handleCloseDialog,
    handleDeleteRow,
    handleDetailEdit,
    paymentTermsListResponse,
    fetchPaymentTermsList
  }

  return (
    <PaymentTermsContext.Provider value={value}>
      {state && (
        <AddPaymentTermsDialog
          open={state}
          readonly={readonly}
          setOpen={bool =>
            setPaymentTermsState(prev => ({
              ...prev,
              state: bool,
              ...(!bool && { paymentTerms: undefined, readonly: undefined })
            }))
          }
          paymentTerm={paymentTerms}
        />
      )}
      {children}
    </PaymentTermsContext.Provider>
  )
}
