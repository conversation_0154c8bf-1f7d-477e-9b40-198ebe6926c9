import { PaymentTermsType } from '@/types/accountTypes'
import { Button, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<PaymentTermsType>()

type RowAction = {
  detail: (row: PaymentTermsType) => void
  onDefault: (row: PaymentTermsType) => void
  loading?: boolean
}

export const tableColumns = (rowActions: RowAction) => [
  columnHelper.accessor('name', {
    header: 'Nama'
  }),
  columnHelper.accessor('discountDays', {
    header: '<PERSON><PERSON>',
    cell: ({ row }) => row.original?.dueDays + ' Hari'
  }),
  columnHelper.accessor('discountPercent', {
    header: 'Dapat Diskon',
    cell: ({ row }) => `${row.original?.discountPercent}%`
  }),
  columnHelper.accessor('dueDays', {
    header: 'Jatuh Tempo',
    cell: ({ row }) => row.original?.dueDays + ' Hari'
  }),
  columnHelper.accessor('isDefault', {
    header: 'Default',
    cell: ({ row }) =>
      row.original?.isDefault ? (
        <Typography color='primary'>Ya</Typography>
      ) : (
        <Typography color='error'>Tidak</Typography>
      )
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex justify-center gap-2'>
        <Button
          size='small'
          variant='outlined'
          color='primary'
          disabled={row.original.isDefault || rowActions?.loading}
          onClick={() => rowActions?.onDefault(row.original)}
        >
          {row.original.isDefault ? 'Default' : 'Jadikan Default'}
        </Button>
        <IconButton onClick={() => rowActions.detail(row.original)} disabled={rowActions?.loading}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    )
  })
]
