import { defaultListData } from '@/api/queryClient'
import { useDeleteAccount } from '@/api/services/account/mutation'
import TaxQueryMethods, { TAX_LIST_QUERY_KEY, TAX_TYPE_QUERY_KEY } from '@/api/services/tax/query'
import AddTaxesDialog from '@/components/dialogs/add-tax-dialog'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { AccountMasterType } from '@/types/accountTypes'
import { ListResponse } from '@/types/api'
import { TaxType, TaxTypeParams } from '@/types/taxTypes'
import { QueryObserverResult, useQuery } from '@tanstack/react-query'
import React, { useState } from 'react'
import { toast } from 'react-toastify'

type TaxContextType = {
  isMobile: boolean
  taxParams: TaxTypeParams
  setTaxParams: React.Dispatch<React.SetStateAction<TaxTypeParams>>
  setPartialTaxParams: (fieldName: keyof TaxTypeParams, value: any) => void
  handleAddClick: () => void
  handleDetail: (data: TaxType) => void
  handleCloseDialog: () => void
  handleDeleteRow: (id: string) => void
  handleDetailEdit: () => void
  taxListResponse: ListResponse<TaxType>
  fetchTaxList: () => Promise<QueryObserverResult<ListResponse<TaxType>, unknown>>
  taxTypeList: AccountMasterType[]
}

const AccountsContext = React.createContext<TaxContextType>({} as TaxContextType)

export const useTax = () => {
  const context = React.useContext(AccountsContext)
  if (context === undefined) {
    throw new Error('useTax must be used within a PayrollProvider')
  }
  return context
}

export const TaxProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { setConfirmState } = useMenu()
  const [{ state, readonly, tax }, setTaxState] = useState<{
    state: boolean
    readonly?: boolean
    tax?: TaxType
  }>({ state: false })
  const [taxParams, setPartialTaxParams, setTaxParams] = usePartialState<TaxTypeParams>({
    page: 1,
    limit: 10
  })

  const { mutate: removeAccountMutate } = useDeleteAccount()

  const { data: taxListResponse, refetch: fetchTaxList } = useQuery({
    queryKey: [TAX_LIST_QUERY_KEY, JSON.stringify(taxParams)],
    queryFn: () => TaxQueryMethods.getTaxList(taxParams),
    placeholderData: defaultListData as ListResponse<TaxType>
  })

  const { data: taxTypeList } = useQuery({
    queryKey: [TAX_TYPE_QUERY_KEY],
    queryFn: async () => (await TaxQueryMethods.getTaxTypeList({ page: 1, limit: Number.MAX_SAFE_INTEGER })).items,
    placeholderData: []
  })

  const handleAddClick = () => {
    setTaxState(prev => ({ ...prev, state: true }))
  }

  const handleDetail = (data: TaxType) => {
    setTaxState(prev => ({ ...prev, tax: data, state: true, readonly: true }))
  }

  const handleDetailEdit = () => {
    setTaxState(prev => ({ ...prev, readonly: false }))
  }

  const handleCloseDialog = () => {
    setTaxState(prev => ({ ...prev, state: false, tax: undefined, readonly: undefined }))
  }

  const handleDeleteRow = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Akun',
      content: 'Apakah kamu yakin ingin menghapus Akun ini? Action ini tidak dapat diubah',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        removeAccountMutate(id, {
          onSuccess: () => {
            toast.success('Akun berhasil dihapus')
            fetchTaxList()
          }
        })
      }
    })
  }

  const value = {
    isMobile,
    taxParams,
    setTaxParams,
    setPartialTaxParams,
    handleAddClick,
    handleDetail,
    handleCloseDialog,
    handleDeleteRow,
    handleDetailEdit,
    taxTypeList,
    taxListResponse,
    fetchTaxList
  }

  return (
    <AccountsContext.Provider value={value}>
      <AddTaxesDialog
        open={state}
        readonly={readonly}
        setOpen={bool =>
          setTaxState(prev => ({ ...prev, state: bool, ...(!bool && { tax: undefined, readonly: undefined }) }))
        }
        taxData={tax}
      />
      {children}
    </AccountsContext.Provider>
  )
}
