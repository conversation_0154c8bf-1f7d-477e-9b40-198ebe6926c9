import { TaxType } from '@/types/taxTypes'
import { Grid, IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<TaxType>()

type RowAction = {
  detail: (row: TaxType) => void
}

export const tableColumns = (rowActions: RowAction) => [
  columnHelper.accessor('taxType.name', {
    header: 'Tipe <PERSON>'
  }),
  columnHelper.accessor('name', {
    header: 'Keterangan'
  }),
  columnHelper.accessor('percentage', {
    header: 'Persentase',
    cell: ({ row }) => `${row.original?.percentage}%`
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <Grid item>
          <IconButton onClick={() => rowActions.detail(row.original)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
