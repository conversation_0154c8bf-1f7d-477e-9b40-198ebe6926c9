// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import TimelineDot from '@mui/lab/TimelineDot'
import TimelineItem from '@mui/lab/TimelineItem'
import TimelineContent from '@mui/lab/TimelineContent'
import TimelineSeparator from '@mui/lab/TimelineSeparator'
import TimelineConnector from '@mui/lab/TimelineConnector'
import Typography from '@mui/material/Typography'

import { Timeline } from '@/components/Timeline'
import { ServiceRequisitionLog, ServiceRequisitionLogStatus } from '@/types/serviceRequisitionsTypes'
import { Avatar } from '@mui/material'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'

const EmptyState: React.FC = () => {
  return (
    <div className='flex flex-col items-center py-12 mt-4 w-full text-center max-md:max-w-full'>
      <h3 className='text-xl font-medium leading-relaxed text-gray-600 text-opacity-90'>Belum ada Log Aktivitas</h3>
      <p className='text-sm leading-5 text-gray-600 text-opacity-60 max-md:max-w-full'>
        Semua aktivitas akan tercatat di sini
      </p>
    </div>
  )
}

type Props = {
  logList?: ServiceRequisitionLog[]
}

const ActivityLogCard = ({ logList = [] }: Props) => {
  return (
    <Card>
      <CardHeader title='Log Aktivitas' />
      <CardContent>
        {logList.length > 0 ? (
          <Timeline>
            {logList?.map(log => {
              let dotColor: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'inherit' | 'grey' =
                'primary'
              let title = ''
              const changes = ((JSON.parse(log.changes) as string[]) ?? []).map(change =>
                change.replaceAll('"', '').replaceAll('{changed}', '→').replaceAll('{added}', '')
              )
              switch (log.status) {
                case ServiceRequisitionLogStatus.CANCELED:
                  title = 'Service Request Dibatalkan'
                  dotColor = 'error'
                  break
                case ServiceRequisitionLogStatus.CREATED:
                  title = 'Service Request Dibuat'
                  break
                case ServiceRequisitionLogStatus.ITEM_UPDATED:
                  title = 'Detil Barang di-edit'
                  break
                case ServiceRequisitionLogStatus.UPDATED:
                  title = 'Detil Service Request di-edit'
                  break
                case ServiceRequisitionLogStatus.CLOSED:
                  title = 'Service Request ditutup'
                  dotColor = 'error'
                  break
                case ServiceRequisitionLogStatus.APPROVAL_UPDATED:
                  title = 'Penerima Pengajuan diganti'
                  break
                case ServiceRequisitionLogStatus.APPROVAL_APPROVED:
                  title = 'Service Request Disetujui'
                  break
                case ServiceRequisitionLogStatus.APPROVAL_REJECTED:
                  title = 'Service Request Ditolak'
                  dotColor = 'error'
                  break
                default:
                  break
              }
              return title ? (
                <TimelineItem key={log.id} className='pt-2'>
                  <TimelineSeparator>
                    <TimelineDot color={dotColor} />
                    <TimelineConnector />
                  </TimelineSeparator>
                  <TimelineContent>
                    <div className='flex flex-wrap items-center justify-between gap-x-2 mbe-1'>
                      <Typography color='text.primary' className='font-medium text-base'>
                        {title}
                      </Typography>
                      <Typography variant='caption'>
                        {formatDistanceToNow(new Date(log.createdAt), {
                          locale: id,
                          addSuffix: true
                        })
                          .replace('sekitar ', '')
                          .replace('kurang dari ', '')}
                      </Typography>
                    </div>
                    {changes.map(change => (
                      <Typography key={change} className='mbe-2 text-sm'>
                        {change}
                      </Typography>
                    ))}
                    <div className='flex items-center gap-3'>
                      <Avatar />
                      <div className='flex flex-col'>
                        <Typography color='text.primary' className='font-medium'>
                          {log.user?.fullName}
                        </Typography>
                        <Typography variant='body2'>{log.user?.title}</Typography>
                      </div>
                    </div>
                  </TimelineContent>
                </TimelineItem>
              ) : null
            })}
          </Timeline>
        ) : (
          <EmptyState />
        )}
      </CardContent>
    </Card>
  )
}

export default ActivityLogCard
