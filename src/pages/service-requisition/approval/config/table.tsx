import { StatusChipColorType } from '@/types/appTypes'
import { Chip, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { srPriorityOptions, srStatusOptions } from '../../list/config/utils'
import { statusChipValue } from '../../approval-detail/components/ApprovalsCard'
import { ServiceRequisition, ServiceRequisitionStatus } from '@/types/serviceRequisitionsTypes'
import { DEFAULT_DEPARTMENT } from '@/data/default/department'

export const statusChipColor: { [key: string]: StatusChipColorType } = {
  [ServiceRequisitionStatus.PROCESSED]: { color: 'warning' },
  [ServiceRequisitionStatus.APPROVED]: { color: 'success' },
  [ServiceRequisitionStatus.CANCELED]: { color: 'error' },
  [ServiceRequisitionStatus.REJECTED]: { color: 'error' },
  [ServiceRequisitionStatus.CLOSED]: { color: 'info' }
}

type SrTypeWithAction = ServiceRequisition & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
}

// Column Definitions
const columnHelper = createColumnHelper<SrTypeWithAction>()

export const tableColumns = (rowAction: RowActionType, userId?: string) => [
  columnHelper.accessor('number', {
    header: 'No. SR',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => {
      const docStatus = row.original.status
      const ownApproval = row.original.approvals?.find(approval => approval.userId === userId)
      return docStatus !== ServiceRequisitionStatus.CANCELED ? (
        <Chip
          label={statusChipValue[ownApproval?.status]?.label}
          color={statusChipValue[ownApproval?.status]?.color}
          variant='tonal'
          size='small'
        />
      ) : (
        <Chip label='Dibatalkan' color='error' variant='tonal' size='small' />
      )
    }
  }),
  columnHelper.accessor('site', {
    header: 'Workshop',
    cell: ({ row }) => <Typography>{row.original.site?.name}</Typography>
  }),
  columnHelper.accessor('department.name', {
    header: 'Departemen',
    cell: ({ row }) => <Typography>{row.original.department?.name ?? DEFAULT_DEPARTMENT.name}</Typography>
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = srPriorityOptions.find(option => option.value === String(row.original.priority))
      return (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority?.color}`} />
          <Typography>{priority?.label}</Typography>
        </div>
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('createdByUser.fullName', {
    header: 'Dibuat Oleh',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.createdByUser?.fullName}</Typography>
        <Typography variant='caption'>{row.original.createdByUser?.title}</Typography>
      </div>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]
