import { ImageItemType, ItemType, VendorType } from '@/types/companyTypes'
import {
  Autocomplete,
  Button,
  Card,
  CardContent,
  CircularProgress,
  debounce,
  FormControl,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useFilePicker } from 'use-file-picker'
import { requestTypeOptions, ServiceRequestType } from '../config/utils'
import { useEffect, useState } from 'react'
import { defaultImageList } from '@/components/dialogs/add-item-dialog'
import PhotoPicker from '@/components/PhotoPicker'
import { useAuth } from '@/contexts/AuthContext'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { SrRmOPayload } from '@/types/srTypes'
import { useUploadDocument } from '@/api/services/file/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { VENDOR_LIST_QUERY_KEY } from '@/api/services/company/query'
import { DEFAULT_CATEGORY } from '@/data/default/category'

const ServiceRequestCard = () => {
  const {
    userProfile: { sites }
  } = useAuth()
  let fetchItemsLoading = false
  const { openFilePicker, filesContent, clear } = useFilePicker({
    multiple: false,
    accept: ['.pdf', '.docx', '.doc', 'xls', 'xlsx', 'image/*'],
    readAs: 'DataURL'
  })

  const [vendorSearchQuery, setVendorSearchQuery] = useState<string>('')
  const [imageList, setImageList] = useState<ImageItemType[]>(defaultImageList)
  const [selectedVendor, setSelectedVendor] = useState<VendorType | null>(null)

  const { control, reset, getValues } = useFormContext<SrRmOPayload>()

  const watchType = useWatch({ control, name: 'type' })

  const { mutateAsync: uploadMutate, isLoading: uploading } = useUploadDocument()

  const {
    data: vendorList,
    remove: removeVendorList,
    isFetching: fetchVendorLoading
  } = useQuery({
    enabled: !!vendorSearchQuery,
    queryKey: [VENDOR_LIST_QUERY_KEY, vendorSearchQuery],
    queryFn: async () => {
      const res = await CompanyQueryMethods.getVendorList({ limit: Number.MAX_SAFE_INTEGER, search: vendorSearchQuery })
      return res.items
    },
    placeholderData: [] as VendorType[]
  })

  useEffect(() => {
    if (filesContent?.[0]?.name) {
      uploadMutate({
        fieldName: 'documentUploadId',
        file: filesContent[0].content,
        fileName: filesContent[0].name,
        scope: 'public-document'
      }).then(res => {
        if (res?.data?.id) {
          reset({
            ...getValues(),
            documentUploadId: res.data.id
          })
        }
      })
    }
  }, [filesContent])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Permintaan Jasa</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='type'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel>Tipe Request</InputLabel>
                  <Select
                    label='Tipe Request'
                    value={value}
                    placeholder='Pilih Tipe Request'
                    className='bg-white'
                    error={Boolean(error)}
                    onChange={({ target }) => onChange(target.value)}
                  >
                    {requestTypeOptions?.map((type, idx) => (
                      <MenuItem key={idx} value={type.value}>
                        <ListItemText>{type.label}</ListItemText>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          {watchType === 'VENDOR' && (
            <Grid item xs={12}>
              <Controller
                control={control}
                name='originSiteId'
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='role-select'>Kirim Dari</InputLabel>
                    <Select
                      key={value}
                      fullWidth
                      id='select-siteId'
                      value={value}
                      onChange={e => onChange(e.target.value)}
                      label='Kirim Dari'
                      size='medium'
                      labelId='siteId-select'
                      inputProps={{ placeholder: 'Pilih Site' }}
                      defaultValue=''
                      error={!!error}
                    >
                      {sites?.map(site => (
                        <MenuItem key={site.id} value={site.id}>
                          {site.name} Warehouse
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
          )}
          {watchType !== null && watchType !== 'INTERNAL' && (
            <>
              <Grid item xs={12}>
                <Typography>Detil Vendor</Typography>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    control={control}
                    name='vendorId'
                    render={({ field: { value, onChange }, fieldState: { error } }) => (
                      <Autocomplete
                        filterOptions={x => x}
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        onInputChange={debounce((e, newValue, reason) => {
                          if (reason === 'input') {
                            setVendorSearchQuery(newValue)
                          }
                        }, 700)}
                        options={vendorList ?? []}
                        freeSolo={!vendorSearchQuery}
                        fullWidth
                        value={selectedVendor}
                        onChange={(e, newValue: VendorType) => {
                          setSelectedVendor(newValue)
                          onChange(newValue.id)
                          removeVendorList()
                        }}
                        noOptionsText='Vendor tidak ditemukan'
                        loading={fetchVendorLoading}
                        renderInput={params => (
                          <TextField
                            {...params}
                            label=''
                            placeholder='Cari kode atau nama vendor'
                            variant='outlined'
                            fullWidth
                            InputProps={{
                              ...params.InputProps,
                              startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                              endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                              onKeyDown: e => {
                                if (e.key === 'Enter') {
                                  e.stopPropagation()
                                }
                              }
                            }}
                            error={!!error}
                          />
                        )}
                        getOptionLabel={(option: VendorType) => `${option.code} | ${option.name}`}
                        renderOption={(props, option) => {
                          const { key, ...optionProps } = props
                          return (
                            <li key={key} {...optionProps}>
                              <Typography>
                                {option.code} | {option.name}
                              </Typography>
                            </li>
                          )
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField disabled label='Kode Vendor' value={selectedVendor?.code ?? '-'} />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  disabled
                  label='Kategori Vendor'
                  value={selectedVendor?.category?.name ?? DEFAULT_CATEGORY.name}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField disabled fullWidth label='Nama Vendor' value={selectedVendor?.name ?? '--'} />
              </Grid>
            </>
          )}
          <Grid item xs={12}>
            <Typography>Dokumen Permintaan Jasa</Typography>
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='documentNumber'
              render={({ field, fieldState: { error } }) => (
                <TextField {...field} ref={field.ref} error={!!error} fullWidth label='Nomor Dokumen' />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2 flex-1'>
              <div className='flex items-center gap-4'>
                <Controller
                  control={control}
                  name='documentUploadId'
                  render={({ fieldState: { error } }) => (
                    <>
                      <TextField
                        key={JSON.stringify(filesContent)}
                        size='small'
                        label='Pilih Dokumen'
                        fullWidth
                        value={filesContent?.[0]?.name}
                        placeholder='Belum ada file dipilih'
                        aria-readonly
                        className='flex-1'
                        error={!!error}
                      />
                      <LoadingButton
                        loading={uploading}
                        startIcon={<></>}
                        variant='contained'
                        onClick={() => openFilePicker()}
                      >
                        Pilih
                      </LoadingButton>
                    </>
                  )}
                />
              </div>
            </div>
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='documentNote'
              render={({ field, fieldState: { error } }) => (
                <TextField fullWidth {...field} multiline rows={3} label='Catatan' error={!!error} />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <InputLabel>Foto Barang (opsional)</InputLabel>
          </Grid>
          <Grid item xs={12}>
            <div className='flex gap-5 overflow-y-hidden max-sm:px-2'>
              {imageList?.map((item, index) => (
                <PhotoPicker
                  key={`${item.content}_${index}`}
                  content={item.content}
                  // disabled={isLoading}
                  onPicked={(content, fileName) => {
                    setImageList(current => {
                      const tempCurrent = [...current]
                      tempCurrent[index] = { content, fileName }
                      return tempCurrent
                    })
                  }}
                  onRemoved={() => {
                    setImageList(current => {
                      const tempCurrent = [...current]
                      tempCurrent[index] = { content: '', fileName: '' }
                      return tempCurrent
                    })
                  }}
                />
              ))}
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default ServiceRequestCard
