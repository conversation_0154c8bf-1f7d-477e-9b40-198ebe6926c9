import React from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm, FormProvider } from 'react-hook-form'
import * as z from 'zod'
import * as Sentry from '@sentry/react'

import { SrRmOPayload } from '@/types/srTypes'
import { useAddWoSr } from '@/api/services/rnm/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import { NavigateFunction, useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { WO_QUERY_DETAIL_KEY } from '@/api/services/rnm/service'
import RnMQueryMethods from '@/api/services/rnm/query'
import { WorkOrderType, WoSegmentType } from '@/types/woTypes'
import { useSr } from './SrContext'

type SrFormContextProps = {
  handleSubmitServiceRequest: (dto?: SrRmOPayload) => void
  addLoading: boolean
  navigate: NavigateFunction
  selectedWoId: string | undefined
  setSelectedWoId: React.Dispatch<React.SetStateAction<string | undefined>>
  woDetail: WorkOrderType
  activeSegment: WoSegmentType | null
  setActiveSegment: React.Dispatch<React.SetStateAction<WoSegmentType | null>>
  selectedWo: WorkOrderType | undefined
  setSelectedWo: React.Dispatch<React.SetStateAction<WorkOrderType | undefined>>
}

const SrFormContext = React.createContext<SrFormContextProps>({} as SrFormContextProps)

export const useSrForm = () => {
  const context = React.useContext(SrFormContext)
  if (context === undefined) {
    throw new Error('useSrForm must be used within a SrFormContextProvider')
  }
  return context
}

const CreateWoSrSchema = z
  .object({
    type: z.enum(['INTERNAL', 'EXTERNAL', 'VENDOR']),
    workOrderSegmentId: z.string(),
    items: z.array(
      z.object({
        itemId: z.string(),
        quantity: z.number(),
        quantityUnit: z.string(),
        largeUnitQuantity: z.number(),
        serialNumberId: z.number().optional().nullable(),
        note: z.string(),
        images: z.array(
          z.object({
            uploadId: z.string()
          })
        )
      })
    ),
    approvals: z.array(
      z.object({
        userId: z.string()
      })
    ),
    note: z.string(),
    priority: z.number(),
    documentNumber: z.string(),
    documentUploadId: z.string(),
    documentNote: z.string(),
    vendorId: z.string().optional().nullable(),
    originSiteId: z.string().optional().nullable(),
    siteId: z.string().optional().nullable()
  })
  .superRefine((values, ctx) => {
    if (values.type !== 'INTERNAL') {
      if (values.vendorId === undefined || values.vendorId === null || values.vendorId === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'vendorId harus diisi',
          path: ['vendorId', 'originSiteId']
        })
      }
    }
    if (values.type === 'VENDOR') {
      if (values.originSiteId === undefined || values.originSiteId === null || values.originSiteId === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'originSiteId harus diisi',
          path: ['originSiteId']
        })
      }
    }
  })

export const SrFormProvider = ({ children }: { children: React.ReactNode }) => {
  const { setConfirmState } = useMenu()
  const navigate = useNavigate()
  const { fetchSrList } = useSr()

  const [selectedWo, setSelectedWo] = React.useState<WorkOrderType | undefined>(undefined)
  const [selectedWoId, setSelectedWoId] = React.useState<string | undefined>(undefined)
  const [activeSegment, setActiveSegment] = React.useState<WoSegmentType | null>(null)

  const methods = useForm<SrRmOPayload>({
    defaultValues: {
      approvals: [],
      items: []
    },
    resolver: zodResolver(CreateWoSrSchema),
    mode: 'all'
  })
  const { mutate: createSrMutate, isLoading: addLoadingMutate } = useAddWoSr()

  const { data: woDetail } = useQuery({
    enabled: !!selectedWoId,
    queryKey: [WO_QUERY_DETAIL_KEY, selectedWoId],
    queryFn: () => RnMQueryMethods.getWoDetail(selectedWoId)
  })

  const handleSubmitServiceRequest = (dto?: SrRmOPayload) => {
    Sentry.captureMessage(`Submit Service Request: ${JSON.stringify(dto)}`)
    setConfirmState({
      open: true,
      title: 'Buat Service Request',
      content:
        'Apakah kamu yakin akan membuat Service Request ini? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Buat Service Request',
      onConfirm: () => {
        createSrMutate(dto, {
          onSuccess: () => {
            toast.success('Service Request berhasil dibuat')
            fetchSrList()
            setTimeout(() => {
              navigate(`/service-request/list`)
            }, 700)
          }
        })
      }
    })
  }

  const value = {
    addLoading: addLoadingMutate,
    handleSubmitServiceRequest,
    navigate,
    selectedWoId,
    setSelectedWoId,
    woDetail,
    selectedWo,
    setSelectedWo,
    activeSegment,
    setActiveSegment
  }

  return (
    <SrFormContext.Provider value={value}>
      <FormProvider {...methods}>{children}</FormProvider>
    </SrFormContext.Provider>
  )
}
