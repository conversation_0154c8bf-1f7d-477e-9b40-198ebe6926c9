// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import PhotoPicker from '@/components/PhotoPicker'
import { IconButton } from '@mui/material'
import { useDeleteSrItem, useUpdateSrItem } from '@/api/services/service-requisitions/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useUploadImage } from '@/api/services/file/mutation'
import { ServiceRequisitionItem } from '@/types/serviceRequisitionsTypes'
import { useAuth } from '@/contexts/AuthContext'
import { useSr } from '../../context/SrContext'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import { isNullOrUndefined } from '@/utils/helper'
import { tableColumns } from '../../detail/table'

const ItemListCard = () => {
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { srData, fetchSrData, fetchLogList, canRemove } = useSr()
  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: deleteItemMutate, isLoading: deleteItemLoading } = useDeleteSrItem()
  const { mutate: updateItemMutate, isLoading: updateItemLoading } = useUpdateSrItem()

  const [srItems, setSrItems] = useState<ServiceRequisitionItem[]>([])

  const isLoading = uploadLoading || deleteItemLoading || updateItemLoading

  const [{ open: addItemOpen, selectedItem }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as ServiceRequisitionItem
  })

  const table = useReactTable({
    data: srItems,
    columns: tableColumns({
      onView: itemData => {
        setAddItemModalState({ open: true, selectedItem: itemData })
      }
    }),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    setSrItems(srData?.items ?? [])
  }, [srData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Barang</Typography>
          </div>
          <div>
            <Table table={table} />
          </div>
        </CardContent>
      </Card>
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? ({} as ServiceRequisitionItem) : current.selectedItem
            }))
          }}
          // canUpdateAll={false}
          isLoading={isLoading}
          onSubmit={() => {}}
          currentItem={selectedItem}
          siteId={srData?.siteId}
          viewOnly
          withoutUnit
        />
      )}
    </>
  )
}

export default ItemListCard
