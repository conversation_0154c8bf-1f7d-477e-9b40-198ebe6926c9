import PhotoPicker from '@/components/PhotoPicker'
import { IconButton, Typography } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { PoPayload } from '../../config/types'

type Props = {
  index: number
  onEditClick: () => void
  onRemoveClick: () => void
}

function ItemCard({ index, onEditClick, onRemoveClick }: Props) {
  const { control } = useFormContext<PoPayload>()
  return (
    <Controller
      name={`items.${index}`}
      control={control}
      render={({
        field: {
          value: {
            item: { name, brandName, number },
            unit,
            images,
            quantity,
            quantityUnit,
            note,
            unitHm,
            unitKm
          }
        }
      }) => {
        const filledImages = images?.filter(image => image.content || image.url)
        return (
          <div className='flex flex-col gap-3 overflow-hidden flex-wrap items-start p-4 w-full rounded-lg bg-gray-600 bg-opacity-10 max-md:max-w-full'>
            <div className='flex gap-10 items-start justify-between flex-wrap w-full'>
              <div className='flex flex-col gap-2 text-base tracking-normal leading-none min-w-[240px] text-gray-600 text-opacity-60'>
                <div className='font-medium text-gray-600 text-opacity-90'>
                  {name} - {brandName} {number}
                </div>
                {unit ? (
                  <>
                    <div>
                      {/* {unit?.subCategory?.name} -  */}
                      {unit?.brandName} {unit?.type}
                    </div>
                    <div className='text-sm'>
                      {unit?.number} - No. Lambung {unit?.hullNumber}
                    </div>
                    <div className='text-sm'>
                      KM: {unitKm ?? 0} km | HM: {unitHm ?? 0} jam
                    </div>
                  </>
                ) : null}
                <div className='text-sm text-green-700'>
                  {quantity} {quantityUnit}
                </div>
              </div>
              <div className='flex items-center'>
                <IconButton size='small' onClick={() => onRemoveClick()}>
                  <i className='ic-baseline-delete-forever text-red-500' />
                </IconButton>
                <IconButton size='small' onClick={() => onEditClick()}>
                  <i className='ic-baseline-edit text-secondary' />
                </IconButton>
              </div>
            </div>
            {(filledImages?.length ?? 0) > 0 && (
              <>
                <div className='w-full h-[1px] bg-[#D9D9D9]' />
                <Typography className='text-xs leading-none text-gray-400'>Foto Barang</Typography>
                <div className='flex gap-5'>
                  {filledImages.map((image, index) => (
                    <PhotoPicker key={`${image.fileName}_${index}`} content={image.content || image.url} isPreview />
                  ))}
                </div>
              </>
            )}
            <div className='mt-1 text-xs text-gray-600 text-opacity-60'>Keterangan: {note ?? '-'}</div>
          </div>
        )
      }}
    />
  )
}

export default ItemCard
