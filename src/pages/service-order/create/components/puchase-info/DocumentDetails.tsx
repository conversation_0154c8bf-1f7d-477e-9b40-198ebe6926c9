import React from 'react'

import { addMonths, formatDate } from 'date-fns'
import { Button, Grid, Typography } from '@mui/material'
import { id } from 'date-fns/locale'
import { paymentMethodOptions } from '@/pages/purchase-order/config/options'
import { ServiceOrder } from '@/types/serviceOrderTypes'
import { fileNameFromUrl } from '@/utils/helper'

interface DocumentDetailsProps {
  soData: ServiceOrder
}

const DocumentDetails: React.FC<DocumentDetailsProps> = ({ soData: { documentNumber, documentUrl } }) => {
  return (
    <div className='flex overflow-hidden flex-col justify-center p-4 w-full text-base font-medium tracking-normal leading-none rounded-lg bg-gray-400 bg-opacity-10 text-gray-600 text-opacity-60 max-md:max-w-full'>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <div className='flex flex-col'>
            <Typography className='text-sm text-gray-400'>Nomor Do<PERSON>men</Typography>
            <Typography className='mt-1 text-base font-medium leading-none'>{documentNumber ?? '-'}</Typography>
          </div>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between w-full items-center'>
            <Typography className='text-opacity-90'>{fileNameFromUrl(documentUrl)}</Typography>
            <Button href={documentUrl} download={!!documentUrl} target='_blank' variant='outlined'>
              Unduh
            </Button>
          </div>
        </Grid>
      </Grid>
    </div>
  )
}

export default DocumentDetails
