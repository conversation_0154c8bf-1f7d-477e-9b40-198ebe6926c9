import React, { useEffect } from 'react'

import InfoSection from './InfoSection'
import PaymentDetails from './PaymentDetails'
import { PriceBreakdown } from './PriceBreakdown'
import { ServiceOrderPayload } from '@/types/serviceOrderTypes'

type Props = {
  purchaseData: ServiceOrderPayload
}

const PurchaseDataSection: React.FC<Props> = ({ purchaseData }: Props) => {
  const { estimateCompletedTime, paymentMethod, paymentDueDate } = purchaseData
  return (
    <section className='flex flex-col gap-3'>
      <PriceBreakdown purchaseData={purchaseData} />
      <PaymentDetails
        paymentMethod={paymentMethod}
        estimatedArrival={estimateCompletedTime}
        paymentDueDate={paymentDueDate}
      />
    </section>
  )
}

export default PurchaseDataSection
