import React from 'react'
import Separator from '@/components/Separator'
import { PurchaseItem } from './PurchaseItem'
import { TAX_PERCENTAGE } from '@/utils/constants'
import { toCurrency } from '@/utils/helper'
import { ServiceOrderDiscountType, ServiceOrderPayload } from '@/types/serviceOrderTypes'

interface PriceBreakdownProps {
  purchaseData: ServiceOrderPayload
}

export const PriceBreakdown: React.FC<PriceBreakdownProps> = ({ purchaseData }) => {
  const { shippingCost, items, discountType, discountValue } = purchaseData

  let subTotalItems = 0
  // let itemsTotalPrice = 0
  let totalPrice = 0
  let totalDiscount = 0
  let totalTax = 0
  items.forEach(item => {
    subTotalItems += item.totalPrice ?? 0
    totalDiscount += item.totalDiscount ?? 0
    totalTax += item.totalTax ?? 0
  })

  let discountAmount = 0
  if (discountValue) {
    if (discountType === ServiceOrderDiscountType.PERCENTAGE) {
      discountAmount = Math.round(subTotalItems * discountValue) / 100
    } else if (discountType === ServiceOrderDiscountType.FLAT) {
      discountAmount = discountValue
    }
  }

  totalPrice = subTotalItems - discountAmount
  totalDiscount += discountAmount

  return (
    <section className='flex overflow-hidden flex-col justify-center p-4 rounded-lg bg-gray-600 bg-opacity-10'>
      <div className='flex flex-col w-full max-md:max-w-full gap-2'>
        <div className='flex flex-col gap-5'>
          {items.map((item, index) => (
            <PurchaseItem key={index} itemData={item} />
          ))}
        </div>
        <Separator containerClassName='mt-1' />
        {/* <div className='flex flex-wrap justify-between items-start mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
          <div className='font-semibold text-gray-600 text-opacity-60'>Subtotal Jasa Setelah Diskon</div>
          <div className='text-gray-600 text-opacity-90'>{toCurrency(subTotalItems)}</div>
        </div> */}
        {discountAmount ? (
          <div className='flex flex-wrap justify-between items-start mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
            <div className='font-semibold text-gray-600 text-opacity-60'>Diskon Jasa</div>
            <div className='text-red-500'>- {toCurrency(discountAmount)}</div>
          </div>
        ) : null}
        <div className='flex flex-wrap justify-between items-start mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
          <div className='font-semibold text-gray-600 text-opacity-60'>Ongkos Kirim</div>
          <div className='text-gray-600 text-opacity-90'>{toCurrency(shippingCost)}</div>
        </div>
        <Separator containerClassName='mt-1' />
        <div className='flex flex-wrap justify-between items-start mt-2 w-full text-base font-bold tracking-normal leading-none max-md:max-w-full'>
          <div className='text-gray-600 text-opacity-90'>TOTAL PROCUREMENT</div>
          <div className='text-green-400'>{toCurrency(subTotalItems - discountAmount + (shippingCost ?? 0))}</div>
        </div>
      </div>
    </section>
  )
}
