import { ServiceOrderDiscountType, ServiceOrderItemPayload, ServiceOrderTaxType } from '@/types/serviceOrderTypes'
import { TAX_PERCENTAGE } from '@/utils/constants'
import { toCurrency } from '@/utils/helper'
import React from 'react'

interface PurchaseItemProps {
  itemData: ServiceOrderItemPayload
}

export const PurchaseItem: React.FC<PurchaseItemProps> = ({ itemData }) => {
  const {
    item: { name },
    serviceName,
    quantity,
    pricePerUnit,
    taxType,
    discountType,
    discountValue,
    isDiscountAfterTax
  } = itemData

  let subtotalPriceIncludeTax = 0
  let totalItemDiscount = 0
  let totalItemPrice = 0
  let taxAmount = 0
  if (pricePerUnit) {
    const subTotalPrice = pricePerUnit * quantity
    taxAmount = taxType === ServiceOrderTaxType.EXCLUDE_TAX ? Math.round(subTotalPrice * TAX_PERCENTAGE) / 100 : 0
    subtotalPriceIncludeTax = subTotalPrice + taxAmount

    if (discountType && discountValue) {
      // if (discountType === PurchaseOrderDiscountType.PERCENTAGE) {
      //   const amount = isDiscountAfterTax ? subtotalPriceIncludeTax : subTotalPrice
      //   totalItemDiscount = Math.round(amount * discountValue) / 100
      // } else if (discountType === PurchaseOrderDiscountType.FLAT) {
      //   totalItemDiscount = discountValue
      // }
      if (isDiscountAfterTax) {
        if (discountType === ServiceOrderDiscountType.PERCENTAGE) {
          totalItemDiscount = Math.round(subtotalPriceIncludeTax * discountValue) / 100
        } else if (discountType === ServiceOrderDiscountType.FLAT) totalItemDiscount = discountValue
      } else if (isDiscountAfterTax === false) {
        if (discountType === ServiceOrderDiscountType.PERCENTAGE)
          totalItemDiscount = Math.round(subTotalPrice * discountValue) / 100
        else if (discountType === ServiceOrderDiscountType.FLAT) totalItemDiscount = discountValue

        taxAmount = Math.round((subTotalPrice - totalItemDiscount) * TAX_PERCENTAGE) / 100
        subtotalPriceIncludeTax = subTotalPrice + taxAmount
      }
    }

    totalItemPrice = Math.round((subtotalPriceIncludeTax - totalItemDiscount) * 100) / 100
  }

  return (
    <div className='flex flex-col'>
      <div className='flex flex-wrap justify-between items-end w-full text-sm tracking-normal leading-none max-md:max-w-full'>
        <div className='flex flex-col text-gray-600 dark:text-inherit text-opacity-6 gap-1'>
          <div className='font-semibold'>{name}</div>
          <div className='mt-1'>
            {serviceName} {quantity}x {toCurrency(pricePerUnit)}
          </div>
        </div>
        <div className='text-gray-600 text-opacity-90'>{toCurrency((pricePerUnit ?? 0) * quantity)}</div>
      </div>
      {taxAmount && isDiscountAfterTax ? (
        <div className='flex flex-wrap justify-between mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
          <div className='font-semibold text-gray-600 dark:text-inherit text-opacity-60'>
            Pajak Jasa {TAX_PERCENTAGE}%
          </div>
          <div className='self-end text-gray-600 dark:text-inherit text-opacity-90'>{toCurrency(taxAmount)}</div>
        </div>
      ) : null}
      {/* {totalItemDiscount ? (
        <div className='flex flex-wrap justify-between items-start mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
          <div className='font-semibold text-gray-600 dark:text-inherit text-opacity-60'>
            Diskon Item ({isDiscountAfterTax ? 'Sesudah Pajak' : 'Sebelum Pajak'})
          </div>
          <div className='text-red-500'>- {toCurrency(totalItemDiscount)}</div>
        </div>
      ) : null} */}
      {taxAmount && !isDiscountAfterTax ? (
        <div className='flex flex-wrap justify-between mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
          <div className='font-semibold text-gray-600 dark:text-inherit text-opacity-60'>
            Pajak Jasa {TAX_PERCENTAGE}%
          </div>
          <div className='self-end text-gray-600 dark:text-inherit text-opacity-90'>{toCurrency(taxAmount)}</div>
        </div>
      ) : null}
    </div>
  )
}
