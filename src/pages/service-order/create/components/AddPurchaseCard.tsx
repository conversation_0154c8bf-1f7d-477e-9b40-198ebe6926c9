// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Button, IconButton, TextField, Grid } from '@mui/material'
import { useFormContext, useWatch, Controller } from 'react-hook-form'
import usePartialState from '@/core/hooks/usePartialState'
import PurchaseDataSection from './puchase-info/PurchaseDataSection'
import { useEffect } from 'react'
import { useSo } from '../../context/SoContext'
import { isNullOrUndefined } from '@/utils/helper'
import { ServiceOrderPayload } from '@/types/serviceOrderTypes'
import AddServicePurchaseDialog from '@/components/dialogs/add-service-purchase'
import { useFilePicker } from 'use-file-picker'
import { useUpdateEffect } from 'react-use'
import { useSrList } from '../../context/SrListContext'

const EmptyState: React.FC = () => {
  return (
    <div className='flex flex-col items-center py-12 mt-4 w-full text-center max-md:max-w-full'>
      <h3 className='text-xl font-medium leading-relaxed text-gray-600 text-opacity-90'>Belum ada data</h3>
      <p className='text-sm leading-5 text-gray-600 text-opacity-60 max-md:max-w-full'>
        Masukkan dan lengkapi data pendukung service order
      </p>
    </div>
  )
}

const AddPurchaseCard = () => {
  const { srData } = useSrList()
  const { showLoading } = useSo()
  const { control, reset, watch, getValues, setValue } = useFormContext<ServiceOrderPayload>()

  const { openFilePicker, filesContent, clear } = useFilePicker({
    multiple: false,
    accept: ['.pdf', '.docx', '.doc', 'xls', 'xlsx', 'image/*'],
    readAs: 'DataURL'
  })

  const itemsWatch = useWatch({
    control,
    name: `items`,
    defaultValue: []
  })

  const [{ open: addPurchaseDialogOpen, purchaseData }, setPartialState, setAddPurchaseState] = usePartialState<{
    open: boolean
    purchaseData?: ServiceOrderPayload
  }>({
    open: false,
    purchaseData: {
      items: []
    }
  })

  useEffect(() => {
    setPartialState('purchaseData', {
      ...getValues(),
      items: itemsWatch
    })
  }, [itemsWatch])

  useUpdateEffect(() => {
    if ((filesContent?.length ?? 0) > 0) {
      setValue('documentContent', filesContent[0]?.content)
      setValue('documentName', filesContent[0]?.name)
    }
  }, [filesContent])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Data Procurement</Typography>
            {!isNullOrUndefined(purchaseData.totalPrice) && (
              <div className='flex gap-1 items-center'>
                <Typography>Edit</Typography>
                <IconButton size='small' onClick={() => setPartialState('open', true)}>
                  <i className='ic-baseline-edit text-secondary' />
                </IconButton>
              </div>
            )}
          </div>

          {!isNullOrUndefined(purchaseData.totalPrice) ? (
            <PurchaseDataSection purchaseData={purchaseData} />
          ) : (
            <>
              <EmptyState />
              <Button
                disabled={showLoading}
                color='primary'
                variant='outlined'
                onClick={() => setPartialState('open', true)}
                className='flex overflow-hidden flex-col justify-center items-center mt-4 w-full text-base font-medium tracking-wide leading-7 text-green-400 rounded-lg border border-solid border-indigo-500 border-opacity-50 max-md:max-w-full'
              >
                Masukkan Data
              </Button>
            </>
          )}
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant='button'>Unggah Dokumen (Opsional)</Typography>
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='documentNumber'
                render={({ field, fieldState: { error } }) => (
                  <TextField {...field} label='Nomor Dokumen' size='small' fullWidth error={!!error} />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <div className='flex flex-col gap-2 flex-1'>
                <div className='flex items-center gap-4'>
                  <TextField
                    key={JSON.stringify(filesContent)}
                    size='small'
                    label='Pilih File'
                    fullWidth
                    value={filesContent?.[0]?.name}
                    placeholder='Belum ada file dipilih'
                    aria-readonly
                    className='flex-1'
                  />
                  <Button variant='contained' onClick={() => openFilePicker()} disabled={showLoading}>
                    Pilih
                  </Button>
                </div>
              </div>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
      {addPurchaseDialogOpen && (
        <AddServicePurchaseDialog
          open={addPurchaseDialogOpen}
          setOpen={isOpen => setPartialState('open', isOpen)}
          purchaseData={purchaseData}
          woSegment={srData?.workOrderSegment}
          onSubmit={submittedData => {
            setAddPurchaseState({
              open: false,
              purchaseData: submittedData
            })
            reset({
              ...getValues(),
              ...submittedData
            })
          }}
        />
      )}
    </>
  )
}

export default AddPurchaseCard
