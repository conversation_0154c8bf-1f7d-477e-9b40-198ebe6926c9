import { array, coerce, object, string, TypeOf } from 'zod'

export const soItemPayloadSchema = object({
  itemId: string(),
  serialNumberId: coerce.number().optional().nullable(),
  quantity: coerce.number(),
  quantityUnit: string(),
  largeUnitQuantity: coerce.number(),
  serviceName: string(),
  serviceDescription: string(),
  serviceFee: coerce.number(),
  taxType: string(),
  taxPercentage: coerce.number(),
  discountType: string(),
  discountValue: coerce.number().optional().nullable(),
  isDiscountAfterTax: coerce.boolean(),
  note: string().optional().nullable(),
  images: array(
    object({
      id: string().optional().nullable(),
      url: string(),
      name: string(),
      mime: string(),
      uploadId: string().optional().nullable(),
      fileName: string().optional().nullable(),
      content: string().optional().nullable()
    })
  )
})

export const soPayloadSchema = object({
  soId: string().optional().nullable(),
  serviceRequisitionId: string().optional().nullable(),
  vendorId: string(),
  documentNumber: string().optional().nullable(),
  documentUploadId: string().optional().nullable(),
  documentNote: string().optional().nullable(),
  documentName: string().optional().nullable(),
  documentContent: string().optional().nullable(),
  currency: string().optional().nullable(),
  exchangeRate: coerce.number().optional().nullable(),
  items: array(soItemPayloadSchema).min(1, { message: 'Barang wajib diisi' }),
  approvals: array(
    object({
      userId: string()
    })
  ),
  note: string().optional().nullable(),
  priority: string(),
  discountType: string().optional().nullable(),
  discountValue: coerce.number().optional().nullable(),
  shippingCost: coerce.number(),
  paymentMethod: string(),
  paymentDueDate: string(),
  estimateCompletedTime: string().optional().nullable(),
  totalTax: coerce.number().optional().nullable(),
  totalPrice: coerce.number().optional().nullable(),
  discountAmount: coerce.number().optional().nullable(),
  subTotalItems: coerce.number().optional().nullable(),
  totalDiscount: coerce.number().optional().nullable(),
  grandTotal: coerce.number().optional().nullable()
})

export type CreateSoInput = TypeOf<typeof soPayloadSchema>
