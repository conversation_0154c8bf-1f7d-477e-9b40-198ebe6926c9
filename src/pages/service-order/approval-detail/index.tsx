// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { B<PERSON>c<PERSON><PERSON>, Button, Chip, Typography } from '@mui/material'
import ApprovalsCard, { statusChipValue } from './components/ApprovalsCard'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useSo } from '../context/SoContext'
import OwnerCard from '@/pages/material-request/detail/components/OwnerCard'
import ActivityLogCard from '../detail/components/ActivityLogCard'
import PurchaseDetailCard from '../detail/components/PurchaseDetailCard'
import { fileNameFromUrl } from '@/utils/helper'
import { useReadSoApproval } from '@/api/services/service-order/mutation'
import { ServiceOrderStatus } from '@/types/serviceOrderTypes'
import AdditionalInfoCard from '../detail/components/AdditionalInfoCard'
import CancellationSection from '../detail/components/CancellationSection'
import SegmentCard from '../detail/components/SegmentCard'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import RequestDetailCard from '../detail/components/RequestDetailCard'

const SoApprovalDetailPage = () => {
  const { userProfile } = useAuth()
  const { soData, fetchSoList, logList, canView } = useSo()

  const { mutate: readMutate } = useReadSoApproval()

  const ownApproval = soData?.approvals?.find(approval => approval.userId === userProfile?.id)

  useEffect(() => {
    if (ownApproval && ownApproval.isRead === false) {
      readMutate(
        {
          isRead: true,
          soId: soData?.id,
          approvalId: ownApproval.id
        },
        { onSuccess: () => fetchSoList() }
      )
    }
  }, [soData])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Service Order</Typography>
          </Link>
          <Link to='/service-order/approval' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Persetujuan Service Order</Typography>
          </Link>
          <Typography>Detil Service Order</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>NO. SO: {soData?.number}</Typography>
              {ownApproval && (
                <>
                  {soData?.status !== ServiceOrderStatus.CANCELED ? (
                    <Chip
                      label={statusChipValue[ownApproval?.status]?.label}
                      color={statusChipValue[ownApproval?.status]?.color}
                      variant='tonal'
                      size='small'
                    />
                  ) : (
                    <Chip label='Dibatalkan' color='error' variant='tonal' size='small' />
                  )}
                </>
              )}
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              {formatDate(soData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
            </Typography>
          </div>
          <div className='flex gap-2 is-full sm:is-auto'>
            {/* <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
            >
              Ekspor
            </Button>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ic-outline-local-printshop' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button> */}
          </div>
        </div>
      </Grid>
      {soData?.items ? (
        <Grid item xs={12}>
          <SegmentCard />
        </Grid>
      ) : null}
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <DocNumberCard docType='SR' warehouseDoc={soData} />
          </Grid>
          <Grid item xs={12}>
            <RequestDetailCard />
          </Grid>
          <Grid item xs={12}>
            <AdditionalInfoCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          {soData?.cancelationType ? (
            <Grid item xs={12}>
              <CancellationSection
                cancellation={{
                  cancelationNote: soData?.cancelationNote,
                  cancelationType: soData?.cancelationType,
                  proofFile: soData?.cancelationProofUrl,
                  proofFileName: fileNameFromUrl(soData?.cancelationProofUrl ?? ''),
                  approvals: [],
                  approvers: soData?.approvals?.filter(approval => approval.isCancelation) ?? []
                }}
              />
            </Grid>
          ) : null}
          {soData && canView ? (
            <Grid item xs={12}>
              <PurchaseDetailCard rawSoData={soData} />
            </Grid>
          ) : null}
          {soData?.status !== ServiceOrderStatus.CANCELED &&
          (soData?.approvals?.filter(approval => !approval.isCancelation).length ?? 0) > 0 ? (
            <Grid item xs={12}>
              <ApprovalsCard />
            </Grid>
          ) : null}
          <Grid item xs={12}>
            <ActivityLogCard logList={logList} />
          </Grid>
          <Grid item xs={12}>
            <OwnerCard user={soData?.createdByUser} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default SoApprovalDetailPage
