import { useEffect, useState } from 'react'
import { Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useRouter } from '@/routes/hooks'
import { useSrList } from '../../context/SrListContext'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { useAuth } from '@/contexts/AuthContext'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { allElementsAreSame } from '@/utils/helper'
import { toast } from 'react-toastify'
import { srPriorityOptions } from '@/pages/service-requisition/list/config/utils'

const SrList = () => {
  const { setConfirmState } = useMenu()
  const router = useRouter()
  const { ownSiteList, departmentList } = useAuth()
  const {
    srListResponse: { items: srList, totalItems, totalPages, limit: limitItems, page: pageItems },
    srsParams,
    setSrsParams,
    setSelectedSrId,
    setPartialSrsParams,
    setSelectedSrList,
    clearSrData
  } = useSrList()

  const [rowSelection, setRowSelection] = useState({})

  const { page, search, siteIds, startDate, endDate, priority, departmentId } = srsParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const table = useReactTable({
    data: srList,
    columns: tableColumns({
      showDetail: id => {
        setSelectedSrId(id)
        router.push(`/service-order/sr-list/${id}`)
      }
    }),
    initialState: {
      pagination: {
        pageSize: srsParams.limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      rowSelection,
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    enableRowSelection: true,
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const selectedRows = table.getSelectedRowModel().flatRows

  const onCreateMultiSrSoClick = () => {
    if (
      allElementsAreSame(selectedRows.map(row => row.original.siteId)) &&
      allElementsAreSame(selectedRows.map(row => row.original.departmentId))
    ) {
      setConfirmState({
        open: true,
        title: 'Buat Service Order',
        content: 'Apakah kamu akan lanjut membuat Service Order dari beberapa Service Request yang dipilih?',
        confirmText: 'Buat Service Order',
        onConfirm: () => {
          setSelectedSrList(selectedRows.map(row => row.original))
          setTimeout(() => {
            router.push('/service-order/sr-list/multi-sr')
          }, 200)
        }
      })
    } else {
      toast.error('Tidak bisa membuat Service Order dari Service Request yang berbeda site/departemen')
    }
  }

  const onFilterChanged = ({ date, priority, site, department }: FilterValues) => {
    setSrsParams(prev => {
      return {
        ...prev,
        startDate: date[0],
        endDate: date[1],
        priority: priority.length > 0 ? priority[0] : undefined,
        siteIds: site.length > 0 ? site[0] : undefined,
        departmentId: department.length > 0 ? department[0] : undefined
      }
    })
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      priority: {
        options: srPriorityOptions,
        values: priority ? [priority] : []
      },
      site: {
        options: ownSiteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: siteIds ? [siteIds] : []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: departmentId ? [departmentId] : []
      }
    })
  }, [srsParams])

  useEffect(() => {
    setSrsParams({
      limit: 10,
      page: 1
    })
    clearSrData()
  }, [])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setPartialSrsParams('search', value)}
            placeholder='Cari Service Request'
            className='is-full sm:is-auto'
          />
          <FilterGroupDialog config={filterGroupConfig} onFilterApplied={onFilterChanged} />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {selectedRows.length > 1 && (
            <Button variant='contained' className='is-full sm:is-auto' onClick={onCreateMultiSrSoClick}>
              Buat Service Order
            </Button>
          )}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Service Request</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua Service Request yang sudah disetujui dan siap dijadikan Service Order ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setSrsParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialSrsParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setSrsParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialSrsParams('page', pageIndex)}
      />
    </Card>
  )
}

export default SrList
