import React from 'react'
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ontent, Chip, Typography } from '@mui/material'

// Type Imports
import { ApproverType } from '@/types/userTypes'
import { CancellationType } from '@/types/apps/ledgerTypes'
import { cancelationTypeOptions } from '../../config/options'

// Component Imports
import ApprovalsCard, { statusChipValue } from '../../approval-detail/components/ApprovalsCard'

interface InfoSectionProps {
  title: string
  content: string
}

const ApproverSection = ({ approver }: { approver: ApproverType }) => {
  const statusValue = statusChipValue[approver.status]

  return (
    <div className='flex flex-col p-4 mt-4 w-full rounded-lg border border-solid border-gray-600 border-opacity-20 max-md:max-w-full'>
      <div className='flex flex-wrap gap-10 justify-between items-start w-full whitespace-nowrap max-md:max-w-full'>
        <div className='pt-0.5 pb-1 text-base tracking-normal rounded-xl text-gray-600 text-opacity-90'>
          {approver?.user?.fullName}
        </div>
        <div className='flex items-center px-1 py-1 text-sm tracking-normal leading-none rounded-2xl bg-[linear-gradient(0deg,rgba(255,255,255,0.88_0%,rgba(255,255,255,0.88)_100%),#6D788D)] text-slate-500'>
          <Chip label={statusValue?.label} color={statusValue?.color} variant='tonal' size='small' />
        </div>
      </div>
      <div className='gap-2.5 self-start mt-1 text-xs tracking-normal leading-none rounded-xl text-gray-600 text-opacity-60'>
        {approver?.user?.title}
      </div>
    </div>
  )
}

const ProofSection = ({ cancellation }: { cancellation: CancellationType }) => {
  return (
    <div className='flex overflow-hidden flex-col justify-center p-4 mt-4 w-full whitespace-nowrap rounded-lg bg-gray-600 bg-opacity-10 max-md:max-w-full'>
      <div className='flex justify-between items-start w-full max-md:max-w-full'>
        <div className='flex flex-col flex-1 shrink w-full basis-0 min-w-[240px] max-md:max-w-full'>
          <div className='text-sm tracking-normal leading-none text-gray-600 dark:text-inherit text-opacity-60'>
            Bukti
          </div>
          <div className='flex flex-wrap gap-10 justify-between items-center mt-1 w-full max-md:max-w-full'>
            <div className='self-stretch my-auto text-base tracking-normal text-gray-600 dark:text-inherit text-opacity-90 max-sm:whitespace-normal max-sm:truncate'>
              {cancellation.proofFileName}
            </div>
            <Button variant='contained' target='_blank' href={cancellation.proofFile} download>
              Unduh
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

const InfoSection: React.FC<InfoSectionProps> = ({ title, content }) => {
  return (
    <div className='flex overflow-hidden flex-col p-4 mt-4 w-full tracking-normal rounded-lg bg-gray-600 bg-opacity-10 max-md:max-w-full'>
      <div className='flex justify-between items-start w-full max-md:max-w-full'>
        <div className='flex flex-col flex-1 shrink w-full basis-0 min-w-[240px] max-md:max-w-full'>
          <div className='text-sm leading-none text-gray-600 dark:text-inherit text-opacity-60'>{title}</div>
          <div className='mt-1 text-base leading-none text-gray-600 dark:text-inherit text-opacity-90'>{content}</div>
        </div>
      </div>
    </div>
  )
}

interface CancellationSectionProps {
  cancellation: CancellationType
}

const CancellationSection: React.FC<CancellationSectionProps> = ({ cancellation }) => {
  const { cancelationNote, cancelationType, approvers } = cancellation

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Pengajuan Pembatalan Service Order</Typography>
          </div>
          <section className='flex flex-col'>
            <InfoSection
              title='Alasan Pembatalan'
              content={cancelationTypeOptions.find(option => option.value === cancelationType)?.label ?? ''}
            />
            <InfoSection title='Catatan' content={cancelationNote ?? ''} />
            <ProofSection cancellation={cancellation} />
          </section>
        </CardContent>
      </Card>
      {(approvers?.length ?? 0) > 0 && (
        <div className='mt-6'>
          <ApprovalsCard isCancelation />
        </div>
      )}
    </>
  )
}

export default CancellationSection
