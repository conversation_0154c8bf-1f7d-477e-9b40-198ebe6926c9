import React from 'react'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import InfoSection from '../../create/components/puchase-info/InfoSection'
import PaymentDetails from '../../create/components/puchase-info/PaymentDetails'
import CompanyQueryMethods, { VENDOR_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { PriceBreakdown } from './PriceBreakdown'
import { Button } from '@mui/material'
import usePartialState from '@/core/hooks/usePartialState'
import { toast } from 'react-toastify'
import { TAX_PERCENTAGE } from '@/utils/constants'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useUploadDocument } from '@/api/services/file/mutation'
import { format } from 'date-fns'
import { useSo } from '../../context/SoContext'
import { ServiceOrder, ServiceOrderPayload } from '@/types/serviceOrderTypes'
import AddServicePurchaseDialog from '@/components/dialogs/add-service-purchase'
import { useUpdateSo } from '@/api/services/service-order/mutation'
import DocumentDetails from '../../create/components/puchase-info/DocumentDetails'

type Props = {
  rawSoData: ServiceOrder
}

const PurchaseDetailCard: React.FC<Props> = ({ rawSoData }) => {
  const { fetchSoData, fetchSoList, fetchLogList } = useSo()
  const { setConfirmState } = useMenu()
  const { estimateCompletedTime, paymentMethod, paymentDueDate, documentUrl, isEditable } = rawSoData

  const soData = {
    ...rawSoData,
    documentContent: documentUrl,
    documentName: documentUrl?.split('/').pop(),
    documentMimeType: documentUrl?.split('.').pop(),
    items: rawSoData?.items?.map(item => ({
      ...item,
      pricePerUnit: item.serviceFee
    }))
  } as ServiceOrder

  const [{ open: addPurchaseDialogOpen, purchaseData }, setPartialState, setAddPurchaseState] = usePartialState<{
    open: boolean
    purchaseData?: ServiceOrderPayload
  }>({
    open: false,
    purchaseData: {
      items: []
    }
  })

  const { mutateAsync: uploadDocumentMutate } = useUploadDocument()
  const { mutate: updateSoMutate } = useUpdateSo()

  const onUpdateSubmit = ({
    items,
    discountValue,
    documentContent,
    documentName,
    ...formInput
  }: ServiceOrderPayload) => {
    setConfirmState({
      open: true,
      title: 'Ubah Data Service Order',
      content:
        'Apakah kamu yakin akan mengubah data Service Order ini? Pastikan semua detil yang kamu masukkan untuk Service Order ini sudah benar',
      confirmText: 'Ubah Service Order',
      onConfirm: () => {
        if (documentContent) {
          uploadDocumentMutate({
            fieldName: `document_${format(new Date(), 'yyyyMMddHHmmss')}`,
            file: documentContent,
            scope: 'public-document',
            fileName: documentName
          })
            .then(uploadResponse => {
              updateSoMutate(
                {
                  ...formInput,
                  soId: soData?.id,
                  items: items
                    .filter(val => !!val.itemId)
                    .map(val => ({
                      id: val.id,
                      itemId: val.itemId,
                      originalItemId: val.originalItemId,
                      quantity: val.quantity,
                      quantityUnit: val.quantityUnit,
                      largeUnitQuantity: val.largeUnitQuantity,
                      serviceFee: val.serviceFee,
                      taxType: val.taxType,
                      taxPercentage: TAX_PERCENTAGE,
                      discountType: val.discountType,
                      isDiscountAfterTax: val.isDiscountAfterTax,
                      note: val.note,
                      images: val.images,
                      ...(val.discountValue && { discountValue: val.discountValue })
                    })),
                  ...(discountValue && { discountValue }),
                  documentUploadId: uploadResponse?.data?.id
                },
                {
                  onSuccess: () => {
                    fetchSoData()
                    fetchSoList()
                    fetchLogList()
                    toast.success('Service Order berhasil diubah')
                    setAddPurchaseState({
                      open: false,
                      purchaseData: {
                        items: []
                      }
                    })
                  }
                }
              )
            })
            .catch(error => {
              const message = error.response?.data?.message
              if (message) {
                toast.error(message)
              } else {
                toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
              }
            })
        } else {
          updateSoMutate(
            {
              ...formInput,
              soId: soData?.id,
              items: items
                .filter(val => !!val.itemId)
                .map(val => ({
                  id: val.id,
                  itemId: val.itemId,
                  originalItemId: val.originalItemId,
                  quantity: val.quantity,
                  quantityUnit: val.quantityUnit,
                  largeUnitQuantity: val.largeUnitQuantity,
                  serviceFee: val.serviceFee,
                  taxType: val.taxType,
                  taxPercentage: TAX_PERCENTAGE,
                  discountType: val.discountType,
                  isDiscountAfterTax: val.isDiscountAfterTax,
                  note: val.note,
                  images: val.images,
                  ...(val.discountValue && { discountValue: val.discountValue })
                })),
              ...(discountValue && { discountValue })
            },
            {
              onSuccess: () => {
                fetchSoData()
                fetchSoList()
                fetchLogList()
                toast.success('Service Order berhasil diubah')
                setAddPurchaseState({
                  open: false,
                  purchaseData: {
                    items: []
                  }
                })
              }
            }
          )
        }
      }
    })
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Data Procurement</Typography>
            {isEditable && (
              <Button
                variant='outlined'
                onClick={() => {
                  setAddPurchaseState({
                    open: true,
                    purchaseData: soData as ServiceOrderPayload
                  })
                }}
              >
                Ubah
              </Button>
            )}
          </div>

          <section className='flex flex-col gap-3'>
            <PriceBreakdown soData={soData} />
            <PaymentDetails
              paymentMethod={paymentMethod}
              estimatedArrival={estimateCompletedTime}
              paymentDueDate={paymentDueDate}
            />
            <DocumentDetails soData={soData} />
          </section>
        </CardContent>
      </Card>
      {addPurchaseDialogOpen && (
        <AddServicePurchaseDialog
          open={addPurchaseDialogOpen}
          setOpen={isOpen => setPartialState('open', isOpen)}
          purchaseData={purchaseData}
          woSegment={soData?.workOrderSegment}
          onSubmit={submittedData => {
            onUpdateSubmit(submittedData)
          }}
        />
      )}
    </>
  )
}

export default PurchaseDetailCard
