import { Card, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'
import { useMemo, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import Table from '@/components/table'
import { WoSegmentType } from '@/types/woTypes'
import RnMQueryMethods from '@/api/services/rnm/query'
import DialogAddSegmentWo from '@/components/dialogs/add-segment-wo'
import { FormProvider, useForm } from 'react-hook-form'
import { SegmentType } from '@/pages/repair-and-maintenance/wo/create-wo/config/schema'
import { useSrList } from '../../context/SrListContext'
import { useSo } from '../../context/SoContext'
import { workOrderSegmentColumns } from '../../sr-detail/components/config/table'

interface SegmentCardProps {
  woId?: string
}

const SegmentCard = () => {
  const { soData } = useSo()
  const [dialogOpen, setDialogOpen] = useState(false)

  const methods = useForm<SegmentType>()

  const handleShowDetail = () => {
    setDialogOpen(true)
    methods.reset({
      jobCodeId: soData?.workOrderSegment?.jobCodeId,
      componentCodeId: soData?.workOrderSegment?.componentCodeId,
      modifierCodeId: soData?.workOrderSegment?.modifierCodeId
    })
  }

  const tableOptions = useMemo(
    () => ({
      data: soData?.workOrderSegment ? [soData?.workOrderSegment] : [],
      columns: workOrderSegmentColumns({
        showDetail: () => handleShowDetail()
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [soData]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Segment</Typography>
          </div>
          <div className='shadow-xs rounded-[8px]'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Segment</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Segment dari Work Order akan ditampilkan di sini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>

      {dialogOpen && (
        <FormProvider {...methods}>
          <DialogAddSegmentWo
            viewOnly
            open={dialogOpen}
            setOpen={setDialogOpen}
            onOpenDialogItem={() => {}}
            onOpenDialogMisc={() => {}}
            configOptions={{
              segment: { ...soData?.workOrderSegment, workOrderId: soData?.workOrderId },
              componentCodeList: [],
              jobCodeList: [],
              modifierCodeList: [],
              fetchJobCodeLoading: false,
              fetchComponentCodeLoading: false,
              fetchModifierCodeLoading: false,
              jobCodeQuery: '',
              setJobCodeQuery: () => {},
              componentCodeQuery: '',
              setComponentCodeQuery: () => {},
              modifierCodeQuery: '',
              setModifierCodeQuery: () => {}
            }}
          />
        </FormProvider>
      )}
    </>
  )
}

export default SegmentCard
