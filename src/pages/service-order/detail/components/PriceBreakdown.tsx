import React from 'react'
import Separator from '@/components/Separator'
import { toCurrency } from '@/utils/helper'
import { ServiceOrder } from '@/types/serviceOrderTypes'
import { PurchaseItem } from '../../create/components/puchase-info/PurchaseItem'

interface PriceBreakdownProps {
  soData: ServiceOrder
}

export const PriceBreakdown: React.FC<PriceBreakdownProps> = ({ soData }) => {
  const { totalServiceFee, items, discountAmount, grandTotal } = soData

  return (
    <section className='flex overflow-hidden flex-col justify-center p-4 rounded-lg bg-gray-600 bg-opacity-10'>
      <div className='flex flex-col w-full max-md:max-w-full gap-2'>
        {items.map((item, index) => (
          <PurchaseItem key={index} itemData={item} />
        ))}
        <Separator containerClassName='mt-1' />
        {discountAmount ? (
          <div className='flex flex-wrap justify-between items-start mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
            <div className='font-semibold text-gray-600 dark:text-inherit text-opacity-60'>Diskon Jasa</div>
            <div className='text-red-500'>- {toCurrency(discountAmount)}</div>
          </div>
        ) : null}
        <div className='flex flex-wrap justify-between items-start mt-2 w-full text-sm tracking-normal leading-none max-md:max-w-full'>
          <div className='font-semibold text-gray-600 dark:text-inherit text-opacity-60'>Ongkos Kirim</div>
          <div className='text-gray-600 dark:text-inherit text-opacity-90'>{toCurrency(totalServiceFee)}</div>
        </div>
        <Separator containerClassName='mt-1' />
        <div className='flex flex-wrap justify-between items-start mt-2 w-full text-base font-bold tracking-normal leading-none max-md:max-w-full'>
          <div className='text-gray-600 dark:text-inherit text-opacity-90'>TOTAL PROCUREMENT</div>
          <div className='text-green-400'>{toCurrency(grandTotal)}</div>
        </div>
      </div>
    </section>
  )
}
