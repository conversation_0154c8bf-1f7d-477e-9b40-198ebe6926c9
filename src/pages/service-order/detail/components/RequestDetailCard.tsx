// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { requestTypeOptions } from '@/pages/repair-and-maintenance/wo/create-wo-sr/config/utils'
import { fileNameFromUrl } from '@/utils/helper'
import { Button, Grid } from '@mui/material'
import Separator from '@/components/Separator'
import { useSo } from '../../context/SoContext'
import { VendorType } from '@/types/companyTypes'

type Props = {
  vendorData?: VendorType
}

const RequestDetailCard = ({ vendorData }: Props) => {
  const { soData } = useSo()
  const requestType = requestTypeOptions.find(option => option.value === soData?.type)
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Per<PERSON><PERSON>an Jasa</Typography>
        </div>
        <div className='flex flex-col gap-6'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Tipe Request
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {requestType?.label ?? '-'}
            </p>
          </div>
          {soData?.vendor && (
            <>
              <div className='flex flex-col gap-2'>
                <Separator containerClassName='mt-0' />
                <p className='tracking-[0.2px] leading-6 text-base font-medium text-[#4c4e64]/[87%] dark:text-inherit'>
                  Detil Vendor
                </p>
              </div>
              <Grid container spacing={4}>
                <Grid item xs={6}>
                  <div className='flex flex-col gap-1'>
                    <Typography variant='subtitle2'>Kode Vendor</Typography>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {soData?.vendor?.code ?? '-'}
                    </p>
                  </div>
                </Grid>
                <Grid item xs={12}>
                  <div className='flex flex-col gap-1'>
                    <Typography variant='subtitle2'>Kontak Vendor</Typography>
                    {vendorData?.addresses?.map(addr => (
                      <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                        {addr.phoneNumber} / {vendorData.email}
                      </p>
                    ))}
                  </div>
                </Grid>
                <Grid item xs={6}>
                  <div className='flex flex-col gap-1'>
                    <Typography variant='subtitle2'>Nama Vendor</Typography>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {soData?.vendor?.name}
                    </p>
                  </div>
                </Grid>
              </Grid>
            </>
          )}

          <div className='flex flex-col gap-2'>
            <Separator containerClassName='mt-0' />
            <p className='tracking-[0.2px] leading-6 text-base font-medium text-[#4c4e64]/[87%] dark:text-inherit'>
              Dokumen Permintaan Jasa
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Nomor Dokumen
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {soData?.documentNumber ?? '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>Dokumen</small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <div className='flex justify-between w-full items-center'>
              <Typography className='text-opacity-90'>{fileNameFromUrl(soData?.documentUrl)}</Typography>
              <Button href={soData?.documentUrl} download={!!soData?.documentUrl} target='_blank' variant='outlined'>
                Unduh
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default RequestDetailCard
