export enum PurchaseOrderStatus {
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED',
  CANCEL_REQUESTED = 'CANCEL_REQUESTED',
  CLOSED = 'CLOSED'
}

export enum PurchaseOrderQuantityUnit {
  PCS = 'PCS',
  BOX = 'BOX',
  GALON = 'GALON',
  LITER = 'LITER',
  DRUM = 'DRUM',
  METER = 'METER',
  CENTIMETER = 'CENTIMETER',
  MILIMETER = 'MILIMETER'
}

export enum PurchaseOrderApprovalStatus {
  PENDING = 'PENDING',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export enum PurchaseOrderLogType {
  ACTIVITY = 'ACTIVITY',
  ITEM_FLOW = 'ITEM_FLOW'
}

export enum PurchaseOrderLogStatus {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCEL_REQUESTED = 'CANCEL_REQUESTED',
  CANCEL_APPROVED = 'CANCEL_APPROVED',
  CANCEL_REJECTED = 'CANCEL_REJECTED',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
  ITEM_UPDATED = 'ITEM_UPDATED',
  APPROVAL_APPROVED = 'APPROVAL_APPROVED',
  APPROVAL_REJECTED = 'APPROVAL_REJECTED',
  APPROVAL_UPDATED = 'APPROVAL_UPDATED',
  ITEM_RECEIVED = 'ITEM_RECEIVED'
}

export enum PurchaseOrderCancelationType {
  ITEM_UNAVAILABLE = 'ITEM_UNAVAILABLE'
}

export enum PurchaseOrderTaxType {
  NON_TAX = 'NON_TAX',
  INCLUDE_TAX = 'INCLUDE_TAX',
  EXCLUDE_TAX = 'EXCLUDE_TAX'
}

export enum PurchaseOrderDiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FLAT = 'FLAT'
}

export enum PurchaseOrderPaymentMethod {
  TERM = 'TERM',
  CASH = 'CASH'
}
