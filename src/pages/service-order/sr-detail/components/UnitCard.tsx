// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports

import { Grid } from '@mui/material'
import { useSrList } from '../../context/SrListContext'
import { DEFAULT_CATEGORY } from '@/data/default/category'

const UnitCard = () => {
  const { srData } = useSrList()
  const unit = srData?.unit
  const unitKm = srData?.unitKm ?? 0
  const unitHm = srData?.unitHm ?? 0
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Unit</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Kode Unit</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.number ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Kode Activa</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.asset?.code ?? '-'}
              </Typography>
            </div>
          </Grid>
          {!!unit?.category && (
            <Grid item xs={6} md={4}>
              <div className='flex flex-col gap-1'>
                <Typography variant='subtitle2'>Kategori Unit</Typography>
                <Typography variant='subtitle1' className='text-textPrimary'>
                  {unit?.category?.name ?? DEFAULT_CATEGORY.name}
                </Typography>
              </div>
            </Grid>
          )}
          {!!unit?.subCategory && (
            <Grid item xs={6} md={4}>
              <div className='flex flex-col gap-1'>
                <Typography variant='subtitle2'>Jenis Unit</Typography>
                <Typography variant='subtitle1' className='text-textPrimary'>
                  {unit?.subCategory?.name ?? '-'}
                </Typography>
              </div>
            </Grid>
          )}
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Type Equipment</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.equipmentType ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Merk Unit</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.brandName ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Tipe Unit</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.type ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Nomor Rangka</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.hullNumber ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Nomor Mesin</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.engineNumber ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Plat Nomor</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.plateNumber ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>KM</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unitKm} km
              </Typography>
            </div>
          </Grid>
          <Grid item xs={6} md={4}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>HM</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unitHm} jam
              </Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default UnitCard
