import { ColumnDef } from '@tanstack/react-table'
import { ServiceOrder, ServiceOrderStatus } from '@/types/serviceOrderTypes'
import { ServiceRequisition } from '@/types/serviceRequisitionsTypes'
import { Button, Chip, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { WoSegmentType } from '@/types/woTypes'
import truncateString from '@/core/utils/truncate'

type TableActions = {
  showDetail: (id: string, isCancelation: boolean) => void
}

export const tableColumns = ({ showDetail }: TableActions, srData?: ServiceRequisition): ColumnDef<ServiceOrder>[] => [
  {
    header: 'No. SO',
    accessorKey: 'number',
    cell: ({ row }) => (
      <Button variant='text' onClick={() => showDetail(row.original.id, false)}>
        {row.original.number}
      </Button>
    )
  },
  {
    header: 'Tanggal Dibuat',
    accessorKey: 'createdAt',
    cell: ({ row }) =>
      formatDate(row.original.createdAt, 'dd/MM/yyyy, HH:mm', {
        locale: id
      })
  },
  {
    header: 'Status',
    accessorKey: 'status',
    cell: ({ row }) => {
      const status = row.original.status
      let color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' | undefined = 'default'
      let label = ''

      switch (status) {
        case ServiceOrderStatus.PROCESSED:
          color = 'default'
          label = 'Menunggu Persetujuan'
          break
        case ServiceOrderStatus.APPROVED:
          color = 'success'
          label = 'Disetujui'
          break
        case ServiceOrderStatus.REJECTED:
          color = 'error'
          label = 'Ditolak'
          break
        case ServiceOrderStatus.CANCELED:
          color = 'error'
          label = 'Dibatalkan'
          break
        case ServiceOrderStatus.CLOSED:
          color = 'success'
          label = 'Selesai'
          break
        case ServiceOrderStatus.CANCEL_REQUESTED:
          color = 'error'
          label = 'Permintaan Pembatalan'
          break
        default:
          color = 'default'
          label = status
          break
      }

      return <Chip label={label} color={color} />
    }
  },
  {
    header: 'Dibuat Oleh',
    accessorKey: 'createdByUser.fullName'
  }
]

export const workOrderSegmentColumns = ({ showDetail }: TableActions): ColumnDef<WoSegmentType>[] => [
  {
    header: 'No.',
    accessorKey: 'number',
    cell: ({ row }) => (
      <Typography color='primary' sx={{ cursor: 'pointer' }} onClick={() => showDetail(row.original.id, false)}>
        {truncateString(row.original.number, 15)}
      </Typography>
    )
  },
  {
    header: 'Job Code',
    accessorKey: 'jobCode',
    cell: ({ row }) =>
      row.original.jobCode
        ? `${row.original.jobCode.code} | ${truncateString(row.original.jobCode.description, 12)}`
        : '-'
  },
  {
    header: 'SMCS',
    accessorKey: 'componentCode',
    cell: ({ row }) =>
      row.original.componentCode
        ? `${row.original.componentCode.code} | ${truncateString(row.original.componentCode.description, 12)}`
        : '-'
  },
  {
    header: 'Modifier',
    accessorKey: 'modifierCode',
    cell: ({ row }) =>
      row.original.modifierCode
        ? `${row.original.modifierCode.code} | ${truncateString(row.original.modifierCode.description, 12)}`
        : '-'
  },
  {
    header: 'DETIL',
    enableSorting: false,
    cell: ({ row }) => (
      <IconButton onClick={() => showDetail(row.original.id, false)}>
        <i className='ri-eye-line text-secondary' />
      </IconButton>
    )
  }
]
