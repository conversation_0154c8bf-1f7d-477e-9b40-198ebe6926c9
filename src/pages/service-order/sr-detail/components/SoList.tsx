import { Card, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'
import { useSrList } from '../../context/SrListContext'
import Table from '@/components/table'
import { tableColumns } from './config/table'
import { useSo } from '../../context/SoContext'
import { useRouter } from '@/routes/hooks'

const SoList = () => {
  const router = useRouter()
  const { setSelectedSo } = useSo()
  const {
    srData,
    soListResponse: { items: soList, totalItems, totalPages },
    setPartialSosParams,
    sosParams: { page }
  } = useSrList()

  const table = useReactTable({
    data: soList,
    columns: tableColumns(
      {
        showDetail: (id, isCancelation) => {
          setSelectedSo({ soId: id, isCancelation })
          router.push(`/service-order/sr-list/${srData?.id}/so/${id}`)
        }
      },
      srData
    ),
    initialState: {
      pagination: {
        pageSize: 10,
        pageIndex: page - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Service Order Terbuat</Typography>
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography>Belum ada Service Order</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua Service Order yang telah kamu buat untuk Service Request ini akan ditampilkan di sini
              </Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => setPartialSosParams('limit', pageSize)}
          onPageChange={pageIndex => setPartialSosParams('page', pageIndex)}
        />
      </CardContent>
    </Card>
  )
}

export default SoList
