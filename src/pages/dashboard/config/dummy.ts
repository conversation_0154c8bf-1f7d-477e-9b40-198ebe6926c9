export const lineData = [
  {
    id: 'Spare Part',
    data: [
      { x: '01/01', y: 249 },
      { x: '01/02', y: 275 },
      { x: '01/03', y: 169 },
      { x: '01/04', y: 20 },
      { x: '01/05', y: 66 },
      { x: '01/06', y: 64 }
    ]
  },
  {
    id: 'Miscellaneous',
    data: [
      { x: '01/01', y: 159 },
      { x: '01/02', y: 278 },
      { x: '01/03', y: 278 },
      { x: '01/04', y: 107 },
      { x: '01/05', y: 76 },
      { x: '01/06', y: 94 }
    ]
  },
  {
    id: 'Lorem Ipsum',
    data: [
      { x: '01/01', y: 284 },
      { x: '01/02', y: 33 },
      { x: '01/03', y: 70 },
      { x: '01/04', y: 19 },
      { x: '01/05', y: 46 },
      { x: '01/06', y: 77 }
    ]
  },
  {
    id: 'Dolor',
    data: [
      { x: '01/01', y: 186 },
      { x: '01/02', y: 161 },
      { x: '01/03', y: 154 },
      { x: '01/04', y: 80 },
      { x: '01/05', y: 166 },
      { x: '01/06', y: 216 }
    ]
  },
  {
    id: 'Sit Amet',
    data: [
      { x: '01/01', y: 141 },
      { x: '01/02', y: 148 },
      { x: '01/03', y: 5 },
      { x: '01/04', y: 252 },
      { x: '01/05', y: 113 },
      { x: '01/06', y: 136 }
    ]
  }
]

export const barData = [
  {
    date: '01/01',
    'Spare Part': 37,
    Miscellaneous: 114,
    'Lorem Ipsum': 141,
    Dolor: 179,
    'Sit Amet': 198
  },
  {
    date: '01/02',
    'Spare Part': 133,
    Miscellaneous: 173,
    'Lorem Ipsum': 76,
    Dolor: 200,
    'Sit Amet': 117
  },
  {
    date: '01/03',
    'Spare Part': 68,
    Miscellaneous: 51,
    'Lorem Ipsum': 41,
    Dolor: 104,
    'Sit Amet': 125
  },
  {
    date: '01/04',
    'Spare Part': 5,
    Miscellaneous: 9,
    'Lorem Ipsum': 81,
    Dolor: 118,
    'Sit Amet': 58
  },
  {
    date: '01/05',
    'Spare Part': 180,
    Miscellaneous: 104,
    'Lorem Ipsum': 91,
    Dolor: 89,
    'Sit Amet': 189
  },
  {
    date: '01/06',
    'Spare Part': 8,
    Miscellaneous: 40,
    'Lorem Ipsum': 80,
    Dolor: 10,
    'Sit Amet': 147
  },
  {
    date: '01/07',
    'Spare Part': 166,
    Miscellaneous: 188,
    'Lorem Ipsum': 23,
    Dolor: 82,
    'Sit Amet': 35
  }
]

export const warehouseDummyData = [
  {
    id: 'Spare Part',
    label: 'Spare Part',
    value: 50,
    color: 'hsla(200, 80%, 60%, 1)'
  },
  {
    id: 'Miscellaneous',
    label: 'Miscellaneous',
    value: 70,
    color: 'hsla(150, 70%, 50%, 1)'
  },
  {
    id: 'Lorem Ipsum',
    label: 'Lorem Ipsum',
    value: 100,
    color: 'hsla(4, 100%, 77%, 1)'
  },
  {
    id: 'Dolor',
    label: 'Dolor',
    value: 40,
    color: 'hsla(4, 100%, 77%, 1)'
  },
  {
    id: 'Sit Amet',
    label: 'Sit Amet',
    value: 30,
    color: 'hsla(223, 85%, 63%, 1)'
  },
  {
    id: 'Lorem2',
    label: 'Lorem2',
    value: 20,
    color: 'hsla(4, 100%, 77%, 1)'
  },
  {
    id: 'Lorem 2',
    label: 'Lorem 2',
    value: 70,
    color: 'hsla(4, 100%, 77%, 1)'
  },
  {
    id: 'Lorem 23',
    label: 'Lorem 23',
    value: 70,
    color: 'hsla(4, 100%, 77%, 1)'
  }
]
