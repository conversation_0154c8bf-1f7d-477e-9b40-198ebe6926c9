import { BarDataType, LineDataType } from '@/components/chart/RawChart'
import truncateString from '@/core/utils/truncate'
import { Item } from '@/types/apps/itemType'
import { camelCaseToCapitalCase } from '@/utils/string'

export const optionsPeriodic = [
  {
    label: 'Semua Periode',
    value: 'ALL'
  },
  {
    label: 'Hari Ini',
    value: 'TODAY'
  },
  {
    label: 'Minggu ini',
    value: 'WEEK'
  },
  {
    label: '2 Minggu Terakhir',
    value: 'TWO_WEEK'
  },
  {
    label: 'Bulan ini',
    value: 'MONTH'
  }
]

export const optionsTab = [
  {
    value: 'pie',
    icon: <i className='pie-icon size-5' />
  },
  {
    value: 'chart',
    icon: <i className='chart-icon size-5' />
  },
  {
    value: 'list',
    icon: <i className='list-icon size-5' />
  }
]

export const getKeys = (items: any[]): string[] => {
  const keys = Object.keys(items?.[0] ?? {})

  return keys.filter(key => key !== 'id' && key !== 'label' && key !== 'date')
}

export const normalizeBarChartData = (items: any[]): BarDataType[] => {
  if (!Array.isArray(items)) {
    console.warn('Input must be an array. Returning original input.')
    return items
  }
  return items.map(item => {
    if (typeof item !== 'object' || item === null) {
      console.warn('Each item must be a non-null object. Returning original item.')
      return item
    }
    const transformedItem: Record<string, any> = { id: item.id, label: item.label }
    for (const key in item) {
      if (key !== 'id' && key !== 'label') {
        try {
          transformedItem[camelCaseToCapitalCase(key)] = item[key]
        } catch (error) {
          console.error(`Error transforming key "${key}":`, error)
        }
      }
    }
    return transformedItem
  })
}

export const normalizeLineChartData = (items: any[]): LineDataType[] => {
  if (!Array.isArray(items)) {
    items = []
  }

  const groupedData: { [key: string]: { id: string; data: { x: string; y: number }[] } } = {}

  items.forEach(item => {
    const label = typeof item.label === 'string' ? item.label : 'Unknown'

    Object.entries(item)
      .filter(([key]) => key !== 'id' && key !== 'label')
      .forEach(([key, value]) => {
        if (!groupedData[key]) {
          groupedData[key] = { id: camelCaseToCapitalCase(key), data: [] }
        }
        groupedData[key].data.push({ x: truncateString(label, 7), y: typeof value === 'number' ? value : 0 })
      })
  })

  return Object.values(groupedData)
}

export const normalizeBarArrayData = (data: Record<string, any>[]): Record<string, any>[] => {
  if (!Array.isArray(data)) {
    console.warn('normalizeArray: input is not an array.')
    return []
  }
  if (data.length === 0) return []

  // Collect all unique keys except 'date'
  const allKeys = new Set<string>()
  data.forEach(item => {
    if (typeof item === 'object' && item !== null) {
      Object.keys(item).forEach(key => {
        if (key !== 'date') allKeys.add(key)
      })
    }
  })

  // Normalize each item to have all keys, filling missing with 0
  return data.map(item => {
    try {
      if (typeof item !== 'object' || item === null) return item
      const normalizedItem: Record<string, any> = { ...item }
      allKeys.forEach(key => {
        if (!(key in normalizedItem)) {
          normalizedItem[key] = 0
        }
      })
      // Transform date from YYYY-MM-DD to MM-DD
      if (normalizedItem.date && typeof normalizedItem.date === 'string') {
        const parts = normalizedItem.date.split('-')
        if (parts.length === 3) {
          normalizedItem.date = parts[1] + '-' + parts[2]
        }
      }
      return normalizedItem
    } catch (err) {
      console.error('normalizeArray: error normalizing item', item, err)
      return item
    }
  })
}

export const normalizeLineArrayData = (data: LineDataType[]): LineDataType[] => {
  if (!Array.isArray(data)) {
    console.warn('normalizeLineArrayData: input is not an array.')
    return []
  }
  if (data.length === 0) return []

  return data.map(item => {
    try {
      if (!item || typeof item !== 'object') return item
      return {
        id: item.id,
        data: item.data.map(data => {
          if (!data || typeof data !== 'object') return data
          return {
            x: data.x && typeof data.x === 'string' ? data.x.substring(5) : data.x,
            y: data.y
          }
        })
      }
    } catch (err) {
      console.error('normalizeLineArrayData: error normalizing item', item, err)
      return item
    }
  })
}
