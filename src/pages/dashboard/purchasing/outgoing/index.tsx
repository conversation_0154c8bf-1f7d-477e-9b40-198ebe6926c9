import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Grid, Typo<PERSON> } from '@mui/material'
import { <PERSON> } from 'react-router-dom'
import OutgoingList from './component/OutgoingList'
import { optionsPeriodic } from '../../config/utils'
import { formatDate } from 'date-fns'
import DashboardQueryMethods, { PO_MG_IN_QUERY_KEY, PO_MG_OUT_QUERY_KEY } from '@/api/services/dashboard/query'
import { useQuery } from '@tanstack/react-query'
import { usePurchaseDashboard } from '../context/DashboardPurchaseContext'

const PurchasingPage = () => {
  const { filterData } = usePurchaseDashboard()

  const { data: outgoingList } = useQuery({
    queryKey: [PO_MG_OUT_QUERY_KEY, 'OUTGOING_LIST_PURCHASING', JSON.stringify(filterData)],
    queryFn: async () =>
      await DashboardQueryMethods.getOutgoingMaterialsByValueItem({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      })
  })

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Dashboard</Typography>
          </Link>
          <Link to='/dashboard/purchasing' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Purchasing</Typography>
          </Link>
          <Typography>Barang Keluar</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h5'>Barang Keluar</Typography>
            <Typography color='primary'>
              {optionsPeriodic.find(opt => opt.value === filterData.period)?.label}
            </Typography>
            <Typography variant='caption'>
              {formatDate(filterData.startDate, 'dd/MM/yyyy')} - {formatDate(filterData.endDate, 'dd/MM/yyyy')}
            </Typography>
          </div>
          <div className='flex gap-2'>
            {/* <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
              // onClick={handleExport}
            >
              Ekspor
            </Button>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ic-outline-local-printshop' />}
              className='px-8 is-full !ml-0 sm:is-auto'
              // onClick={handlePrint}
            >
              Cetak
            </Button> */}
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <OutgoingList outgoingList={outgoingList?.items ?? []} />
      </Grid>
    </Grid>
  )
}

export default PurchasingPage
