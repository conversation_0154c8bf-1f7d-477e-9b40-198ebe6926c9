import { Card, CardContent, Typography, Grid } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { useQuery } from '@tanstack/react-query'
import { useMemo } from 'react'
import DashboardQueryMethods from '@/api/services/dashboard/query'
import { useNavigate } from 'react-router-dom'
import { usePurchaseDashboard } from '../../../context/DashboardPurchaseContext'

const OutgoingCard = () => {
  const { selectedPurchaseId: itemId, filterData } = usePurchaseDashboard()
  const navigate = useNavigate()

  const { data: docList } = useQuery({
    queryKey: ['OUTGOING_DOCS_LIST_KEY', itemId],
    queryFn: () =>
      DashboardQueryMethods.getDetailOutgoingQuantityByDocuments({
        itemId,
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      }),
    enabled: !!itemId,
    placeholderData: { items: [], updatedAt: '' }
  })

  const items = useMemo(() => docList?.items ?? [], [docList])

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns({
        detail: (id: string) => navigate(`/mr/list/${id}`)
      }),
      initialState: {
        pagination: {
          pageIndex: 0,
          pageSize: 10
        }
      },
      state: {
        pagination: {
          pageIndex: 0,
          pageSize: items?.length ?? 10
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Jumlah Barang Keluar</Typography>
          <Typography color='primary'>
            {items.reduce((acc, curr) => acc + curr.outQuantity, 0)} {items?.[0]?.quantityUnit}
          </Typography>
        </div>
        <Grid container spacing={4}>
          {/* <Grid item xs={12}>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-1'>
                <Typography>Total Sudah Diterima</Typography>
                <Typography sx={{ fontWeight: 700 }} color='primary'>
                  {items.reduce((acc, curr) => acc + curr.received_quantity, 0)} PCS
                </Typography>
              </div>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-1'>
                <Typography>Total Belum Diterima</Typography>
                <Typography sx={{ fontWeight: 700 }} color='error'>
                  {items.reduce((acc, curr) => acc + curr.remaining_quantity, 0)} PCS
                </Typography>
              </div>
            </div>
          </Grid> */}
          <Grid item xs={12}>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Pembelian</Typography>
                </td>
              }
              disablePagination
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default OutgoingCard
