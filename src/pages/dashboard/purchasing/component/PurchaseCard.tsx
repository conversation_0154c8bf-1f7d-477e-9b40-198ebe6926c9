import { Card, CardContent, Tab, Tabs, Typography } from '@mui/material'
import Pie<PERSON>hart from '@/components/chart/PieChart'
import Raw<PERSON>hart from '@/components/chart/RawChart'
import { getKeys, normalizeBarChartData, normalizeLineChartData, optionsTab } from '../../config/utils'
import TabPanel from '@/core/components/mui/TabPanel'
import truncateString from '@/core/utils/truncate'
import { usePurchaseDashboard } from '../context/DashboardPurchaseContext'
import { DefaultRawDatum, PieTooltipProps } from '@nivo/pie'
import { toCurrency } from '@/utils/helper'

const PurchaseCard = () => {
  const { mgInStatistics, setChartModel, chartModel, mgInStatisticsChart, poByCategory } = usePurchaseDashboard()

  const barChart = mgInStatisticsChart?.map(item => ({ ...item, label: truncateString(item.label, 7) }))

  return (
    <Card>
      <CardContent className='space-y-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Total Pembelian</Typography>
          <Tabs
            value={chartModel?.purchase}
            onChange={(e, value) => setChartModel(prev => ({ ...prev, purchase: value }))}
            className='rounded-[8px] border'
            aria-label='tabs'
            sx={{
              '.MuiTab-root': { padding: '0px !important', minWidth: '48px' },
              '.Mui-selected': { backgroundColor: 'var(--mui-palette-primary-main)', color: 'white !important' }
            }}
          >
            {optionsTab.map(opt => (
              <Tab key={opt.value} value={opt.value} icon={opt.icon} aria-label={opt.value} />
            ))}
          </Tabs>
        </div>
        <TabPanel value={chartModel?.purchase} index={'pie'}>
          <PieChart
            format='currency'
            value='currency'
            data={poByCategory?.items?.map(po => ({ id: po.label, value: po.value, label: po.label })) ?? []}
            tooltip={(props: PieTooltipProps<DefaultRawDatum>) => (
              <div className='p-2 rounded-md bg-white shadow-md w-full flex gap-2 items-center'>
                <div className='size-4' style={{ backgroundColor: props.datum.color }}></div>
                <Typography className='w-fit'>{`${props.datum.label}:`}</Typography>
                <Typography>{`${toCurrency(props.datum.value)}`}</Typography>
              </div>
            )}
          />
        </TabPanel>
        <TabPanel value={chartModel?.purchase} index={'chart'}>
          <RawChart
            chartType='line'
            format='currency'
            data={normalizeLineChartData(mgInStatisticsChart)}
            lineProps={{
              bottomTickRotation: -45,
              margin: { top: 50, right: 10, bottom: 70, left: 90 },
              axisLeft: {
                format: props => toCurrency(props),
                tickValues: 5
              },
              yScale: {
                type: 'linear',
                max: 'auto',
                stacked: true
              },
              legends: [
                {
                  anchor: 'bottom',
                  direction: 'row',
                  justify: false,
                  translateX: 4,
                  translateY: 70,
                  itemWidth: 90,
                  itemHeight: 20,
                  itemsSpacing: 4,
                  symbolSize: 7,
                  symbolShape: 'circle',
                  itemDirection: 'left-to-right',
                  itemTextColor: '#777',
                  effects: [
                    {
                      on: 'hover',
                      style: {
                        itemBackground: 'rgba(0, 0, 0, .03)',
                        itemOpacity: 1
                      }
                    }
                  ]
                }
              ]
            }}
          />
        </TabPanel>
        <TabPanel value={chartModel?.purchase} index={'list'}>
          <RawChart
            chartType='bar'
            format='currency'
            data={normalizeBarChartData(barChart)}
            keys={getKeys(normalizeBarChartData(barChart))}
            indexBy='label'
            barProps={{
              margin: { top: 50, right: 10, bottom: 70, left: 90 },
              axisLeft: {
                format: val => toCurrency(val),
                tickValues: 5
              },
              legends: [
                {
                  dataFrom: 'keys',
                  anchor: 'bottom',
                  direction: 'row',
                  justify: false,
                  translateX: 20,
                  translateY: 76,
                  itemsSpacing: 2,
                  itemWidth: 100,
                  itemHeight: 20,
                  itemDirection: 'left-to-right',
                  itemOpacity: 0.85,
                  symbolSize: 12,
                  effects: [
                    {
                      on: 'hover',
                      style: {
                        itemOpacity: 1
                      }
                    }
                  ]
                }
              ]
            }}
          />
        </TabPanel>
      </CardContent>
    </Card>
  )
}

export default PurchaseCard
