import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import PurchaseList from './component/PurchaseList'
import { optionsPeriodic } from '../../config/utils'
import { formatDate } from 'date-fns'
import DashboardQueryMethods, { PO_MG_IN_QUERY_KEY } from '@/api/services/dashboard/query'
import { useQuery } from '@tanstack/react-query'
import { usePurchaseDashboard } from '../context/DashboardPurchaseContext'

const PurchasingPage = () => {
  const { filterData } = usePurchaseDashboard()

  const { data: purchaseList } = useQuery({
    queryKey: [PO_MG_IN_QUERY_KEY, 'PURCHASE_LIST_PURCHASING', JSON.stringify(filterData)],
    queryFn: async () =>
      await DashboardQueryMethods.getPurchaseOrdersValueByItem({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      })
  })

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Dashboard</Typography>
          </Link>
          <Link to='/dashboard/purchasing' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Purchasing</Typography>
          </Link>
          <Typography>Total Pembelian</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h5'>Total Pembelian</Typography>
            <Typography color='primary'>
              {optionsPeriodic.find(opt => opt.value === filterData.period)?.label}
            </Typography>
            <Typography variant='caption'>
              {formatDate(filterData.startDate, 'dd/MM/yyyy')} - {formatDate(filterData.endDate, 'dd/MM/yyyy')}
            </Typography>
          </div>
          <div className='flex gap-2'>
            {/* <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
              // onClick={handleExport}
            >
              Ekspor
            </Button>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ic-outline-local-printshop' />}
              className='px-8 is-full !ml-0 sm:is-auto'
              // onClick={handlePrint}
            >
              Cetak
            </Button> */}
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <PurchaseList purchaseList={purchaseList?.items ?? []} />
      </Grid>
    </Grid>
  )
}

export default PurchasingPage
