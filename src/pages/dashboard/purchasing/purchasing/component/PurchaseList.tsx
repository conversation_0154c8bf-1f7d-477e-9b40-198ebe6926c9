import { Box, Card, CardContent, Grid, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tablePurchaseColumns } from '../config/table'
import Table from '@/components/table'
import { useMemo } from 'react'
import { PurchaseOrderStatisticsType } from '@/types/dashboardTypes'
import { usePurchaseDashboard } from '../../context/DashboardPurchaseContext'
import { toCurrency } from '@/utils/helper'

type PurchaseListProps = {
  purchaseList: PurchaseOrderStatisticsType[]
}

const PurchaseList = (props: PurchaseListProps) => {
  const { purchaseList } = props
  const { navigate, setSelectedPurchaseId, purchaseSummaryData: mgInSummary } = usePurchaseDashboard()

  const tableOptions = useMemo(
    () => ({
      data: purchaseList ?? [],
      columns: tablePurchaseColumns({
        summaryPurchase: mgInSummary?.totalPurchase ?? 0,
        detail: (id: string) => {
          setSelectedPurchaseId(id)
          navigate(`purchase/${id}`)
        }
      }),
      initialState: {
        pagination: {
          pageIndex: 0,
          pageSize: 10
        }
      },
      state: {
        pagination: {
          pageIndex: 0,
          pageSize: purchaseList?.length ?? 10
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [purchaseList]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between'>
          <Typography variant='h5'>Kategori Spare Parts</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-2'>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-1'>
                <Typography>Total Pembelian</Typography>
                <Typography sx={{ fontWeight: 700 }}>{toCurrency(mgInSummary?.totalPurchase)}</Typography>
              </div>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-1'>
                <Typography>Total Sudah Diterima</Typography>
                <Typography sx={{ fontWeight: 700 }} color='primary'>
                  {toCurrency(mgInSummary?.totalReceived)}
                </Typography>
              </div>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-1'>
                <Typography>Total Belum Diterima</Typography>
                <Typography sx={{ fontWeight: 700 }} color='error'>
                  {toCurrency(mgInSummary?.totalRemaining)}
                </Typography>
              </div>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='shadow-xs rounded-[8px]'>
              <Table
                table={table}
                emptyLabel={
                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                    <Typography>Belum ada Pembelian</Typography>
                  </td>
                }
                disablePagination
              />
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default PurchaseList
