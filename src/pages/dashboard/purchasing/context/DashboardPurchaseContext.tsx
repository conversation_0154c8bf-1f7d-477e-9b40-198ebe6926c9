import DashboardQueryMethods, { PO_MG_IN_QUERY_KEY, PO_MG_OUT_QUERY_KEY } from '@/api/services/dashboard/query'
import { PieDataType } from '@/components/chart/PieChart'
import { BarDataType, LineDataType } from '@/components/chart/RawChart'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { GenericListResponse } from '@/types/api'
import {
  MgInStatistics,
  MgInStatisticsChartType,
  MgOutStatistics,
  PurchaseOrderStatisticsType,
  PurchaseSummaryType,
  PurchaseTableColumns,
  PurchasingSummaryType,
  PurchasingSummaryValuesType,
  StatisticParams
} from '@/types/dashboardTypes'
import { useQuery } from '@tanstack/react-query'
import React, { useEffect, useState } from 'react'
import { NavigateFunction, useNavigate, useParams, useLocation } from 'react-router-dom'
import { useLocalStorage } from 'react-use'

type DashboardPurchaseContextProps = {
  isMobile: boolean
  selectedPurchaseId: string | null
  setSelectedPurchaseId: React.Dispatch<React.SetStateAction<string | null>>
  navigate: NavigateFunction
  filterData: StatisticParams
  setFilterData: React.Dispatch<React.SetStateAction<StatisticParams>>
  mgInStatistics: MgInStatisticsChartType[]
  mgInStatisticsChart: PurchaseOrderStatisticsType[]
  mgOutSummary: MgOutStatistics
  mgOutSummaryValue: MgOutStatistics
  mgInChartData: (BarDataType | LineDataType)[]
  mgOutChartData: (BarDataType | LineDataType)[]
  purchaseSummaryData: PurchasingSummaryType
  poByCategory: GenericListResponse<PurchasingSummaryValuesType>
  chartModel: {
    mgIn: string
    mgOut: string
    purchase: string
  }
  setChartModel: React.Dispatch<
    React.SetStateAction<{
      mgIn: string
      mgOut: string
      purchase: string
    }>
  >
  isWarehousePage: boolean
  isWorkshopPage: boolean
}

const DashboardPurchaseContext = React.createContext<DashboardPurchaseContextProps>({} as DashboardPurchaseContextProps)

export const usePurchaseDashboard = () => {
  const context = React.useContext(DashboardPurchaseContext)
  if (!context) {
    throw new Error('usePurchaseDashboard must be used within a DashboardPurchaseContextProvider')
  }
  return context
}

export const DashboardPurchaseContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const location = useLocation()
  const { pId } = useParams()

  const isWarehousePage = location.pathname.includes('warehouse')
  const isWorkshopPage = location.pathname.includes('workshop')

  const [selectedPurchaseId, setSelectedPurchaseId] = useState<string | null>(pId)
  const dateNow = new Date()
  const endDate = new Date(dateNow)
  endDate.setDate(dateNow.getDate() + 1)

  const defaultFilterData = {
    siteIds: '',
    departmentId: '',
    period: 'TODAY',
    startDate: dateNow.toISOString(),
    endDate: endDate.toISOString()
  }

  const [chartModel, setChartModel] = useState<{
    mgIn: string
    mgOut: string
    purchase: string
  }>({
    mgIn: 'chart',
    mgOut: 'chart',
    purchase: 'pie'
  })
  const [filterData, setFilterData] = useLocalStorage<StatisticParams & { period?: string }>(
    'filterPurchase',
    defaultFilterData
  )

  const { data: mgInStatistics } = useQuery({
    enabled: chartModel.purchase === 'pie',
    queryKey: [PO_MG_IN_QUERY_KEY, 'CHART_PURCHASING', JSON.stringify(filterData)],
    queryFn: () =>
      DashboardQueryMethods.getPurchaseOrdersInCategories({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      }),
    placeholderData: [] as MgInStatisticsChartType[]
  })

  const { data: mgInStatisticsChart } = useQuery({
    enabled: chartModel.purchase !== 'pie',
    queryKey: [PO_MG_IN_QUERY_KEY, 'LINE_CHART_PURCHASING', JSON.stringify(filterData)],
    queryFn: async () =>
      (
        await DashboardQueryMethods.getPurchaseOrdersValueByItem({
          startDate: filterData.startDate,
          endDate: filterData.endDate,
          siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
          departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
        })
      ).items ?? [],
    placeholderData: [] as PurchaseOrderStatisticsType[]
  })

  const { data: mgOutSummary } = useQuery({
    queryKey: [PO_MG_OUT_QUERY_KEY, 'SUMMARY_PURCHASING', JSON.stringify(filterData)],
    queryFn: () =>
      DashboardQueryMethods.getPurchaseOrdersOut({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      })
  })

  const { data: mgOutSummaryValue } = useQuery({
    queryKey: [PO_MG_IN_QUERY_KEY, 'SUMMARY_PURCHASING', JSON.stringify(filterData)],
    queryFn: () =>
      DashboardQueryMethods.getPurchaseOrdersOutValue({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      })
  })

  const { data: mgInChartData } = useQuery({
    queryKey: ['MATERIALS_IN_KEY_PURCHASING', chartModel.mgIn, JSON.stringify(filterData)],
    queryFn: () => {
      if (chartModel.mgIn === 'chart') {
        return DashboardQueryMethods.getMaterialsValueInLine({
          startDate: filterData.startDate,
          endDate: filterData.endDate,
          siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
          departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
        })
      }
      if (chartModel.mgIn === 'list') {
        return DashboardQueryMethods.getMaterialsValueInBar({
          startDate: filterData.startDate,
          endDate: filterData.endDate,
          siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
          departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
        })
      }
    },
    placeholderData: [] as (LineDataType | BarDataType)[]
  })

  const { data: mgOutChartData } = useQuery({
    queryKey: ['MATERIALS_OUT_KEY_PURCHASING', chartModel.mgOut, JSON.stringify(filterData)],
    queryFn: () => {
      if (chartModel.mgOut === 'chart') {
        return DashboardQueryMethods.getMaterialsOutValueLine({
          startDate: filterData.startDate,
          endDate: filterData.endDate,
          siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
          departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
        })
      }
      if (chartModel.mgOut === 'list') {
        return DashboardQueryMethods.getMaterialsOutValueBar({
          startDate: filterData.startDate,
          endDate: filterData.endDate,
          siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
          departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
        })
      }
    },
    placeholderData: [] as (LineDataType | BarDataType)[]
  })

  const { data: purchaseSummaryData } = useQuery({
    queryKey: ['PURCHASE_SUMMARY_KEY', JSON.stringify(filterData)],
    queryFn: () =>
      DashboardQueryMethods.getPurchaseSummary({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      })
  })

  const { data: poByCategory } = useQuery({
    queryKey: ['PURCHASE_SUMARY_CATEGORIZED', JSON.stringify(filterData)],
    queryFn: () =>
      DashboardQueryMethods.getPurchaseByCategory({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : undefined,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      })
  })

  useEffect(() => {
    setSelectedPurchaseId(pId)
  }, [pId])

  const value = {
    isMobile,
    selectedPurchaseId,
    setSelectedPurchaseId,
    navigate,
    filterData,
    setFilterData,
    mgInStatistics,
    mgOutSummary,
    mgOutSummaryValue,
    chartModel,
    setChartModel,
    mgInStatisticsChart,
    mgInChartData,
    mgOutChartData,
    isWarehousePage,
    isWorkshopPage,
    purchaseSummaryData,
    poByCategory
  }

  return <DashboardPurchaseContext.Provider value={value}>{children}</DashboardPurchaseContext.Provider>
}
