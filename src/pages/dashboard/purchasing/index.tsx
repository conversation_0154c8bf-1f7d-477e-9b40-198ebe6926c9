import { useAuth } from '@/contexts/AuthContext'
import { Grid, Typography } from '@mui/material'
import FilterData from './component/FilterData'
import PurchaseCard from './component/PurchaseCard'
import PurchaseSummaryCard from './component/PurchaseSummaryCard'
import StockMovementCard from './component/StockMovementCard'
import { StatisticParams } from '@/types/dashboardTypes'
import { usePurchaseDashboard } from './context/DashboardPurchaseContext'

const WarehouseDashboard = () => {
  const { filterData, setFilterData, mgOutSummaryValue, purchaseSummaryData } = usePurchaseDashboard()

  const onChangeFilter = (filterData: StatisticParams) => {
    setFilterData(prev => ({ ...prev, ...filterData }))
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Typography variant='h4'>Dashboard Purchasing</Typography>
      </Grid>
      <Grid item xs={12}>
        <FilterData filterData={filterData} onChangeFilter={onChangeFilter} />
      </Grid>
      <Grid item xs={12}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <PurchaseCard />
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <PurchaseSummaryCard
                  type='IN'
                  format='currency'
                  currentQuantity={purchaseSummaryData?.totalReceived ?? 0}
                  totalQuantity={purchaseSummaryData?.totalPurchase ?? 0}
                  quantityUnit='PCS'
                />
              </Grid>
              <Grid item xs={12}>
                <PurchaseSummaryCard
                  type='OUT'
                  format='currency'
                  currentQuantity={mgOutSummaryValue?.totalOutgoing ?? 0}
                  totalQuantity={purchaseSummaryData?.totalReceived ?? 0}
                  quantityUnit='PCS'
                />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <StockMovementCard type='IN' />
      </Grid>
      <Grid item xs={12} md={6}>
        <StockMovementCard type='OUT' />
      </Grid>
    </Grid>
  )
}

export default WarehouseDashboard
