import { Breadcrumbs, Button, Grid, Typography } from '@mui/material'
import { Link, useParams } from 'react-router-dom'
import ItemDetailCard from './component/ItemDetailCard'
import OutgoingCard from './component/OutgoingCard'
import { useWarehouseDashboard } from '../../context/WarehouseContext'
import { useAuth } from '@/contexts/AuthContext'
import { optionsPeriodic } from '@/pages/dashboard/config/utils'
import { formatDate } from 'date-fns'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods from '@/api/services/company/query'

const PurchasingPage = () => {
  const { filterData, selectedPurchaseId } = useWarehouseDashboard()
  const { pId } = useParams()
  const { ownSiteList } = useAuth()

  const { data: itemData } = useQuery({
    enabled: !!pId || !!selectedPurchaseId,
    queryFn: () => CompanyQueryMethods.getItem(pId || selectedPurchaseId)
  })

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Dashboard</Typography>
          </Link>
          <Link to='/dashboard/warehouse' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Warehouse</Typography>
          </Link>
          <Link to='/dashboard/warehouse/outgoing' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Barang Keluar</Typography>
          </Link>
          <Typography>Detil Barang Keluar</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h5'>Nama Barang: {itemData?.name}</Typography>
            <Typography color='primary'>
              {ownSiteList?.find(site => site.id === filterData.siteIds)?.name ?? ''}
            </Typography>
            <Typography variant='caption'>
              {optionsPeriodic.find(opt => opt.value === filterData.period)?.label} (
              {`${formatDate(new Date(filterData.startDate), 'dd/MM/yyyy')} - ${formatDate(new Date(filterData.endDate), 'dd/MM/yyyy')}`}
              )
            </Typography>
          </div>
          <div className='flex gap-2'>
            {/* <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ri-upload-2-line' />}
              className='is-full sm:is-auto'
              // onClick={handleExport}
            >
              Ekspor
            </Button>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ic-outline-local-printshop' />}
              className='px-8 is-full !ml-0 sm:is-auto'
              // onClick={handlePrint}
            >
              Cetak
            </Button> */}
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <ItemDetailCard itemData={itemData} />
      </Grid>
      <Grid item xs={12} md={6}>
        <OutgoingCard />
      </Grid>
    </Grid>
  )
}

export default PurchasingPage
