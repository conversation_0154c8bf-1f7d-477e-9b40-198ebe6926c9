import truncateString from '@/core/utils/truncate'
import { OutgoingDocumentListType } from '@/types/dashboardTypes'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

type RowActionType = {
  detail?: (id: string) => void
}

const columnHelper = createColumnHelper<OutgoingDocumentListType>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No Dokumen',
    cell: ({ row }) => (
      <Typography color='primary' onClick={() => rowAction.detail(row.original.id)} sx={{ cursor: 'pointer' }}>
        {truncateString(row.original.number, 12)}
      </Typography>
    )
  }),
  columnHelper.accessor('outQuantity', {
    header: 'Barang Keluar',
    cell: ({ row }) => `${row.original.outQuantity} ${row.original.quantityUnit}`
  }),
  columnHelper.accessor('stock', {
    header: 'Stok',
    cell: ({ row }) => `${row.original.stock} ${row.original.quantityUnit}`
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original.id)} className='flex gap-2 items-center'>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
