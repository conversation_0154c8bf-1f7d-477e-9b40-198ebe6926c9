import { OutgoingStatisticsType } from '@/types/dashboardTypes'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

type RowActionType = {
  detail?: (id: string) => void
}

const columnHelper = createColumnHelper<OutgoingStatisticsType>()

export const tablePurchaseColumns = (rowAction: RowActionType) => [
  columnHelper.display({
    id: 'number',
    header: '#',
    size: 50,
    cell: ({ row }) => row.index + 1
  }),
  columnHelper.accessor('label', {
    header: 'Nama Item'
  }),
  columnHelper.accessor('value', {
    header: 'Jumlah <PERSON> Keluar',
    cell: ({ row }) => <Typography color='error'>{`${row.original.value} PCS`}</Typography>
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <div className='flex gap-2 items-center'>
          <IconButton onClick={() => rowAction.detail?.(row.original.id)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </div>
      )
    }
  })
]
