import { useAuth } from '@/contexts/AuthContext'
import { Grid, Typography } from '@mui/material'
import FilterData from './component/FilterData'
import PurchaseCard from './component/PurchaseCard'
import PurchaseSummaryCard from './component/PurchaseSummaryCard'
import StockMovementCard from './component/StockMovementCard'
import { useWarehouseDashboard } from './context/WarehouseContext'
import { StatisticParams } from '@/types/dashboardTypes'

const WarehouseDashboard = ({ title }: { title?: string }) => {
  const { filterData, setFilterData, mgInSummary, mgOutSummary } = useWarehouseDashboard()

  const onChangeFilter = (filterData: StatisticParams) => {
    setFilterData(prev => ({ ...prev, ...filterData }))
  }

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Typography variant='h4'>Dashboard {title ?? 'Warehouse'}</Typography>
      </Grid>
      <Grid item xs={12}>
        <FilterData filterData={filterData} onChangeFilter={onChangeFilter} />
      </Grid>
      <Grid item xs={12}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <PurchaseCard />
          </Grid>
          <Grid item xs={12} md={6}>
            <Grid container spacing={4}>
              <Grid item xs={12}>
                <PurchaseSummaryCard
                  type='IN'
                  currentQuantity={mgInSummary?.totalReceived ?? 0}
                  totalQuantity={mgInSummary?.totalPurchase ?? 0}
                  quantityUnit='PCS'
                />
              </Grid>
              <Grid item xs={12}>
                <PurchaseSummaryCard
                  type='OUT'
                  currentQuantity={mgOutSummary?.totalOutgoing ?? 0}
                  totalQuantity={mgInSummary?.totalReceived ?? 0}
                  quantityUnit='PCS'
                />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <StockMovementCard type='IN' />
      </Grid>
      <Grid item xs={12} md={6}>
        <StockMovementCard type='OUT' />
      </Grid>
    </Grid>
  )
}

export default WarehouseDashboard
