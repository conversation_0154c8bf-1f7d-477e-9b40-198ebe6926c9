import truncateString from '@/core/utils/truncate'
import { PurchaseDocumentListType } from '@/types/dashboardTypes'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

type RowActionType = {
  detail?: (id: string) => void
}

const columnHelper = createColumnHelper<PurchaseDocumentListType>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'No Dokumen',
    cell: ({ row }) => (
      <Typography color='primary' onClick={() => rowAction.detail(row.original.id)} sx={{ cursor: 'pointer' }}>
        {truncateString(row.original.number, 12)}
      </Typography>
    )
  }),
  columnHelper.accessor('smallQuantity', {
    header: 'Dipesan',
    cell: ({ row }) => `${row.original.smallQuantity} pcs`
  }),
  columnHelper.accessor('receivedQuantity', {
    header: 'Diterima',
    cell: ({ row }) => `${row.original.receivedQuantity} pcs`
  }),
  columnHelper.accessor('remainingQuantity', {
    header: 'Blm Diterima',
    cell: ({ row }) => (
      <Typography
        color={row.original.remainingQuantity > 0 ? 'error' : 'textSecondary'}
      >{`${row.original.remainingQuantity} pcs`}</Typography>
    )
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original.id)} className='flex gap-2 items-center'>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
