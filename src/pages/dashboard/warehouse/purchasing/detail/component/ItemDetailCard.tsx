import { DEFAULT_CATEGORY } from '@/data/default/category'
import { ItemType } from '@/types/companyTypes'
import { Card, CardContent, Typography, Grid } from '@mui/material'

const ItemDetailCard = ({ itemData }: { itemData?: ItemType }) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Barang</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2'>
              <small>Kategori Item</small>
              <Typography variant='h6'>{itemData?.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Nama Item</small>
              <Typography variant='h6'>{itemData?.name ?? '-'}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Merk Item</small>
              <Typography variant='h6'>{itemData?.brandName ?? '-'}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Satuan Besar</small>
              <Typography variant='h6'>{itemData?.largeUnit ?? '-'}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Satuan Kecil</small>
              <Typography variant='h6'>{itemData?.smallUnit ?? '-'}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Jumlah Satuan Besar</small>
              <Typography variant='h6'>{itemData?.largeUnitQuantity ?? '-'}</Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default ItemDetailCard
