import { DepartmentType, SiteType } from '@/types/companyTypes'
import { PurchaseTableColumns, PurchaseDocsTable } from '@/types/dashboardTypes'
import { UserType } from '@/types/userTypes'

export const dummyPurchaseData: PurchaseTableColumns[] = [
  {
    id: 'a3f1c9e2-7b4d-4f8a-9d3e-1a2b3c4d5e6f',
    item: {
      id: 'b4d2e3f4-5a6b-7c8d-9e0f-1a2b3c4d5e6f',
      number: 'ITM-001',
      vendorNumber: 'VND-001',
      name: 'Excavator',
      description: 'Ergonomic wireless mouse with adjustable DPI',
      brandName: 'LogiTech',
      largeUnit: 'Box',
      smallUnit: 'Piece',
      largeUnitQuantity: 10,
      categoryId: 'c5d6e7f8-9a0b-1c2d-3e4f-5a6b7c8d9e0f',
      companyId: 'd7e8f9a0-b1c2-d3e4-f5a6-b7c8d9e0f1a2',
      parentCompanyId: 'e9f0a1b2-c3d4-e5f6-a7b8-c9d0e1f2a3b4',
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-02T00:00:00Z',
      category: {
        id: 'c5d6e7f8-9a0b-1c2d-3e4f-5a6b7c8d9e0f',
        type: 'CategoryType1',
        code: 'CAT001',
        name: 'Computer Accessories',
        parentId: 'f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6',
        companyId: 'd7e8f9a0-b1c2-d3e4-f5a6-b7c8d9e0f1a2',
        parentCompanyId: 'e9f0a1b2-c3d4-e5f6-a7b8-c9d0e1f2a3b4',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z'
      },
      images: [],
      createdByUser: {
        id: 'f2a3b4c5-d6e7-f8a9-b0c1-d2e3f4a5b6c7',
        name: 'Alice Johnson',
        fullName: 'Alice Johnson',
        title: 'Manager',
        email: '<EMAIL>',
        role: 'admin',
        country: 'USA',
        contact: '*********',
        position: 'Manager',
        username: 'alicej',
        avatar: '',
        avatarColor: 'info',
        department: undefined,
        division: undefined,
        divisionId: null,
        threshold: undefined,
        phoneNumber: '*********',
        status: 'active',
        companyId: 'd7e8f9a0-b1c2-d3e4-f5a6-b7c8d9e0f1a2',
        subCompanyId: null,
        departmentId: null,
        permissionGroupId: '',
        sites: [],
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z'
      }
    },
    totalPurchase: 100,
    percentPurchase: 75,
    percentReceived: 60,
    received: 60,
    notReceived: 40,
    quantityUnit: 'pcs'
  },
  {
    id: 'b7c8d9e0-f1a2-3b4c-5d6e-7f8a9b0c1d2e',
    item: {
      id: 'c8d9e0f1-a2b3-4c5d-6e7f-8a9b0c1d2e3f',
      number: 'ITM-002',
      vendorNumber: 'VND-002',
      name: 'Drill Rig',
      description: 'RGB backlit mechanical keyboard with blue switches',
      brandName: 'KeyMaster',
      largeUnit: 'Box',
      smallUnit: 'Piece',
      largeUnitQuantity: 20,
      categoryId: 'd9e0f1a2-b3c4-5d6e-7f8a-9b0c1d2e3f4a',
      companyId: 'e0f1a2b3-c4d5-6e7f-8a9b-0c1d2e3f4a5b',
      parentCompanyId: 'f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6',
      createdAt: '2023-02-01T00:00:00Z',
      updatedAt: '2023-02-02T00:00:00Z',
      category: {
        id: 'd9e0f1a2-b3c4-5d6e-7f8a-9b0c1d2e3f4a',
        type: 'CategoryType2',
        code: 'CAT002',
        name: 'Keyboards',
        parentId: 'a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6',
        companyId: 'e0f1a2b3-c4d5-6e7f-8a9b-0c1d2e3f4a5b',
        parentCompanyId: 'f1a2b3c4-d5e6-f7a8-b9c0-d1e2f3a4b5c6',
        createdAt: '2023-02-01T00:00:00Z',
        updatedAt: '2023-02-02T00:00:00Z'
      },
      images: [],
      createdByUser: {
        id: 'a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8',
        name: 'Bob Smith',
        fullName: 'Bob Smith',
        title: 'Staff',
        email: '<EMAIL>',
        role: 'user',
        country: 'Canada',
        contact: '*********',
        position: 'Staff',
        username: 'bobsmith',
        avatar: '',
        avatarColor: 'info',
        department: undefined,
        division: undefined,
        divisionId: null,
        threshold: undefined,
        phoneNumber: '*********',
        status: 'active',
        companyId: 'e0f1a2b3-c4d5-6e7f-8a9b-0c1d2e3f4a5b',
        subCompanyId: null,
        departmentId: null,
        permissionGroupId: '',
        sites: [],
        createdAt: '2023-02-01T00:00:00Z',
        updatedAt: '2023-02-02T00:00:00Z'
      }
    },
    totalPurchase: 200,
    percentPurchase: 50,
    percentReceived: 40,
    received: 80,
    notReceived: 120,
    quantityUnit: 'pcs'
  },
  {
    id: 'c9d0e1f2-a3b4-c5d6-e7f8-a9b0c1d2e3f4',
    item: {
      id: 'd0e1f2a3-b4c5-d6e7-f8a9-b0c1d2e3f4a5',
      number: 'ITM-003',
      vendorNumber: 'VND-003',
      name: 'Mining Helmet',
      description: 'High-resolution 27-inch LED monitor with HDMI and DisplayPort',
      brandName: 'ViewTech',
      largeUnit: 'Box',
      smallUnit: 'Piece',
      largeUnitQuantity: 15,
      categoryId: 'e1f2a3b4-c5d6-e7f8-a9b0-c1d2e3f4a5b6',
      companyId: 'f2a3b4c5-d6e7-f8a9-b0c1-d2e3f4a5b6c7',
      parentCompanyId: 'a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8',
      createdAt: '2023-03-01T00:00:00Z',
      updatedAt: '2023-03-02T00:00:00Z',
      category: {
        id: 'e1f2a3b4-c5d6-e7f8-a9b0-c1d2e3f4a5b6',
        type: 'CategoryType3',
        code: 'CAT003',
        name: 'Monitors',
        parentId: 'b4c5d6e7-f8a9-b0c1-d2e3-f4a5b6c7d8e9',
        companyId: 'f2a3b4c5-d6e7-f8a9-b0c1-d2e3f4a5b6c7',
        parentCompanyId: 'a3b4c5d6-e7f8-a9b0-c1d2-e3f4a5b6c7d8',
        createdAt: '2023-03-01T00:00:00Z',
        updatedAt: '2023-03-02T00:00:00Z'
      },
      images: [],
      createdByUser: {
        id: 'b4c5d6e7-f8a9-b0c1-d2e3-f4a5b6c7d8e9',
        name: 'Carol White',
        fullName: 'Carol White',
        title: 'Editor',
        email: '<EMAIL>',
        role: 'editor',
        country: 'UK',
        contact: '1122334455',
        position: 'Editor',
        username: 'carolwhite',
        avatar: '',
        avatarColor: 'info',
        department: undefined,
        division: undefined,
        divisionId: null,
        threshold: undefined,
        phoneNumber: '*********',
        status: 'active',
        companyId: 'e0f1a2b3-c4d5-6e7f-8a9b-0c1d2e3f4a5b',
        subCompanyId: null,
        departmentId: null,
        permissionGroupId: '',
        sites: [],
        createdAt: '2023-03-01T00:00:00Z',
        updatedAt: '2023-03-02T00:00:00Z'
      }
    },
    totalPurchase: 150,
    percentPurchase: 80,
    percentReceived: 70,
    received: 105,
    notReceived: 45,
    quantityUnit: 'pcs'
  }
]

export const purchaseTableDummyData: PurchaseDocsTable[] = [
  {
    number: 'PO-2024-001',
    document: {
      id: 'doc-001',
      number: 'PO-2024-001',
      itemsCount: 5,
      itemsStock: 100,
      approvalsCount: 2,
      note: 'Urgent order for project X',
      cancelationNote: '',
      status: 'approved',
      companyId: 'comp-001',
      parentCompanyId: 'comp-000',
      departmentId: 'dept-001',
      siteId: 'site-001',
      originSite: { id: 'site-001', name: 'Main Warehouse' } as SiteType,
      destinationSite: { id: 'site-002', name: 'Project Site' } as SiteType,
      isClosed: false,
      createdAt: '2024-04-01T10:00:00Z',
      updatedAt: '2024-04-02T12:00:00Z',
      createdBy: 'user-001',
      items: [],
      approvals: [],
      department: { id: 'dept-001', name: 'Procurement', code: 'PRC' } as DepartmentType,
      site: { id: 'site-001', name: 'Main Warehouse' } as SiteType,
      forSite: { id: 'site-002', name: 'Project Site' } as SiteType,
      createdByUser: {
        id: 'user-001',
        name: 'John Doe',
        fullName: 'John Doe',
        title: 'Manager'
      } as unknown as UserType,
      closedAt: '',
      closedBy: { id: 'user-002', name: 'Jane Smith', fullName: 'Jane Smith', title: 'CEO' } as unknown as UserType
    },
    orderQty: 100,
    unit: 'pcs',
    received: 80,
    notReceived: 20,
    id: '1'
  },
  {
    number: 'PO-2024-002',
    document: {
      id: 'doc-002',
      number: 'PO-2024-002',
      itemsCount: 3,
      itemsStock: 50,
      approvalsCount: 1,
      note: 'Standard order',
      cancelationNote: '',
      status: 'pending',
      companyId: 'comp-001',
      parentCompanyId: 'comp-000',
      departmentId: 'dept-002',
      siteId: 'site-003',
      originSite: { id: 'site-003', name: 'Secondary Warehouse' } as SiteType,
      destinationSite: { id: 'site-004', name: 'Remote Site' } as SiteType,
      isClosed: false,
      createdAt: '2024-04-05T09:00:00Z',
      updatedAt: '2024-04-06T11:00:00Z',
      createdBy: 'user-003',
      items: [],
      approvals: [],
      department: { id: 'dept-002', name: 'Logistics', code: 'LOG' } as DepartmentType,
      site: { id: 'site-003', name: 'Secondary Warehouse' } as SiteType,
      forSite: { id: 'site-004', name: 'Remote Site' } as SiteType,
      createdByUser: {
        id: 'user-003',
        name: 'Alice Johnson',
        fullName: 'A Johnson',
        title: 'Director'
      } as unknown as UserType,
      closedAt: '',
      closedBy: { id: 'user-004', name: 'Bob Brown', fullName: 'B Brown', title: 'Manager' } as unknown as UserType
    },
    orderQty: 50,
    unit: 'boxes',
    received: 30,
    notReceived: 20,
    id: '2'
  },
  {
    number: 'PO-2024-003',
    document: {
      id: 'doc-003',
      number: 'PO-2024-003',
      itemsCount: 10,
      itemsStock: 200,
      approvalsCount: 3,
      note: 'Bulk order for new site',
      cancelationNote: '',
      status: 'approved',
      companyId: 'comp-002',
      parentCompanyId: 'comp-000',
      departmentId: 'dept-003',
      siteId: 'site-005',
      originSite: { id: 'site-005', name: 'Central Warehouse' } as SiteType,
      destinationSite: { id: 'site-006', name: 'New Site' } as SiteType,
      isClosed: true,
      createdAt: '2024-03-20T08:00:00Z',
      updatedAt: '2024-03-25T15:00:00Z',
      createdBy: 'user-005',
      items: [],
      approvals: [],
      department: { id: 'dept-003', name: 'Operations', code: 'OPS' } as DepartmentType,
      site: { id: 'site-005', name: 'Central Warehouse' } as SiteType,
      forSite: { id: 'site-006', name: 'New Site' } as SiteType,
      createdByUser: {
        id: 'user-005',
        name: 'Charlie Davis',
        fullName: 'C Davis',
        title: 'Director'
      } as unknown as UserType,
      closedAt: '2024-03-30T10:00:00Z',
      closedBy: { id: 'user-006', name: 'Diana Evans', fullName: 'D Evans', title: 'Manager' } as unknown as UserType
    },
    orderQty: 200,
    unit: 'units',
    received: 200,
    notReceived: 0,
    id: '3'
  }
]
