import truncateString from '@/core/utils/truncate'
import { PurchaseOrderStatisticsType } from '@/types/dashboardTypes'
import { IconButton, Typography, Tooltip } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

type RowActionType = {
  detail?: (id: string) => void
  purchaseList: PurchaseOrderStatisticsType[]
}

const columnHelper = createColumnHelper<PurchaseOrderStatisticsType>()

export const tablePurchaseColumns = (rowAction: RowActionType) => [
  columnHelper.display({
    id: 'number',
    header: '#',
    cell: ({ row }) => row.index + 1
  }),
  columnHelper.accessor('label', {
    header: 'Nama Item',
    cell: ({ row }) => (
      <Tooltip title={row.original.label}>
        <Typography sx={{ cursor: 'default' }}>{truncateString(row.original.label, 20)}</Typography>
      </Tooltip>
    )
  }),
  columnHelper.accessor('totalPurchase', {
    header: 'Jumlah Pembelian',
    cell: ({ row }) => `${row.original.totalPurchase} PCS`
  }),
  columnHelper.display({
    id: 'percentPurchase',
    header: '% Pembelian',
    cell: ({ row }) => {
      const { purchaseList } = rowAction
      const totalPurchase = purchaseList.reduce((acc, curr) => acc + curr.totalPurchase, 0)
      const percentPurchase = (row.original.totalPurchase / totalPurchase) * 100
      return <Typography color='primary'>{percentPurchase.toFixed(1)}%</Typography>
    }
  }),
  columnHelper.accessor('totalReceived', {
    header: 'Sudah Diterima',
    cell: ({ row }) => <Typography color='primary'>{`${row.original.totalReceived} PCS`}</Typography>
  }),
  columnHelper.accessor('totalRemaining', {
    header: 'Belum Diterima',
    cell: ({ row }) => <Typography color='error'>{`${row.original.totalRemaining} PCS`}</Typography>
  }),
  columnHelper.display({
    id: 'percentReceived',
    header: '% Penerimaan',
    cell: ({ row }) => {
      let percentReceived = (row.original.totalReceived / row.original.totalPurchase) * 100
      percentReceived = percentReceived === Infinity ? 0 : percentReceived
      percentReceived = row.original.totalRemaining === 0 ? 100 : percentReceived
      return <Typography color={percentReceived >= 100 ? 'primary' : 'error'}>{percentReceived.toFixed(1)}%</Typography>
    }
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <div className='flex gap-2 items-center'>
          <IconButton onClick={() => rowAction.detail?.(row.original.id)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </div>
      )
    }
  })
]
