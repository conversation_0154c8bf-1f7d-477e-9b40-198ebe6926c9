import { Card, CardContent, Tab, Tabs, Typography } from '@mui/material'
import <PERSON><PERSON><PERSON> from '@/components/chart/PieChart'
import Raw<PERSON>hart from '@/components/chart/RawChart'
import { useWarehouseDashboard } from '../context/WarehouseContext'
import { getKeys, normalizeBarChartData, normalizeLineChartData, optionsTab } from '../../config/utils'
import TabPanel from '@/core/components/mui/TabPanel'
import truncateString from '@/core/utils/truncate'

const PurchaseCard = () => {
  const { mgInStatistics, setChartModel, chartModel, mgInStatisticsChart } = useWarehouseDashboard()

  const barChart = mgInStatisticsChart?.map(item => ({ ...item, label: truncateString(item.label, 7) }))

  return (
    <Card>
      <CardContent className='space-y-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Total Pembelian</Typography>
          <Tabs
            value={chartModel?.purchase}
            onChange={(e, value) => setChartModel(prev => ({ ...prev, purchase: value }))}
            className='rounded-[8px] border'
            aria-label='tabs'
            sx={{
              '.MuiTab-root': { padding: '0px !important', minWidth: '48px' },
              '.Mui-selected': { backgroundColor: 'var(--mui-palette-primary-main)', color: 'white !important' }
            }}
          >
            {optionsTab.map(opt => (
              <Tab key={opt.value} value={opt.value} icon={opt.icon} aria-label={opt.value} />
            ))}
          </Tabs>
        </div>
        <TabPanel value={chartModel?.purchase} index={'pie'}>
          <PieChart
            data={mgInStatistics?.map(mgIn => ({ id: mgIn.label, value: mgIn.value, label: mgIn.label })) ?? []}
          />
        </TabPanel>
        <TabPanel value={chartModel?.purchase} index={'chart'}>
          <RawChart
            chartType='line'
            data={normalizeLineChartData(mgInStatisticsChart)}
            lineProps={{
              bottomTickRotation: -45,
              margin: { top: 50, right: 50, bottom: 70, left: 60 },
              legends: [
                {
                  anchor: 'bottom',
                  direction: 'row',
                  justify: false,
                  translateX: 4,
                  translateY: 70,
                  itemWidth: 90,
                  itemHeight: 20,
                  itemsSpacing: 4,
                  symbolSize: 7,
                  symbolShape: 'circle',
                  itemDirection: 'left-to-right',
                  itemTextColor: '#777',
                  effects: [
                    {
                      on: 'hover',
                      style: {
                        itemBackground: 'rgba(0, 0, 0, .03)',
                        itemOpacity: 1
                      }
                    }
                  ]
                }
              ]
            }}
          />
        </TabPanel>
        <TabPanel value={chartModel?.purchase} index={'list'}>
          <RawChart
            chartType='bar'
            data={normalizeBarChartData(barChart)}
            keys={getKeys(normalizeBarChartData(barChart))}
            indexBy='label'
            barProps={{
              legends: [
                {
                  dataFrom: 'keys',
                  anchor: 'bottom',
                  direction: 'row',
                  justify: false,
                  translateX: 20,
                  translateY: 76,
                  itemsSpacing: 2,
                  itemWidth: 100,
                  itemHeight: 20,
                  itemDirection: 'left-to-right',
                  itemOpacity: 0.85,
                  symbolSize: 12,
                  effects: [
                    {
                      on: 'hover',
                      style: {
                        itemOpacity: 1
                      }
                    }
                  ]
                }
              ]
            }}
          />
        </TabPanel>
      </CardContent>
    </Card>
  )
}

export default PurchaseCard
