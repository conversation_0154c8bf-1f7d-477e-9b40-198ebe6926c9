import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { FormControl, InputLabel, MenuItem, Select, Typography } from '@mui/material'
import { optionsPeriodic } from '../../config/utils'
import { StatisticParams } from '@/types/dashboardTypes'
import { useWarehouseDashboard } from '../context/WarehouseContext'
import { CompanySiteType } from '@/types/companyTypes'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type FilterProps = {
  onChangeFilter?: (filter: Partial<StatisticParams> & { period?: string }) => void
  filterData: StatisticParams & { period?: string }
}

const FilterData = (props: FilterProps) => {
  const { isWarehousePage, mgInSummary } = useWarehouseDashboard()

  const dateNow = new Date()
  const endDate = new Date(dateNow)
  endDate.setDate(dateNow.getDate() + 1)

  const [filterData, setFilterData] = useState<StatisticParams & { period?: string }>(props.filterData)
  const { ownSiteList } = useAuth()

  const handleChangeSite = (event: React.ChangeEvent<HTMLInputElement>) => {
    props?.onChangeFilter?.({ siteIds: event.target.value })
    setFilterData(prev => ({ ...prev, siteIds: event.target.value }))
  }

  const handleChangePeriod = (event: React.ChangeEvent<HTMLInputElement>) => {
    let startDate: Date | string = new Date()
    let period = 'TODAY'
    let endDate: Date | string
    switch (event.target.value) {
      case 'ALL':
        period = 'ALL'
        const currentYear = new Date().getFullYear()
        startDate = new Date(currentYear, 0, 1).toISOString()
        endDate = new Date(currentYear, 11, 31, 23, 59, 59).toISOString()
        break
      case 'TODAY':
        period = 'TODAY'
        endDate = new Date(startDate)
        endDate.setDate(dateNow.getDate() + 1)
        startDate = startDate.toISOString()
        endDate = endDate.toISOString()
        break
      case 'WEEK':
        period = 'WEEK'
        endDate = new Date(startDate)
        endDate.setDate(startDate.getDate() + 7)
        startDate = startDate.toISOString()
        endDate = endDate.toISOString()
        break
      case 'TWO_WEEK':
        period = 'TWO_WEEK'
        endDate = new Date(startDate)
        endDate.setDate(startDate.getDate() + 14)
        startDate = new Date().toISOString()
        endDate = endDate.toISOString()
        break
      case 'MONTH':
        period = 'MONTH'
        const startOfMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1)
        const endOfMonth = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0)
        startDate = startOfMonth.toISOString()
        endDate = endOfMonth.toISOString()
        break
      default:
        break
    }
    setFilterData(prev => ({ ...prev, startDate: startDate as string, endDate: endDate as string, period }))
    props?.onChangeFilter?.({ startDate: startDate as string, endDate: endDate as string, period })
  }

  return (
    <div className='flex justify-between items-baseline'>
      <div className='flex items-center w-2/3 gap-2'>
        <FormControl fullWidth size='small'>
          <InputLabel>Lokasi</InputLabel>
          <Select
            onChange={handleChangeSite}
            defaultValue={filterData.siteIds}
            size='small'
            label='Lokasi'
            className='bg-white'
          >
            <MenuItem value=''>Semua Lokasi</MenuItem>
            {ownSiteList
              ?.filter(site =>
                isWarehousePage ? CompanySiteType.WAREHOUSE === site.type : CompanySiteType.WORKSHOP === site.type
              )
              ?.map(site => (
                <MenuItem key={site.id} value={site.id}>
                  {site.name}
                </MenuItem>
              ))}
          </Select>
        </FormControl>
        <FormControl fullWidth size='small'>
          <InputLabel>Periode</InputLabel>
          <Select
            onChange={handleChangePeriod}
            defaultValue={filterData.period}
            size='small'
            label='Periode'
            className='bg-white'
          >
            {optionsPeriodic?.map(periodic => (
              <MenuItem key={periodic.value} value={periodic.value}>
                {periodic.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </div>
      <Typography>
        Last Update:{' '}
        {mgInSummary?.updatedAt
          ? formatDate(new Date(mgInSummary?.updatedAt), 'dd/MM/yyyy HH:mm', { locale: id })
          : '-'}
      </Typography>
    </div>
  )
}

export default FilterData
