import ChevronRight from '@/components/menu/svg/ChevronRight'
import { Card, CardContent, IconButton, Typography } from '@mui/material'
import { useNavigate } from 'react-router-dom'

type Props = {
  type: 'IN' | 'OUT'
  currentQuantity: number
  totalQuantity: number
  quantityUnit: string
  percentage?: number
}

const PurchaseSummaryCard = (props: Props) => {
  const { type, currentQuantity, totalQuantity, quantityUnit, percentage = null } = props
  const navigate = useNavigate()

  const calculatePercentage = (percentage: number | null, currentQuantity: number, totalQuantity: number): string => {
    if (percentage !== undefined && percentage !== null) {
      return `${percentage}%`
    }

    if (totalQuantity === 0) {
      return '0%'
    }

    const calculatedPercentage = (currentQuantity / totalQuantity) * 100

    return `${calculatedPercentage.toFixed(1)}%`
  }

  return (
    <Card>
      <CardContent className='space-y-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>
            Barang {type === 'IN' ? 'Masuk' : 'Keluar'} dari {type === 'IN' ? 'Pembelian' : 'Barang <PERSON>k'}
          </Typography>
          <IconButton onClick={() => navigate(type === 'IN' ? 'purchase' : 'outgoing')} color='primary'>
            <ChevronRight />
          </IconButton>
        </div>
        <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex justify-between items-center'>
          <div className='flex gap-4 items-center'>
            <Typography
              sx={{ fontWeight: 600, fontSize: '40px', lineHeight: '32px', letterSpacing: '0.15px' }}
              variant='h3'
            >
              {currentQuantity}
            </Typography>
            <Typography variant='h5' color='secondary'>
              {`/ ${totalQuantity} ${quantityUnit}`}
            </Typography>
          </div>
          <Typography variant='h4' color={type === 'IN' ? 'primary' : 'error'}>
            {calculatePercentage(percentage, currentQuantity, totalQuantity)}
          </Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default PurchaseSummaryCard
