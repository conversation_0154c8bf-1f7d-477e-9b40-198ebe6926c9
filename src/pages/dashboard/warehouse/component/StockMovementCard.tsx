import { Card, CardContent, Tab, Tabs, Typography } from '@mui/material'
import { getKeys, normalizeBarArrayData, normalizeLineArrayData, optionsTab } from '../../config/utils'
import TabPanel from '@/core/components/mui/TabPanel'
import StockChart, { LineDataType } from '@/components/chart/RawChart'
import { useWarehouseDashboard } from '../context/WarehouseContext'

type Props = {
  type: 'IN' | 'OUT'
}

const StockMovementCard = (props: Props) => {
  const { type } = props
  const { chartModel, setChartModel, mgInChartData, mgOutChartData } = useWarehouseDashboard()

  const optTab = optionsTab.filter(opt => opt.value !== 'pie')

  return (
    <Card>
      <CardContent className='space-y-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Barang {type === 'IN' ? 'Masuk' : 'Keluar'}</Typography>
          <Tabs
            value={type === 'IN' ? chartModel?.mgIn : chartModel?.mgOut}
            onChange={(e, value) =>
              setChartModel(prev => ({ ...prev, ...(type === 'IN' ? { mgIn: value } : { mgOut: value }) }))
            }
            className='rounded-[8px] border'
            aria-label='tabs'
            sx={{
              '.MuiTab-root': { padding: '0px !important', minWidth: '48px' },
              '.Mui-selected': { backgroundColor: 'var(--mui-palette-primary-main)', color: 'white !important' }
            }}
          >
            {optTab.map(opt => (
              <Tab key={opt.value} value={opt.value} icon={opt.icon} aria-label={opt.value} />
            ))}
          </Tabs>
        </div>
        <TabPanel value={type === 'IN' ? chartModel?.mgIn : chartModel?.mgOut} index={'chart'}>
          <StockChart
            chartType='line'
            data={normalizeLineArrayData(
              type === 'IN' ? (mgInChartData as LineDataType[]) : (mgOutChartData as LineDataType[])
            )}
            lineProps={{
              bottomTickRotation: -45
            }}
          />
        </TabPanel>
        <TabPanel value={type === 'IN' ? chartModel?.mgIn : chartModel?.mgOut} index={'list'}>
          <StockChart
            chartType='bar'
            data={normalizeBarArrayData(type === 'IN' ? mgInChartData : mgOutChartData)}
            keys={getKeys(normalizeBarArrayData(type === 'IN' ? mgInChartData : mgOutChartData))}
            indexBy='date'
            barProps={{
              colors: { scheme: 'category10' },
              legends: [
                {
                  dataFrom: 'keys',
                  anchor: 'bottom',
                  direction: 'row',
                  justify: false,
                  translateX: 20,
                  translateY: 76,
                  itemsSpacing: 2,
                  itemWidth: 100,
                  itemHeight: 20,
                  itemDirection: 'left-to-right',
                  itemOpacity: 0.85,
                  symbolSize: 12,
                  effects: [
                    {
                      on: 'hover',
                      style: {
                        itemOpacity: 1
                      }
                    }
                  ]
                }
              ]
            }}
          />
        </TabPanel>
      </CardContent>
    </Card>
  )
}

export default StockMovementCard
