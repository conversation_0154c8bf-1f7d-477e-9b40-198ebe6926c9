import { PieDataType } from '@/components/chart/PieChart'
import { useQuery } from '@tanstack/react-query'
import { createContext, useContext, useEffect, useState } from 'react'
import { dummyPurchaseData } from '../purchasing/config/dummyData'
import { NavigateFunction, useLocation, useNavigate, useParams } from 'react-router-dom'
import {
  PurchaseSummaryType,
  PurchaseTableColumns,
  MgInStatisticsChartType,
  StatisticParams,
  MgInStatistics,
  MgOutStatistics,
  PurchaseOrderStatisticsType
} from '@/types/dashboardTypes'
import { warehouseDummyData } from '../../config/dummy'
import DashboardQueryMethods, { PO_MG_IN_QUERY_KEY, PO_MG_OUT_QUERY_KEY } from '@/api/services/dashboard/query'
import { BarDataType, LineDataType } from '@/components/chart/RawChart'
import { useLocalStorage } from 'react-use'
import { useAuth } from '@/contexts/AuthContext'

type WarehouseContextProps = {
  warehouseData: PieDataType[]
  purchaseList: PurchaseTableColumns[]
  purchaseData: PurchaseTableColumns
  selectedPurchaseId: string | null
  purchaseSummary: PurchaseSummaryType
  setSelectedPurchaseId: React.Dispatch<React.SetStateAction<string | null>>
  navigate: NavigateFunction
  filterData: StatisticParams & { period?: string }
  setFilterData: React.Dispatch<React.SetStateAction<StatisticParams & { period?: string }>>
  mgInStatistics: MgInStatisticsChartType[]
  mgInStatisticsChart: PurchaseOrderStatisticsType[]
  mgInSummary: MgInStatistics
  mgOutSummary: MgOutStatistics
  mgInChartData: (BarDataType | LineDataType)[]
  mgOutChartData: (BarDataType | LineDataType)[]
  chartModel: {
    mgIn: string
    mgOut: string
    purchase: string
  }
  setChartModel: React.Dispatch<
    React.SetStateAction<{
      mgIn: string
      mgOut: string
      purchase: string
    }>
  >
  isWarehousePage: boolean
  isWorkshopPage: boolean
}

const WarehouseContext = createContext<WarehouseContextProps>({} as WarehouseContextProps)

export const useWarehouseDashboard = () => {
  const context = useContext(WarehouseContext)
  if (context === undefined) {
    throw new Error('useWarehouseDashboard must be used within a WarehouseContextProvider')
  }
  return context
}

export const WarehouseContextProvider = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const { pId } = useParams()
  const { userProfile } = useAuth()

  const isWarehousePage = location.pathname.includes('warehouse')
  const isWorkshopPage = location.pathname.includes('workshop')

  const [selectedPurchaseId, setSelectedPurchaseId] = useState<string | null>(pId)
  const dateNow = new Date()
  const endDate = new Date(dateNow)
  endDate.setDate(dateNow.getDate() + 1)

  const defaultFilterData = {
    siteIds: '',
    departmentId: '',
    period: 'TODAY',
    startDate: dateNow.toISOString(),
    endDate: endDate.toISOString()
  }

  const [chartModel, setChartModel] = useState<{
    mgIn: string
    mgOut: string
    purchase: string
  }>({
    mgIn: 'chart',
    mgOut: 'chart',
    purchase: 'pie'
  })
  const [filterData, setFilterData] = useLocalStorage<StatisticParams & { period?: string }>(
    'filterDashboard',
    defaultFilterData
  )

  const { data: mgInStatistics } = useQuery({
    enabled: chartModel.purchase === 'pie',
    queryKey: [PO_MG_IN_QUERY_KEY, 'CHART', JSON.stringify(filterData), location.pathname],
    queryFn: () => {
      const siteIds = isWarehousePage
        ? userProfile?.sites
            ?.filter(site => site.type === 'WAREHOUSE')
            ?.map(site => site.id)
            .join(',')
        : userProfile?.sites
            ?.filter(site => site.type === 'WORKSHOP')
            ?.map(site => site.id)
            .join(',')
      return DashboardQueryMethods.getPurchaseOrdersInCategories({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : siteIds,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      })
    },
    placeholderData: [] as MgInStatisticsChartType[]
  })

  const { data: mgInStatisticsChart } = useQuery({
    enabled: chartModel.purchase !== 'pie',
    queryKey: [PO_MG_IN_QUERY_KEY, 'LINE_CHART', JSON.stringify(filterData), location.pathname],
    queryFn: async () => {
      const siteIds = isWarehousePage
        ? userProfile?.sites
            ?.filter(site => site.type === 'WAREHOUSE')
            ?.map(site => site.id)
            .join(',')
        : userProfile?.sites
            ?.filter(site => site.type === 'WORKSHOP')
            ?.map(site => site.id)
            .join(',')
      return (
        (
          await DashboardQueryMethods.getPurchaseOrdersByItem({
            startDate: filterData.startDate,
            endDate: filterData.endDate,
            siteIds: !!filterData.siteIds ? filterData.siteIds : siteIds,
            departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
          })
        ).items ?? []
      )
    },
    placeholderData: [] as PurchaseOrderStatisticsType[]
  })

  const { data: mgInSummary } = useQuery({
    queryKey: [PO_MG_IN_QUERY_KEY, 'SUMMARY', JSON.stringify(filterData), location.pathname],
    queryFn: () => {
      const siteIds = isWarehousePage
        ? userProfile?.sites
            ?.filter(site => site.type === 'WAREHOUSE')
            ?.map(site => site.id)
            .join(',')
        : userProfile?.sites
            ?.filter(site => site.type === 'WORKSHOP')
            ?.map(site => site.id)
            .join(',')
      return DashboardQueryMethods.getPurchaseOrdersInSummary({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : siteIds,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      })
    }
  })

  const { data: mgOutSummary } = useQuery({
    queryKey: [PO_MG_OUT_QUERY_KEY, 'SUMMARY', JSON.stringify(filterData), location.pathname],
    queryFn: () => {
      const siteIds = isWarehousePage
        ? userProfile?.sites
            ?.filter(site => site.type === 'WAREHOUSE')
            ?.map(site => site.id)
            .join(',')
        : userProfile?.sites
            ?.filter(site => site.type === 'WORKSHOP')
            ?.map(site => site.id)
            .join(',')
      return DashboardQueryMethods.getPurchaseOrdersOut({
        startDate: filterData.startDate,
        endDate: filterData.endDate,
        siteIds: !!filterData.siteIds ? filterData.siteIds : siteIds,
        departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
      })
    }
  })

  const { data: purchaseSummary } = useQuery({
    queryKey: ['PURCHASE_SUMMARY_KEY'],
    queryFn: () => {
      return new Promise<PurchaseSummaryType>(resolve => {
        setTimeout(() => {
          resolve({
            purchase: dummyPurchaseData.reduce((acc, curr) => acc + curr.totalPurchase, 0),
            received: dummyPurchaseData.reduce((acc, curr) => acc + curr.received, 0),
            nonReceived: dummyPurchaseData.reduce((acc, curr) => acc + curr.notReceived, 0)
          })
        }, 500)
      })
    }
  })

  const { data: mgInChartData } = useQuery({
    queryKey: ['MATERIALS_IN_KEY', chartModel.mgIn, JSON.stringify(filterData), location.pathname],
    queryFn: () => {
      const siteIds = isWarehousePage
        ? userProfile?.sites
            ?.filter(site => site.type === 'WAREHOUSE')
            ?.map(site => site.id)
            .join(',')
        : userProfile?.sites
            ?.filter(site => site.type === 'WORKSHOP')
            ?.map(site => site.id)
            .join(',')
      if (chartModel.mgIn === 'chart') {
        return DashboardQueryMethods.getMaterialsInLine({
          startDate: filterData.startDate,
          endDate: filterData.endDate,
          siteIds: !!filterData.siteIds ? filterData.siteIds : siteIds,
          departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
        })
      }
      if (chartModel.mgIn === 'list') {
        return DashboardQueryMethods.getMaterialsInBar({
          startDate: filterData.startDate,
          endDate: filterData.endDate,
          siteIds: !!filterData.siteIds ? filterData.siteIds : siteIds,
          departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
        })
      }
    },
    placeholderData: [] as (LineDataType | BarDataType)[]
  })

  const { data: mgOutChartData } = useQuery({
    queryKey: ['MATERIALS_OUT_KEY', chartModel.mgOut, JSON.stringify(filterData), location.pathname],
    queryFn: () => {
      const siteIds = isWarehousePage
        ? userProfile?.sites
            ?.filter(site => site.type === 'WAREHOUSE')
            ?.map(site => site.id)
            .join(',')
        : userProfile?.sites
            ?.filter(site => site.type === 'WORKSHOP')
            ?.map(site => site.id)
            .join(',')
      if (chartModel.mgOut === 'chart') {
        return DashboardQueryMethods.getMaterialsOutLine({
          startDate: filterData.startDate,
          endDate: filterData.endDate,
          siteIds: !!filterData.siteIds ? filterData.siteIds : siteIds,
          departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
        })
      }
      if (chartModel.mgOut === 'list') {
        return DashboardQueryMethods.getMaterialsOutBar({
          startDate: filterData.startDate,
          endDate: filterData.endDate,
          siteIds: !!filterData.siteIds ? filterData.siteIds : siteIds,
          departmentId: !!filterData.departmentId ? filterData.departmentId : undefined
        })
      }
    },
    placeholderData: [] as (LineDataType | BarDataType)[]
  })

  const { data: warehouseData } = useQuery({
    queryKey: ['WAREHOUSE_DATA_KEY'],
    queryFn: () => {
      return new Promise<PieDataType[]>(resolve => {
        setTimeout(() => {
          resolve(warehouseDummyData)
        }, 700)
      })
    }
  })

  const { data: purchaseList } = useQuery({
    queryKey: ['PURCHASE_LIST_KEY'],
    queryFn: () => {
      return new Promise<PurchaseTableColumns[]>(resolve => {
        setTimeout(() => {
          resolve(dummyPurchaseData)
        }, 500)
      })
    }
  })

  const { data: purchaseData } = useQuery({
    enabled: !!pId,
    queryKey: ['PURCHASE_QUERY_KEY', pId],
    queryFn: () => {
      return new Promise<PurchaseTableColumns>(resolve => {
        setTimeout(() => {
          resolve(dummyPurchaseData.find(purchase => purchase.id === pId))
        }, 500)
      })
    }
  })

  useEffect(() => {
    setSelectedPurchaseId(pId)
  }, [pId])

  const value = {
    selectedPurchaseId,
    setSelectedPurchaseId,
    warehouseData,
    purchaseList,
    purchaseData,
    purchaseSummary,
    navigate,
    filterData,
    setFilterData,
    mgInStatistics,
    mgInSummary,
    mgOutSummary,
    chartModel,
    setChartModel,
    mgInStatisticsChart,
    mgInChartData,
    mgOutChartData,
    isWarehousePage,
    isWorkshopPage
  }

  return <WarehouseContext.Provider value={value}>{children}</WarehouseContext.Provider>
}
