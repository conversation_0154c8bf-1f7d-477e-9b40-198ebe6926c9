import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListParams } from '@/types/payload'
import React from 'react'

type UnitContextType = {
  unitParams: ListParams
  setPartialUnitParams: (fieldName: string, value: any) => void
  setUnitParams: React.Dispatch<React.SetStateAction<ListParams>>
  onSubmitHandler: () => void
}

const UnitContext = React.createContext<UnitContextType>({} as UnitContextType)

export const useUnit = () => {
  const context = React.useContext(UnitContext)
  if (context === undefined) {
    throw new Error('useUnit must be used within a UnitContextProvider')
  }
  return context
}

export const UnitContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { setConfirmState } = useMenu()
  const [unitParams, setPartialUnitParams, setUnitParams] = usePartialState<ListParams>({
    limit: 10,
    page: 1
  })

  const onSubmitHandler = () => {
    setConfirmState({
      open: true,
      title: 'Tambah Unit',
      content: 'Apakah kamu yakin ingin menambahkan unit ini?',
      confirmText: 'Ya',
      onConfirm: () => {
        console.log('submit')
      }
    })
  }

  const value = {
    unitParams,
    setPartialUnitParams,
    setUnitParams,
    onSubmitHandler
  }
  return <UnitContext.Provider value={value}>{children}</UnitContext.Provider>
}
