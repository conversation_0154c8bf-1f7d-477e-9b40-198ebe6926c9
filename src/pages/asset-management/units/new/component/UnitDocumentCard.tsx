import { But<PERSON>, Card, CardContent, TextField, Typography, Grid } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useCallback, useMemo, useState } from 'react'
import Table from '@/components/table'
import DetailHistoryUnitDoc from '@/components/dialogs/detail-history-unit-doc'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'

type UnitDocumentCardProps = {
  documentType: 'BPKB' | 'STNK' | 'KIR' | 'Asuransi'
}

const UnitDocumentCard = () => {
  const [openHistory, setOpenHistory] = useState(false)
  const [insurances, setInsurances] = useState<any[]>([])
  const [activeDocument, setActiveDocument] = useState<UnitDocumentCardProps['documentType'] | null>(null)
  const DocumentItem = useCallback((type: UnitDocumentCardProps['documentType']) => {
    return (
      <div className='flex flex-col gap-2'>
        <div className='flex justify-between items-center'>
          <Typography variant='h6'>{type}</Typography>
        </div>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <div className='flex items-center gap-2'>
              <TextField label={`Unggah ${type}`} variant='outlined' fullWidth />
              <Button variant='contained'>Unggah</Button>
            </div>
          </Grid>
          <Grid item xs={12} md={6}>
            <AppReactDatepicker
              boxProps={{ className: 'is-full' }}
              // selected={value ? toDate(value) : undefined}
              // onChange={(date: Date) => onChange(date.toISOString())}
              dateFormat='eeee dd/MM/yyyy'
              minDate={new Date()}
              customInput={
                <TextField fullWidth label='Tanggal Mulai Berlaku' placeholder='Pilih Tanggal' className='flex-1' />
              }
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <AppReactDatepicker
              boxProps={{ className: 'is-full' }}
              // selected={value ? toDate(value) : undefined}
              // onChange={(date: Date) => onChange(date.toISOString())}
              dateFormat='eeee dd/MM/yyyy'
              minDate={new Date()}
              customInput={
                <TextField fullWidth label='Tanggal Habis Berlaku' placeholder='Pilih Tanggal' className='flex-1' />
              }
            />
          </Grid>
        </Grid>
      </div>
    )
  }, [])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <Typography variant='h5'>Dokumen Unit</Typography>
          <div className='grid grid-cols-1 gap-3'>
            {DocumentItem('BPKB')}
            {DocumentItem('KIR')}
            {DocumentItem('STNK')}
            {DocumentItem('BPKB')}
            <div className='flex flex-col gap-4'>
              <div className='flex justify-between items-center'>
                <Typography variant='h6'>Asuransi (opsional)</Typography>
              </div>
              <div className='rounded-[8px] bg-[#4C4E640D] p-4 flex flex-col gap-4'>
                <Typography>Asuransi 1</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      className='bg-white'
                      fullWidth
                      placeholder='Nama Asuransi'
                      label='Nama Asuransi'
                      variant='outlined'
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex items-center gap-2'>
                      <TextField label={`Unggah Asuransi`} className='bg-white' variant='outlined' fullWidth />
                      <Button variant='contained'>Unggah</Button>
                    </div>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <AppReactDatepicker
                      boxProps={{ className: 'is-full' }}
                      // selected={value ? toDate(value) : undefined}
                      // onChange={(date: Date) => onChange(date.toISOString())}
                      dateFormat='eeee dd/MM/yyyy'
                      minDate={new Date()}
                      customInput={
                        <TextField
                          fullWidth
                          label='Tanggal Mulai Berlaku'
                          placeholder='Pilih Tanggal'
                          className='flex-1 bg-white'
                        />
                      }
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <AppReactDatepicker
                      boxProps={{ className: 'is-full' }}
                      // selected={value ? toDate(value) : undefined}
                      // onChange={(date: Date) => onChange(date.toISOString())}
                      dateFormat='eeee dd/MM/yyyy'
                      minDate={new Date()}
                      customInput={
                        <TextField
                          fullWidth
                          label='Tanggal Habis Berlaku'
                          placeholder='Pilih Tanggal'
                          className='flex-1 bg-white'
                        />
                      }
                    />
                  </Grid>
                </Grid>
              </div>
              <Button variant='outlined' color='primary' size='small'>
                Tambah Asuransi
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      <DetailHistoryUnitDoc open={openHistory} setOpen={setOpenHistory} type={activeDocument} />
    </>
  )
}

export default UnitDocumentCard
