import { Card, CardContent, Typography, Grid, TextField, FormControl, InputLabel, Select } from '@mui/material'
import { UnitType } from '@/types/companyTypes'

type UnitDetailProps = {
  unitData?: UnitType
}

const UnitDetail = ({ unitData }: UnitDetailProps) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Unit</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <TextField placeholder='Kode Unit' label='Kode Unit' />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField placeholder='Kode Activa' label='Kode Activa' />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id='unit-category-label'>Kategori Unit</InputLabel>
              <Select
                labelId='unit-category-label'
                id='unit-category'
                label='Kategori Unit'
                defaultValue='Alat Berat'
              ></Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id='unit-sub-category-label'>Tipe Equipment</InputLabel>
              <Select
                labelId='unit-sub-category-label'
                id='unit-sub-category'
                label='Tipe Equipment'
                defaultValue='Operational'
              ></Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField placeholder='Merk Unit' label='Merk Unit' />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField placeholder='Tipe Unit' label='Tipe Unit' />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField placeholder='Nomor Rangka' label='Nomor Rangka' />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField placeholder='Nomor Mesin' label='Nomor Mesin' />
          </Grid>
          <Grid item xs={12}>
            <TextField fullWidth placeholder='Plat Nomor' label='Plat Nomor' />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default UnitDetail
