import { defaultImageList } from '@/components/dialogs/add-item-dialog'
import PhotoPicker from '@/components/PhotoPicker'
import { ImageItemType } from '@/types/companyTypes'
import { Card, CardContent, Typography } from '@mui/material'
import { useState } from 'react'

const ItemPhotosCard = () => {
  const [imageList, setImageList] = useState<ImageItemType[]>(defaultImageList)

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <Typography variant='h5'>Foto Unit</Typography>
        <div className='flex flex-col gap-2'>
          <div className='flex gap-5 overflow-y-hidden max-sm:px-2'>
            {imageList?.map((item, index) => (
              <PhotoPicker
                key={`${item.content}_${index}`}
                content={item.content}
                // disabled={isLoading}
                onPicked={(content, fileName) => {
                  setImageList(current => {
                    const tempCurrent = [...current]
                    tempCurrent[index] = { content, fileName }
                    return tempCurrent
                  })
                }}
                onRemoved={() => {
                  setImageList(current => {
                    const tempCurrent = [...current]
                    tempCurrent[index] = { content: '', fileName: '' }
                    return tempCurrent
                  })
                }}
              />
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default ItemPhotosCard
