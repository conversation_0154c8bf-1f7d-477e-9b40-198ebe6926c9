import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { Card, CardContent, FormControl, Grid, InputLabel, Select, TextField, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const AdditionalDetailCard = () => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Data Lainnya</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <TextField fullWidth placeholder='Aset Milik' label='Aset Milik' />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel id='unit-category-label'>Lokasi Unit</InputLabel>
              <Select
                labelId='unit-category-label'
                id='unit-category'
                label='Lokasi Unit'
                defaultValue='Sanga - Sanga'
              ></Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField fullWidth placeholder='Spesifikasi (opsional)' label='Spesifikasi (opsional)' />
          </Grid>
          <Grid item xs={12}>
            <AppReactDatepicker
              boxProps={{ className: 'is-full' }}
              // selected={value ? toDate(value) : undefined}
              // onChange={(date: Date) => onChange(date.toISOString())}
              dateFormat='eeee dd/MM/yyyy'
              minDate={new Date()}
              customInput={
                <TextField fullWidth label='Tanggal Pembelian' placeholder='Pilih Tanggal' className='flex-1' />
              }
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel id='unit-category-label'>Kondisi Unit</InputLabel>
              <Select
                labelId='unit-category-label'
                id='unit-category'
                label='Kondisi Unit'
                placeholder='Pilih Kondisi'
                defaultValue='Break Down'
              ></Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel id='unit-category-label'>Tipe SM</InputLabel>
              <Select
                labelId='unit-category-label'
                id='unit-category'
                label='Tipe SM'
                placeholder='Pilih Tipe SM'
              ></Select>
            </FormControl>
          </Grid>
          <Grid item xs={12}>
            <TextField multiline rows={4} fullWidth placeholder='Keterangan' label='Keterangan' />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default AdditionalDetailCard
