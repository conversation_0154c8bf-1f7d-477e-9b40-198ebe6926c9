import { Card, CardContent, Typography } from '@mui/material'

const QRCard = () => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <Typography variant='h5'>Kode QR Unit</Typography>
        <div className='grid grid-cols-5 gap-2'>
          <img className='aspect-square w-[225px]' alt='QR Code' />
        </div>
      </CardContent>
    </Card>
  )
}

export default QRCard
