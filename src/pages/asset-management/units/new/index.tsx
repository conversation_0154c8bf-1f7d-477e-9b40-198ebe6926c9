import { Bread<PERSON>rum<PERSON>, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import UnitDetail from './component/UnitDetail'
import AdditionalDetailCard from './component/AdditionalDetail'
import ItemPhotosCard from './component/ItemPhotosCard'
import UnitDocumentCard from './component/UnitDocumentCard'
import { useUnit } from '../context/UnitContext'

const UnitAssetNewPage = () => {
  const { onSubmitHandler } = useUnit()
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Manajemen Aset</Typography>
          </Link>
          <Link to='/company-data/assets/unit' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Aset Unit</Typography>
          </Link>
          <Typography>Tambah Unit</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col gap-1'>
            <Typography variant='h4'>Tambah Unit</Typography>
            <Typography variant='caption'>Lengkapi data unit dan tambahkan ke daftar aset unit</Typography>
          </div>
          <div className='flex gap-2 items-center'>
            <Button variant='outlined' color='secondary'>
              Batalkan
            </Button>
            <Button variant='contained' onClick={onSubmitHandler} color='primary'>
              Tambah Unit
            </Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <UnitDetail />
          </Grid>
          <Grid item xs={12}>
            <UnitDocumentCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <AdditionalDetailCard />
          </Grid>
          <Grid item xs={12}>
            <ItemPhotosCard />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default UnitAssetNewPage
