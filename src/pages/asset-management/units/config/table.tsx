import { DEFAULT_CATEGORY } from '@/data/default/category'
import { CodeNameType } from '@/types/common'
import { SiteType } from '@/types/companyTypes'
import { Chip, Grid, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

type AssetUnitType = {
  number: string
  id: string
  code: string
  category: CodeNameType
  status: string
  name: string
  type: string
  site: SiteType
  createdAt: string
}

type RowActionType = {
  detail?: (AssetUnitType) => void
  edit?: (AssetUnitType) => void
  delete?: (AssetUnitType) => void
}

const columnHelper = createColumnHelper<AssetUnitType>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'Kode Unit',
    cell: ({ row }) => <Typography color='primary'>{row.original.number}</Typography>
  }),
  columnHelper.accessor('code', {
    header: 'Kode Aktiva',
    cell: ({ row }) => <Typography color='primary'>{row.original.code}</Typography>
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => <Chip label={row.original.status} color='default' variant='tonal' />
  }),
  columnHelper.accessor('category.name', {
    header: 'Kategori Unit',
    cell: ({ row }) => row.original.category?.name ?? DEFAULT_CATEGORY.name
  }),
  columnHelper.accessor('type', {
    header: 'Type Equipment'
  }),
  columnHelper.accessor('site.name', {
    header: 'Lokasi Unit'
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={2}>
        <Grid item>
          <IconButton>
            <i className='ri-delete-bin-line text-primary' />
          </IconButton>
        </Grid>
        <Grid item>
          <IconButton>
            <i className='ri-eye-line text-primary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
