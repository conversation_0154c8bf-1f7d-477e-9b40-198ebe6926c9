import { But<PERSON>, Card, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useCallback, useMemo, useState } from 'react'
import { insuranceTableColumns } from '../config/table'
import Table from '@/components/table'
import DetailHistoryUnitDoc from '@/components/dialogs/detail-history-unit-doc'

type UnitDocumentCardProps = {
  documentType: 'BPKB' | 'STNK' | 'KIR' | 'Asuransi'
}

const UnitDocumentCard = () => {
  const [openHistory, setOpenHistory] = useState(false)
  const [activeDocument, setActiveDocument] = useState<UnitDocumentCardProps['documentType'] | null>(null)
  const DocumentItem = useCallback((type: UnitDocumentCardProps['documentType']) => {
    return (
      <div className='rounded-[8px] bg-[#4C4E640D] p-4 flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h6'>{type}</Typography>
          <Button
            variant='outlined'
            size='small'
            onClick={() => {
              setOpenHistory(true)
              setActiveDocument(type)
            }}
          >
            Lihat Riwayat
          </Button>
        </div>
        <div className='flex flex-col gap-1'>
          <small>Dokumen {type}</small>
          <div className='flex justify-between items-end'>
            <Typography variant='h6'>BPKB-99834-AUD</Typography>
            <Typography color='primary' sx={{ textDecoration: 'underline' }}>
              Unduh
            </Typography>
          </div>
        </div>
        <div className='flex justify-between items-center'>
          <div className='flex flex-col gap-1'>
            <small>Tanggal Mulai berlaku</small>
            <Typography variant='h6'>{formatDate(new Date(), 'dd/MM/yyyy', { locale: id })}</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Tanggal Habis berlaku</small>
            <Typography variant='h6'>{formatDate(new Date(), 'dd/MM/yyyy', { locale: id })}</Typography>
          </div>
        </div>
      </div>
    )
  }, [])

  const tableOptions = useMemo(
    () => ({
      data: [],
      columns: insuranceTableColumns(() => {}),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    []
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <Typography variant='h5'>Dokumen Unit</Typography>
          <div className='grid grid-cols-1 gap-3'>
            {DocumentItem('BPKB')}
            {DocumentItem('KIR')}
            {DocumentItem('STNK')}
            {DocumentItem('BPKB')}
            <div className='rounded-[8px] bg-[#4C4E640D] p-4 flex flex-col gap-4'>
              <div className='flex justify-between items-center'>
                <Typography variant='h6'>Asuransi</Typography>
                <Button
                  variant='outlined'
                  size='small'
                  onClick={() => {
                    setOpenHistory(true)
                    setActiveDocument('Asuransi')
                  }}
                >
                  Lihat Riwayat
                </Button>
              </div>
              <div className='rounded-md shadow-sm'>
                <Table
                  headerColor='green'
                  table={table}
                  className='!bg-white'
                  emptyLabel={
                    <td
                      colSpan={table.getVisibleFlatColumns().length}
                      className='text-center h-60 bg-white rounded-b-md'
                    >
                      <Typography> Belum ada Asuransi</Typography>
                      <Typography className='text-sm text-gray-400'>
                        Semua asuransi yang telah kamu buat akan ditampilkan di sini
                      </Typography>
                    </td>
                  }
                  disablePagination
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <DetailHistoryUnitDoc open={openHistory} setOpen={setOpenHistory} type={activeDocument} />
    </>
  )
}

export default UnitDocumentCard
