import { Card, CardContent, Typography } from '@mui/material'

const ItemPhotosCard = () => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <Typography variant='h5'>Foto Unit</Typography>
        <div className='grid grid-cols-5 gap-2'>
          <img className='aspect-square rounded-3xl' alt='img' />
          <img className='aspect-square rounded-3xl' alt='img' />
          <img className='aspect-square rounded-3xl' alt='img' />
          <img className='aspect-square rounded-3xl' alt='img' />
          <img className='aspect-square rounded-3xl' alt='img' />
        </div>
      </CardContent>
    </Card>
  )
}

export default ItemPhotosCard
