import { Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const AdditionalDetailCard = () => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Data Lainnya</Typography>
        </div>
        <div className='grid md:grid-cols-1 gap-4'>
          <div className='flex flex-col gap-1'>
            <small>Aset Milik</small>
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              PT Equalindo 360
            </p>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Lokasi Unit</small>
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'><PERSON><PERSON> - <PERSON>a</p>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Spesifikasi</small>
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>-</p>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Tanggal Pembelian</small>
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {formatDate(new Date(), 'dd/MM/yyyy', { locale: id })}
            </p>
          </div>
          <div className='grid md:grid-cols-2 gap-4'>
            <div className='flex flex-col gap-1'>
              <small>Kondisi Unit</small>
              <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>Break Down</p>
            </div>
            <div className='flex flex-col gap-1'>
              <small>Tipe SM</small>
              <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>SM 250</p>
            </div>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Keterangan</small>
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>-</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default AdditionalDetailCard
