import { Breadcrumbs, <PERSON>, Grid, Typo<PERSON>, But<PERSON> } from '@mui/material'
import { Link } from 'react-router-dom'
import UnitDetail from './component/UnitDetail'
import AdditionalDetailCard from './component/AdditionalDetail'
import ItemPhotosCard from './component/ItemPhotosCard'
import QRCard from './component/QrCodeCard'
import CreatedByCard from './component/CreatedByCard'
import UnitDocumentCard from './component/UnitDocumentCard'

const UnitDetailPage = () => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Manajemen Aset</Typography>
          </Link>
          <Link to='/company-data/assets/unit' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Aset Unit</Typography>
          </Link>
          <Typography>Detil Unit</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-center'>
          <div className='flex flex-col'>
            <div className='flex gap-2 items-center'>
              <Typography variant='h5'>Volvo XC90</Typography>
              <Chip label='Status' size='small' color='default' variant='tonal' />
            </div>
            <Typography variant='caption'>Kode Unit: 0110192029</Typography>
          </div>
          <div className='flex gap-2 items-center'>
            {/* <Button
              className='is-full sm:is-auto'
              startIcon={<i className='ri-upload-2-line' />}
              color='secondary'
              variant='outlined'
            >
              Ekspor
            </Button>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ic-outline-local-printshop' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button> */}
            <Button color='error' variant='outlined'>
              Hapus Barang
            </Button>
            <Button variant='contained'>Edit Barang</Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <UnitDetail />
          </Grid>
          <Grid item xs={12}>
            <UnitDocumentCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <AdditionalDetailCard />
          </Grid>
          <Grid item xs={12}>
            <ItemPhotosCard />
          </Grid>
          <Grid item xs={12}>
            <QRCard />
          </Grid>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default UnitDetailPage
