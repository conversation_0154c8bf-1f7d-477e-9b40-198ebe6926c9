import { Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type InsuranceType = {
  name: string
  description: string
  expiredAt: string
  startAt: string
  id: string
}

const columnHelper = createColumnHelper<InsuranceType>()

export const insuranceTableColumns = (onHistory: () => void) => [
  columnHelper.accessor('name', {
    header: '<PERSON><PERSON>'
  }),
  columnHelper.accessor('startAt', {
    header: 'Tanggal Mulai',
    cell: ({ row }) => formatDate(new Date(row.original.startAt), 'dd/MM/yyyy', { locale: id })
  }),
  columnHelper.accessor('expiredAt', {
    header: 'Tanggal Berakhir',
    cell: ({ row }) => formatDate(new Date(row.original.expiredAt), 'dd/MM/yyyy', { locale: id })
  }),
  columnHelper.accessor('id', {
    header: '',
    cell: ({ row }) => (
      <Typography color='primary' sx={{ textDecoration: 'underline' }} onClick={onHistory}>
        Riwayat
      </Typography>
    )
  })
]
