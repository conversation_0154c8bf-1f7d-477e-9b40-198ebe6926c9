import { Grid, Typography } from '@mui/material'
import DocumentList from './component/DocumentList'

const DocumentPage = ({ alternativeTitle }: { alternativeTitle?: string }) => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>{alternativeTitle ?? 'List Dokumen'}</Typography>
            <Typography>Semua dokumen unit yang sudah terdaftar akan ditampilkan di sini</Typography>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <DocumentList />
      </Grid>
    </Grid>
  )
}

export default DocumentPage
