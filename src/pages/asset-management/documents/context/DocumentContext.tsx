import { defaultListData } from '@/api/queryClient'
import CompanyQueryMethods from '@/api/services/company/query'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { DocumentType, DocumentUnitType, UnitType } from '@/types/companyTypes'
import { DocumentParams, ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import React from 'react'
import { NavigateFunction, useLocation, useNavigate } from 'react-router-dom'

type DocumentContextProps = {
  navigate: NavigateFunction
  isMobile: boolean
  documentParams: ListParams
  setPartialDocumentParams: (fieldName: string, value: any) => void
  setDocumentParams: React.Dispatch<React.SetStateAction<ListParams>>
  documentList: ListResponse<DocumentUnitType>
}

const DocumentContext = React.createContext<DocumentContextProps>({} as DocumentContextProps)

export const useDocument = () => {
  const context = React.useContext(DocumentContext)
  if (context === undefined) {
    throw new Error('useDocument must be used within a DocumentContextProvider')
  }
  return context
}

export const DocumentContextProvider = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate()
  const { isMobile } = useMobileScreen()
  const locations = useLocation()

  const isInsurancePage = locations.pathname.includes('insurance')

  const [documentParams, setPartialDocumentParams, setDocumentParams] = usePartialState<DocumentParams>({
    limit: 10,
    page: 1,
    types: isInsurancePage
      ? (['INSURANCE_FIRST', 'INSURANCE_SECOND', 'INSURANCE_THIRD'] as any)
      : (['BPKB', 'STNK', 'KIR', 'SIO'] as any)
  })

  const { data: documentList } = useQuery({
    queryKey: ['DOCUMENT_LIST_QUERY_KEY', JSON.stringify(documentParams), locations.pathname],
    queryFn: () => {
      const types =
        documentParams.types.length === 1
          ? documentParams.types
          : isInsurancePage
            ? (['INSURANCE_FIRST', 'INSURANCE_SECOND', 'INSURANCE_THIRD'] as any)
            : (['BPKB', 'STNK', 'KIR', 'SIO'] as any)
      return CompanyQueryMethods.getDocumentList({ ...documentParams, types })
    },
    placeholderData: defaultListData as ListResponse<DocumentUnitType>
  })

  const value = {
    navigate,
    isMobile,
    documentParams,
    setPartialDocumentParams,
    setDocumentParams,
    documentList
  }

  return <DocumentContext.Provider value={value}>{children}</DocumentContext.Provider>
}
