import { toCurrency } from '@/utils/helper'
import { <PERSON>, CardContent, Divider, Typography } from '@mui/material'

const ItemDetailCard = () => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <Typography variant='h5'>Detail <PERSON>ang</Typography>
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <small>Kode Induk</small>
            <Typography variant='h6'>001</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small><PERSON><PERSON></small>
            <Typography variant='h6'>123465</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Kategori Item</small>
            <Typography variant='h6'>Spare part</Typography>
          </div>
        </div>
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <small>Nama Item</small>
            <Typography variant='h6'>Compression Ring</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Merk Item</small>
            <Typography variant='h6'>Sanken</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Satuan Besar</small>
            <Typography variant='h6'>Dus</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Satuan Kecil</small>
            <Typography variant='h6'>Pcs</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Konversi Satuan</small>
            <Typography variant='h6'>1 Dus = 10 Pcs</Typography>
          </div>
        </div>
        <Divider />
        <Typography variant='h5'>Informasi Vendor</Typography>
        <div className='grid grid-cols-2 gap-4'>
          <div className='flex flex-col gap-1'>
            <Typography variant='h6'>KK Tractor Shop</Typography>
            <small>Kode Vendor: 123456</small>
          </div>
        </div>
        <Divider />
        <Typography variant='h5'>Informasi Harga</Typography>
        <div className='flex flex-col gap-4'>
          <div className='flex flex-col gap-1'>
            <small>Harga Satuan Kecil</small>
            <Typography variant='h6'>{toCurrency(100000)}</Typography>
          </div>
          <div className='flex flex-col gap-1'>
            <small>Jenis Pajak</small>
            <Typography variant='h6'>Non pajak</Typography>
          </div>
          <div className='grid grid-cols-2'>
            <div className='flex flex-col gap-1'>
              <small>Jenis Diskon</small>
              <Typography variant='h6'>Diskon Persentase</Typography>
            </div>
            <div className='flex flex-col gap-1'>
              <small>Jumlah Diskon</small>
              <Typography variant='h6'>10%</Typography>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default ItemDetailCard
