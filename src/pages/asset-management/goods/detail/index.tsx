import { Breadcrumbs, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import ItemDetailCard from './component/ItemDetailCard'
import ItemPhotosCard from './component/ItemPhotosCard'
import CreatedByCard from './component/CreatedByCard'

const GoodDetailPage = () => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumbs'>
          <Link to='#'>
            <Typography color='var(--mui-palette-text-disabled)'>Manajemen Aset</Typography>
          </Link>
          <Link to='/company-data/assets/goods'>
            <Typography color='var(--mui-palette-text-disabled)'>Aset Barang</Typography>
          </Link>
          <Typography>Detail Barang</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-center'>
          <div className='flex flex-col'>
            <Typography variant='h5'>Kode Barang: 2025/ISH-1990</Typography>
            <Typography variant='caption'>Kode Eksternal: 0110192029</Typography>
          </div>
          <div className='flex gap-2 items-center'>
            {/* <Button
              className='is-full sm:is-auto'
              startIcon={<i className='ri-upload-2-line' />}
              color='secondary'
              variant='outlined'
            >
              Ekspor
            </Button>
            <Button
              color='secondary'
              variant='outlined'
              startIcon={<i className='ic-outline-local-printshop' />}
              className='is-full sm:is-auto'
            >
              Cetak
            </Button> */}
            <Button color='error' variant='outlined'>
              Hapus Barang
            </Button>
            <Button variant='contained'>Edit Barang</Button>
          </div>
        </div>
      </Grid>
      <Grid item xs={12} md={6}>
        <ItemDetailCard />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <ItemPhotosCard />
          </Grid>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default GoodDetailPage
