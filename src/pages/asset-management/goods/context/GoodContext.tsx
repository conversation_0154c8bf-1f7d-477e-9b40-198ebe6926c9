import DialogItemAsset from '@/components/dialogs/add-item-asset'
import usePartialState from '@/core/hooks/usePartialState'
import { ItemParams } from '@/types/payload'
import React, { createContext, useState } from 'react'

type GoodContextProps = {
  itemParams: ItemParams
  setPartialItemParams: (fieldName: string, value: any) => void
  setItemsParams: React.Dispatch<React.SetStateAction<ItemParams>>
  openDialogItem: () => void
}

const GoodContext = createContext<GoodContextProps>({} as GoodContextProps)

export const useGoods = () => {
  const context = React.useContext(GoodContext)
  if (context === undefined) {
    throw new Error('useGoods must be used within a GoodContextProvider')
  }
  return context
}

export const GoodContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [dialogItem, setDialogItem] = useState<boolean>(false)
  const [itemParams, setPartialItemParams, setItemsParams] = usePartialState<ItemParams>({
    limit: 10,
    page: 1
  })

  const openDialogItem = () => {
    setDialogItem(true)
  }
  const value = {
    itemParams,
    setPartialItemParams,
    setItemsParams,
    openDialogItem
  }
  return (
    <GoodContext.Provider value={value}>
      <DialogItemAsset open={dialogItem} setOpen={setDialogItem} />
      {children}
    </GoodContext.Provider>
  )
}
