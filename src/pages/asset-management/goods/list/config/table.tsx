import { DEFAULT_CATEGORY } from '@/data/default/category'
import { ItemType } from '@/types/companyTypes'
import { Grid, Icon, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const columnHelper = createColumnHelper<ItemType>()

type RowActionType = {
  detail?: (ItemType) => void
  edit?: (ItemType) => void
  delete?: (ItemType) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'Kode Barang',
    cell: ({ row }) => (
      <Typography
        color='primary'
        role='button'
        sx={{ cursor: 'pointer' }}
        onClick={() => rowAction.detail?.(row.original)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('name', {
    header: 'Nama Item'
  }),
  columnHelper.accessor('brandName', {
    header: 'Merk Item'
  }),
  columnHelper.accessor('category.name', {
    header: 'Kategori Item',
    cell: ({ row }) => row.original.category?.name ?? DEFAULT_CATEGORY.name
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tanggal Dibuat',
    cell: ({ row }) => formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id })
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container spacing={2} rowSpacing={4}>
        <Grid item>
          <IconButton>
            <i className='ri-pencil-fill text-primary' />
          </IconButton>
        </Grid>
        <Grid item>
          <IconButton>
            <i className='ri-eye-line text-primary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
