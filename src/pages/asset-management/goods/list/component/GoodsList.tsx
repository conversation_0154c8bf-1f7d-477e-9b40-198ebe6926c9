import { <PERSON><PERSON>, Card, CardContent, FormControl, InputLabel, Select, Typography } from '@mui/material'
import { useMemo, useState } from 'react'
import { tableColumns } from '../config/table'
import {
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import DebouncedInput from '@/components/DebounceInput'
import { useGoods } from '../../context/GoodContext'
import ImportDialog from '@/components/dialogs/import-dialog'
import { ExportImportScope } from '@/types/exportImportTypes'

const GoodsList = () => {
  const { itemParams, setItemsParams, openDialogItem } = useGoods()

  const [importDialogOpen, setimportDialogOpen] = useState<boolean>(false)

  const { page, search, limit } = itemParams
  const table = useReactTable({
    data: [],
    columns: tableColumns({}),
    initialState: {
      pagination: {
        pageIndex: page,
        pageSize: limit
      }
    },
    manualPagination: true,
    // rowCount: totalItems,
    // pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const renderAdditional = (
    <div className='flex justify-center flex-col gap-4 bg-[#F5F5F5] p-4'>
      <Typography>Perhatikan</Typography>
      <ul className='list-disc pl-4 space-y-1 text-secondary'>
        <li>
          <Typography variant='body2'>
            Lorem ipsum dolor sit amet consectetur adipiscing elit sed do euismod tempor incididunt ut labore et dolore
            magna aliqua
          </Typography>
        </li>
        <li>
          <Typography variant='body2'>
            Lorem ipsum dolor sit amet consectetur adipiscing elit sed do euismod tempor incididunt ut labore et dolore
            magna aliqua
          </Typography>
        </li>
        <li>
          <Typography variant='body2'>
            Amet consectetur adipiscing elit sed do euismod tempor incididunt ut labore et dolore magna aliqua. Ut enim
            ad
          </Typography>
        </li>
        <li>
          <Typography variant='body2'>
            Lorem ipsum dolor sit amet consectetur adipiscing elit sed do euismod tempor
          </Typography>
        </li>
      </ul>
    </div>
  )

  return (
    <>
      <ImportDialog
        open={importDialogOpen}
        scope={ExportImportScope.ITEM}
        onSubmit={console.log}
        setOpen={() => setimportDialogOpen(!importDialogOpen)}
        type='ITEM'
        renderAdditional={renderAdditional}
      />
      <Card>
        <CardContent className='flex flex-col gap-2'>
          <div className='flex justify-between items-center'>
            <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
              <DebouncedInput
                value={search}
                onChange={value => setItemsParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto md:max-w-[240px]'
              />
              <div className='flex gap-2 items-center'>
                <InputLabel id='filter'>Filter:</InputLabel>
                <Select className='md:w-[200px]' size='small' labelId='filter' placeholder='Pilih Kategori'></Select>
              </div>
            </div>
            <div className='space-x-2'>
              {/* <Button
                className='is-full sm:is-auto'
                startIcon={<i className='ri-upload-2-line' />}
                color='secondary'
                variant='outlined'
              >
                Ekspor
              </Button> */}
              <Button
                color='primary'
                variant='outlined'
                startIcon={<i className='mdi-file-document-outline' />}
                className='is-full sm:is-auto'
                onClick={() => setimportDialogOpen(true)}
              >
                Impor List
              </Button>
              <Button
                className='is-full sm:is-auto'
                startIcon={<i className='ri-add-circle-line' />}
                color='primary'
                variant='contained'
                onClick={() => openDialogItem()}
              >
                Tambah Barang
              </Button>
            </div>
          </div>
        </CardContent>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <div className='flex flex-col items-center justify-center'>
                <Typography variant='h5'>Belum ada aset</Typography>
                <div className='text-sm text-gray-400'>
                  Semua aset perusahaan berupa barang yang sudah terdaftar akan ditampilkan di sini
                </div>
              </div>
            </td>
          }
        />
      </Card>
    </>
  )
}

export default GoodsList
