import { defaultListData } from '@/api/queryClient'
import { useDeleteAsset } from '@/api/services/asset-management/mutation'
import AssetManagementQueryMethods from '@/api/services/asset-management/query'
import { CODE_ACTIVA_QUERY_LIST_KEY } from '@/api/services/asset-management/services'
import AddKodeActiva from '@/components/dialogs/add-kode-activa-dialog'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { AssetType } from '@/types/assetTypes'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import React, { createContext, useState } from 'react'
import { NavigateFunction, useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'

type CodeActivaContextProps = {
  activaParams: ListParams
  setPartialActivaParams: (fieldName: string, value: any) => void
  setActivaParams: React.Dispatch<React.SetStateAction<ListParams>>
  navigate: NavigateFunction
  dialogActiva: { state: boolean; asset?: AssetType }
  setDialogActiva: React.Dispatch<React.SetStateAction<{ state: boolean; asset?: AssetType }>>
  codeActivaResponse: ListResponse<AssetType>
  selectedAsset: AssetType
  setSelectedAsset: React.Dispatch<React.SetStateAction<AssetType>>
  handleDelete: (id: string) => void
  handleDetail: (asset: AssetType) => void
}

const CodeActivaContext = createContext({} as CodeActivaContextProps)

export const useCodeActiva = () => {
  const context = React.useContext(CodeActivaContext)
  if (context === undefined) {
    throw new Error('useCodeActiva must be used within a CodeActivaContextProvider')
  }
  return context
}

export const CodeActivaContextProvider = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate()
  const { setConfirmState } = useMenu()
  const [dialogActiva, setDialogActiva] = useState<{ state: boolean; asset?: AssetType }>({ state: false, asset: null })
  const [activaParams, setPartialActivaParams, setActivaParams] = usePartialState<ListParams>({
    page: 1,
    limit: 10
  })
  const [selectedAsset, setSelectedAsset] = useState<AssetType | null>(null)

  const { data: codeActivaResponse, refetch: refetchCodeActivaResponse } = useQuery({
    queryKey: [CODE_ACTIVA_QUERY_LIST_KEY, JSON.stringify(activaParams)],
    queryFn: () => AssetManagementQueryMethods.getAssets(activaParams),
    placeholderData: defaultListData as ListResponse<AssetType>
  })

  const { mutate: deleteMutate, isLoading: deleteLoading } = useDeleteAsset()

  const handleDetail = (asset: AssetType) => {
    setSelectedAsset(asset)
    setDialogActiva({ state: true, asset })
  }

  const handleDelete = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Kode Aktiva',
      content: 'Apakah kamu yakin ingin menghapus kode aktiva ini?',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        deleteMutate(id, {
          onSuccess: () => {
            toast.success('Kode aktiva berhasil dihapus')
            refetchCodeActivaResponse()
          }
        })
      }
    })
  }

  const value = {
    navigate,
    dialogActiva,
    activaParams,
    setDialogActiva,
    setPartialActivaParams,
    setActivaParams,
    codeActivaResponse,
    selectedAsset,
    setSelectedAsset,
    handleDelete,
    handleDetail
  }

  return (
    <CodeActivaContext.Provider value={value}>
      <AddKodeActiva
        data={dialogActiva.asset}
        onSuccesfullCb={refetchCodeActivaResponse}
        open={dialogActiva.state}
        setOpen={bool => setDialogActiva({ state: bool, ...(!bool && { asset: null }) })}
      />
      {children}
    </CodeActivaContext.Provider>
  )
}
