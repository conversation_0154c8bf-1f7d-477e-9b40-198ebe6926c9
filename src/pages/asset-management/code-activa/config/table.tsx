import { createColumnHelper } from '@tanstack/react-table'
import { Grid, IconButton, Typography } from '@mui/material'
import { AssetType } from '@/types/assetTypes'

type RowActionType = {
  delete: (id: string) => void
  detail: (date: AssetType) => void
}

const columnHelper = createColumnHelper<AssetType>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('code', {
    header: 'Kode Activa',
    cell: ({ row }) => (
      <Typography
        color='primary'
        onClick={() => rowAction.detail(row.original)}
        role='button'
        sx={{ cursor: 'pointer' }}
      >
        {row.original.code}
      </Typography>
    )
  }),
  columnHelper.accessor('name', {
    header: '<PERSON><PERSON>'
  }),
  columnHelper.accessor('note', {
    header: 'Keterangan',
    cell: ({ row }) => (!!row?.original?.note ? row.original.note : '-')
  }),
  columnHelper.display({
    id: 'actions',
    header: 'Action',
    cell: ({ row }) => (
      <Grid container>
        <Grid item xs={12}>
          <IconButton onClick={() => rowAction.delete(row.original.id)}>
            <i className='ri-delete-bin-7-line text-textSecondary' />
          </IconButton>
        </Grid>
      </Grid>
    )
  })
]
