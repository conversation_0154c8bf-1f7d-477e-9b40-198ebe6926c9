import { unitSchema } from '@/types/companyTypes'
import { z } from 'zod'

export const createAssetSchemaDto = z.object({
  // Detil Aset
  code: z.string({ message: 'Wajib diisi' }),
  name: z.string({ message: 'Wajib diisi' }),
  type: z.string({ message: 'Wajib dipilih' }),
  subType: z.string({ message: 'Wajib dipilih' }),
  ownerCompanyId: z.string({ message: 'Wajib dipilih' }),
  ownerSiteId: z.string({ message: 'Wajib dipilih' }),
  purchasedDate: z.string({ message: 'Wajib dipilih' }),
  usedDate: z.string({ message: 'Wajib dipilih' }),
  note: z.string().nullable().optional(),

  // Detil Unit
  unitId: z.string().nullable().optional(),
  unit: unitSchema.nullable().optional(),

  // Parent Asset Detail
  isLinkedToOtherAsset: z.boolean().nullable().optional().default(false),
  parentId: z.string().nullable().optional(),

  // PIC Aset
  picId: z.string({ message: 'Wajib dipilih' }),

  // Nilai Aset
  currencyId: z.string({ message: 'Wajib dipilih' }),
  baseValue: z.number({ message: 'Wajib diisi' }).min(0, { message: 'Nilai Aset Asal harus lebih dari 0' }),
  exchangeRate: z.number().min(0, { message: 'Nilai Tukar harus lebih dari 0' }).default(1),
  depreciationMethod: z.string({ message: 'Wajib dipilih' }),

  // Detil Pencatatan
  assetAccountId: z.string({ message: 'Wajib diisi' }),
  depreciationAccountId: z.string({ message: 'Wajib diisi' }),
  expenseAccountId: z.string({ message: 'Wajib diisi' }),

  // Foto Aset
  images: z
    .array(
      z.object({
        uploadId: z.string().optional().nullable(),
        fileName: z.string().optional().nullable(),
        content: z.string().optional().nullable(),
        url: z.string().optional().nullable()
      })
    )
    .optional()
    .nullable()
})

export type AssetDtoType = z.infer<typeof createAssetSchemaDto>
