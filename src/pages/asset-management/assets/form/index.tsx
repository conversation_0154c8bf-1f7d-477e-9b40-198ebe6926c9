import { useRouter } from '@/routes/hooks'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Grid, Typography } from '@mui/material'
import { Link, useLocation } from 'react-router-dom'
import { FormProvider, useForm } from 'react-hook-form'
import { createAssetSchemaDto, AssetDtoType } from './config/schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import * as Sentry from '@sentry/react'
import { useEffect, useMemo } from 'react'

// Import all form components
import AssetDetail from './component/AssetDetail'
import ParentAssetDetail from './component/ParentAssetDetail'

import PicAsset from './component/PicAsset'
import AssetValue from './component/AssetValue'
import AssetPhoto from './component/AssetPhoto'
import AccountingDetail from './component/AccountingDetail'
import { useUploadImage } from '@/api/services/file/mutation'
import { useCreateAsset, useUpdateAsset } from '@/api/services/asset-management/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useAsset } from '../context/AssetContext'
import LoadingButton from '@mui/lab/LoadingButton'

const AssetForm = () => {
  const router = useRouter()
  const { search } = useLocation()
  const searchParams = new URLSearchParams(search)
  const assetId = searchParams.get('id')

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutateAsync: createMutate, isLoading: isCreateLoading } = useCreateAsset()
  const { mutateAsync: updateMutate, isLoading: isUpdateLoading } = useUpdateAsset()
  const { setConfirmState } = useMenu()
  const { assetDetailData, refetchAssetList, setSelectedAssetId } = useAsset()

  const isEditMode = !!assetId
  const isLoading = isCreateLoading || isUpdateLoading

  // Set selected asset ID when in edit mode
  useEffect(() => {
    if (assetId) {
      setSelectedAssetId(assetId)
    }
  }, [assetId, setSelectedAssetId])

  // Prepare default values for edit mode
  const defaultValues = useMemo(() => {
    if (isEditMode && assetDetailData) {
      return {
        code: assetDetailData.code || '',
        name: assetDetailData.name || '',
        ownerCompanyId: assetDetailData.ownerCompanyId || '',
        ownerSiteId: assetDetailData.ownerSiteId || '',
        picId: assetDetailData.picId || '',
        type: assetDetailData.type || '',
        subType: assetDetailData.subType || '',
        unitId: assetDetailData.unit?.id || '',
        unit: assetDetailData.unit || undefined,
        purchasedDate: assetDetailData.purchasedDate || '',
        usedDate: assetDetailData.usedDate || '',
        currencyId: assetDetailData.currencyId || '',
        baseValue: assetDetailData.baseValue || 0,
        exchangeRate: assetDetailData.exchangeRate || 1,
        depreciationMethod: assetDetailData.depreciationMethod || '',
        note: assetDetailData.note || '',
        assetAccountId: assetDetailData.assetAccountId || '',
        depreciationAccountId: assetDetailData.depreciationAccountId || '',
        expenseAccountId: assetDetailData.expenseAccountId || '',
        parentId: assetDetailData.parentId || '',
        isLinkedToOtherAsset: !!assetDetailData.parentId,
        parent: assetDetailData?.parent,
        images:
          assetDetailData.images?.map(img => ({
            uploadId: img.uploadId,
            fileName: img.fileName || '',
            content: img.url
          })) || []
      }
    }
    return {
      exchangeRate: 1,
      isLinkedToOtherAsset: false
    }
  }, [isEditMode, assetDetailData])

  const method = useForm<AssetDtoType>({
    resolver: zodResolver(createAssetSchemaDto),
    defaultValues
  })

  // Reset form when asset data changes in edit mode
  useEffect(() => {
    if (isEditMode && assetDetailData) {
      method.reset(defaultValues)
    }
  }, [isEditMode, assetDetailData, defaultValues, method])

  const onSubmit = async (inputValues: AssetDtoType) => {
    const actionText = isEditMode ? 'Perbarui' : 'Simpan'
    const successText = isEditMode ? 'Aset berhasil diperbarui' : 'Aset berhasil ditambahkan'

    setConfirmState({
      open: true,
      title: `${actionText} Aset`,
      content: `Apakah kamu yakin ingin ${actionText.toLowerCase()} aset ini? Pastikan semua data yang kamu masukkan sudah benar`,
      onConfirm: () => {
        // Handle image uploads for new images only
        const newImages = (inputValues.images ?? []).filter(item => !!item.fileName && !item.uploadId)
        const existingImages = (inputValues.images ?? [])
          .filter(item => !item.fileName && item.uploadId)
          .map(img => ({
            uploadId: img.uploadId
          }))

        Promise.all(
          newImages.map(item =>
            uploadMutate({
              fieldName: `asset_image_${inputValues.code}`,
              file: item.content,
              scope: 'public-image',
              fileName: item.fileName
            })
          )
        )
          .then(async values => {
            const newUploadIds = values.map(val => ({
              uploadId: val.data?.id ?? ''
            }))

            // Combine existing and new images
            const allImages = [...existingImages, ...newUploadIds]

            const payload = {
              code: inputValues.code,
              name: inputValues.name,
              ownerCompanyId: inputValues.ownerCompanyId,
              ownerSiteId: inputValues.ownerSiteId,
              picId: inputValues.picId,
              type: inputValues.type,
              subType: inputValues.subType,
              unitId: inputValues.unitId,
              purchasedDate: inputValues.purchasedDate,
              usedDate: inputValues.usedDate,
              currencyId: inputValues.currencyId,
              baseValue: inputValues.baseValue,
              exchangeRate: inputValues.exchangeRate,
              depreciationMethod: inputValues.depreciationMethod,
              note: inputValues.note,
              assetAccountId: inputValues.assetAccountId,
              depreciationAccountId: inputValues.depreciationAccountId,
              expenseAccountId: inputValues.expenseAccountId,
              parentId: inputValues.parentId || null,
              images: allImages
            }

            if (isEditMode) {
              await updateMutate({ id: assetId!, ...payload })
            } else {
              await createMutate(payload)
            }

            toast.success(successText)
            refetchAssetList()
            if (assetId) {
              router.replace(`/accounting/assets/list/${assetId}`)
            } else {
              router.replace('/accounting/assets/list')
            }
          })
          .catch(error => {
            const message = error.response?.data?.message
            if (message) {
              toast.error(message)
            } else {
              toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
            }
          })
      },
      confirmText: actionText,
      confirmColor: 'primary'
    })
  }

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='/accounting/assets/list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Aset</Typography>
            </Link>
            <Typography>{isEditMode ? 'Edit Aset' : 'Tambah Aset'}</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>{isEditMode ? 'Edit Aset' : 'Tambah Aset'}</Typography>
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {isEditMode
                  ? 'Edit dan perbarui data aset perusahaan kamu'
                  : 'Tambah dan lengkapi data aset perusahaan kamu'}
              </Typography>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
              <div className='flex gap-2'>
                <Link to={assetId ? `/accounting/assets/list/${assetId}` : '/accounting/assets/list'} replace>
                  <Button color='secondary' variant='outlined' className='is-full sm:is-auto'>
                    Batalkan
                  </Button>
                </Link>
              </div>
              <LoadingButton
                startIcon={<></>}
                loading={uploadLoading || isLoading}
                loadingPosition='start'
                variant='contained'
                className='px-8 is-full !ml-0 sm:is-auto'
                onClick={method.handleSubmit(onSubmit, errors => {
                  console.error({ errors })
                  Sentry.captureException(errors)
                })}
              >
                {isEditMode ? 'Perbarui Aset' : 'Simpan Aset'}
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <FormProvider {...method}>
          <Grid item xs={12}>
            <Grid container spacing={4}>
              {/* Left Column */}
              <Grid item xs={12} md={6}>
                <Grid container spacing={4}>
                  <Grid item xs={12}>
                    <AssetDetail />
                  </Grid>
                  <Grid item xs={12}>
                    <ParentAssetDetail />
                  </Grid>
                  <Grid item xs={12}>
                    <AccountingDetail />
                  </Grid>
                </Grid>
              </Grid>

              {/* Right Column */}
              <Grid item xs={12} md={6}>
                <Grid container spacing={4}>
                  <Grid item xs={12}>
                    <PicAsset />
                  </Grid>
                  <Grid item xs={12}>
                    <AssetValue />
                  </Grid>
                  <Grid item xs={12}>
                    <AssetPhoto />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </FormProvider>
      </Grid>
    </>
  )
}

export default AssetForm
