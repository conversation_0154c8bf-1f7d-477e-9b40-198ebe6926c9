import { <PERSON><PERSON>, <PERSON><PERSON>ield, Card, CardContent, Typo<PERSON>, Autocomplete, CircularProgress, debounce } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { AssetDtoType } from '../config/schema'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { UnitType } from '@/types/companyTypes'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import CompanyQueryMethods, { ITEM_LIST_QUERY_KEY } from '@/api/services/company/query'

const UnitDetail = () => {
  const { control } = useFormContext<AssetDtoType>()
  const [unitSearchQuery, setUnitSearchQuery] = useState('')
  const [selectedUnit, setSelectedUnit] = useState<UnitType | null>(null)

  const {
    data: { items: unitList },
    isLoading: fetchUnitsLoading
  } = useQuery({
    enabled: !!unitSearchQuery,
    queryKey: [ITEM_LIST_QUERY_KEY, unitSearchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getUnitList({
        ...(unitSearchQuery && { search: unitSearchQuery })
      })
    },
    placeholderData: defaultListData as ListResponse<UnitType>
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Unit</Typography>
        </div>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='unitId'
              render={({ field: { onChange }, fieldState: { error } }) => (
                <Autocomplete
                  filterOptions={x => x}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setUnitSearchQuery(newValue)
                    }
                  }, 700)}
                  options={unitList || []}
                  freeSolo={!unitSearchQuery}
                  fullWidth
                  value={selectedUnit}
                  onChange={(e, newValue: UnitType | null) => {
                    onChange(newValue?.id)
                    setSelectedUnit(newValue)
                  }}
                  noOptionsText='Unit tidak ditemukan'
                  loading={fetchUnitsLoading}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='Cari Unit'
                      placeholder='Cari kode atau nomor lambung'
                      variant='outlined'
                      fullWidth
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                        endAdornment: <>{fetchUnitsLoading ? <CircularProgress size={20} /> : null}</>,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                  getOptionLabel={(option: UnitType) =>
                    `${option.number} | ${option?.type} | ${option?.category?.name || '-'}`
                  }
                  renderOption={(props, option) => {
                    const { key, ...optionProps } = props
                    return (
                      <li key={key} {...optionProps}>
                        <Typography>
                          {option.number} | {option.type} | {option.category?.name || '-'}
                        </Typography>
                      </li>
                    )
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField disabled fullWidth label='Kode Unit' value={selectedUnit?.number ?? '-'} />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField disabled fullWidth label='Kategori Unit' value={selectedUnit?.category?.name ?? '-'} />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField disabled fullWidth label='Merk Unit' value={selectedUnit?.brandName ?? '-'} />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField disabled fullWidth label='Tipe Unit' value={selectedUnit?.type ?? '-'} />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField disabled fullWidth label='Nomor Lambung' value={selectedUnit?.hullNumber ?? '-'} />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default UnitDetail
