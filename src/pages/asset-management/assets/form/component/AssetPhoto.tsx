import { useEffect, useState } from 'react'
import { Card, CardContent, Typography } from '@mui/material'
import { useFormContext } from 'react-hook-form'
import { AssetDtoType } from '../config/schema'
import PhotoPicker from '@/components/PhotoPicker'
import { ImageItemType } from '@/types/companyTypes'
import { useUpdateEffect } from 'react-use'
import { useAsset } from '../../context/AssetContext'

// Default image list for PhotoPicker (5 slots)
const defaultImageList = new Array(5).fill({
  content: '',
  fileName: ''
}) as ImageItemType[]

const AssetPhoto = () => {
  const { assetDetailData } = useAsset()
  const { setValue } = useFormContext<AssetDtoType>()
  const [imageList, setImageList] = useState<ImageItemType[]>(defaultImageList)

  useUpdateEffect(() => {
    setValue(
      'images',
      imageList.filter(img => !!img.content)
    )
  }, [imageList])

  useEffect(() => {
    if (assetDetailData?.images?.length > 0) {
      setImageList([
        ...assetDetailData?.images.map(img => ({
          uploadId: img.uploadId,
          fileName: img.fileName || '',
          content: img.url
        })),
        ...(new Array(5 - (assetDetailData?.images?.length ?? 0)).fill({
          content: '',
          fileName: ''
        }) as ImageItemType[])
      ])
    }
  }, [assetDetailData?.images])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Foto Aset</Typography>
        </div>

        <div className='flex gap-5 overflow-y-hidden max-sm:px-2'>
          {imageList?.map((item, index) => (
            <PhotoPicker
              key={`${item.content}_${index}`}
              content={item.content}
              onPicked={(content, fileName) => {
                setImageList(current => {
                  const tempCurrent = [...current]
                  tempCurrent[index] = { content, fileName }
                  return tempCurrent
                })
              }}
              onRemoved={() => {
                setImageList(current => {
                  const tempCurrent = [...current]
                  tempCurrent[index] = { content: '', fileName: '' }
                  return tempCurrent
                })
              }}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

export default AssetPhoto
