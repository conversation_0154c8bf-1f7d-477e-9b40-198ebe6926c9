import {
  <PERSON>rid,
  TextField,
  Card,
  CardContent,
  Typography,
  Checkbox,
  FormControlLabel,
  Box,
  IconButton,
  InputAdornment
} from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { AssetDtoType } from '../config/schema'
import { AssetType } from '@/types/assetTypes'
import AssetManagementQueryMethods from '@/api/services/asset-management/query'
import { ASSET_QUERY_LIST_KEY } from '@/api/services/asset-management/services'
import { useAsset } from '../../context/AssetContext'

const ParentAssetDetail = () => {
  const { assetDetailData } = useAsset()
  const { control, setValue } = useFormContext<AssetDtoType>()
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [selectedAsset, setSelectedAsset] = useState<AssetType | null>(assetDetailData?.parent)

  // Watch the checkbox value
  const isLinkedToOtherAsset = useWatch({
    control,
    name: 'isLinkedToOtherAsset',
    defaultValue: false
  })

  // Debounce search term
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
    }, 700)
    return () => clearTimeout(handler)
  }, [searchTerm])

  useEffect(() => {
    setSelectedAsset(assetDetailData?.parent)
  }, [assetDetailData?.parent])

  // Search for assets
  const { data: searchResults } = useQuery({
    queryKey: [ASSET_QUERY_LIST_KEY, 'search', debouncedSearchTerm],
    queryFn: () =>
      AssetManagementQueryMethods.getAssets({
        search: debouncedSearchTerm,
        limit: 10,
        isParent: true
      }),
    enabled: !!debouncedSearchTerm && debouncedSearchTerm.length > 2
  })

  const handleCheckboxChange = (checked: boolean) => {
    setValue('isLinkedToOtherAsset', checked)
    if (!checked) {
      setSelectedAsset(null)
      setValue('parentId', '')
    }
  }

  const handleAssetSelect = (asset: AssetType) => {
    setSelectedAsset(asset)
    setValue('parentId', asset.id)
    setSearchTerm('')
  }

  const handleRemoveAsset = () => {
    setSelectedAsset(null)
    setValue('parentId', '')
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Aset Dalam Aset</Typography>
        </div>

        <Grid container spacing={2}>
          {/* Checkbox for linking to other asset */}
          <Grid item xs={12}>
            <Controller
              control={control}
              name='isLinkedToOtherAsset'
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      {...field}
                      checked={field.value || false}
                      onChange={e => handleCheckboxChange(e.target.checked)}
                      color='primary'
                    />
                  }
                  label='Hubungkan ke Aset Lain'
                />
              )}
            />
          </Grid>

          {/* Search field - only show when checkbox is checked */}
          {isLinkedToOtherAsset && !selectedAsset && (
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Cari Aset'
                placeholder='Cari kode aset atau nama aset'
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position='start'>
                      <i className='ri-search-line' />
                    </InputAdornment>
                  )
                }}
              />

              {/* Search results dropdown */}
              {searchTerm && searchResults?.items && searchResults.items.length > 0 && (
                <Box
                  sx={{
                    mt: 1,
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    maxHeight: 200,
                    overflow: 'auto',
                    backgroundColor: 'background.paper'
                  }}
                >
                  {searchResults.items.map(asset => (
                    <Box
                      key={asset.id}
                      sx={{
                        p: 2,
                        cursor: 'pointer',
                        borderBottom: 1,
                        borderColor: 'divider',
                        '&:hover': {
                          backgroundColor: 'action.hover'
                        },
                        '&:last-child': {
                          borderBottom: 0
                        }
                      }}
                      onClick={() => handleAssetSelect(asset)}
                    >
                      <Typography variant='body2' fontWeight={500}>
                        {asset.name}
                      </Typography>
                      <Typography variant='caption' color='text.secondary'>
                        Kode Aset {asset.code}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              )}

              {/* No results message */}
              {searchTerm && searchResults?.items && searchResults.items.length === 0 && (
                <Box sx={{ mt: 1, p: 2, textAlign: 'center' }}>
                  <Typography variant='body2' color='text.secondary'>
                    Tidak ada aset ditemukan
                  </Typography>
                </Box>
              )}
            </Grid>
          )}

          {/* Selected asset display */}
          {isLinkedToOtherAsset && selectedAsset && (
            <Grid item xs={12}>
              <Box
                sx={{
                  p: 2,
                  backgroundColor: 'rgba(76, 78, 100, 0.05)',
                  borderRadius: 1,
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <Box>
                  <Typography variant='body1' fontWeight={500}>
                    {selectedAsset.name}
                  </Typography>
                  <Typography variant='body2' color='primary'>
                    Kode Aset {selectedAsset.code}
                  </Typography>
                </Box>
                <IconButton size='small' onClick={handleRemoveAsset} sx={{ color: 'error.main' }}>
                  <i className='ri-close-line' />
                </IconButton>
              </Box>
            </Grid>
          )}
        </Grid>
      </CardContent>
    </Card>
  )
}

export default ParentAssetDetail
