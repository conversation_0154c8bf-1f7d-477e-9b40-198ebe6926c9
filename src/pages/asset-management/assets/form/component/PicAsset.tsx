import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Card, CardContent, Typography, Autocomplete } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { useQuery } from '@tanstack/react-query'
import { AssetDtoType } from '../config/schema'
import UserQueryMethods, { USER_LIST_QUERY_KEY } from '@/api/services/user/query'
import { UserType } from '@/types/userTypes'
import { useEffect, useState } from 'react'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { useAsset } from '../../context/AssetContext'

const PicAsset = () => {
  const { assetDetailData } = useAsset()
  const { control } = useFormContext<AssetDtoType>()
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null)

  // Fetch user list
  const { data: userList } = useQuery({
    queryKey: [USER_LIST_QUERY_KEY],
    queryFn: () => UserQueryMethods.getUserList({ limit: Number.MAX_SAFE_INTEGER }),
    placeholderData: defaultListData as ListResponse<UserType>
  })

  useEffect(() => {
    setSelectedUser(userList?.items?.find(user => user.id === assetDetailData?.picId))
  }, [userList, assetDetailData?.picId])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>PIC Aset</Typography>
        </div>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='picId'
              render={({ field, fieldState: { error } }) => (
                <Autocomplete
                  {...field}
                  options={userList?.items || []}
                  getOptionLabel={(option: UserType) => option.fullName}
                  isOptionEqualToValue={(option: UserType, value: UserType) => option.id === value.id}
                  onChange={(_, newValue) => {
                    setSelectedUser(newValue)
                    field.onChange(newValue?.id || '')
                  }}
                  value={selectedUser || null}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='Nama PIC'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: <>{params.InputProps.endAdornment}</>
                      }}
                    />
                  )}
                  filterOptions={(options, { inputValue }) => {
                    return options.filter(
                      option =>
                        option?.fullName?.toLowerCase().includes(inputValue.toLowerCase()) ||
                        option?.email?.toLowerCase().includes(inputValue.toLowerCase())
                    )
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField value={selectedUser?.department?.name || ''} label='Departemen' fullWidth disabled />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default PicAsset
