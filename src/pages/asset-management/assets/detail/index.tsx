import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { Box, Breadcrumbs, <PERSON>ton, Card, CardContent, Grid, Typography, Avatar } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

import { useAsset } from '../context/AssetContext'
import { useRouter } from '@/routes/hooks'
import { toCurrency } from '@/utils/helper'
import Loading from '@/components/Loading'
import DetailCard from './components/DetailCard'
import AccountCard from './components/AccountCard'
import PicCard from './components/PicCard'
import ParentCard from './components/ParentCard'
import ChildrenCard from './components/ChildrenCard'
import ValueCard from './components/ValueCard'
import MobilizationCard from './components/MobilizationCard'
import PhotoCard from './components/PhotoCard'
import CreatedByCard from './components/CreatedByCard'
import AddMobilizationDialog from '@/components/dialogs/add-mobilization-dialog'
import { AssetStatus } from '../config/enum'
import LoadingButton from '@mui/lab/LoadingButton'

const AssetDetailPage = () => {
  const { assetId } = useParams()
  const router = useRouter()
  const {
    handleDelete,
    setSelectedAssetId,
    assetDetailData,
    isLoadingAssetDetail,
    refetchAssetDetail,
    refetchMobilizations,
    isLoadingDelete
  } = useAsset()
  const [mobilizationDialogOpen, setMobilizationDialogOpen] = useState(false)

  const assetData = assetDetailData
  const isLoading = isLoadingAssetDetail

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export asset data')
  }

  const handleEdit = () => {
    router.replace(`/accounting/assets/form?id=${assetId}`)
  }

  const handleMobilization = () => {
    setMobilizationDialogOpen(true)
  }

  const handleMobilizationSuccess = () => {
    refetchAssetDetail()
    refetchMobilizations()
  }

  const handleDeleteAsset = () => {
    if (assetId) {
      handleDelete(assetId)
    }
  }

  if (isLoading) {
    return (
      <Box display='flex' justifyContent='center' alignItems='center' minHeight='400px'>
        <Loading />
      </Box>
    )
  }

  if (!assetData) {
    return (
      <Box display='flex' justifyContent='center' alignItems='center' minHeight='400px'>
        <Typography>Aset tidak ditemukan</Typography>
      </Box>
    )
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='/accounting/assets/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Aset</Typography>
          </Link>
          <Typography color='text.primary'>Detil Aset</Typography>
        </Breadcrumbs>
      </Breadcrumbs>

      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-end', mb: 3, pt: 2.5 }}>
        <div className='flex flex-col items-start max-sm:text-center'>
          <Typography className='text-left md:text-justify' variant='h4'>
            {assetData?.name}
          </Typography>
          <Typography>Kode Aset {assetData?.code}</Typography>
        </div>

        <Box sx={{ display: 'flex', gap: 3 }}>
          {/* <Button variant='outlined' startIcon={<i className='ri-upload-2-line' />} onClick={handleExport}>
            Ekspor
          </Button> */}
          <LoadingButton
            startIcon={<></>}
            loading={isLoadingDelete}
            loadingPosition='start'
            variant='outlined'
            color='error'
            size='small'
            onClick={handleDeleteAsset}
            className='px-8 is-full !ml-0 sm:is-auto'
          >
            Hapus Aset
          </LoadingButton>
          <Button variant='outlined' onClick={handleEdit}>
            Edit Aset
          </Button>
          <Button variant='contained' onClick={handleMobilization}>
            Mobilisasi Aset
          </Button>
        </Box>
      </Box>
      {assetDetailData?.status === AssetStatus.DISPOSED && (
        <div className='flex items-center w-full p-4 bg-red-100 rounded-lg my-6'>
          <i className='ri-error-warning-line text-red-500 mr-2' />
          <Typography>Aset ini sudah ditandai untuk Dispose</Typography>
        </div>
      )}

      {/* Content Grid */}
      <Grid container spacing={4}>
        {/* Left Column */}
        <Grid item xs={12} md={6}>
          {/* Asset Details Card */}
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <DetailCard assetData={assetData} />
            </Grid>
            {assetData?.parent && (
              <Grid item xs={12}>
                <ParentCard assetData={assetData} />
              </Grid>
            )}
            {(assetData?.children?.length ?? 0) > 0 && (
              <Grid item xs={12}>
                <ChildrenCard assetData={assetData} />
              </Grid>
            )}
            <Grid item xs={12}>
              <AccountCard assetData={assetData} />
            </Grid>
          </Grid>
        </Grid>

        {/* Right Column */}
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {/* PIC Asset Card */}
            <Grid item xs={12}>
              <PicCard assetData={assetData} />
            </Grid>
            <Grid item xs={12}>
              <ValueCard assetData={assetData} />
            </Grid>
            <Grid item xs={12}>
              <MobilizationCard assetData={assetData} />
            </Grid>
            {assetData?.images?.length > 0 && (
              <Grid item xs={12}>
                <PhotoCard assetData={assetData} />
              </Grid>
            )}
            <Grid item xs={12}>
              <CreatedByCard assetData={assetData} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Add Mobilization Dialog */}
      {mobilizationDialogOpen && (
        <AddMobilizationDialog
          open={mobilizationDialogOpen}
          setOpen={setMobilizationDialogOpen}
          assetData={assetData}
          onSuccess={handleMobilizationSuccess}
        />
      )}
    </Box>
  )
}

export default AssetDetailPage
