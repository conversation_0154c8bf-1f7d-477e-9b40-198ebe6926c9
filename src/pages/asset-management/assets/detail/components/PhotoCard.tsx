// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Grid } from '@mui/material'

import { AssetType } from '@/types/assetTypes'
import PhotoPicker from '@/components/PhotoPicker'

interface PhotoCardProps {
  assetData: AssetType
}

const PhotoCard = ({ assetData }: PhotoCardProps) => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Card>
          <CardContent className='flex flex-col gap-6'>
            <div className='flex justify-between items-center'>
              <Typography variant='h5'>Foto Aset</Typography>
            </div>
            <div className='flex items-center gap-4'>
              {assetData?.images?.map(image => <PhotoPicker key={image?.id} content={image?.url} isPreview />)}
            </div>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default PhotoCard
