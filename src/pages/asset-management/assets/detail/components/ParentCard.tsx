// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Grid, Box, IconButton } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useRouter } from '@/routes/hooks'

import { AssetType } from '@/types/assetTypes'

interface ParentCardProps {
  assetData: AssetType
}

const ParentCard = ({ assetData }: ParentCardProps) => {
  const router = useRouter()
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Card>
          <CardContent className='flex flex-col gap-6'>
            <div className='flex justify-between items-center'>
              <Typography variant='h5'>Aset Induk</Typography>
            </div>
            <div className='bg-gray-100 p-4 flex rounded-lg'>
              <div className='flex flex-col gap-1 flex-1'>
                <Typography className='text-textPrimary text-base'>{assetData?.parent?.name || '-'}</Typography>
                <Typography
                  className='text-primary text-sm cursor-pointer'
                  onClick={() => router.push(`/accounting/assets/list/${assetData?.parent?.id}`)}
                >
                  Kode Aset {assetData?.parent?.code || '-'}
                </Typography>
              </div>
              <IconButton
                className='size-10'
                onClick={() => router.push(`/accounting/assets/list/${assetData?.parent?.id}`)}
              >
                <i className='ri-arrow-right-s-line' />
              </IconButton>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default ParentCard
