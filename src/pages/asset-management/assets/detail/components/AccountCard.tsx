// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Grid, Box } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

import { AssetType } from '@/types/assetTypes'

interface AccountCardProps {
  assetData: AssetType
}

const AccountCard = ({ assetData }: AccountCardProps) => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Card>
          <CardContent className='flex flex-col gap-6'>
            <div className='flex justify-between items-center'>
              <Typography variant='h5'>Akun Aset</Typography>
            </div>
            <div className='flex flex-col gap-6'>
              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Kode Akun Aset
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  [{assetData?.assetAccount?.code || '-'}] {assetData?.assetAccount?.name || '-'}
                </p>
              </div>

              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Akun Akumulasi Penyusutan
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  [{assetData?.depreciationAccount?.code || '-'}] {assetData?.depreciationAccount?.name || '-'}
                </p>
              </div>

              <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
                <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                  <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                    Akun Beban Penyusutan
                  </small>
                </label>
                <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
                <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                  [{assetData?.expenseAccount?.code || '-'}] {assetData?.expenseAccount?.name || '-'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default AccountCard
