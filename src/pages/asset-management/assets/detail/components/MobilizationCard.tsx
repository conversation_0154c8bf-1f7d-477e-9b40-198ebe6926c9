// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { Grid, IconButton } from '@mui/material'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { createColumnHelper, getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table'

import { AssetType, AssetMobilization } from '@/types/assetTypes'
import { ASSET_MOBILIZATION_LIST_KEY } from '@/api/services/asset-management/services'
import AssetManagementQueryMethods from '@/api/services/asset-management/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ListParams } from '@/types/payload'
import Table from '@/components/table'
import MobilizationDetailDialog from '@/components/dialogs/mobilization-detail-dialog'
import { useAsset } from '../../context/AssetContext'

interface MobilizationCardProps {
  assetData: AssetType
}

// Create column helper for mobilization table
const columnHelper = createColumnHelper<AssetMobilization>()

// Define table columns based on Figma design
const mobilizationColumns = (assetData: AssetType) => [
  columnHelper.accessor('previousSite', {
    header: 'LOKASI AWAL',
    cell: ({ row }) => row.original.previousSite?.name ?? '-'
  }),
  columnHelper.accessor('currentSite', {
    header: 'PINDAH KE',
    cell: ({ row }) => row.original.currentSite?.name ?? '-'
  }),
  columnHelper.accessor('createdAt', {
    header: 'TGL MOBILISASI',
    cell: ({ row }) => {
      if (!row.original.createdAt) return '-'
      return format(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id })
    }
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      const [openDialog, setOpenDialog] = useState(false)
      return (
        <>
          <IconButton size='small' onClick={() => setOpenDialog(true)}>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
          {openDialog && (
            <MobilizationDetailDialog
              open={openDialog}
              setOpen={setOpenDialog}
              assetData={assetData}
              mobilization={row.original}
            />
          )}
        </>
      )
    }
  })
]

const MobilizationCard = ({ assetData }: MobilizationCardProps) => {
  const { mobilizationParams, setMobilizationParams, mobilizationResponse } = useAsset()

  const mobilizationList = mobilizationResponse?.items || []
  const totalItems = mobilizationResponse?.totalItems || 0
  const totalPages = mobilizationResponse?.totalPages || 0

  // Setup table
  const table = useReactTable({
    data: mobilizationList,
    columns: mobilizationColumns(assetData),
    initialState: {
      pagination: {
        pageSize: mobilizationParams.limit || 10,
        pageIndex: (mobilizationParams.page || 1) - 1
      }
    },
    state: {
      pagination: {
        pageSize: mobilizationParams.limit || 10,
        pageIndex: (mobilizationParams.page || 1) - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel()
  })

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Card>
          <CardContent className='flex flex-col gap-6'>
            <div className='flex justify-between items-center'>
              <Typography variant='h5'>Riwayat Mobilisasi</Typography>
            </div>

            {mobilizationList.length === 0 ? (
              <div className='flex flex-col items-center justify-center p-4'>
                <Typography variant='h5'>Belum ada mobilisasi</Typography>
                <Typography className='text-sm text-gray-400'>
                  Riwayat perpindahan aset ini akan ditampilkan di sini
                </Typography>
              </div>
            ) : (
              <Table
                table={table}
                headerColor='green'
                onPageChange={pageIndex => {
                  setMobilizationParams(prev => ({ ...prev, page: pageIndex }))
                }}
                onRowsPerPageChange={pageSize => {
                  setMobilizationParams(prev => ({ ...prev, limit: pageSize, page: 1 }))
                }}
              />
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  )
}

export default MobilizationCard
