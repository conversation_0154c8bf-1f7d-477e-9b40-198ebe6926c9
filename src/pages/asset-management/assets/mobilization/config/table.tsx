import { createColumnHelper } from '@tanstack/react-table'
import { Grid, IconButton, Typography } from '@mui/material'
import { AssetType } from '@/types/assetTypes'
import { toCurrency } from '@/utils/helper'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'

type RowActionType = {
  delete: (id: string) => void
  detail: (date: AssetType) => void
}

const columnHelper = createColumnHelper<AssetType>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('code', {
    header: 'Kode Aktiva',
    cell: ({ row }) => (
      <Typography
        color='primary'
        onClick={() => rowAction.detail(row.original)}
        role='button'
        sx={{ cursor: 'pointer' }}
      >
        {row.original.code}
      </Typography>
    )
  }),
  columnHelper.accessor('name', {
    header: '<PERSON>a Aset'
  }),
  columnHelper.accessor('ownerSite', {
    header: 'Lokasi Awal',
    cell: ({ row }) => row.original.ownerSite?.name ?? '-'
  }),
  columnHelper.accessor('ownerSiteId', {
    header: 'Pindah Ke',
    cell: ({ row }) => row.original.ownerSite?.name ?? '-'
  }),
  columnHelper.accessor('createdAt', {
    header: 'TGL Mobilisasi',
    cell: ({ row }) => format(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id })
  }),
  columnHelper.accessor('pic', {
    header: 'PIC Aset',
    cell: ({ row }) => row.original.pic?.fullName ?? '-'
  }),
  columnHelper.accessor('picId', {
    header: 'PIC Penerimaan',
    cell: ({ row }) => row.original.pic?.fullName ?? '-'
  })
  // columnHelper.display({
  //   id: 'actions',
  //   header: 'Action',
  //   cell: ({ row }) => (
  //     <Grid container>
  //       <Grid item xs={3}>
  //         <IconButton onClick={() => rowAction.delete(row.original.id)}>
  //           <i className='ri-delete-bin-7-line text-textSecondary' />
  //         </IconButton>
  //       </Grid>
  //       <Grid item xs={3}>
  //         <IconButton onClick={() => rowAction.detail(row.original)}>
  //           <i className='ri-eye-line text-textSecondary' />
  //         </IconButton>
  //       </Grid>
  //     </Grid>
  //   )
  // })
]
