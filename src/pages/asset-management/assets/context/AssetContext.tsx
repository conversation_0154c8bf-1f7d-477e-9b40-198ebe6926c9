import { defaultListData } from '@/api/queryClient'
import { useDeleteAsset, useDisposeAsset, useMobilizeAsset } from '@/api/services/asset-management/mutation'
import AssetManagementQueryMethods from '@/api/services/asset-management/query'
import {
  ASSET_QUERY_LIST_KEY,
  ASSET_QUERY_DETAIL_KEY,
  ASSET_MOBILIZATION_LIST_KEY,
  ASSET_VALUE_LOGS_LIST_KEY
} from '@/api/services/asset-management/services'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { useRouter } from '@/routes/hooks'
import { ListResponse } from '@/types/api'
import { AssetMobilization, AssetType } from '@/types/assetTypes'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import React, { createContext, useEffect, useState } from 'react'
import { NavigateFunction, useNavigate, useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

type AssetContextProps = {
  assetParams: ListParams
  setPartialAssetParams: (fieldName: string, value: any) => void
  setAssetParams: React.Dispatch<React.SetStateAction<ListParams>>
  navigate: NavigateFunction
  assetResponse: ListResponse<AssetType>
  selectedAsset: AssetType
  setSelectedAsset: React.Dispatch<React.SetStateAction<AssetType>>
  selectedAssetId: string | null
  setSelectedAssetId: React.Dispatch<React.SetStateAction<string | null>>
  assetDetailData: AssetType | undefined
  isLoadingAssetDetail: boolean
  handleDelete: (id: string) => void
  handleDetail: (asset: AssetType) => void
  refetchAssetList: () => void
  refetchAssetDetail: () => void
  mobilizationParams: ListParams
  setMobilizationParams: React.Dispatch<React.SetStateAction<ListParams>>
  mobilizationResponse: ListResponse<AssetMobilization>
  refetchMobilizations: () => void
  isLoadingDelete: boolean
}

const AssetContext = createContext({} as AssetContextProps)

export const useAsset = () => {
  const context = React.useContext(AssetContext)
  if (context === undefined) {
    throw new Error('useAsset must be used within a AssetContextProvider')
  }
  return context
}

export const AssetContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { assetId } = useParams()
  const navigate = useNavigate()
  const { setConfirmState } = useMenu()
  const router = useRouter()
  const [assetParams, setPartialAssetParams, setAssetParams] = usePartialState<ListParams>({
    page: 1,
    limit: 10
  })
  const [selectedAsset, setSelectedAsset] = useState<AssetType | null>(null)
  const [selectedAssetId, setSelectedAssetId] = useState<string | null>(null)

  const [mobilizationParams, setMobilizationParams] = useState<ListParams>({
    page: 1,
    limit: 10
  })

  const { data: assetResponse, refetch: refetchAssetList } = useQuery({
    queryKey: [ASSET_QUERY_LIST_KEY, JSON.stringify(assetParams)],
    queryFn: () => AssetManagementQueryMethods.getAssets(assetParams),
    placeholderData: defaultListData as ListResponse<AssetType>
  })

  const {
    data: assetDetailData,
    isLoading: isLoadingAssetDetail,
    refetch: refetchAssetDetail
  } = useQuery({
    queryKey: [ASSET_QUERY_DETAIL_KEY, selectedAssetId, assetId],
    queryFn: () => AssetManagementQueryMethods.getAsset(selectedAssetId!),
    enabled: !!selectedAssetId
  })

  const { data: mobilizationResponse, refetch: refetchMobilizations } = useQuery({
    queryKey: [ASSET_MOBILIZATION_LIST_KEY, assetDetailData?.id, JSON.stringify(mobilizationParams)],
    queryFn: () => AssetManagementQueryMethods.getPaginatedMobilization(assetDetailData?.id!, mobilizationParams),
    placeholderData: defaultListData as ListResponse<AssetMobilization>,
    enabled: !!assetDetailData?.id
  })

  const { mutate: deleteMutate, isLoading: isLoadingDelete } = useDeleteAsset()

  const handleDetail = (asset: AssetType) => {
    setSelectedAsset(asset)
  }

  const handleDelete = (id: string) => {
    setConfirmState({
      open: true,
      title: 'Hapus Aset',
      content: 'Apakah kamu yakin ingin menghapus aset ini?',
      confirmText: 'Hapus',
      confirmColor: 'error',
      onConfirm: () => {
        deleteMutate(id, {
          onSuccess: () => {
            toast.success('Aset berhasil dihapus')
            refetchAssetList()
            router.replace('/accounting/assets/list')
          }
        })
      }
    })
  }

  useEffect(() => {
    if (assetId) {
      setSelectedAssetId(assetId)
    }
  }, [assetId, setSelectedAssetId])

  const value = {
    navigate,
    assetParams,
    setPartialAssetParams,
    setAssetParams,
    assetResponse,
    selectedAsset,
    setSelectedAsset,
    selectedAssetId,
    setSelectedAssetId,
    assetDetailData,
    isLoadingAssetDetail,
    mobilizationParams,
    setMobilizationParams,
    mobilizationResponse,
    refetchMobilizations,
    handleDelete,
    handleDetail,
    refetchAssetList,
    refetchAssetDetail,
    isLoadingDelete
  }

  return <AssetContext.Provider value={value}>{children}</AssetContext.Provider>
}
