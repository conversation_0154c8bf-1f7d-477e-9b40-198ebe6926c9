import { Grid, Typography } from '@mui/material'
import SrApprovalList from './components/SrApprovalList'

const StuffRequestPage = ({ type }: { type: 'TAKE' | 'RETURN' }) => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <div className='flex flex-col'>
          <Typography variant='h4'>
            Persetujuan {type === 'TAKE' ? 'Pengambilan' : 'Pengembalian'} Barang MRO
          </Typography>
          <Typography>
            Semua {type === 'TAKE' ? 'Pengambilan' : 'Pengembalian'} Barang untuk MRO yang harus kamu setujui akan
            ditampilkan di sini
          </Typography>
        </div>
      </Grid>
      <Grid item xs={12}>
        <SrApprovalList />
      </Grid>
    </Grid>
  )
}

export default StuffRequestPage
