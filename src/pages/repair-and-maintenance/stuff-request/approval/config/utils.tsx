import { toTitleCase } from '@/utils/helper'

export const stuffRequestStatus = (status: string) => {
  switch (status) {
    case 'PROCESSED':
      return {
        label: '<PERSON>unggu',
        color: 'default'
      }
    case 'APPROVED':
      return {
        label: 'Disetujui',
        color: 'success'
      }
    case 'REJECTED':
      return {
        label: '<PERSON><PERSON><PERSON>',
        color: 'error'
      }
    case 'CANCELED':
      return {
        label: 'Dibatalkan',
        color: 'error'
      }
    default:
      return {
        label: toTitleCase(status),
        color: 'default'
      }
  }
}

export const stuffStatusOptions = [
  {
    value: 'PROCESSED',
    label: 'Menunggu'
  },
  {
    value: 'APPROVED',
    label: 'Disetujui'
  },
  {
    value: 'REJECTED',
    label: '<PERSON><PERSON><PERSON>'
  },
  {
    value: 'CANCELED',
    label: '<PERSON><PERSON>alkan'
  }
]
