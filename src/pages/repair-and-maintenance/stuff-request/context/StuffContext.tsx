import { defaultListData } from '@/api/queryClient'
import { WpQueryMethods } from '@/api/services/wp/query'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import { useRouter } from '@/routes/hooks'
import { ListResponse } from '@/types/api'
import { StuffRequestParams, StuffRequestType } from '@/types/wpTypes'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import { createContext, ReactNode, useContext } from 'react'
import { useLocation, useParams } from 'react-router-dom'

type StuffRequestContextProps = {
  isMobile: boolean
  stuffListResponse: ListResponse<StuffRequestType>
  stuffData: StuffRequestType
  stuffParams: StuffRequestParams
  setPartialStuffParam: (fieldName: keyof StuffRequestParams, value: any) => void
  setStuffParams: React.Dispatch<React.SetStateAction<StuffRequestParams>>
  router: ReturnType<typeof useRouter>
  isTake: boolean
  isReturn: boolean
  refetchStuffData: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<StuffRequestType, unknown>>
  refetchStuffList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<StuffRequestType>, unknown>>
}

const StuffContext = createContext<StuffRequestContextProps>({} as StuffRequestContextProps)

export const useStuffContext = () => {
  const context = useContext(StuffContext)
  if (!context) {
    throw new Error('useStuffContext must be used within a StuffContextProvider')
  }
  return context
}

export const StuffContextProvider = ({ children }: { children: ReactNode }) => {
  const router = useRouter()
  const params = useParams()
  const { pathname } = useLocation()
  const { isMobile } = useMobileScreen()
  const [stuffParams, setPartialStuffParam, setStuffParams] = usePartialState<StuffRequestParams>({
    limit: 10,
    page: 1
  })

  const isTake = pathname.includes('take')
  const isReturn = pathname.includes('return')

  const { data: stuffData, refetch: refetchStuffData } = useQuery({
    enabled: !!params.stuffReqId,
    queryKey: ['STUFF_REQUEST_KEY', params.stuffReqId],
    queryFn: () => WpQueryMethods.getWpStuffRequest(params.stuffReqId ?? '')
  })

  const { data: stuffListResponse, refetch: refetchStuffList } = useQuery({
    queryKey: [isTake ? 'TAKE_STUFF_REQUEST_KEY' : 'RETURN_STUFF_REQUEST_KEY', JSON.stringify(stuffParams)],
    queryFn: () => {
      let res: Promise<ListResponse<StuffRequestType>>
      if (isTake) {
        res = WpQueryMethods.getStuffReqToMe({ ...stuffParams, type: 'TAKE' })
      }
      if (isReturn) {
        res = WpQueryMethods.getStuffReqToMe({ ...stuffParams, type: 'RETURN' })
      }
      return res
    },
    placeholderData: defaultListData as ListResponse<StuffRequestType>
  })

  const value = {
    router,
    isMobile,
    stuffParams,
    setPartialStuffParam,
    setStuffParams,
    stuffListResponse,
    stuffData,
    isTake,
    isReturn,
    refetchStuffData,
    refetchStuffList
  }
  return <StuffContext.Provider value={value}>{children}</StuffContext.Provider>
}
