import * as Sentry from '@sentry/react'
import LoadingButton from '@mui/lab/LoadingButton'
import { Breadcrumbs, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import { useJob } from '../context/JobContext'
import JobCodeList from './components/JobCodeList'
import { toast } from 'react-toastify'

const CreateCodeComponent = () => {
  const { navigate, fields, onSubmitHandler, loadingAddJobCode } = useJob()
  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kode</Typography>
            </Link>
            <Link to='/rnm/job-code' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Job Code</Typography>
            </Link>
            <Typography>Tambah Job Code</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
            <div className='flex flex-col max-sm:text-center'>
              <Typography variant='h4'>Tambah Job Code</Typography>
              <Typography>Tambahkan job code untuk digunakan dalam dokumen MRO</Typography>
            </div>
            <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
              <Button
                color='secondary'
                variant='outlined'
                disabled={loadingAddJobCode}
                onClick={() => navigate('/rnm/job-code')}
              >
                Batalkan
              </Button>
              <LoadingButton
                startIcon={<></>}
                loading={loadingAddJobCode}
                variant='contained'
                onClick={fields.handleSubmit(onSubmitHandler, errors => {
                  console.error(errors)
                  Sentry.captureException(errors)
                  Object.entries(errors).forEach(([field, error]) => {
                    toast.error(`${field}: ${error?.message}`, {
                      autoClose: 5000
                    })
                  })
                })}
              >
                Simpan Semua
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <JobCodeList />
        </Grid>
      </Grid>
    </>
  )
}

export default CreateCodeComponent
