import * as Sentry from '@sentry/react'
import { defaultListData } from '@/api/queryClient'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { ListParams } from '@/types/payload'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQuery } from '@tanstack/react-query'
import { createContext, useContext, useState } from 'react'
import { useFieldArray, UseFieldArrayReturn, useForm, UseFormReturn } from 'react-hook-form'
import { NavigateFunction, useNavigate } from 'react-router-dom'
import { createComponentSchema, JobCodeType as FieldComponentType } from '../create/config/schema'
import { useMenu } from '@/components/menu/contexts/menuContext'

import EditJobCodeDialog from '@/components/dialogs/edit-code-job-code-dialog'
import { CodeParams, CodePayload, CodeType } from '@/types/codes'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useAddJobCode, useEditJobCode, useRemoveJobCode } from '@/api/services/rnm/mutation'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'

interface JobCodeProps {
  componentParams: ListParams
  setComponentParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialComponentParams: (fieldName: keyof ListParams, value: any) => void
  isMobile: boolean
  navigate: NavigateFunction
  fields: UseFormReturn<FieldComponentType, any, undefined>
  fieldArray: UseFieldArrayReturn<FieldComponentType, 'items', 'id'>
  onSubmitHandler: (data: FieldComponentType) => void
  handleEditJobCode: (item: CodeType) => void
  loadingAddJobCode: boolean
  loadingEditJobCode: boolean
  jobCodeList: ListResponse<CodeType>
  activeJobCode: CodeType
  onDeleteHandler: (id: string) => void
}

const CodeContext = createContext<JobCodeProps>({} as JobCodeProps)

export const useJob = () => {
  const context = useContext(CodeContext)
  if (!context) {
    throw new Error('useCode must be used within a CodeContextProvider')
  }
  return context
}

export type JobType = {
  id: string
  code: string
  description: string
  createdAt: string
  number: number
  smcs: string
  task: string
  cluster: string
}

export type CodeJobType = {
  id: string
  name: string
  code: string
  description: string
}

export const JobContextProvider = ({ children }: any) => {
  const { setConfirmState } = useMenu()
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const { userProfile } = useAuth()
  const [componentParams, setPartialComponentParams, setComponentParams] = usePartialState<CodeParams>({
    limit: 10,
    page: 1,
    type: 'JOB'
  })
  const [activeJobCode, setActiveJobCode] = useState<CodeType | null>(null)

  const fields = useForm<FieldComponentType>({
    resolver: zodResolver(createComponentSchema)
  })

  const fieldArray = useFieldArray({ control: fields.control, name: 'items' })

  const { data: jobCodeList, refetch: refetchCodeList } = useQuery({
    queryKey: ['CODE_JOB_CODE_QUERY_KEY', JSON.stringify(componentParams)],
    queryFn: () => {
      return RnMQueryMethods.getCodeList(componentParams)
    },
    placeholderData: defaultListData as ListResponse<CodeType>
  })

  const { mutate: addJobCodeMutate, isLoading: loadingAddJobCode } = useAddJobCode()
  const { mutate: editJobCodeMutate, isLoading: loadingEditJobCode } = useEditJobCode()
  const { mutate: removeJobCodeMutate } = useRemoveJobCode()

  const handleEditJobCode = (item: CodeType) => {
    setActiveJobCode(item)
  }

  const onSubmitHandler = (data: FieldComponentType) => {
    Sentry.captureMessage(`Submit Code Job: ${JSON.stringify(data)}`)
    setConfirmState({
      open: true,
      title: 'Simpan Job Code',
      content:
        'Apakah kamu yakin akan menyimpan dan menambahkan list job code? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Simpan',
      onConfirm: () => {
        addJobCodeMutate(
          {
            companyId: userProfile?.companyId,
            type: 'JOB',
            codes: data?.items?.map(x => ({
              code: x?.code,
              codeFamilyIds: [],
              description: x?.description,
              newLifeRecognition: x?.newLifeRecognition === 'true',
              isUseStock: x?.isUseStock === 'true',
              usage: x?.usage
            }))
          },
          {
            onSuccess(data) {
              toast.success('Job Code berhasil dibuat')
              setTimeout(() => {
                fields.reset({ items: [] })
                refetchCodeList()
                navigate('/rnm/job-code')
              }, 1000)
            }
          }
        )
      }
    })
  }

  const onEditHandler = (item: CodeType) => {
    editJobCodeMutate(
      {
        id: String(item.id),
        code: item.code,
        description: item.description,
        usage: item.usage,
        newLifeRecognition: item.newLifeRecognition,
        isUseStock: item.isUseStock,
        codeFamilyIds: []
      },
      {
        onSuccess(data) {
          setActiveJobCode(null)
          toast.success('Job Code berhasil diubah')
          refetchCodeList()
        }
      }
    )
  }

  const onDeleteHandler = (id: string) => {
    removeJobCodeMutate(id, {
      onSuccess() {
        refetchCodeList()
        toast.success('Job Code berhasil dihapus')
      }
    })
  }

  const value = {
    isMobile,
    componentParams,
    setPartialComponentParams,
    setComponentParams,
    navigate,
    fields,
    fieldArray,
    jobCodeList,
    onSubmitHandler,
    handleEditJobCode,
    activeJobCode,
    loadingAddJobCode,
    loadingEditJobCode,
    onDeleteHandler
  }
  return (
    <CodeContext.Provider value={value}>
      {activeJobCode && (
        <EditJobCodeDialog
          onSubmitHandler={onEditHandler}
          handleClose={() => setActiveJobCode(null)}
          open={!!activeJobCode}
          jobCode={activeJobCode}
          setOpen={setActiveJobCode}
        />
      )}
      {children}
    </CodeContext.Provider>
  )
}

export default CodeContext
