import { createColumnHelper } from '@tanstack/react-table'
import { ComponentType } from '../../context/CodeContext'
import { id } from 'date-fns/locale'
import { Grid, IconButton } from '@mui/material'
import { formatDate } from 'date-fns'
import { CodeType } from '@/types/codes'

const columnHelper = createColumnHelper<CodeType>()

type RowAction = {
  detail: (item: CodeType) => void
  delete: (item: CodeType) => void
}

export const tableColumns = (rowActions?: RowAction) => [
  columnHelper.accessor('code', {
    header: 'KODE KOMPONEN'
  }),
  columnHelper.accessor('description', {
    header: 'DESKRIPSI'
  }),
  columnHelper.accessor('family', {
    header: 'FAMILY',
    cell: ({ row }) => row.original.codeFamilies?.map(family => family.code).join(', ')
  }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) => formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id })
  }),
  columnHelper.accessor('id', {
    header: 'ACTION',
    cell: ({ row }) => (
      <Grid container spacing={1}>
        <IconButton onClick={() => rowActions.delete(row.original)}>
          <i className='ri-delete-bin-7-line text-googlePlus' />
        </IconButton>
        <IconButton onClick={() => rowActions.detail(row.original)}>
          <i className='ri-pencil-line' />
        </IconButton>
      </Grid>
    )
  })
]
