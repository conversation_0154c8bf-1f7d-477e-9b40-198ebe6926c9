import Grid from '@mui/material/Grid'
import { Typography } from '@mui/material'
import CodeFamily from './components/CodeFamilyCard'
import ComponentList from './components/ComponentList'

const CodeComponentListPage = () => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Component</Typography>
            <Typography>Semua kode komponen yang sudah terdaftar akan ditampilkan di sini</Typography>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <CodeFamily />
      </Grid>
      <Grid item xs={12}>
        <ComponentList />
      </Grid>
    </Grid>
  )
}

export default CodeComponentListPage
