import { <PERSON>, Card, CardContent, CardHeader, Typography } from '@mui/material'
import { useComponent } from '../../context/CodeContext'

const CodeFamily = () => {
  const { codeFamilyList, loadingCodeFamilyList } = useComponent()
  return (
    <Card>
      <CardHeader title='Kode Family' />
      <CardContent>
        {loadingCodeFamilyList ? (
          <Box>Loading...</Box>
        ) : (
          <Box className='grid grid-cols-2 md:grid-cols-[repeat(4,minmax(200px,1fr))] max-h-56 overflow-y-auto md:overflow-y-hidden md:max-h-[unset] gap-4 gap-y-2'>
            {codeFamilyList?.map(family => (
              <Box
                key={family.id}
                className='bg-[#4C4E640D] px-2 py-3 rounded-[10px] grid grid-cols-[auto_1fr] justify-items-end items-center'
              >
                <Typography>{family.code}</Typography>
                <Typography variant='caption' align='right'>
                  {family.description}
                </Typography>
              </Box>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

export default CodeFamily
