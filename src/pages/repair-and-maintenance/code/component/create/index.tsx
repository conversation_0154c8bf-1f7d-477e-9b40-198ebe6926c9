import * as Sentry from '@sentry/react'
import LoadingButton from '@mui/lab/LoadingButton'
import { Breadcrumbs, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import { useComponent } from '../context/CodeContext'
import ComponentList from './components/ComponentList'
import { toast } from 'react-toastify'

const CreateCodeComponent = () => {
  const { navigate, fields, onSubmitHandler, loadingAddComponentCode } = useComponent()
  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kode</Typography>
            </Link>
            <Link to='/rnm/component' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Component</Typography>
            </Link>
            <Typography>Tambah Component</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
            <div className='flex flex-col max-sm:text-center'>
              <Typography variant='h4'>Tambah Component</Typography>
              <Typography>Tambahkan component untuk digunakan dalam dokumen MRO</Typography>
            </div>
            <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
              <Button
                color='secondary'
                variant='outlined'
                disabled={loadingAddComponentCode}
                onClick={() => {
                  fields.reset({ items: [] })
                  navigate('/rnm/component')
                }}
              >
                Batalkan
              </Button>
              <LoadingButton
                startIcon={<></>}
                loading={loadingAddComponentCode}
                variant='contained'
                onClick={fields.handleSubmit(onSubmitHandler, errors => {
                  console.error(errors)
                  Sentry.captureException(errors)
                  Object.entries(errors).forEach(([field, error]) => {
                    toast.error(`${field}: ${error?.message}`, {
                      autoClose: 5000
                    })
                  })
                })}
              >
                Simpan Semua
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <ComponentList />
        </Grid>
      </Grid>
    </>
  )
}

export default CreateCodeComponent
