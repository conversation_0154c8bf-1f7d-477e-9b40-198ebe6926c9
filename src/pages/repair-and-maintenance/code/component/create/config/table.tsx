import { createColumnHelper } from '@tanstack/react-table'
import { CodeComponentType, ComponentType } from '../../context/CodeContext'
import {
  Box,
  Checkbox,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  ListItemText,
  MenuItem,
  Select,
  TextField
} from '@mui/material'
import { type ComponentType as FieldComponentType } from './schema'
import { Controller, UseFormReturn } from 'react-hook-form'
import { CodeType } from '@/types/codes'

const columnHelper = createColumnHelper<FieldComponentType['items']>()

type RowAction = {
  onDelete: (index: number) => void
}

type TableColumnsType = {
  codeFamilies?: CodeType[]
  fields: UseFormReturn<FieldComponentType, any, undefined>
} & RowAction

export const tableColumns = (props: TableColumnsType) => {
  const { onDelete, codeFamilies, fields } = props
  return [
    columnHelper.accessor('code', {
      header: 'KODE KOMPONEN',
      size: 70,
      cell: ({ row }) => {
        return (
          <Box sx={{ width: '100%' }}>
            <Controller
              control={fields.control}
              name={`items.${row.index}.code`}
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <TextField
                    error={Boolean(error)}
                    size='small'
                    defaultValue={value}
                    onBlur={e => onChange(e.target.value)}
                  />
                </FormControl>
              )}
            />
          </Box>
        )
      }
    }),
    columnHelper.accessor('description', {
      header: 'DESKRIPSI',
      size: 250,
      cell: ({ row }) => {
        return (
          <Controller
            control={fields.control}
            name={`items.${row.index}.description`}
            render={({ field: { onChange, value }, fieldState: { error } }) => (
              <FormControl fullWidth>
                <TextField
                  error={Boolean(error)}
                  size='small'
                  defaultValue={value}
                  onBlur={e => onChange(e.target.value)}
                />
              </FormControl>
            )}
          />
        )
      }
    }),
    columnHelper.accessor('family', {
      header: 'FAMILY',
      size: 150,
      cell: ({ row }) => {
        return (
          <FormControl fullWidth>
            <Controller
              control={fields.control}
              name={`items.${row.index}.family`}
              render={({ field: { onChange, value }, fieldState: { error } }) => {
                return (
                  <Select
                    error={Boolean(error)}
                    id='numbering-divider'
                    value={value}
                    renderValue={selected =>
                      selected?.map(f => codeFamilies?.find(c => String(c.id) === f)?.code).join(', ')
                    }
                    onChange={e => onChange(e.target.value)}
                    placeholder='Pilih Family'
                    multiple
                    size='small'
                    inputProps={{
                      className: 'bg-white dark:bg-inherit'
                    }}
                    MenuProps={{
                      PaperProps: {
                        className: 'max-h-[200px] overflow-y-auto'
                      }
                    }}
                  >
                    {codeFamilies?.map(family => (
                      <MenuItem key={family.id} value={String(family.id)}>
                        <Checkbox checked={value?.includes(String(family.id))} />
                        <ListItemText primary={family.code} />
                      </MenuItem>
                    ))}
                  </Select>
                )
              }}
            />
          </FormControl>
        )
      }
    }),
    columnHelper.accessor('id', {
      header: '',
      size: 50,
      cell: ({ row }) => (
        <Grid container justifyContent='center' spacing={1}>
          <IconButton onClick={() => onDelete(row.index)}>
            <i className='ri-delete-bin-7-line text-googlePlus' />
          </IconButton>
        </Grid>
      )
    })
  ]
}
