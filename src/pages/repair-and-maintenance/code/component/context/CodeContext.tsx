import * as Sentry from '@sentry/react'
import { defaultListData } from '@/api/queryClient'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { ListParams } from '@/types/payload'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQuery } from '@tanstack/react-query'
import { createContext, useContext, useEffect, useState } from 'react'
import { FormProvider, useFieldArray, UseFieldArrayReturn, useForm, UseFormReturn } from 'react-hook-form'
import { NavigateFunction, useNavigate } from 'react-router-dom'
import {
  createComponentSchema,
  ComponentType as FieldComponentType,
  ObjectComponentType
} from '../create/config/schema'
import { useMenu } from '@/components/menu/contexts/menuContext'
import EditCodeComponentDialog from '@/components/dialogs/edit-code-component-dialog'
import { CodeParams, CodeType } from '@/types/codes'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useAddJobCode, useEditJobCode, useRemoveJobCode } from '@/api/services/rnm/mutation'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'

interface CodeComponentProps {
  codeFamilyList: CodeType[]
  componentsList: ListResponse<CodeType>
  activeComponent: CodeType
  loadingCodeFamilyList: boolean
  componentParams: ListParams
  setComponentParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialComponentParams: (fieldName: keyof ListParams, value: any) => void
  isMobile: boolean
  navigate: NavigateFunction
  fields: UseFormReturn<FieldComponentType, any, undefined>
  fieldArray: UseFieldArrayReturn<FieldComponentType, 'items', 'id'>
  onSubmitHandler: (data: FieldComponentType) => void
  onDeleteHandler: (id: string) => void
  onEditHandler: (data: ObjectComponentType) => void
  handleEditComponent: (item: CodeType) => void
  loadingAddComponentCode: boolean
  loadingUpdateComponentCode: boolean
}

const CodeContext = createContext<CodeComponentProps>({} as CodeComponentProps)

export const useComponent = () => {
  const context = useContext(CodeContext)
  if (!context) {
    throw new Error('useCode must be used within a CodeContextProvider')
  }
  return context
}

export type ComponentType = {
  id: string
  code: string
  description: string
  family: string[]
  createdAt: string
  [key: string]: any
}

export type CodeComponentType = {
  id: string
  name: string
  code: string
  description: string
}

export const CodeContextProvider = ({ children }: any) => {
  const { setConfirmState } = useMenu()
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const { userProfile } = useAuth()
  const [componentParams, setPartialComponentParams, setComponentParams] = usePartialState<CodeParams>({
    limit: 10,
    page: 1,
    type: 'COMPONENT'
  })
  const [activeComponent, setActiveComponent] = useState<CodeType | null>(null)

  const fields = useForm<FieldComponentType>({
    resolver: zodResolver(createComponentSchema)
  })

  const fieldArray = useFieldArray({ control: fields.control, name: 'items' })

  const { data: codeFamilyList, isLoading: loadingCodeFamilyList } = useQuery({
    queryKey: ['CODE_FAMILY_QUERY_KEY'],
    queryFn: async () => {
      const response = await RnMQueryMethods.getCodeList({
        limit: Number.MAX_SAFE_INTEGER,
        page: 1,
        type: 'FAMILY'
      })
      return response?.items || []
    },
    placeholderData: [] as CodeType[]
  })

  const { data: componentsList, refetch: refetchCodeList } = useQuery({
    queryKey: ['CODE_COMPONENT_QUERY_KEY', JSON.stringify(componentParams)],
    queryFn: () => RnMQueryMethods.getCodeList(componentParams),
    placeholderData: defaultListData as ListResponse<CodeType>
  })

  const { mutate: addComponentCode, isLoading: loadingAddComponentCode } = useAddJobCode()
  const { mutate: removeComponentCode } = useRemoveJobCode()
  const { mutate: updateComponentCode, isLoading: loadingUpdateComponentCode } = useEditJobCode()

  const onSubmitHandler = (data: FieldComponentType) => {
    Sentry.captureMessage(`Submit Component Code: ${JSON.stringify(data)}`)
    setConfirmState({
      open: true,
      title: 'Simpan Component',
      content:
        'Apakah kamu yakin akan menyimpan dan menambahkan list komponen? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Simpan',
      onConfirm: () => {
        addComponentCode(
          {
            type: 'COMPONENT',
            companyId: userProfile?.companyId,
            codes: data?.items?.map(component => ({
              code: component?.code,
              codeFamilyIds: component?.family?.map(f => +f),
              description: component?.description,
              newLifeRecognition: false,
              usage: '---'
            }))
          },
          {
            onSuccess: () => {
              toast.success('Component Berhasil dibuat')
              setTimeout(() => {
                fields.reset({ items: [] })
                refetchCodeList()
                navigate('/rnm/component')
              }, 1000)
            }
          }
        )
      }
    })
  }

  const onEditHandler = (data: ObjectComponentType) => {
    updateComponentCode(
      {
        id: data.id,
        code: data.code,
        description: data.description,
        newLifeRecognition: false,
        usage: '---',
        codeFamilyIds: data.family?.map(f => +f)
      },
      {
        onSuccess: () => {
          toast.success('Component Berhasil diubah')
          refetchCodeList()
          setActiveComponent(null)
        }
      }
    )
  }

  const onDeleteHandler = (id: string) => {
    removeComponentCode(id, {
      onSuccess: () => {
        refetchCodeList()
        toast.success('Component Berhasil dihapus')
      }
    })
  }

  const handleEditComponent = (item: CodeType) => {
    setActiveComponent(item)
  }

  const value = {
    isMobile,
    codeFamilyList,
    loadingCodeFamilyList,
    componentParams,
    setPartialComponentParams,
    setComponentParams,
    navigate,
    fields,
    fieldArray,
    onSubmitHandler,
    onDeleteHandler,
    onEditHandler,
    handleEditComponent,
    componentsList,
    activeComponent,
    loadingAddComponentCode,
    loadingUpdateComponentCode
  }
  return (
    <CodeContext.Provider value={value}>
      <FormProvider {...fields}>
        {children}
        {!!activeComponent && (
          <EditCodeComponentDialog
            familyList={codeFamilyList}
            component={activeComponent}
            open={!!activeComponent}
            setOpen={setActiveComponent}
            handleClose={() => setActiveComponent(null)}
          />
        )}
      </FormProvider>
    </CodeContext.Provider>
  )
}

export default CodeContext
