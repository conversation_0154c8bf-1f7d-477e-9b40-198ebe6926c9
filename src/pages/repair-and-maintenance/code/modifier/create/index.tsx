import * as Sentry from '@sentry/react'
import LoadingButton from '@mui/lab/LoadingButton'
import { Breadcrumbs, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import { useModifier } from '../context/ModifierContext'
import ModifierList from './component/ModifierList'
import { toast } from 'react-toastify'

const CreateCodeComponent = () => {
  const { navigate, field, handleSubmitModifier, loadingAddModifierCode } = useModifier()
  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kode</Typography>
            </Link>
            <Link to='/rnm/modifier' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kode Modifier</Typography>
            </Link>
            <Typography>Tambah Kode Modifier</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
            <div className='flex flex-col max-sm:text-center'>
              <Typography variant='h4'>Tambah Kode Modifier</Typography>
              <Typography>Tambahkan kode modifier untuk digunakan dalam dokumen MRO</Typography>
            </div>
            <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
              <Button
                color='secondary'
                variant='outlined'
                disabled={loadingAddModifierCode}
                onClick={() => navigate('/rnm/modifier')}
              >
                Batalkan
              </Button>
              <LoadingButton
                startIcon={<></>}
                loading={loadingAddModifierCode}
                variant='contained'
                onClick={field.handleSubmit(handleSubmitModifier, errors => {
                  console.error(errors)
                  Sentry.captureException(errors)
                  Object.entries(errors).forEach(([field, error]) => {
                    toast.error(`${field}: ${error?.message}`, {
                      autoClose: 5000
                    })
                  })
                })}
              >
                Simpan Semua
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <ModifierList />
        </Grid>
      </Grid>
    </>
  )
}

export default CreateCodeComponent
