import DebouncedInput from '@/components/DebounceInput'
import { Box, Button, Card, FormHelperText, IconButton, Menu, MenuItem, Typography } from '@mui/material'
import { memo, MouseEvent, useCallback, useRef, useState } from 'react'
import OpenDialogOnElementClick from '@/components/dialogs/OpenDialogOnElementClick'
import AddRoleDialog from '@/components/dialogs/add-role-dialog'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { useModifier } from '../../context/ModifierContext'

const JobCodeList = () => {
  const {
    isMobile,
    modifierList: { items, totalItems, totalPages },
    modifierParams: { page, search },
    fieldArray: { append, remove, fields },
    codeFamilyList,
    field
  } = useModifier()

  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [actionBtn, setBtn] = useState<boolean>(false)
  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const table = useReactTable({
    data: fields as any,
    columns: tableColumns({
      codeFamilies: codeFamilyList,
      delete: item => {
        remove(items.indexOf(item))
      },
      field
    }),
    initialState: {
      pagination: {
        pageSize: 10,
        pageIndex: page - 1
      }
    },
    manualPagination: true,
    rowCount: fields.length,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const handleAddClose = () => {
    setAddAnchorEl(null)
  }

  const handleAddClick = useCallback((event: MouseEvent<HTMLButtonElement>) => {
    append({ code: null, description: null, family: [] })
  }, [])

  const actionButton = (
    <></>
    // <Button
    //   color='secondary'
    //   variant='outlined'
    //   startIcon={<i className='ri-upload-2-line' />}
    //   className='is-full sm:is-auto'
    // >
    //   Ekspor
    // </Button>
  )

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
          <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
            <Typography variant='h5'>List Kode Modifier</Typography>
          </div>
          <Button variant='outlined' aria-haspopup='true' onClick={handleAddClick} className='is-full sm:is-auto'>
            Tambah List
          </Button>
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Data</Typography>
              <Typography className='text-sm text-gray-400'>
                Semua data segment dan job code akan ditampilkan di sini
              </Typography>
            </td>
          }
        />
        {(field.formState.errors.items || field.formState.errors.items?.root) && (
          <FormHelperText sx={{ my: 4 }} error>
            {field.formState.errors.items?.root?.message || field.formState.errors.items?.message}
          </FormHelperText>
        )}
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default memo(JobCodeList)
