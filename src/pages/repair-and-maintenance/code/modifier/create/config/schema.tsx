import * as z from 'zod'

export const createModifierSchema = z.object({
  items: z
    .array(
      z.object({
        code: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
        description: z.string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
        family: z
          .array(z.string({ message: 'Wajib Diisi' }).min(1, { message: 'Wajib Diisi' }))
          .min(1, { message: 'Wajib Diisi' })
      })
    )
    .min(1, { message: 'Minimal 1 item component' })
})

export type ModifierFieldType = Required<z.TypeOf<typeof createModifierSchema>>
