import * as Sentry from '@sentry/react'
import { defaultListData } from '@/api/queryClient'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import React, { useState } from 'react'
import { useFieldArray, UseFieldArrayReturn, useForm, UseFormReturn } from 'react-hook-form'
import { NavigateFunction, useNavigate } from 'react-router-dom'
import { createModifierSchema, ModifierFieldType } from '../create/config/schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { CodeComponentType, ComponentType } from '../../component/context/CodeContext'

import EditModifierDialog from '@/components/dialogs/edit-code-modifier'
import { CodeParams, CodeType } from '@/types/codes'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useAddJobCode, useEditJobCode, useRemoveJobCode } from '@/api/services/rnm/mutation'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'

interface ModifierContextProps {
  modifierList: ListResponse<CodeType>
  codeFamilyList: CodeType[]
  isMobile: boolean
  modifierParams: ListParams
  setPartialModifierParams: (fieldName: keyof ListParams, value: any) => void
  setModifierParams: React.Dispatch<React.SetStateAction<ListParams>>
  field: UseFormReturn<ModifierFieldType, any, undefined>
  fieldArray: UseFieldArrayReturn<ModifierFieldType, 'items', 'id'>
  navigate: NavigateFunction
  handleSubmitModifier: (data: ModifierFieldType) => void
  onDeleteHandler: (id: string) => void
  handleSubmitEditModifier: (data: ComponentType) => void
  activeModifier: CodeType
  loadingAddModifierCode: boolean
  loadingEditModifierCode: boolean
  setActiveModifier: React.Dispatch<React.SetStateAction<CodeType>>
  handleEditModifier: (item: CodeType) => void
}

export type ModifierType = {
  code: string
  description: string
  id: string
  family: string
  createdAt: string
}

const ModifierContext = React.createContext({} as ModifierContextProps)

export const useModifier = () => {
  const context = React.useContext(ModifierContext)
  if (context === undefined) {
    throw new Error('useModifier must be used within a ModifierProvider')
  }
  return context
}

export const ModifierProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const { setConfirmState } = useMenu()
  const {
    userProfile: { companyId }
  } = useAuth()
  const [modifierParams, setPartialModifierParams, setModifierParams] = usePartialState<CodeParams>({
    page: 1,
    limit: 10,
    type: 'MODIFIER'
  })

  const [activeModifier, setActiveModifier] = useState<CodeType | null>(null)

  const field = useForm<ModifierFieldType>({
    resolver: zodResolver(createModifierSchema)
  })

  const fieldArray = useFieldArray({ control: field.control, name: 'items' })

  const { data: modifierList, refetch: refetchModifierList } = useQuery({
    queryKey: ['MODIFIER_LIST_KEY', JSON.stringify(modifierParams)],
    queryFn: () => RnMQueryMethods.getCodeList(modifierParams),
    placeholderData: defaultListData as ListResponse<CodeType>
  })

  const { data: codeFamilyList } = useQuery({
    queryKey: ['CODE_FAMILY_QUERY_KEY'],
    queryFn: async () => {
      const response = await RnMQueryMethods.getCodeList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        type: 'FAMILY'
      })
      return response.items
    },
    placeholderData: [] as CodeType[]
  })

  const { mutate: addModifierCode, isLoading: loadingAddModifierCode } = useAddJobCode()
  const { mutate: editModifierCode, isLoading: loadingEditModifierCode } = useEditJobCode()
  const { mutate: removeModifierCode } = useRemoveJobCode()

  const handleSubmitModifier = (data: ModifierFieldType) => {
    Sentry.captureMessage(`Submit Code Modifier: ${JSON.stringify(data)}`)
    setConfirmState({
      open: true,
      title: 'Simpan Kode Modifier',
      content:
        'Apakah kamu yakin akan menyimpan dan menambahkan list kode modifier? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Simpan',
      onConfirm: () => {
        addModifierCode(
          {
            type: 'MODIFIER',
            companyId,
            codes: data?.items?.map(c => ({
              code: c?.code,
              description: c?.description,
              usage: '---',
              newLifeRecognition: false,
              codeFamilyIds: c?.family?.map(f => +f)
            }))
          },
          {
            onSuccess: () => {
              toast.success('Kode Modifier Berhasil dibuat')
              refetchModifierList()
              setTimeout(() => {
                field.reset({ items: [] })
                navigate('/rnm/modifier')
              }, 1000)
            }
          }
        )
      }
    })
  }

  const handleSubmitEditModifier = (data: ComponentType) => {
    editModifierCode(
      {
        id: data.id,
        code: data.code,
        description: data.description,
        usage: '---',
        newLifeRecognition: false,
        codeFamilyIds: data.family?.map(f => +f)
      },
      {
        onSuccess: () => {
          toast.success('Kode Modifier Berhasil diubah')
          refetchModifierList()
          setActiveModifier(null)
        }
      }
    )
  }

  const handleEditModifier = (item: CodeType) => {
    setActiveModifier(item)
  }

  const onDeleteHandler = (id: string) => {
    removeModifierCode(id, {
      onSuccess: () => {
        toast.success('Kode Modifier Berhasil dihapus')
        refetchModifierList()
      }
    })
  }

  const value = {
    isMobile,
    modifierParams,
    modifierList,
    fieldArray,
    field,
    codeFamilyList,
    setPartialModifierParams,
    setModifierParams,
    navigate,
    handleSubmitModifier,
    activeModifier,
    setActiveModifier,
    handleEditModifier,
    onDeleteHandler,
    handleSubmitEditModifier,
    loadingAddModifierCode,
    loadingEditModifierCode
  }

  return (
    <ModifierContext.Provider value={value}>
      {activeModifier && (
        <EditModifierDialog
          familyList={codeFamilyList}
          modifier={activeModifier}
          open={!!activeModifier}
          setOpen={setActiveModifier}
          handleClose={() => setActiveModifier(null)}
        />
      )}
      {children}
    </ModifierContext.Provider>
  )
}
