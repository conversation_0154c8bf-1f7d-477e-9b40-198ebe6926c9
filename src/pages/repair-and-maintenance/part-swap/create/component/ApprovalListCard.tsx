// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { Autocomplete, createFilterOptions, TextField } from '@mui/material'

// Component Imports

import { UserType } from '@/types/userTypes'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { toCurrency } from '@/utils/helper'

type ApprovalListCardProps = {
  approverList?: UserType[]
  scope?: DefaultApprovalScope
  amount?: number
}

const ApprovalListCard = ({ approverList, scope, amount = 0 }: ApprovalListCardProps) => {
  const filter = createFilterOptions<any>()

  const isPurchaseApproval =
    scope === DefaultApprovalScope.DirectPurchase || scope === DefaultApprovalScope.PurchaseOrder

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Ajukan <PERSON></Typography>
        </div>
        <div className='flex flex-col gap-4'>
          {approverList
            ?.filter(approver => approver.threshold <= amount || !isPurchaseApproval)
            ?.map((approver, index) => (
              <Autocomplete
                key={approver.id}
                value={approver}
                filterOptions={(options, params) => {
                  const filtered = filter(options, params)

                  return filtered
                }}
                options={approverList}
                getOptionLabel={option => {
                  // Value selected with enter, right from the input
                  if (typeof option === 'string') {
                    return option
                  }

                  // Regular option
                  return option.fullName
                }}
                renderOption={(props, option) => {
                  const { key, ...optionProps } = props

                  return (
                    <li key={key} {...optionProps}>
                      {option.fullName}
                    </li>
                  )
                }}
                readOnly
                freeSolo
                renderInput={params => (
                  <TextField
                    {...params}
                    label={`Persetujuan Tingkat ${Number(index) + 1}`}
                    InputProps={{
                      endAdornment: isPurchaseApproval ? (
                        <Typography variant='caption' className='!text-green-700'>
                          {toCurrency(approver.threshold ?? 0)}
                        </Typography>
                      ) : null
                    }}
                  />
                )}
              />
            ))}
        </div>
      </CardContent>
    </Card>
  )
}

export default ApprovalListCard
