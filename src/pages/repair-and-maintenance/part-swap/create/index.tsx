import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Grid, Typo<PERSON> } from '@mui/material'
import { Link, useNavigate, useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import LoadingButton from '@mui/lab/LoadingButton'
import { useEffect, useState } from 'react'

import { useAuth } from '@/contexts/AuthContext'

import ItemListCard from './component/ItemListCard'
import AdditionalInfoCard from './component/AdditionalInfoCard'
import ApprovalListCard from './component/ApprovalListCard'

import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import UnitSwapCard from './component/UnitSwapCard'
import * as Sentry from '@sentry/react'
import { toast } from 'react-toastify'
import { FormProvider, useForm } from 'react-hook-form'
import { WoPartSwapDtoType } from '@/types/partSwapTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import { createPartSwapSchema } from './config/schema'
import { useCreatePartSwap } from '@/api/services/part-swap/mutation'
import { WO_QUERY_DETAIL_KEY } from '@/api/services/rnm/service'
import RnMQueryMethods from '@/api/services/rnm/query'
import { usePartSwap } from '../context/PartSwapContext'
import { PartSwapFormContext, usePartSwapForm } from '../context/FormContext'
import { WoSegmentType, WorkOrderType } from '@/types/woTypes'
import SegmentCard from './component/SegmentCard'

const CreateWoPartSwap = () => {
  const { userProfile } = useAuth()
  const params = useParams()
  const { selectedWoId } = usePartSwapForm()
  const { navigate, setConfirmState } = usePartSwap()

  const method = useForm<WoPartSwapDtoType>({
    resolver: zodResolver(createPartSwapSchema)
  })

  const { handleSubmit, reset, getValues } = method

  const scope = DefaultApprovalScope.PartSwap

  const { mutate: createPartSwap, isLoading: creatingPartSwap } = useCreatePartSwap()

  const { data: woDetail } = useQuery({
    enabled: !!selectedWoId,
    queryKey: [WO_QUERY_DETAIL_KEY, selectedWoId],
    queryFn: () => RnMQueryMethods.getWoDetail(selectedWoId)
  })

  const { data: approverList } = useQuery({
    enabled: !!woDetail?.siteId && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, woDetail?.siteId, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope,
        siteId: woDetail?.siteId,
        departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  const handleSubmitPartSwap = (data: WoPartSwapDtoType) => {
    Sentry.captureMessage(`Submit Part Swap: ${JSON.stringify(data)}`)
    setConfirmState({
      open: true,
      title: 'Buat Part Swap',
      content: 'Apakah kamu yakin akan membuat Part Swap ini? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Buat Part Swap',
      onConfirm: () => {
        createPartSwap(data, {
          onSuccess: () => {
            toast.success('Part Swap berhasil dibuat')
            setTimeout(() => {
              navigate('/part-swap/list')
            }, 700)
          }
        })
      }
    })
  }

  useEffect(() => {
    reset({
      ...getValues(),
      workOrderSegmentId: params?.segmentId,
      destinationUnitHm: woDetail?.unitHm,
      destinationUnitKm: woDetail?.unitKm,
      destinationUnitId: woDetail?.unitId,
      destinationUnitSiteId: woDetail?.siteId,
      priority: 4
    })
  }, [woDetail])

  useEffect(() => {
    reset({
      ...getValues(),
      approvals: approverList?.map(approver => ({
        userId: approver.user?.id
      }))
    })
  }, [approverList])

  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='/part-swap/list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Part Swap</Typography>
            </Link>
            <Typography>Buat Part Swap</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
            <div className='flex flex-col max-sm:text-center'>
              <Typography variant='h4'>Buat Part Swap</Typography>
              <Typography>Buat dokumen permintaan part Swap untuk diajukan kepada role terkait</Typography>
            </div>
            <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
              <Button color='secondary' variant='outlined' disabled={creatingPartSwap} onClick={() => navigate(-1)}>
                Batalkan
              </Button>
              <LoadingButton
                startIcon={<></>}
                loading={creatingPartSwap}
                variant='contained'
                onClick={handleSubmit(handleSubmitPartSwap, errors => {
                  console.log(getValues())
                  console.error(errors)
                  Sentry.captureException(errors)
                  Object.entries(errors).forEach(([field, error]) => {
                    toast.error(`${field}: ${error?.message}`, {
                      autoClose: 5000
                    })
                  })
                })}
              >
                Buat Part Swap
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <SegmentCard />
        </Grid>
        <Grid item xs={12}>
          <UnitSwapCard />
        </Grid>
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <AdditionalInfoCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default CreateWoPartSwap
