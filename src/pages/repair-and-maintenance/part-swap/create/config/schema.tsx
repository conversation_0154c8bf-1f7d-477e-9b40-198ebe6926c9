import { z } from 'zod'

const imageSchema = z.object({
  uploadId: z.string().uuid()
})

export const partSwapItemSchema = z.object({
  swapType: z.enum(['EXCHANGED', 'MOVED']),
  originItemId: z.string().uuid(),
  originSerialNumber: z.string().optional().nullable(),
  destinationItemId: z.string().uuid(),
  destinationSerialNumberId: z.number().optional().nullable(),
  quantity: z.number().min(1),
  quantityUnit: z.string(),
  largeUnitQuantity: z.number().min(1),
  note: z.string().optional().nullable(),
  images: z.array(imageSchema)
})

const approvalSchema = z.object({
  userId: z.string().uuid()
})

export const createPartSwapSchema = z.object({
  workOrderSegmentId: z.string().uuid(),
  items: z.array(partSwapItemSchema).min(1, { message: 'items segment kosong' }),
  approvals: z.array(approvalSchema),
  note: z.string().min(1),
  priority: z.number().min(1).max(5),
  originUnitSiteId: z.string().uuid(),
  originUnitId: z.string().uuid(),
  originUnitKm: z.number().nonnegative(),
  originUnitHm: z.number().nonnegative(),
  destinationUnitSiteId: z.string().uuid(),
  destinationUnitId: z.string().uuid(),
  destinationUnitKm: z.number().nonnegative(),
  destinationUnitHm: z.number().nonnegative()
})
