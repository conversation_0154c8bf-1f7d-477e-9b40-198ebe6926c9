// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { But<PERSON>, Chip, TextField } from '@mui/material'
import { ThemeColor } from '@/core/types'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import { useAuth } from '@/contexts/AuthContext'
import { useState } from 'react'
import { ApproverType, UserType } from '@/types/userTypes'
import EditApproverDialog, { EditApproverInput } from '@/components/dialogs/edit-approver-dialog'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { MaterialTransferApprovalStatus, UserRole } from '@/pages/material-transfer/config/enum'
import {
  useUpdateMtApprovalStatus,
  useUpdateMtApprover,
  useUpdateMtCancelationStatus
} from '@/api/services/mt/mutation'
import { useMb } from '@/pages/material-borrow/context/MbContext'
import CompanyQueryMethods, { SITE_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { SiteType } from '@/types/companyTypes'
import { MrStatus } from '@/types/mrTypes'
import {
  useUpdatePartSwapApprovalStatus,
  useUpdatePartSwapApprover,
  useUpdatePartSwapCancelState
} from '@/api/services/part-swap/mutation'
import { usePartSwap } from '../../context/PartSwapContext'

type StatusChipType = {
  label: string
  color: ThemeColor
}

// Vars
export const statusChipValue: { [key: string]: StatusChipType } = {
  WAITING: { label: 'Menunggu', color: 'secondary' },
  PENDING: { label: 'Menunggu', color: 'secondary' },
  APPROVED: { label: 'Disetujui', color: 'success' },
  REJECTED: { label: 'Ditolak', color: 'error' }
}

const ApprovalsCard = ({ isCancelation, role }: { isCancelation?: boolean; role?: string }) => {
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { partSwapDetail, fetchPartSwapDetail, fetchPartSwapList, fetchPartSwapLogs, isApprovalPage } = usePartSwap()

  const { mutate: updateCancelationStatusMutate } = useUpdatePartSwapCancelState()
  const { mutate: updStatusMutate } = useUpdatePartSwapApprovalStatus()
  const { mutate: updateApproverMutate, isLoading: updateApproverLoading } = useUpdatePartSwapApprover()

  const updateStatusMutate = updStatusMutate

  const [{ open: editApproverOpen, selectedApproval }, setEditApproverModalState] = useState<{
    open: boolean
    selectedApproval?: ApproverType
  }>({
    open: false
  })

  const {
    data: { items: siteList }
  } = useQuery({
    queryKey: [SITE_LIST_QUERY_KEY],
    queryFn: () => {
      return CompanyQueryMethods.getSiteList({
        limit: Number.MAX_SAFE_INTEGER
      })
    },
    placeholderData: defaultListData as ListResponse<SiteType>
  })

  const approvalList = partSwapDetail?.approvals

  let ownApprovalIndex = -1
  const ownApproval = approvalList?.find((approval, index) => {
    ownApprovalIndex = index
    return approval.userId === userProfile?.id
  })

  const handleApprove = (id: number) => {
    setConfirmState({
      open: true,
      title: 'Setujui Part Swap',
      content: `Apakah kamu yakin akan menyetujui Part Swap ini? Action ini tidak bisa diubah`,
      confirmText: 'Setujui',
      onConfirm: () => {
        updateStatusMutate(
          {
            partSwapId: partSwapDetail?.id,
            approvalId: id,
            status: MaterialTransferApprovalStatus.APPROVED,
            rejectionNote: ''
          },
          {
            onSuccess: () => {
              toast.success(
                isCancelation ? 'Pengajuan Pembatalan Part Swap berhasil disetujui' : 'Part Swap berhasil disetujui'
              )
              fetchPartSwapDetail()
              fetchPartSwapLogs()
              fetchPartSwapList()
            }
          }
        )
      }
    })
  }

  const handleReject = (id: number) => {
    let rejectionNote
    setConfirmState({
      open: true,
      title: isCancelation ? 'Tolak Pembatalan Part Swap' : 'Tolak Part Swap',
      content: (
        <div className='flex flex-col gap-2'>
          <Typography>Apakah kamu yakin akan menolak Part Swap ini? Action ini tidak bisa diubah</Typography>
          <TextField fullWidth onBlur={e => (rejectionNote = e.target.value)} label='Alasan' />
        </div>
      ) as any,
      confirmText: 'Tolak',
      onConfirm: () => {
        updateStatusMutate(
          {
            partSwapId: partSwapDetail?.id,
            approvalId: id,
            status: MaterialTransferApprovalStatus.REJECTED,
            rejectionNote
          },
          {
            onSuccess: () => {
              toast.success(
                isCancelation ? 'Pengajuan Pembatalan Part Swap berhasil ditolak' : 'Part Swap berhasil ditolak'
              )
              fetchPartSwapDetail()
              fetchPartSwapLogs()
              fetchPartSwapList()
            }
          }
        )
      },
      confirmColor: 'error'
    })
  }

  const handleUpdateApprover = (formData: EditApproverInput) => {
    updateApproverMutate(
      {
        partSwapId: partSwapDetail?.id,
        approvalId: selectedApproval?.id,
        ...formData
      },
      {
        onSuccess: () => {
          toast.success('Penerima Pengajuan berhasil diganti')
          fetchPartSwapDetail()
          fetchPartSwapLogs()
          setEditApproverModalState({ open: false })
        }
      }
    )
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>Pengajuan Persetujuan</div>
          <div className='flex flex-col gap-4'>
            {approvalList?.map((approval, index) => {
              const statusValue = statusChipValue[approval.status]
              return (
                <div
                  key={approval.id}
                  className='rounded-lg border border-[#4c4e64]/22 p-4 flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'
                >
                  <div className='flex justify-between items-start self-stretch relative w-full bg-transparent'>
                    <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                      {approval.user?.fullName}
                    </p>
                    {ownApproval?.status === MaterialTransferApprovalStatus.WAITING &&
                    approval.status === MaterialTransferApprovalStatus.WAITING &&
                    partSwapDetail?.status !== MrStatus.CANCELED &&
                    isApprovalPage ? (
                      <div className='flex gap-2 items-center self-center'>
                        <Button
                          variant='contained'
                          size='small'
                          color='error'
                          onClick={() => handleReject(approval.id)}
                        >
                          Tolak
                        </Button>
                        <Button variant='contained' size='small' onClick={() => handleApprove(approval.id)}>
                          Setujui
                        </Button>
                      </div>
                    ) : (
                      <Chip label={statusValue?.label} color={statusValue?.color} variant='tonal' size='small' />
                    )}
                  </div>
                  <div className='flex justify-between items-start w-full'>
                    <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                      <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>{approval.user?.title}</small>
                    </label>
                    {approval.respondedAt ? (
                      <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                        <small className='text-sm text-[#4c4e64]/60 dark:text-inherit'>
                          {formatDate(approval.respondedAt, 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
                        </small>
                      </label>
                    ) : null}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
      {editApproverOpen ? (
        <EditApproverDialog
          open={editApproverOpen}
          setOpen={open =>
            setEditApproverModalState(current => ({
              open: open,
              selectedApproval: open ? current.selectedApproval : undefined
            }))
          }
          selectedApproval={selectedApproval}
          scope={DefaultApprovalScope.MaterialRequest}
          onSubmit={handleUpdateApprover}
          isLoading={updateApproverLoading}
          approvalList={approvalList}
        />
      ) : null}
    </>
  )
}

export default ApprovalsCard
