export const getStatusConfig = (status: string): { color: string; label: string } => {
  // PROCESSED, APPROVED, REJECTED, CANCELED, CLOSED
  switch (status) {
    case 'PROCESSED':
      return {
        color: 'success',
        label: 'Diproses'
      }
    case 'APPROVED':
      return {
        color: 'success',
        label: 'Disetujui'
      }
    case 'REJECTED':
      return {
        color: 'error',
        label: '<PERSON><PERSON><PERSON>'
      }
    case 'CANCELED':
      return {
        color: 'error',
        label: 'Di<PERSON>alkan'
      }
    case 'CLOSED':
      return {
        color: 'info',
        label: 'Seles<PERSON>'
      }
    default:
      return {
        color: 'default',
        label: status
      }
  }
}
