export const getStatusConfig = (status: string): { color: string; label: string } => {
  // PROCESSED, APPROVED, REJECTED, CANCELED, CLOSED
  switch (status) {
    case 'PROCESSED':
      return {
        color: 'warning',
        label: 'Diprose<PERSON>'
      }
    case 'APPROVED':
      return {
        color: 'success',
        label: 'Disetujui'
      }
    case 'REJECTED':
      return {
        color: 'error',
        label: '<PERSON><PERSON><PERSON>'
      }
    case 'CANCELED':
      return {
        color: 'error',
        label: 'Dibatalkan'
      }
    case 'CLOSED':
      return {
        color: 'info',
        label: 'Seles<PERSON>'
      }
    case 'WAITING':
      return {
        color: 'default',
        label: '<PERSON>unggu'
      }
    default:
      return {
        color: 'default',
        label: status
      }
  }
}

export const partSwapStatuses = [
  {
    value: 'WAITING',
    label: 'Menunggu'
  },
  {
    value: 'APPROVED',
    label: 'Disetujui'
  },
  {
    value: 'REJECTED',
    label: '<PERSON><PERSON><PERSON>'
  }
]
