import truncateString from '@/core/utils/truncate'
import { PartSwapItem } from '@/types/partSwapTypes'
import { IconButton, Tooltip, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<PartSwapItem>()

export const originColumns = (detail: (item: PartSwapItem) => void) => [
  columnHelper.accessor('originItem.number', {
    header: 'KODE PART'
  }),
  columnHelper.accessor('originSerialNumber.number', {
    header: 'NO SERIAL',
    cell: ({ row }) => row.original?.originSerialNumber?.number ?? '-'
  }),
  columnHelper.accessor('originItem.name', {
    header: 'NAMA ITEM',
    cell: ({ row }) => truncateString(row.original?.originItem?.name ?? '-', 10)
  }),
  columnHelper.accessor('quantity', {
    header: 'QTY',
    cell: ({ row }) => `${row.original.quantity} ${row.original.quantityUnit}`
  }),
  columnHelper.accessor('note', {
    header: 'KETERANGAN',
    cell: ({ row }) => (
      <Tooltip title={row.original.note}>
        <Typography>{truncateString(row.original.note ?? '-', 10)}</Typography>
      </Tooltip>
    )
  }),
  columnHelper.display({
    id: 'action',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => detail(row.original)} className='flex items-center gap-0.5'>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]

export const destinationColumns = (detail: (item: PartSwapItem) => void) => [
  columnHelper.accessor('destinationItem.number', {
    header: 'KODE PART'
  }),
  columnHelper.accessor('destinationSerialNumber.number', {
    header: 'NO SERIAL',
    cell: ({ row }) => row.original?.destinationSerialNumber?.number ?? '-'
  }),
  columnHelper.accessor('destinationItem.name', {
    header: 'NAMA ITEM',
    cell: ({ row }) => truncateString(row.original?.destinationItem?.name ?? '-', 10)
  }),
  columnHelper.accessor('quantity', {
    header: 'QTY',
    cell: ({ row }) => `${row.original.quantity} ${row.original.quantityUnit}`
  }),
  columnHelper.accessor('note', {
    header: 'KETERANGAN',
    cell: ({ row }) => (
      <Tooltip title={row.original.note}>
        <Typography>{truncateString(row.original.note ?? '-', 10)}</Typography>
      </Tooltip>
    )
  }),
  columnHelper.display({
    id: 'action',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => detail(row.original)} className='flex items-center gap-0.5'>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
