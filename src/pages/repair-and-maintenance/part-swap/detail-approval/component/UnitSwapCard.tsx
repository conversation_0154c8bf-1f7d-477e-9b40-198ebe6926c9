import { useMemo, useState } from 'react'
import {
  Autocomplete,
  Card,
  CardContent,
  CircularProgress,
  debounce,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { UnitType } from '@/types/companyTypes'
import { Controller, useWatch } from 'react-hook-form'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, {
  SITE_LIST_QUERY_KEY,
  UNIT_LIST_QUERY_KEY,
  UNIT_QUERY_KEY
} from '@/api/services/company/query'
import { hoursToMinutes } from '@/pages/repair-and-maintenance/fr/create/config/utils'
import { usePartSwap } from '../../context/PartSwapContext'
import { DEFAULT_CATEGORY } from '@/data/default/category'

const UnitSwapCard = () => {
  const { partSwapDetail } = usePartSwap()

  const { data: originUnit } = useQuery({
    enabled: !!partSwapDetail?.originUnitId,
    queryKey: [UNIT_QUERY_KEY, partSwapDetail?.originUnitId],
    queryFn: () => CompanyQueryMethods.getUnit(partSwapDetail?.originUnitId)
  })

  const { data: destinationUnit } = useQuery({
    enabled: !!partSwapDetail?.destinationUnitId,
    queryKey: [UNIT_QUERY_KEY, partSwapDetail?.destinationUnitId],
    queryFn: () => CompanyQueryMethods.getUnit(partSwapDetail?.destinationUnitId)
  })

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Unit</Typography>
          </div>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-2'>
                <Typography variant='h5'>Unit Asal</Typography>
                <Grid container spacing={4}>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Kode Unit</small>
                      <Typography>{originUnit?.number ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Kode Activa</small>
                      <Typography>{originUnit?.asset?.code ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Kategori Unit</small>
                      <Typography>{originUnit?.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Jenis Unit</small>
                      <Typography>{originUnit?.subCategory?.name ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Merk Unit</small>
                      <Typography>{originUnit?.brandName ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Tipe Unit</small>
                      <Typography>{originUnit?.type ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Nomor Rangka</small>
                      <Typography>{originUnit?.hullNumber ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Nomor Mesin</small>
                      <Typography>{originUnit?.engineNumber ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Plat Nomor</small>
                      <Typography>{originUnit?.plateNumber ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <Divider />
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Lokasi Unit</small>
                      <Typography>{partSwapDetail?.originUnitSite?.name ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <Divider />
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>KM</small>
                      <Typography>{partSwapDetail?.originUnitKm ?? '-'} KM</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>HM</small>
                      <Typography>{partSwapDetail?.originUnitHm ?? '-'} Jam</Typography>
                    </div>
                  </Grid>
                </Grid>
              </div>
            </Grid>
            <Grid item xs={12} md={6}>
              <div className='bg-[#4C4E640D] rounded-[8px] p-4 flex flex-col gap-2'>
                <Typography variant='h5'>Unit Tujuan</Typography>
                <Grid container spacing={4}>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Kode Unit</small>
                      <Typography>{destinationUnit?.number ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Kode Activa</small>
                      <Typography>{destinationUnit?.asset?.code ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Kategori Unit</small>
                      <Typography>{destinationUnit?.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Jenis Unit</small>
                      <Typography>{destinationUnit?.subCategory?.name ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Merk Unit</small>
                      <Typography>{destinationUnit?.brandName ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Tipe Unit</small>
                      <Typography>{destinationUnit?.type ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Nomor Rangka</small>
                      <Typography>{destinationUnit?.hullNumber ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Nomor Mesin</small>
                      <Typography>{destinationUnit?.engineNumber ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Plat Nomor</small>
                      <Typography>{destinationUnit?.plateNumber ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <Divider />
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>Lokasi Unit</small>
                      <Typography>{partSwapDetail?.destinationUnitSite?.name ?? '-'}</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <Divider />
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>KM</small>
                      <Typography>{partSwapDetail?.destinationUnitKm ?? '-'} KM</Typography>
                    </div>
                  </Grid>
                  <Grid item xs={12}>
                    <div className='flex flex-col gap-2'>
                      <small>HM</small>
                      <Typography>{partSwapDetail?.destinationUnitHm ?? '-'} Jam</Typography>
                    </div>
                  </Grid>
                </Grid>
              </div>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </>
  )
}

export default UnitSwapCard
