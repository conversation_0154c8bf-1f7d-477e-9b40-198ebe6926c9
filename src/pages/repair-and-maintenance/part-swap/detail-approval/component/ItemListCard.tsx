import {
  Card,
  CardContent,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography
} from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import { useParams } from 'react-router-dom'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useQuery } from '@tanstack/react-query'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { SrRmOPayload } from '@/types/srTypes'
import { ReactNode, useEffect, useMemo, useState } from 'react'
import { WarehouseItemType } from '@/types/appTypes'
import AddOriginPartDialog from '@/components/dialogs/add-origin-part'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { usePartSwap } from '../../context/PartSwapContext'
import { destinationColumns, originColumns } from '../config/table'

const ItemListCard = () => {
  const params = useParams()
  const { partSwapDetail } = usePartSwap()

  const [dialogItem, setDialogItem] = useState<{ origin: boolean; item: boolean }>({ origin: false, item: false })
  const [activeItem, setActiveItem] = useState<WarehouseItemType | null>(null)
  const [activeOriginIndexItem, setActiveOriginIndexItem] = useState<number | null>(null)

  const tableOptionsOrigin = useMemo(
    () => ({
      data: partSwapDetail?.items ?? [],
      columns: originColumns(item => {
        setActiveItem({ ...item, itemId: item.originItemId } as WarehouseItemType)
        setDialogItem(curr => ({ ...curr, item: true }))
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [partSwapDetail]
  )
  const tableOptions = useMemo(
    () => ({
      data: partSwapDetail?.items ?? [],
      columns: destinationColumns(item => {
        setActiveItem({ ...item, itemId: item.destinationItemId } as WarehouseItemType)
        setDialogItem(curr => ({ ...curr, item: true }))
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [partSwapDetail]
  )

  const table = useReactTable<any>(tableOptions)
  const tableOrigin = useReactTable<any>(tableOptionsOrigin)

  const swapType = useMemo(() => {
    switch (partSwapDetail?.items?.[0]?.swapType) {
      case 'EXCHANGED':
        return 'Ditukar'
      case 'MOVED':
        return 'Dipindahkan'
      default:
        return '-'
    }
  }, [partSwapDetail])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Parts</Typography>
          </div>
          <FormControl fullWidth>
            <small>Tipe Part Swap</small>
            <Typography>{swapType}</Typography>
          </FormControl>
          <div className='grid md:grid-cols-2 gap-4'>
            <div className='flex flex-col bg-[#4C4E640D] p-4 rounded-[8px] gap-2'>
              <Typography variant='h5'>Part Unit Asal</Typography>
              <div className='shadow-sm rounded-[8px]'>
                <Table
                  headerColor='green'
                  table={tableOrigin}
                  emptyLabel={
                    <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography>Belum ada Barang</Typography>
                      <Typography className='text-sm text-gray-400'>
                        Tambahkan barang yang ingin dimasukkan dalam Part Swap ini
                      </Typography>
                    </td>
                  }
                  disablePagination
                />
              </div>
            </div>
            <div className='flex flex-col bg-[#4C4E640D] p-4 rounded-[8px] gap-2'>
              <Typography variant='h5'>Part Unit Tujuan</Typography>
              <div className='shadow-sm rounded-[8px]'>
                <Table
                  headerColor='green'
                  table={table}
                  emptyLabel={
                    <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography>Belum ada Barang</Typography>
                      <Typography className='text-sm text-gray-400'>
                        Tambahkan barang yang ingin dimasukkan dalam Material Request ini
                      </Typography>
                    </td>
                  }
                  disablePagination
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {dialogItem.item && (
        <AddWarehouseItemDialog
          open={dialogItem.item}
          setOpen={bool => setDialogItem(curr => ({ ...curr, item: bool }))}
          onSubmit={() => {}}
          viewOnly
          withoutUnit
          currentItem={activeItem}
        />
      )}
      {dialogItem.origin && (
        <AddOriginPartDialog
          open={dialogItem.origin}
          setOpen={bool => setDialogItem(curr => ({ ...curr, origin: bool }))}
          onSubmit={console.log}
          withoutUnit
          currentItem={{}}
        />
      )}
    </>
  )
}

export default ItemListCard
