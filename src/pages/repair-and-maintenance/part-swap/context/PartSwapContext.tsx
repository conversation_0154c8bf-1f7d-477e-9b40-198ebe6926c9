import { defaultListData } from '@/api/queryClient'
import PartSwapQueryMethods from '@/api/services/part-swap/query'
import { PART_SWAP_QUERY_KEY, PART_SWAP_QUERY_LIST_KEY } from '@/api/services/part-swap/service'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { ConfirmDialogType, useMenu } from '@/components/menu/contexts/menuContext'
import { useAuth } from '@/contexts/AuthContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { PartSwapLogType, PartSwapParams, PartSwapType } from '@/types/partSwapTypes'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import React, { createContext, useContext, useEffect, useState } from 'react'
import { NavigateFunction, useLocation, useNavigate, useParams } from 'react-router-dom'

interface PartSwapContextProps {
  isMobile: boolean
  partSwapParams: PartSwapParams
  setPartialPartSwapParams: (fieldName: keyof PartSwapParams, value: any) => void
  setPartSwapParams: React.Dispatch<React.SetStateAction<Partial<PartSwapParams>>>
  partSwapList: ListResponse<PartSwapType>
  selectedPartSwapId: string | undefined
  setSelectedPartSwapId: React.Dispatch<React.SetStateAction<string | undefined>>
  setConfirmState: React.Dispatch<React.SetStateAction<ConfirmDialogType>>
  navigate: NavigateFunction
  partSwapDetail: PartSwapType
  partSwapLogs: PartSwapLogType[]
  fetchPartSwapList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<PartSwapType>, unknown>>
  fetchPartSwapDetail: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<PartSwapType, unknown>>
  fetchPartSwapLogs: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<PartSwapLogType[], unknown>>
  isApprovalPage: boolean
}

const PartSwapContext = createContext<PartSwapContextProps>({} as PartSwapContextProps)

export const usePartSwap = () => {
  const context = useContext(PartSwapContext)
  if (context === undefined) {
    throw new Error('usePartSwap must be used within a PartSwapContextProvider')
  }
  return context
}

export const PartSwapProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const params = useParams()
  const { userProfile } = useAuth()
  const { pathname } = useLocation()
  const { setConfirmState } = useMenu()

  const isApprovalPage = pathname?.includes('approval')

  const [selectedPartSwapId, setSelectedPartSwapId] = useState<string | undefined>()
  const [partSwapParams, setPartialPartSwapParams, setPartSwapParams] = usePartialState<PartSwapParams>({
    page: 1,
    limit: 10
  })

  const { data: partSwapList, refetch: fetchPartSwapList } = useQuery({
    queryKey: [PART_SWAP_QUERY_LIST_KEY, JSON.stringify(partSwapParams), isApprovalPage],
    queryFn: () => {
      const siteIds = userProfile?.sites
        ?.filter(site => site.type === 'WORKSHOP')
        .map(site => site.id)
        .join(',')
      if (isApprovalPage) {
        return PartSwapQueryMethods.getPartSwapListToMe(partSwapParams)
      }
      return PartSwapQueryMethods.getPartSwapList({ ...partSwapParams, siteIds })
    },
    placeholderData: defaultListData as ListResponse<PartSwapType>
  })

  const { data: partSwapDetail, refetch: fetchPartSwapDetail } = useQuery({
    enabled: !!selectedPartSwapId,
    queryKey: [PART_SWAP_QUERY_KEY, selectedPartSwapId],
    queryFn: () => PartSwapQueryMethods.getPartSwap(selectedPartSwapId)
  })

  const { data: partSwapLogs, refetch: fetchPartSwapLogs } = useQuery({
    enabled: !!selectedPartSwapId,
    queryKey: ['PART_SWAP_LOGS_QUERY_KEY', selectedPartSwapId],
    queryFn: async () => {
      const res = await PartSwapQueryMethods.getPartSwapLogs({
        partSwapId: selectedPartSwapId,
        limit: Number.MAX_SAFE_INTEGER
      })
      return res.items ?? []
    },
    placeholderData: [] as PartSwapLogType[]
  })

  useEffect(() => {
    if (params?.psId) {
      setSelectedPartSwapId(params.psId)
    }
  }, [params])

  const value = {
    isMobile,
    partSwapParams,
    navigate,
    setPartialPartSwapParams,
    setPartSwapParams,
    partSwapList,
    selectedPartSwapId,
    setSelectedPartSwapId,
    setConfirmState,
    partSwapDetail,
    partSwapLogs,
    fetchPartSwapList,
    fetchPartSwapDetail,
    fetchPartSwapLogs,
    isApprovalPage
  }
  return <PartSwapContext.Provider value={value}>{children}</PartSwapContext.Provider>
}
