import RnMQueryMethods from '@/api/services/rnm/query'
import { WO_QUERY_DETAIL_KEY } from '@/api/services/rnm/service'
import { WorkOrderType, WoSegmentType } from '@/types/woTypes'
import { useQuery } from '@tanstack/react-query'
import React, { useState } from 'react'

type PartSwapFormContextProps = {
  woDetail: WorkOrderType
  selectedWoId: string
  setSelectedWoId: React.Dispatch<React.SetStateAction<string>>
  activeSegment: WoSegmentType | null
  setActiveSegment: React.Dispatch<React.SetStateAction<WoSegmentType | null>>
  selectedWo: WorkOrderType | null
  setSelectedWo: React.Dispatch<React.SetStateAction<WorkOrderType | null>>
}

export const PartSwapFormContext = React.createContext<PartSwapFormContextProps>({} as PartSwapFormContextProps)

export const usePartSwapForm = () => {
  const context = React.useContext(PartSwapFormContext)
  if (context === undefined) {
    throw new Error('usePartSwapForm must be used within a PartSwapFormContextProvider')
  }
  return context
}

export const PartSwapFormProvider = ({ children }: { children: React.ReactNode }) => {
  const [activeSegment, setActiveSegment] = useState<WoSegmentType | null>(null)
  const [selectedWoId, setSelectedWoId] = useState<string>('')
  const [selectedWo, setSelectedWo] = useState<WorkOrderType | null>(null)

  const { data: woDetail } = useQuery({
    enabled: !!selectedWoId,
    queryKey: [WO_QUERY_DETAIL_KEY, selectedWoId],
    queryFn: () => RnMQueryMethods.getWoDetail(selectedWoId)
  })

  const value = {
    activeSegment,
    setActiveSegment,
    selectedWoId,
    setSelectedWoId,
    woDetail,
    selectedWo,
    setSelectedWo
  }
  return <PartSwapFormContext.Provider value={value}>{children}</PartSwapFormContext.Provider>
}
