import { StuffRequestType } from '@/types/wpTypes'
import { Card, CardContent, Grid, Typography } from '@mui/material'

type AdditionalInfoCardProps = {
  data: StuffRequestType
}

const AdditionalInfoCard = ({ data }: AdditionalInfoCardProps) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Lainnya</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12} className='flex flex-col gap-2'>
            <small><PERSON><PERSON></small>
            <Typography>{data?.type === 'TAKE' ? 'Pengambilan Barang' : 'Pengembalian Barang'}</Typography>
          </Grid>
          <Grid item xs={12} md={6} className='flex flex-col gap-2'>
            <small>Departemen</small>
            <Typography>{data?.department?.name}</Typography>
          </Grid>
          <Grid item xs={12} md={6} className='flex flex-col gap-2'>
            <small>Lokasi Workshop</small>
            <Typography>{data?.site?.name}</Typography>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
