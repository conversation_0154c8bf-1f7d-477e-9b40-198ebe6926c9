import { StuffRequestItem } from '@/types/wpTypes'
import { IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<StuffRequestItem>()

type RowActionType = {
  detail: (item: StuffRequestItem) => void
}

export const itemstableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('item.number', {
    header: 'Kode Barang',
    cell: ({ row }) => row.original.item.number
  }),
  columnHelper.accessor('serialNumberId', {
    header: 'No. Serial',
    cell: ({ row }) => row.original.serialNumberId ?? '-'
  }),
  columnHelper.accessor('item.name', {
    header: 'Nama Item'
  }),
  columnHelper.accessor('item.brandName', {
    header: 'Merk Item'
  }),
  columnHelper.accessor('note', {
    header: 'Keterangan',
    cell: ({ row }) => (!!row.original.note ? row.original.note : '-')
  }),
  columnHelper.accessor('smallQuantity', {
    header: 'Qty',
    cell: ({ row }) => row.original.smallQuantity + ' ' + row.original.quantityUnit
  }),
  columnHelper.display({
    id: 'detail',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
