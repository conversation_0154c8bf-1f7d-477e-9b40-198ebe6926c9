import { WpSessionType } from '@/types/wpTypes'
import { IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'

const columnHelper = createColumnHelper<WpSessionType>()

export const sessionsTableColumns = ({ detail }: { detail: (row: WpSessionType) => void }) => [
  columnHelper.accessor('startedAt', {
    header: 'Start',
    cell: ({ row }) => (row.original?.startedAt ? formatDate(new Date(row.original.startedAt), 'HH:mm') : '-')
  }),
  columnHelper.accessor('stoppedAt', {
    header: 'End',
    cell: ({ row }) => (row.original?.stoppedAt ? formatDate(new Date(row.original.stoppedAt), 'HH:mm') : '-')
  }),
  columnHelper.accessor('laborsCount', {
    header: 'Labor',
    cell: ({ row }) => row.original?.laborsCount ?? '-'
  }),
  columnHelper.accessor('note', {
    header: 'Remarks',
    cell: ({ row }) => row.original?.note ?? '-'
  }),
  columnHelper.display({
    id: 'action',
    header: '',
    cell: ({ row }) => (
      <IconButton size='small' onClick={() => detail(row.original)}>
        <i className='ri-eye-line text-textSecondary' />
      </IconButton>
    )
  })
]
