export const calculateMinutes = (startDate: string, endDate: string): number => {
  const start = new Date(startDate)
  const end = new Date(endDate)

  const differenceInMilliseconds = end.getTime() - start.getTime()

  const differenceInMinutes = differenceInMilliseconds / (1000 * 60)

  return differenceInMinutes
}

export const truncateFromEnd = (s: string, length: number): string => {
  return s.length <= length ? s : s.slice(-length)
}

export const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${String(hours).padStart(2, '0')} jam ${String(minutes).padStart(2, '0')} mnt ${String(secs).padStart(2, '0')} dtk`
}

export const didEndExceedEstimateArrow = (endedAt: string, estimatedEndedAt: string): boolean => {
  const actualEnd = new Date(endedAt)
  const estimatedEnd = new Date(estimatedEndedAt)

  if (isNaN(actualEnd.getTime()) || isNaN(estimatedEnd.getTime())) {
    console.warn('One or both date strings are invalid.')
    return false
  }

  return actualEnd.getTime() > estimatedEnd.getTime()
}
