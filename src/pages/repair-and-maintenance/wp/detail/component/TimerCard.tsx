import { <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import { useEffect, useRef, useState } from 'react'
import { useWp } from '../../context/WpContext'
import { calculateMinutes, didEndExceedEstimateArrow, formatTime } from '../config/utils'
import LoadingButton from '@mui/lab/LoadingButton'
import StopWpTimerDialog from '@/components/dialogs/stop-wp-dialog'
import { UserType } from '@/types/userTypes'
import {
  getSortedRowModel,
  getFilteredRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getCoreRowModel,
  useReactTable,
  getFacetedMinMaxValues
} from '@tanstack/react-table'
import { sessionsTableColumns } from '../config/table'
import Table from '@/components/table'
import { WpSessionType } from '@/types/wpTypes'
import DialogTimerDetails from './dialogs/dialog-timer-details'

const TimerCard = () => {
  const { wpDetail, onWpStop, loadingTimer, onstartTimerWp, onstopTimerWp } = useWp()

  let startWorking = wpDetail?.timerStartedAt ?? new Date().toISOString()
  let estimatedTime = calculateMinutes(startWorking, wpDetail?.estimatedEndedAt) // in minutes
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const endDateTime = new Date(new Date(startWorking).getTime() + estimatedTime * 60 * 1000)
  const [timeLeft, setTimeLeft] = useState<number>()
  const [isActive, setIsActive] = useState(false)
  const [dialogStop, setDialogStop] = useState(false)
  const [selectedSession, setSelectedSession] = useState<WpSessionType | null>(null)

  const calculateDurationInSeconds = (timerStartedAt: string, dateNow: string): number => {
    const startTime = new Date(timerStartedAt)
    const currentTime = new Date(dateNow)

    const durationInMilliseconds = currentTime.getTime() - startTime.getTime()

    const totalSeconds = Math.floor(durationInMilliseconds / 1000)

    return totalSeconds
  }

  useEffect(() => {
    if (isActive && wpDetail) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(curr => (curr ?? 0) + 1)
      }, 1000)
    } else {
      clearInterval(intervalRef.current!)
    }
    return () => clearInterval(intervalRef.current!)
  }, [endDateTime, isActive, wpDetail])

  const stopTimer = () => {
    setIsActive(false)
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
    }
  }

  const table = useReactTable({
    data: wpDetail?.sessions ?? [],
    columns: sessionsTableColumns({ detail: setSelectedSession }),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    wpDetail?.timerStartedAt
      ? setTimeLeft(
          calculateDurationInSeconds(wpDetail?.timerStartedAt, new Date().toISOString()) + wpDetail?.durationInSec
        )
      : setTimeLeft(wpDetail?.durationInSec)
    if (wpDetail?.status && wpDetail?.status !== 'IN_PROCESS') {
      setIsActive(false)
    } else {
      wpDetail?.timerStartedAt ? setIsActive(true) : setIsActive(false)
    }
  }, [wpDetail])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Timer Pengerjaan</Typography>
          </div>
          <div className='flex justify-between bg-[#F5F5F7] rounded-[8px] p-[24px_16px]'>
            <Typography variant='h5' color='primary'>
              {formatTime(timeLeft ?? 0)}
            </Typography>
            <LoadingButton
              loading={loadingTimer}
              startIcon={<></>}
              onClick={() => {
                if (!isActive) {
                  onstartTimerWp()
                  // onstartTimerWp(() => {
                  //   setIsActive(true)
                  // })
                } else {
                  setDialogStop(true)
                }
              }}
              disabled={wpDetail?.status !== 'IN_PROCESS'}
              variant='outlined'
              color='error'
            >
              {isActive ? 'Stop Timer' : 'Jalankan Timer'}
            </LoadingButton>
          </div>
          {didEndExceedEstimateArrow(wpDetail?.endedAt, wpDetail?.estimatedEndedAt) && (
            <div className='flex gap-4 items-center rounded-md bg-[#FF4D4926] p-4'>
              <i className='warning-triangle-red text-error' />
              <Typography component='span'>Melewati waktu estimasi penyelesaian</Typography>
            </div>
          )}
          {wpDetail?.sessions.length > 0 && (
            <div className='shadow-sm rounded-md'>
              <Table headerColor='green' table={table} disablePagination />
            </div>
          )}
        </CardContent>
      </Card>
      {dialogStop && (
        <StopWpTimerDialog
          callback={stopTimer}
          open={dialogStop}
          setOpen={setDialogStop}
          wpId={wpDetail?.id}
          labors={wpDetail?.labors as unknown as UserType[]}
          timer={timeLeft ?? 0}
        />
      )}
      {!!selectedSession && (
        <DialogTimerDetails
          open={!!selectedSession}
          setOpen={open => setSelectedSession(!open && null)}
          row={selectedSession}
        />
      )}
    </>
  )
}

export default TimerCard
