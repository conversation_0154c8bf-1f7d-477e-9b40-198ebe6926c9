import { create<PERSON>ontext, ReactN<PERSON>, useContext, useEffect, useMemo, useRef, useState } from 'react'
import { NavigateFunction, useLocation, useNavigate, useParams } from 'react-router-dom'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { CodeType } from '@/types/codes'
import { ListResponse } from '@/types/api'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import usePartialState from '@/core/hooks/usePartialState'
import { ListParams } from '@/types/payload'
import { defaultListData } from '@/api/queryClient'
import { ConfirmDialogType, useMenu } from '@/components/menu/contexts/menuContext'
import AddReportDialog from '@/components/dialogs/add-report-wp'
import { Checkbox, FormControlLabel, FormHelperText, Grid, <PERSON><PERSON>ield, Typography } from '@mui/material'
import { CreateWpDto, WpLogType, WpParams, WpReportType, WpType, WpStartDto, WpStartTimerDto } from '@/types/wpTypes'
import { WP_LIST_QUERY_KEY, WpQueryMethods } from '@/api/services/wp/query'
import { WorkOrderType } from '@/types/woTypes'
import {
  useApproveWp,
  useCreateWp,
  useRejectWp,
  useStartProcessWp,
  useStartTimerWp,
  useStopProcessWp,
  useStopTimerWp
} from '@/api/services/wp/mutation'
import { toast } from 'react-toastify'
import { updateEstimatedEndedAt } from '../created/config/utils'
import * as Sentry from '@sentry/react'
import { useAuth } from '@/contexts/AuthContext'
import DebouncedInput from '@/components/DebounceInput'
import { UserType } from '@/types/userTypes'
import {
  createColumnHelper,
  getFacetedUniqueValues,
  getFacetedRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  getCoreRowModel,
  getFacetedMinMaxValues,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import UserQueryMethods, { USER_LIST_QUERY_KEY } from '@/api/services/user/query'
import ConfirmDialog from '@/components/dialogs/confirm-dialog'

interface WorkProcessContextProps {
  isMobile: boolean
  selectedWpId: string
  selectedWo: WorkOrderType
  setSelectedWpId: React.Dispatch<React.SetStateAction<string | null>>
  setSelectedWo: React.Dispatch<React.SetStateAction<WorkOrderType>>
  setActiveReport: React.Dispatch<React.SetStateAction<WpReportType>>
  activeWp: WpType
  activeReport: WpReportType
  wpParams: ListParams
  setWpParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialWpParam: (fieldName: keyof ListParams, value: any) => void
  setActiveWp: React.Dispatch<React.SetStateAction<WpType | null>>
  wpList: ListResponse<WpType>
  wpDetail: WpType
  wpLogs: WpLogType[]
  wpReports: WpReportType[]
  navigate: NavigateFunction
  onSubmitWp: (dto: CreateWpDto) => void
  onStartWp: () => void
  onWpStop: (cb?: () => void) => void
  onstartTimerWp: (cb?: () => void) => void
  onstopTimerWp: (cb?: () => void) => void
  approveWp: () => void
  rejectWp: () => void
  setDialogReport: React.Dispatch<React.SetStateAction<boolean>>
  isCreateWpLoading: boolean
  isStartProcessWpLoading: boolean
  isStopProcessWpLoading: boolean
  isApproveWpLoading: boolean
  isRejectWpLoading: boolean
  loadingTimer: boolean
  refetchWpReports: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<WpReportType[], unknown>>
  refetchWpDetail: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<WpType, unknown>>
}

const WorkProcessContext = createContext<WorkProcessContextProps>({} as WorkProcessContextProps)

export type SegmentType = {
  id: string
  jobCode: CodeType
  smcs: CodeType
  modifier: CodeType
  parts: number
  misc: string
}

// Removed duplicate LaborType - using the one from wpTypes.ts

export const useWp = () => {
  const context = useContext(WorkProcessContext)
  if (!context) {
    throw new Error('useWp must be used within a WorkProcessContextProvider')
  }
  return context
}

type DialogProps = {
  laborList: UserType[]
  onCheckedChange?: (value: string[]) => void
}

const DialogStartWp = ({ laborList, onCheckedChange = () => {} }: DialogProps) => {
  const [qs, setQs] = useState<string>()
  const [tempLaborList, setTempLaborList] = useState<UserType[]>(laborList)
  const [rowSelection, setRowSelection] = useState({})

  useEffect(() => {
    if (qs) {
      const temp = tempLaborList.filter(labor => labor?.fullName?.toLowerCase().includes(qs.toLowerCase()))
      setTempLaborList(temp)
    } else {
      setTempLaborList(laborList)
    }
  }, [qs])

  const columnHelper = createColumnHelper<UserType>()

  const table = useReactTable({
    data: tempLaborList,
    columns: [
      columnHelper.display({
        id: 'select',
        size: 20,
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      }),
      columnHelper.accessor('fullName', {
        header: 'Nama'
      }),
      columnHelper.accessor('title', {
        header: 'Jabatan'
      })
    ],
    state: {
      rowSelection
    },
    onRowSelectionChange: setRowSelection,
    enableRowSelection: true,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    const selectedRows = table.getSelectedRowModel().rows
    const selectedLaborUsers = selectedRows.map(row => row.original)
    const selectedIds = selectedLaborUsers.map(user => user.id)

    onCheckedChange(selectedIds)
  }, [rowSelection])

  return (
    <div className='flex flex-col gap-4'>
      <Typography>
        Apakah kamu yakin akan memulai pengerjaan Work Process? Pilih atau tambahkan mekanik yang akan mengerjakan work
        process saat ini. Jangan lupa melengkapi laporan proses pengerjaan saat sedang mengerjakan Work Process ini
      </Typography>
      <div className='bg-[#4C4E640D] flex flex-col gap-2 p-4 rounded-md'>
        <DebouncedInput
          value={qs}
          placeholder='Masukkan mekanik'
          className='w-full bg-white'
          onChange={(val: string) => setQs(val)}
        />
        <div className='rounded-lg bg-white'>
          <Table
            containerClassName='max-h-64 overflow-y-auto'
            stickyHeader
            headerColor='green'
            table={table}
            disablePagination
            emptyLabel={'Belum ada mekanik'}
          />
        </div>
      </div>
    </div>
  )
}

export const WpContextProvider = ({ children }: { children: ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const params = useParams()
  const { setConfirmState } = useMenu()
  const {
    userProfile: { sites }
  } = useAuth()
  const isReviewPage = useLocation().pathname.includes('review')
  const isWpIn = useLocation().pathname.includes('wp-in')

  const [wpParams, setPartialWpParam, setWpParams] = usePartialState<WpParams>({
    limit: 10,
    page: 1
  })

  const selectedLaborIds = useRef<string[]>([])
  const [confirmStartWp, setConfirmStartWp] = useState<Partial<ConfirmDialogType & { type: 'start' | 'continue' }>>({
    open: false,
    title: '',
    content: '',
    confirmText: '',
    confirmColor: '',
    onConfirm: () => {},
    onCancel: () => {},
    type: 'start'
  })
  const [selectedWo, setSelectedWo] = useState<WorkOrderType | null>(null)
  const [selectedWpId, setSelectedWpId] = useState<string | null>(params?.wpId)
  const [activeWp, setActiveWp] = useState<WpType | null>(null)
  const [activeReport, setActiveReport] = useState<WpReportType | null>(null)
  const [dialogReport, setDialogReport] = useState<boolean>(false)

  const { mutate: createWp, isLoading: isCreateWpLoading } = useCreateWp()
  const { mutate: startProcessWp, isLoading: isStartProcessWpLoading } = useStartProcessWp()
  const { mutate: stopProcessWp, isLoading: isStopProcessWpLoading } = useStopProcessWp()
  const { mutate: startTimerWp, isLoading: isStartTimerWpLoading } = useStartTimerWp()
  const { mutate: stopTimerWp, isLoading: isStopTimerWpLoading } = useStopTimerWp()
  const { mutate: approveWpMutate, isLoading: isApproveWpLoading } = useApproveWp()
  const { mutate: rejectWpMutate, isLoading: isRejectWpLoading } = useRejectWp()

  const loadingTimer = isStartTimerWpLoading || isStopTimerWpLoading

  const { data: wpList, refetch: refetchWpList } = useQuery({
    queryKey: [WP_LIST_QUERY_KEY, JSON.stringify(wpParams), String(isReviewPage), String(isWpIn)],
    queryFn: () => {
      const siteIds = sites
        ?.filter(site => site.type === 'WORKSHOP')
        .map(site => site.id)
        .join(',')
      if (isReviewPage) {
        return WpQueryMethods.getWpList({ ...wpParams, status: 'IN_REVIEW' })
      } else if (isWpIn) {
        return WpQueryMethods.getWpListToMe(wpParams)
      } else {
        return WpQueryMethods.getWpList({ ...wpParams, siteIds })
      }
    },
    placeholderData: defaultListData as ListResponse<WpType>
  })

  const { data: wpDetail, refetch: refetchWpDetail } = useQuery({
    enabled: !!selectedWpId,
    queryKey: ['WP_DETAIL_KEY', selectedWpId],
    queryFn: () => WpQueryMethods.getWpDetail(selectedWpId)
  })

  const { data: wpLogs, refetch: refetchWpLogs } = useQuery({
    enabled: !!selectedWpId,
    queryKey: ['WP_LOGS_KEY', selectedWpId],
    queryFn: async () => {
      const res = await WpQueryMethods.getWpLogs(selectedWpId)
      return res.items
    },
    placeholderData: [] as WpLogType[]
  })

  const { data: wpReports, refetch: refetchWpReports } = useQuery({
    enabled: !!selectedWpId,
    queryKey: ['WP_REPORTS_KEY', selectedWpId],
    queryFn: async () => {
      const res = await WpQueryMethods.getWpReports({ workProcessId: selectedWpId, limit: Number.MAX_SAFE_INTEGER })
      return res.items
    },
    placeholderData: [] as WpReportType[]
  })

  const { data: users } = useQuery({
    queryKey: [USER_LIST_QUERY_KEY],
    queryFn: async () => {
      return (await UserQueryMethods.getUserList({ limit: Number.MAX_SAFE_INTEGER })).items
    },
    placeholderData: []
  })

  const onSubmitWp = (dto: CreateWpDto) => {
    Sentry.captureMessage(`Submit Work Process: ${JSON.stringify(dto)}`)
    setConfirmState({
      open: true,
      title: 'Buat Work Process',
      content:
        'Apakah kamu yakin akan membuat Work Process ini dan assign ke labor terkait? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Buat Work Process',
      onConfirm: () => {
        createWp(
          {
            workOrderId: dto.workOrderId,
            workOrderSegmentId: dto.workOrderSegmentId,
            workOrderSegmentIds: dto.workOrderSegmentIds,
            laborIds: [...new Set(dto.laborIds)],
            estimatedEndedAt: updateEstimatedEndedAt(dto.estimatedEndedAt, dto.estimatedHourEndedAt)
          },
          {
            onSuccess: () => {
              toast.success('Berhasil membuat Work Process')
              refetchWpList()
              setTimeout(() => {
                navigate(`/wp/list`)
              }, 700)
            }
          }
        )
      }
    })
  }

  const onConfirmStartWp = () => {
    if (selectedLaborIds.current.length === 0) {
      toast.error('Labor Wajib Diisi')
      return
    }
    if (confirmStartWp.type === 'start') {
      startProcessWp(
        { workProcessId: selectedWpId, laborUserIds: selectedLaborIds.current },
        {
          onSuccess: () => {
            toast.success('WP dimulai')
            setConfirmStartWp(current => ({ ...current, open: false }))
            refetchWpDetail()
            refetchWpLogs()
            refetchWpReports()
            refetchWpList()
          }
        }
      )
    } else if (confirmStartWp.type === 'continue') {
      startTimerWp(
        {
          workProcessId: selectedWpId,
          laborUserIds: selectedLaborIds.current
        },
        {
          onSuccess: () => {
            toast.info('Timer sedang berjalan')
            setConfirmStartWp(current => ({ ...current, open: false }))
            refetchWpDetail()
            refetchWpLogs()
            refetchWpReports()
          }
        }
      )
    }
  }
  const onStartWp = () => {
    setConfirmStartWp({
      open: true,
      title: 'Mulai Pengerjaan Work Process',
      confirmText: 'Mulai Pengerjaan',
      type: 'start'
    })
  }

  const onWpStop = (cb?: () => void) => {
    setConfirmState({
      open: true,
      title: 'Selesaikan Pengerjaan Work Process',
      content:
        'Apakah kamu yakin akan menyelesaikan pengerjaan Work Process dan ajukan review ke atasan? Pastikan kamu sudah melengkapi laporan proses pengerjaan',
      confirmText: 'Selesaikan Pengerjaan',
      onConfirm: () => {
        stopProcessWp(
          { workProcessId: selectedWpId },
          {
            onSuccess: () => {
              toast.success('Work Process selesai')
              cb?.()
              refetchWpDetail()
              refetchWpLogs()
              refetchWpReports()
              refetchWpList()
            }
          }
        )
      }
    })
  }

  const onstartTimerWp = (cb?: () => void) => {
    setConfirmStartWp({
      open: true,
      title: 'Mulai Pengerjaan Work Process',
      confirmText: 'Mulai Pengerjaan',
      type: 'continue'
    })
  }

  const onstopTimerWp = (cb?: () => void) => {
    stopTimerWp(
      {
        workProcessId: selectedWpId
      },
      {
        onSuccess: () => {
          toast.info('Timer berhenti')
          cb?.()
          refetchWpDetail()
          refetchWpLogs()
          refetchWpReports()
        }
      }
    )
  }

  const approveWp = () => {
    setConfirmState({
      open: true,
      title: 'Setujui Review WP',
      content:
        'Apakah kamu yakin akan menyetujui pengerjaan WP ini dan menyelesaikan proses? Action ini tidak dapat diubah',
      confirmText: 'Setujui Review',
      onConfirm: () => {
        approveWpMutate(selectedWpId, {
          onSuccess: () => {
            toast.success('Work Process telah disetujui')
            refetchWpDetail()
            refetchWpLogs()
            refetchWpList()
          }
        })
      }
    })
  }

  const rejectWp = () => {
    let rejectReason = ''
    setConfirmState({
      open: true,
      title: 'Tolak Review Work Process',
      content: (
        <Grid container spacing={2}>
          <Grid item xs={12}>
            Apakah kamu yakin akan menolak pengerjaan Work Process ini dan kembalikan ke Mekanik untuk melanjutkan
            pengerjaan? Action ini tidak dapat diubah
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              defaultValue={rejectReason}
              onChange={e => (rejectReason = e.target.value)}
              label='Alasan'
            />
          </Grid>
        </Grid>
      ) as any,
      confirmText: 'Tolak Review',
      onConfirm: () => {
        rejectWpMutate(
          {
            rejectReason,
            workProcessId: selectedWpId,
            reportId: activeReport?.id
          },
          {
            onSuccess: () => {
              toast.success('Work Process telah ditolak')
              refetchWpDetail()
              refetchWpLogs()
              refetchWpList()
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    if (params?.wpId) setSelectedWpId(params.wpId)
  }, [params])

  const value = {
    navigate,
    wpList,
    wpDetail,
    wpParams,
    wpLogs,
    wpReports,
    setPartialWpParam,
    setWpParams,
    isMobile,
    selectedWpId,
    selectedWo,
    setSelectedWpId,
    setSelectedWo,
    activeWp,
    setActiveWp,
    onSubmitWp,
    onStartWp,
    onWpStop,
    onstartTimerWp,
    onstopTimerWp,
    approveWp,
    rejectWp,
    setDialogReport,
    isCreateWpLoading,
    isStartProcessWpLoading,
    isStopProcessWpLoading,
    isApproveWpLoading,
    isRejectWpLoading,
    loadingTimer,
    refetchWpDetail,
    refetchWpReports,
    activeReport,
    setActiveReport
  }
  return (
    <WorkProcessContext.Provider value={value}>
      {dialogReport && (
        <AddReportDialog
          report={activeReport}
          onSuccessAdd={refetchWpReports}
          open={dialogReport}
          setOpen={setDialogReport}
        />
      )}
      <ConfirmDialog
        title={confirmStartWp.title}
        content={(<DialogStartWp laborList={users} onCheckedChange={val => (selectedLaborIds.current = val)} />) as any}
        confirmText={confirmStartWp.confirmText}
        confirmColor={confirmStartWp.confirmColor}
        open={confirmStartWp.open}
        setOpen={open => setConfirmState(current => ({ ...current, open }))}
        onConfirm={onConfirmStartWp}
        onCancel={() => setConfirmStartWp(current => ({ ...current, open: false }))}
      />
      {children}
    </WorkProcessContext.Provider>
  )
}
