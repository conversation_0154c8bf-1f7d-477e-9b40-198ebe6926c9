import { createColumnHelper } from '@tanstack/react-table'
import { Chip, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { UnitType } from '@/types/companyTypes'
import { DEFAULT_CATEGORY } from '@/data/default/category'

const columnHelper = createColumnHelper<UnitType>()

type RowAction = {
  detail: (unit: UnitType) => void
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('number', {
    header: 'KODE UNIT',
    cell: ({ row }) => {
      return (
        <Typography
          sx={{ cursor: 'pointer' }}
          role='button'
          onClick={() => rowAction.detail(row.original)}
          color='primary'
        >
          {row.original.number}
        </Typography>
      )
    }
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => {
      return (
        <Chip
          variant='tonal'
          size='small'
          label={row.original.status === 'ACTIVE' ? 'Aktif' : 'Tidak aktif'}
          color={row.original.status === 'ACTIVE' ? 'success' : 'error'}
        />
      )
    }
  }),
  columnHelper.accessor('category.name', {
    header: 'JENIS UNIT',
    cell: ({ row }) => row.original.category?.name ?? DEFAULT_CATEGORY.name
  }),
  columnHelper.accessor('brandName', {
    header: 'MERK UNIT'
  }),
  columnHelper.accessor('type', {
    header: 'TIPE UNIT'
  }),
  columnHelper.accessor('hullNumber', {
    header: 'NO LAMBUNG'
  }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) => {
      return formatDate(new Date(row.original.createdAt), 'd/M/yyyy', { locale: id })
    }
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
