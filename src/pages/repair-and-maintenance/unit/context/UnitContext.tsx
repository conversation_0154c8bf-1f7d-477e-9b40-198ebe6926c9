import { defaultListData } from '@/api/queryClient'
import CompanyQueryMethods from '@/api/services/company/query'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import React, { useEffect, useState } from 'react'
import { NavigateFunction, useNavigate, useParams } from 'react-router-dom'
import { UnitType as _UnitType } from '@/types/companyTypes'
import RnMQueryMethods from '@/api/services/rnm/query'
import { FieldReportStatus, FrType } from '@/types/frTypes'
import { FrDtoType } from '../../fr/create/config/schema'
import { useCreateFr } from '@/api/services/rnm/mutation'
import { toast } from 'react-toastify'
import { minutesToHours } from '../../fr/create/config/utils'
import { WO_QUERY_LIST_KEY } from '@/api/services/rnm/service'
import { WorkOrderType, WoStatus } from '@/types/woTypes'
import * as Sentry from '@sentry/react'

export type CompanyUnitType = _UnitType

interface UnitContextProps {
  isMobile: boolean
  loadingFrCreate: boolean
  unitParams: ListParams
  setUnitParams: React.Dispatch<React.SetStateAction<ListParams>>
  setPartialUnitParam: (fieldName: keyof ListParams, value: any) => void
  unitList: ListResponse<CompanyUnitType>
  frList: FrType[]
  schedules: FrType[]
  histories: WorkOrderType[]
  woList: WorkOrderType[]
  selectedUnitId: string
  activeUnit: CompanyUnitType
  unitData: _UnitType
  setSelectedUnitId: React.Dispatch<React.SetStateAction<string | null>>
  navigate: NavigateFunction
  onSubmitFieldReport: (data: FrDtoType) => void
}

export const UnitContext = React.createContext<UnitContextProps>({} as UnitContextProps)

export const useUnit = () => {
  const context = React.useContext(UnitContext)
  if (context === undefined) {
    throw new Error('useUnit must be used within a UnitContextProvider')
  }
  return context
}

export const UnitContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const { setConfirmState } = useMenu()
  const navigate = useNavigate()
  const { unitId } = useParams()

  const [selectedUnitId, setSelectedUnitId] = useState<string | null>(unitId)
  const [unitParams, setPartialUnitParam, setUnitParams] = usePartialState<ListParams>({
    limit: 10,
    page: 1
  })

  const [activeUnit, setActiveUnit] = useState<CompanyUnitType | null>(null)

  const { data: unitList } = useQuery({
    queryKey: ['UNIT_QUERY_KEY', JSON.stringify(unitParams)],
    queryFn: () => {
      return CompanyQueryMethods.getUnitList(unitParams)
    },
    placeholderData: defaultListData as ListResponse<CompanyUnitType>
  })

  const { data: unitData } = useQuery({
    queryKey: ['UNIT_QUERY_DETAIL_KEY', unitId, selectedUnitId],
    enabled: !!selectedUnitId,
    queryFn: () => {
      return CompanyQueryMethods.getUnit(selectedUnitId)
    }
  })

  const { data: frList, refetch: refetchFrList } = useQuery({
    enabled: !!selectedUnitId,
    queryKey: ['FR_LIST_UNIT', selectedUnitId],
    queryFn: async () => {
      const res = await RnMQueryMethods.getFrList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        unitId: selectedUnitId,
        status: FieldReportStatus.CREATED
      })
      return res.items
    }
  })

  const { data: woList, refetch: refetchWoList } = useQuery({
    enabled: !!selectedUnitId,
    queryKey: [WO_QUERY_LIST_KEY, selectedUnitId],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        unitId: selectedUnitId,
        status: WoStatus.ACTIVE
      })
      return res.items
    }
  })

  const { data: schedules, refetch: refetchSchedule } = useQuery({
    enabled: !!selectedUnitId,
    queryKey: ['FR_QUERY_LIST_KEY', selectedUnitId],
    queryFn: async () => {
      const res = await RnMQueryMethods.getFrList({
        isScheduled: true,
        status: FieldReportStatus.CREATED,
        unitId: selectedUnitId
      })
      return res?.items ?? []
    }
  })

  const { data: histories } = useQuery({
    enabled: !!selectedUnitId,
    queryKey: [WO_QUERY_LIST_KEY, 'HISTORY', selectedUnitId],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        unitId: selectedUnitId,
        status: WoStatus.COMPLETED
      })
      return res.items
    }
  })

  const { mutate: createFr, isLoading: loadingFrCreate } = useCreateFr()

  const onSubmitFieldReport = (data: FrDtoType) => {
    Sentry.captureMessage(`Submit Field Report: ${JSON.stringify(data)}`)
    setConfirmState({
      title: 'Buat FR',
      open: true,
      content:
        'Apakah kamu yakin akan menambahkan FR untuk unit ini? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Buat FR',
      onConfirm: () => {
        createFr(
          { ...data, unitHm: minutesToHours(data.unitHm, data.unitMnt) },
          {
            onSuccess: () => {
              toast.success('FR berhasil dibuat')
              setTimeout(() => {
                refetchFrList()
                refetchSchedule()
                refetchWoList()
                navigate(`/rnm/unit/${selectedUnitId}`)
              }, 1000)
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    if (unitId) {
      setSelectedUnitId(unitId)
    }
  }, [unitId])

  useEffect(() => {
    if (selectedUnitId) {
      setActiveUnit(unitList?.items?.find(unit => unit.id === selectedUnitId))
    } else {
      setActiveUnit(null)
    }
  }, [selectedUnitId])

  const value = {
    isMobile,
    unitParams,
    setUnitParams,
    setPartialUnitParam,
    unitList,
    frList,
    schedules,
    histories,
    woList,
    selectedUnitId,
    setSelectedUnitId,
    navigate,
    activeUnit,
    unitData,
    onSubmitFieldReport,
    loadingFrCreate
  }
  return <UnitContext.Provider value={value}>{children}</UnitContext.Provider>
}
