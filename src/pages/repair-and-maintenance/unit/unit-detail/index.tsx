import { useRouter } from '@/routes/hooks'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Chip, Grid, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import { useUnit } from '../context/UnitContext'

import UnitDetail from './component/UnitDetail'
import DataUnitDetail from './component/DataUnitDetail'
import DocNumberCard from './component/DocNumberCard'
import WoLogHistory from './component/WoLogHistory'
import Permission from '@/core/components/Permission'

const UnitDetailPage = () => {
  const router = useRouter()
  const { activeUnit, selectedUnitId, unitData, frList, woList, schedules } = useUnit()

  const unitDetail = unitData ?? activeUnit

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='/rnm/unit' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Unit</Typography>
            </Link>
            <Typography>Detil Unit</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>{unitDetail?.brandName + ' ' + unitDetail?.type}</Typography>
                <Chip
                  label={unitDetail?.status === 'ACTIVE' ? 'Aktif' : 'Tidak aktif'}
                  color={unitDetail?.status === 'ACTIVE' ? 'success' : 'error'}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>Kode Unit: {unitDetail?.number}</Typography>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
              <div className='flex gap-2'>
                {/* <Button
                  color='secondary'
                  variant='outlined'
                  startIcon={<i className='ri-upload-2-line' />}
                  className='is-full sm:is-auto'
                >
                  Ekspor
                </Button>
                <Button
                  color='secondary'
                  variant='outlined'
                  startIcon={<i className='ic-outline-local-printshop' />}
                  className='is-full sm:is-auto'
                >
                  Cetak
                </Button> */}
              </div>
              <Permission permission={['field-report.create', 'field-report.update']}>
                <Button
                  variant='contained'
                  // disabled={!isCanDoRma}
                  // color={!isCanDoRma ? 'secondary' : 'primary'}
                  className='is-full sm:is-auto'
                  onClick={() => router.push(`/rnm/unit/${selectedUnitId}/create-fr`)}
                >
                  Buat FR
                </Button>
              </Permission>
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <UnitDetail unitData={unitDetail} />
            </Grid>
            <Grid item xs={12}>
              <DataUnitDetail />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <DocNumberCard frList={frList} docType='FR' />
            </Grid>
            <Grid item xs={12}>
              <DocNumberCard docType='WO' woList={woList} />
            </Grid>
            <Grid item xs={12}>
              <DocNumberCard docType='SCHEDULE' frList={schedules} />
            </Grid>
            <Grid item xs={12}>
              <WoLogHistory />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  )
}

export default UnitDetailPage
