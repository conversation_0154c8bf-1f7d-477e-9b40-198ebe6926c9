import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import { useUnit } from '../../context/UnitContext'
import DocumentDetailUnit from '@/components/dialogs/detail-document-unit'

const DataUnitDetail = () => {
  const { unitData } = useUnit()
  const [openDocumentDialog, setOpenDocumentDialog] = useState(false)
  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Data Unit</Typography>
            <Button size='small' variant='outlined' onClick={() => setOpenDocumentDialog(true)}>
              Riwayat
            </Button>
          </div>
          <div className='flex flex-col gap-6'>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>KM</small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                {`${unitData?.km ?? 0} km`}
              </p>
            </div>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>HM</small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
                {`${unitData?.hm} jam`}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      {openDocumentDialog && (
        <DocumentDetailUnit
          open={openDocumentDialog}
          setOpen={setOpenDocumentDialog}
          handleClose={() => setOpenDocumentDialog(false)}
          unit={unitData}
        />
      )}
    </>
  )
}

export default DataUnitDetail
