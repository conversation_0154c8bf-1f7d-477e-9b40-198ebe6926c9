import { Card, CardContent, Typography } from '@mui/material'
import { useUnit } from '../../context/UnitContext'
import { historyTable } from '../config/table'
import { useMemo } from 'react'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'

const WoLogHistory = () => {
  const { histories, navigate } = useUnit()

  const tableOptions = useMemo(
    () => ({
      data: histories ?? [],
      columns: historyTable({
        detail: id => {
          navigate(`/wo/created/${id}`)
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [histories]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Riwayat Perbaikan</Typography>
        </div>
        <div className='shadow-sm rounded-[8px]'>
          <Table
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography variant='h5'>Belum ada Riwayat</Typography>
                <Typography variant='caption'>Riwayat perbaikan dari unit ini akan ditampilkan di sini</Typography>
              </td>
            }
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default WoLogHistory
