import ChevronRight from '@/components/menu/svg/ChevronRight'
import { frTableConfig, woTableConfig } from '@/pages/repair-and-maintenance/fr/created/config/table'
import { FrType } from '@/types/frTypes'
import { WorkOrderType } from '@/types/woTypes'
import { Card, CardContent, Chip, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { scheduleTable } from '../config/table'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'

type DocNumberCardProps = {
  docType: 'FR' | 'WO' | 'SCHEDULE'
  frList?: FrType[]
  woList?: WorkOrderType[]
}

const DocNumberCard = ({ docType, frList = [], woList }: DocNumberCardProps) => {
  const navigate = useNavigate()

  const tableOptions = useMemo(
    () => ({
      data: frList ?? [],
      columns: scheduleTable({
        detail: id => {
          navigate(`/fr/created/${id}`)
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [frList]
  )

  const table = useReactTable<any>(tableOptions)

  const renderEmpty = () => (
    <div className='flex flex-col items-center justify-center p-6'>
      <Typography variant='h5'>Belum ada {{ WO: 'WO', FR: 'FR', SCHEDULE: 'Jadwal' }[docType]}</Typography>
      <Typography variant='caption'>
        {
          {
            WO: 'WO terbuat untuk unit ini akan ditampilkan di sini',
            FR: 'FR terbuat untuk unit ini akan ditampilkan di sini',
            SCHEDULE: 'Jadwal perawatan untuk unit ini akan ditampilkan di sini'
          }[docType]
        }
      </Typography>
    </div>
  )

  const renderList = () => {
    switch (docType) {
      case 'FR':
        if (frList?.length > 0) {
          return frList?.map(fr => (
            <div key={fr.id} className='flex gap-2 items-center justify-between bg-[#4C4E640D] rounded-[8px] p-4'>
              <div className='flex flex-col gap-2'>
                <Typography>
                  NO. FR: {fr.number}
                  <Chip
                    sx={{ ml: 2 }}
                    variant='tonal'
                    size='small'
                    label={frTableConfig[fr.status]?.label}
                    color={frTableConfig[fr.status]?.color}
                  />
                </Typography>
                <Typography variant='caption'>
                  {fr.createdAt ? formatDate(new Date(fr.createdAt), 'eeee, dd/MM/yyyy', { locale: id }) : '-'}
                </Typography>
              </div>
              <ChevronRight
                role='button'
                onClick={() => navigate(`/fr/created/${fr.id}`)}
                className='text-primary cursor-pointer'
              />
            </div>
          ))
        } else {
          return renderEmpty()
        }
      case 'WO':
        if (woList?.length > 0) {
          return woList?.map(wo => (
            <div key={wo.id} className='flex gap-2 items-center justify-between bg-[#4C4E640D] rounded-[8px] p-4'>
              <div className='flex flex-col gap-2'>
                <Typography>
                  NO. WO: {wo.number}
                  <Chip
                    sx={{ ml: 2 }}
                    variant='tonal'
                    size='small'
                    label={woTableConfig(wo.status)?.label}
                    color={woTableConfig(wo.status)?.color as any}
                  />
                </Typography>
                <Typography variant='caption'>
                  {wo.createdAt ? formatDate(new Date(wo.createdAt), 'eeee, dd/MM/yyyy', { locale: id }) : '-'}
                </Typography>
              </div>
              <ChevronRight
                role='button'
                onClick={() => navigate(`/wo/created/${wo.id}`)}
                className='text-primary cursor-pointer'
              />
            </div>
          ))
        } else {
          return renderEmpty()
        }
      case 'SCHEDULE':
        if (frList?.length > 0) {
          return (
            <div className='shadow-md rounded-[8px]'>
              <Table headerColor='green' table={table} />
            </div>
          )
        } else {
          return renderEmpty()
        }
      default:
        break
    }
  }

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>
            {{ FR: 'Field Report Terbuat', WO: 'Work Order Saat ini', SCHEDULE: 'Schedule' }[docType]}
          </Typography>
        </div>
        <>{renderList()}</>
      </CardContent>
    </Card>
  )
}

export default DocNumberCard
