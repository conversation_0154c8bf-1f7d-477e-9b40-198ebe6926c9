import truncateString from '@/core/utils/truncate'
import { frTableConfig } from '@/pages/repair-and-maintenance/fr/created/config/table'
import { FrType } from '@/types/frTypes'
import { WorkOrderType } from '@/types/woTypes'
import { Chip, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const scheduleHelper = createColumnHelper<FrType>()
const historyHelper = createColumnHelper<WorkOrderType>()

type RowActionType = {
  detail: (id: string) => void
}

export const scheduleTable = (rowAction: RowActionType) => [
  scheduleHelper.accessor('number', {
    header: 'NO DOKUMEN',
    cell: ({ row }) => (
      <Typography
        color='primary'
        variant='body2'
        sx={{ cursor: 'pointer' }}
        onClick={() => rowAction.detail(row.original.id)}
      >
        {truncateString(row.original.number, 15)}
      </Typography>
    )
  }),
  scheduleHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => (
      <Chip
        variant='tonal'
        size='small'
        color={frTableConfig[row.original?.status]?.color}
        label={frTableConfig[row.original?.status]?.label}
      />
    )
  }),
  scheduleHelper.accessor('scheduleDate', {
    header: 'TANGGAL SCHEDULE',
    cell: ({ row }) => (
      <Typography color='error'>
        {' '}
        <i className='clock mr-2' />
        {formatDate(new Date(row.original.scheduleDate), 'dd/MM/yyyy')}
      </Typography>
    )
  }),
  scheduleHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original.id)} className='flex items-center gap-0.5'>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]

export const historyTable = (rowAction: RowActionType) => [
  historyHelper.accessor('number', {
    header: 'NO WO',
    cell: ({ row }) => (
      <Typography
        color='primary'
        variant='body2'
        sx={{ cursor: 'pointer' }}
        onClick={() => rowAction.detail(row.original.id)}
      >
        {truncateString(row.original.number, 20)}
      </Typography>
    )
  }),
  historyHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) =>
      row.original?.createdAt ? formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id }) : '-'
  }),
  historyHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original.id)} className='flex items-center gap-0.5'>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
