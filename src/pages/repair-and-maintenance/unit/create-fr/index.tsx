import * as Sentry from '@sentry/react'
import { useRouter } from '@/routes/hooks'
import { <PERSON>readcrumbs, Button, Chip, Grid, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import { useUnit } from '../context/UnitContext'

import UnitDetail from '../unit-detail/component/UnitDetail'
import CardUnitDetail from './component/CardUnitDetail'
import CardProblem from './component/CardProblem'
import CardSchedule from './component/CardSchedule'
import LocationCard from './component/LocationCard'
import { FormProvider, useForm } from 'react-hook-form'
import { createFrSchemaDto, FrDtoType } from '../../fr/create/config/schema'
import { useEffect } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import { hoursToMinutes } from '../../fr/create/config/utils'
import Permission from '@/core/components/Permission'
import { toast } from 'react-toastify'

const UnitDetailPage = () => {
  const { activeUnit, selectedUnitId, onSubmitFieldReport, unitData, loadingFrCreate } = useUnit()

  const unitDetail = unitData ?? activeUnit

  const method = useForm<FrDtoType>({
    resolver: zodResolver(createFrSchemaDto)
  })

  const { handleSubmit, reset } = method

  useEffect(() => {
    if (unitDetail) {
      const [hours, minutes] = hoursToMinutes(unitDetail?.hm ?? 0)
      reset({
        unitId: unitDetail.id,
        unitHm: hours ?? 0,
        unitMnt: minutes ?? 0,
        unitKm: unitDetail.km ?? 0
      })
    }
  }, [unitDetail])

  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='/rnm/unit' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Unit</Typography>
            </Link>
            <Link to={'/rnm/unit/' + selectedUnitId} replace>
              <Typography color='var(--mui-palette-text-disabled)'>Detil Unit</Typography>
            </Link>
            <Typography>Buat FR</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>{unitDetail?.brandName + ' ' + unitDetail?.type}</Typography>
                <Chip
                  label={unitDetail?.status === 'ACTIVE' ? 'Aktif' : 'Tidak aktif'}
                  color={unitDetail?.status === 'ACTIVE' ? 'success' : 'error'}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>Kode Unit: {unitDetail?.number}</Typography>
            </div>
            <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
              <div className='flex gap-2'>
                <Link to={'/rnm/unit/' + selectedUnitId} replace>
                  <Button
                    color='secondary'
                    disabled={loadingFrCreate}
                    variant='outlined'
                    className='is-full sm:is-auto'
                  >
                    Batalkan
                  </Button>
                </Link>
              </div>
              <Permission permission={['field-report.create', 'field-report.update']}>
                <LoadingButton
                  variant='contained'
                  loading={loadingFrCreate}
                  className='is-full sm:is-auto'
                  onClick={handleSubmit(onSubmitFieldReport, errors => {
                    console.error(errors)
                    Sentry.captureException(errors)
                    Object.entries(errors).forEach(([field, error]) => {
                      toast.error(`${field}: ${error?.message}`, {
                        autoClose: 5000
                      })
                    })
                  })}
                >
                  Buat FR
                </LoadingButton>
              </Permission>
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <UnitDetail unitData={unitDetail} />
            </Grid>
            <Grid item xs={12}>
              <CardUnitDetail />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <LocationCard />
            </Grid>
            <Grid item xs={12}>
              <CardProblem />
            </Grid>
            <Grid item xs={12}>
              <CardSchedule />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default UnitDetailPage
