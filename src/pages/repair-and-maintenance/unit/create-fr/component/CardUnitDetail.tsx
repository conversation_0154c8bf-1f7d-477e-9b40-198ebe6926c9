import NumberField from '@/components/numeric/NumberField'
import { FrDtoType } from '@/pages/repair-and-maintenance/fr/create/config/schema'
import { <PERSON><PERSON>, Card, CardContent, Grid, TextField, Typography } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'

const DataUnitDetail = () => {
  const { control } = useFormContext<FrDtoType>()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Data Unit</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='unitKm'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  ref={field.ref}
                  fullWidth
                  label='KM'
                  InputProps={{
                    endAdornment: 'km',
                    inputComponent: NumberField as any
                  }}
                  error={!!error}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='unitHm'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  ref={field.ref}
                  fullWidth
                  label='HM'
                  InputProps={{
                    endAdornment: 'jam',
                    inputComponent: NumberField as any
                  }}
                  error={!!error}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='unitMnt'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  fullWidth
                  label='MNT'
                  value={value}
                  onChange={onChange}
                  InputProps={{
                    endAdornment: 'mnt',
                    inputComponent: NumberField as any
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default DataUnitDetail
