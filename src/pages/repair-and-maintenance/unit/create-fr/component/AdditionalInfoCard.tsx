// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import FormControl from '@mui/material/FormControl'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import Select from '@mui/material/Select'
import { useMemo } from 'react'
import { WarehouseDataType } from '@/types/appTypes'
import { Controller, useFormContext } from 'react-hook-form'
import { TextField } from '@mui/material'
import { FrDtoType } from '@/pages/repair-and-maintenance/fr/create/config/schema'
import { mrPriorityOptions } from '@/pages/repair-and-maintenance/wo/create-wo-mr/config/util'
import { useAuth } from '@/contexts/AuthContext'

const AdditionalInfoCard = () => {
  const { userProfile } = useAuth()
  const {
    control,
    formState: { errors }
  } = useFormContext<FrDtoType>()
  const items = useMemo(() => userProfile?.sites?.filter(site => site.type === 'WORKSHOP') ?? [], [userProfile])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Lainnya</Typography>
        </div>
        <div className='flex flex-col gap-4'>
          <Controller
            control={control}
            name='destinationSiteId'
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <FormControl fullWidth>
                <InputLabel id='role-select'>Workshop Tujuan</InputLabel>
                <Select
                  key={value}
                  fullWidth
                  id='select-siteId'
                  value={value}
                  onChange={e => onChange(e.target.value)}
                  label='Workshop Tujuan'
                  size='medium'
                  labelId='siteId-select'
                  inputProps={{ placeholder: 'Workshop Tujuan' }}
                  defaultValue=''
                  error={!!error}
                >
                  {items?.map(site => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />

          <Controller
            control={control}
            name='priority'
            render={({ field: { value, onChange }, fieldState: { error } }) => (
              <FormControl>
                <InputLabel id='role-select'>Pilih Prioritas</InputLabel>
                <Select
                  // key={value}
                  fullWidth
                  id='select-priority'
                  onChange={e => {
                    onChange(+e.target.value)
                  }}
                  label='Pilih Prioritas'
                  size='medium'
                  labelId='siteId-select'
                  inputProps={{ placeholder: 'Pilih Prioritas' }}
                  defaultValue=''
                  error={!!error}
                >
                  {mrPriorityOptions?.map(priority => (
                    <MenuItem key={priority.value} value={priority.value}>
                      <div className='flex items-center gap-2'>
                        <div className={`size-2 ${priority.color}`} />
                        {priority.label}
                      </div>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
