import { useMemo } from 'react'
import { Card, CardContent, Grid, Typography, FormControl, InputLabel, MenuItem, Select } from '@mui/material'
import { useAuth } from '@/contexts/AuthContext'
import { useForm<PERSON>ontext, Controller } from 'react-hook-form'
import { FrDtoType } from '@/pages/repair-and-maintenance/fr/create/config/schema'

const LocationCard = () => {
  const { userProfile } = useAuth()
  const { control } = useFormContext<FrDtoType>()

  const workshops = useMemo(() => userProfile?.sites?.filter(site => site.type === 'WORKSHOP') ?? [], [userProfile])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Lokasi</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='siteId'
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='role-select'>Lokasi Unit</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-siteId'
                    value={value}
                    onChange={e => onChange(e.target.value)}
                    label='Lokasi Unit'
                    size='medium'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Lokasi Unit' }}
                    defaultValue=''
                    error={!!error}
                  >
                    {userProfile?.sites?.map(site => (
                      <MenuItem key={site.id} value={site.id}>
                        {site.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='destinationSiteId'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='role-select'>Serahkan Ke Workshop</InputLabel>
                  <Select
                    key={value}
                    fullWidth
                    id='select-siteId'
                    value={value}
                    onChange={e => onChange(e.target.value)}
                    label='Serahkan Ke Workshop'
                    size='medium'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Serahkan Ke Workshop' }}
                    defaultValue=''
                    error={!!error}
                  >
                    {workshops?.map(site => (
                      <MenuItem key={site.id} value={site.id}>
                        {site.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default LocationCard
