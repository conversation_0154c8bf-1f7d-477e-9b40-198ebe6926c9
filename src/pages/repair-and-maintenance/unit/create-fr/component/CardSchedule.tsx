import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { FrDtoType } from '@/pages/repair-and-maintenance/fr/create/config/schema'
import {
  Card,
  CardContent,
  FormControl,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { formatISO, toDate } from 'date-fns'
import { Controller, useFormContext } from 'react-hook-form'

const CardSchedule = () => {
  const { control } = useFormContext<FrDtoType>()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Tambah Schedule (Opsional)</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='scheduleDate'
              render={({ field, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={field.value ? toDate(field.value) : undefined}
                  onChange={(date: Date) => field.onChange(formatISO(date))}
                  dateFormat='eeee dd/MM/yyyy'
                  minDate={new Date()}
                  customInput={
                    <TextField
                      fullWidth
                      label='Tanggal Penjadwalan'
                      placeholder='Pilih Tanggal'
                      className='flex-1'
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  }
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='scheduleReminderType'
              render={({ field, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='role-select'>Pilih Pengingat</InputLabel>
                  <Select
                    fullWidth
                    id='select-siteId'
                    value={field.value}
                    onChange={e => field.onChange(e.target.value)}
                    label='Pilih Pengingat'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Pilih Pengingat' }}
                    defaultValue=''
                  >
                    {['ONE_WEEK_BEFORE', 'ONE_DAY_BEFORE'].map((date, idx) => (
                      <MenuItem key={idx} value={date}>
                        <ListItemText>{date.replaceAll('_', ' ')}</ListItemText>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default CardSchedule
