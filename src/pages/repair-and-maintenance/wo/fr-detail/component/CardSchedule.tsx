import { Card, CardContent, Typography } from '@mui/material'
import { useWo } from '../../context/WoContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const CardSchedule = () => {
  const { frDetail } = useWo()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Schedule</Typography>
        </div>
        <div className='flex flex-col gap-6'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Tanggal Penjadwalan
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {frDetail?.scheduleDate
                ? formatDate(new Date(frDetail?.scheduleDate), 'eeee, dd/MM/yyyy', { locale: id })
                : '-'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CardSchedule
