import { Ava<PERSON>, Card, CardContent, Typography } from '@mui/material'

const CloseReason = ({ reason }: { reason: string }) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Field Report Ditutup</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small>Alasan Ditutup</small>
          <Typography>{reason}</Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default CloseReason
