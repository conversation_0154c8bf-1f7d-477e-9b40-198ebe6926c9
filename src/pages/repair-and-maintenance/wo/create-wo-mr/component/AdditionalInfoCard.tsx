// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'

import { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { mrPriorityOptions } from '../config/util'
import { MrPayloadExtend, useWoMr } from '../../context/WoMrContext'
import { useWo } from '../../context/WoContext'
import { CompanySiteType } from '@/types/companyTypes'

const AdditionalInfoCard = () => {
  const { userProfile } = useAuth()
  const { woDetail } = useWo()
  const { setPartialPayload, payload } = useWoMr()

  const { control, reset, getValues } = useFormContext<MrPayloadExtend>()

  useEffect(() => {
    if (woDetail?.siteId) {
      setPartialPayload('forSiteId', woDetail?.siteId)
      reset({ ...getValues(), forSiteId: woDetail?.siteId })
    }
  }, [woDetail])

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Lainnya</Typography>
        </div>
        <Controller
          control={control}
          name='siteId'
          render={({ field: { value, onChange }, fieldState: { error } }) => (
            <FormControl>
              <InputLabel id='role-select'>Warehouse</InputLabel>
              <Select
                key={payload.siteId}
                fullWidth
                id='select-siteId'
                value={value}
                onChange={e => {
                  setPartialPayload('siteId', e.target.value)
                  onChange(e.target.value)
                }}
                label='Warehouse'
                size='medium'
                labelId='siteId-select'
                inputProps={{ placeholder: 'Warehouse' }}
                defaultValue=''
                error={!!error}
              >
                {userProfile?.sites
                  ?.filter(site => site.type === CompanySiteType.WAREHOUSE)
                  ?.map(site => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          )}
        />
        <Controller
          control={control}
          name='forSiteId'
          render={({ field: { value, onChange }, fieldState: { error } }) => (
            <FormControl>
              <InputLabel id='role-select'>Workshop</InputLabel>
              <Select
                key={payload.forSiteId}
                fullWidth
                id='select-siteId'
                value={value}
                onChange={e => {
                  setPartialPayload('forSiteId', e.target.value)
                  onChange(e.target.value)
                }}
                label='Workshop'
                size='medium'
                labelId='siteId-select'
                inputProps={{ placeholder: 'Workshop' }}
                defaultValue=''
                error={!!error}
                disabled
              >
                {userProfile?.sites
                  ?.filter(site => site.type === CompanySiteType.WORKSHOP)
                  ?.map(site => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          )}
        />
        <Controller
          control={control}
          name='priority'
          render={({ field: { value, onChange }, fieldState: { error } }) => (
            <FormControl>
              <InputLabel id='role-select'>Pilih Prioritas</InputLabel>
              <Select
                // key={value}
                fullWidth
                id='select-priority'
                onChange={e => {
                  setPartialPayload('priority', e.target.value)
                  onChange(e.target.value)
                }}
                label='Pilih Prioritas'
                size='medium'
                labelId='siteId-select'
                inputProps={{ placeholder: 'Pilih Prioritas' }}
                defaultValue=''
                error={!!error}
              >
                {mrPriorityOptions?.map(priority => (
                  <MenuItem key={priority.value} value={priority.value}>
                    <div className='flex items-center gap-2'>
                      <div className={`size-2 ${priority.color}`} />
                      {priority.label}
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        />
        <Controller
          control={control}
          name='note'
          render={({ field, fieldState: { error } }) => (
            <TextField
              {...field}
              fullWidth
              label='Catatan'
              variant='outlined'
              placeholder='Masukkkan Catatan'
              className='mbe-5'
              error={!!error}
            />
          )}
        />
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
