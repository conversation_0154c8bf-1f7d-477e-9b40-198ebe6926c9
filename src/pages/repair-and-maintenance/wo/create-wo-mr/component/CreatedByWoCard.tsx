import { useNavigate } from 'react-router-dom'
import { Card, CardContent, IconButton, Typography } from '@mui/material'
import ChevronRight from '@/components/menu/svg/ChevronRight'
import { MrType } from '@/types/mrTypes'
import { WorkOrderType, WoSegmentType } from '@/types/woTypes'

const CreateByWoCard = ({ segmentData, woData }: { segmentData?: WoSegmentType; woData?: WorkOrderType }) => {
  const navigate = useNavigate()
  return (
    <Card>
      <CardContent className='flex flex-col gap-1'>
        <div className='flex justify-between items-center'>
          <Typography variant='caption' color='secondary'>
            Dibuat dari Work Order
          </Typography>
        </div>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>{woData?.number}</Typography>
          <IconButton onClick={() => navigate(`/wo/created/${segmentData?.workOrderId}`)}>
            <ChevronRight />
          </IconButton>
        </div>
        <Typography>Segment No. {segmentData?.number ?? '-'}</Typography>
      </CardContent>
    </Card>
  )
}

export default CreateByWoCard
