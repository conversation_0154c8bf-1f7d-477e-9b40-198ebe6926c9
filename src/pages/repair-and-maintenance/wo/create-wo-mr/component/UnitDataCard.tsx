import { WarehouseDataType } from '@/types/appTypes'
import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useWo } from '../../context/WoContext'

const UnitDataCard = () => {
  const { woDetail } = useWo()

  const unitKm = woDetail?.unitKm
  const unitHm = woDetail?.unitHm

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Data Unit</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>KM</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unitKm ?? 0} km
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>HM</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unitHm ?? 0} jam
              </Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default UnitDataCard
