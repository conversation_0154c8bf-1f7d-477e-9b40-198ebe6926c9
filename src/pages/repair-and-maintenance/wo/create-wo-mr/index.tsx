import { Link, useParams } from 'react-router-dom'
import * as z from 'zod'
import { useQuery } from '@tanstack/react-query'
import { B<PERSON>crumbs, Button, Grid, Typography } from '@mui/material'
import { useWo } from '../context/WoContext'
import LoadingButton from '@mui/lab/LoadingButton'
import ItemListCard from './component/ItemListCard'
import AdditionalInfoCard from './component/AdditionalInfoCard'
import UnitCard from './component/UnitCard'
import UnitDataCard from './component/UnitDataCard'
import ApprovalListCard from './component/ApprovalListCard'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useAuth } from '@/contexts/AuthContext'
import { MrPayloadExtend, useWoMr } from '../context/WoMrContext'
import { useEffect } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import RnMQueryMethods from '@/api/services/rnm/query'
import CreateByWoCard from './component/CreatedByWoCard'
import * as Sentry from '@sentry/react'
import { toast } from 'react-toastify'

const CreateWoMr = () => {
  const params = useParams()
  const { userProfile } = useAuth()
  const { navigate, selectedWoId, woDetail, handleSubmitMr, creatingMr, activeSegment } = useWo()
  const { setPartialPayload, payload } = useWoMr()

  const method = useForm<MrPayloadExtend>({
    resolver: zodResolver(
      z.object({
        siteId: z.string(),
        priority: z.string(),
        note: z.string()
      })
    )
  })

  const scope = DefaultApprovalScope.MaterialRequest

  const { data: approverList } = useQuery({
    enabled: !!woDetail?.siteId && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, woDetail?.siteId, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope,
        siteId: woDetail?.siteId,
        departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  useEffect(() => {
    setPartialPayload(
      'approvals',
      approverList?.map(approver => ({
        userId: approver.user?.id
      }))
    )
  }, [approverList])

  useEffect(() => {
    setPartialPayload('unitId', woDetail?.unit?.id)
    setPartialPayload('unitKm', woDetail?.unitKm)
    setPartialPayload('unitHm', woDetail?.unitHm)
  }, [woDetail])

  useEffect(() => {
    setPartialPayload('workOrderSegmentId', activeSegment?.id || params?.segmentId)
  }, [activeSegment])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Order</Typography>
          </Link>
          <Link to='/wo/created' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Order Terbuat</Typography>
          </Link>
          <Link to={'/wo/created/' + selectedWoId} replace>
            <Typography color='var(--mui-palette-text-disabled)'>Detil Work Order</Typography>
          </Link>
          <Typography>Buat Material Request</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
          <div className='flex flex-col max-sm:text-center'>
            <Typography variant='h4'>Buat Material Request</Typography>
            <Typography>Buat Material Request untuk diajukan kepada eselon terkait</Typography>
          </div>
          <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
            <Button
              color='secondary'
              variant='outlined'
              // disabled={showLoading}
              onClick={() => navigate(-1)}
            >
              Batalkan
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={creatingMr}
              variant='contained'
              onClick={method.handleSubmit(
                () => handleSubmitMr(payload),
                errors => {
                  console.error(errors)
                  Sentry.captureException(errors)
                  Object.entries(errors).forEach(([field, error]) => {
                    toast.error(`${field}: ${error?.message}`, {
                      autoClose: 5000
                    })
                  })
                }
              )}
            >
              Buat Material Request
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <FormProvider {...method}>
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <CreateByWoCard segmentData={activeSegment} woData={woDetail} />
            </Grid>
            <Grid item xs={12}>
              <AdditionalInfoCard />
            </Grid>
            <Grid item xs={12}>
              <UnitCard />
            </Grid>
            <Grid item xs={12}>
              <UnitDataCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
            </Grid>
          </Grid>
        </Grid>
      </FormProvider>
    </Grid>
  )
}

export default CreateWoMr
