import * as z from 'zod'

export const createPreReleaseSchema = z.object({
  workOrderId: z.string().uuid(),
  templateId: z.string().uuid(),
  assignedTo: z.string().uuid(),
  note: z.string().optional().nullable(),
  checkPoints: z
    .array(
      z.object({
        templateCheckPointId: z.string().uuid(),
        isChecked: z.boolean(),
        note: z.string()
      })
    )
    .min(1),
  workType: z.enum(['SCHEDULED', 'UNSCHEDULED']),
  workName: z.string(),
  approvals: z
    .array(
      z.object({
        userId: z.string().uuid()
      })
    )
    .min(1)
})

export type CreatePreReleaseDtoType = Required<z.TypeOf<typeof createPreReleaseSchema>>
