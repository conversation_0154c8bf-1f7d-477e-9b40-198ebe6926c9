import { CheckPointType, ParameterType } from '@/types/parameterTypes'
import { PreReleaseDtoType } from '@/types/preReleaseTypes'
import { Checkbox, Grid, TextField, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { Control, Controller, FieldArrayWithId } from 'react-hook-form'

type ParameterTable = FieldArrayWithId<PreReleaseDtoType, 'checkPoints', 'id'> & CheckPointType

type RowPropsType = {
  control: Control<PreReleaseDtoType, any>
  withoutCheck?: boolean
}

const parameterHelper = createColumnHelper<ParameterTable>()

export const parameterTableColumns = (rowProps: RowPropsType) => [
  parameterHelper.display({
    id: 'numbering',
    size: 20,
    header: 'NO',
    cell: ({ row }) => row.index + 1
  }),
  parameterHelper.accessor('description', {
    header: 'DESKRIPSI',

    cell: ({ row }) => (
      <div className='flex flex-col gap-2'>
        <Typography component='li'>{row.original.name}</Typography>
        <Typography variant='body2' sx={{ ml: '20px', maxWidth: '300px', textWrap: 'balance' }}>
          {row.original.description}
        </Typography>
      </div>
    )
  }),
  ...(rowProps.withoutCheck
    ? []
    : [
        parameterHelper.accessor('id', {
          header: 'CHECK POINT',
          size: 20,
          cell: ({ row }) => (
            <Grid container justifyContent='center' alignItems='center'>
              <Controller
                control={rowProps.control}
                name={`checkPoints.${row.index}.isChecked`}
                render={({ field: { value, onChange } }) => (
                  <Checkbox color='info' checked={value} onChange={(e, checked) => onChange(checked)} />
                )}
              />
            </Grid>
          )
        }),
        parameterHelper.display({
          id: 'note',
          size: 350,
          header: 'CATATAN',
          cell: ({ row }) => (
            <Controller
              control={rowProps.control}
              name={`checkPoints.${row.index}.note`}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  defaultValue={field.value}
                  onBlur={e => field.onChange(e.target.value)}
                  ref={field.ref}
                  size='small'
                  fullWidth
                  error={!!error}
                />
              )}
            />
          )
        })
      ])
]
