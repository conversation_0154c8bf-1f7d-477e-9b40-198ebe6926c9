import { Card, CardContent, Divider, Typography } from '@mui/material'
import UnitDetail from './UnitDetail'
import CheckerField from './Checker'
import CheckListTable from './Checklist'

const WorkDetail = () => {
  return (
    <Card>
      <CardContent>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'><PERSON><PERSON></Typography>
        </div>
        <div className='flex flex-col gap-4'>
          <Typography>Unscheduled Service</Typography>
          <Divider />
          <CheckerField />
        </div>
      </CardContent>
    </Card>
  )
}

export default WorkDetail
