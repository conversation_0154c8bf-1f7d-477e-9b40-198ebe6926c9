import { Box, Typography } from '@mui/material'

const AttentionCard = () => {
  return (
    <div className='rounded-[8px] bg-[#4C4E640D] p-4 px-6 flex items-center'>
      <i className='triangle-pentung-icon size-8 text-error' />
      <div>
        <Box component='ul' sx={{ marginLeft: '3rem', '& li': { py: 1 } }}>
          <li>
            <Typography className='font-semibold text-base'>Parkirkan unit pada tempat rata dengan aman</Typography>
          </li>
          <Typography className='font-normal text-sm' variant='body2'>
            Park the unit on flat area safely
          </Typography>
          <li>
            <Typography className='font-semibold text-base'>Aktifkan Parking Brake</Typography>
          </li>
          <Typography className='font-normal text-sm' variant='body2'>
            Ensuring parking brake is 'ON'
          </Typography>
          <li>
            <Typography className='font-semibold text-base'>
              Yakinkan bahwa unit yang diinspeksi dalam kondisi aman
            </Typography>
          </li>
          <Typography className='font-normal text-sm' variant='body2'>
            Ensure the unit is safe to inspect
          </Typography>
        </Box>
      </div>
    </div>
  )
}

export default AttentionCard
