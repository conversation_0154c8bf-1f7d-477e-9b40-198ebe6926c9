import { PreReleaseDtoType } from '@/types/preReleaseTypes'
import { Card, CardContent, Grid, TextField, Typography } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'

const NoteCard = () => {
  const { control } = useFormContext<PreReleaseDtoType>()

  return (
    <Card>
      <CardContent className='space-y-1'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Catatan</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  error={!!error}
                  ref={field.ref}
                  fullWidth
                  label='Catatan'
                  className='bg-white rounded-lg'
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default NoteCard
