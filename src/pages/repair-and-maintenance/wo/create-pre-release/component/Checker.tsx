import ParameterQueryMethods from '@/api/services/parameter/query'
import { FORMAT_QUERY_LIST_KEY } from '@/api/services/parameter/service'
import UserQueryMethods, { USER_LIST_QUERY_KEY } from '@/api/services/user/query'
import { PreReleaseDtoType } from '@/types/preReleaseTypes'
import { UserType } from '@/types/userTypes'
import {
  Autocomplete,
  debounce,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useFormContext } from 'react-hook-form'

const CheckerField = () => {
  const { control } = useFormContext<PreReleaseDtoType>()

  const [userQuery, setUserQuery] = useState<string>('')

  const { data: users, isFetching: isFetchingUser } = useQuery({
    enabled: !!userQuery,
    queryKey: [USER_LIST_QUERY_KEY, userQuery],
    queryFn: async () => {
      const res = await UserQueryMethods.getUserList({ limit: Number.MAX_SAFE_INTEGER, search: userQuery })
      return res.items ?? []
    }
  })

  const { data: formatList } = useQuery({
    queryKey: [FORMAT_QUERY_LIST_KEY],
    queryFn: async () => {
      const res = await ParameterQueryMethods.getPreReleaseTemplates({ limit: Number.MAX_SAFE_INTEGER })
      return res.items ?? []
    }
  })

  return (
    <div className='flex flex-col gap-4'>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Controller
            control={control}
            name='workName'
            render={({ field, fieldState: { error } }) => (
              <TextField fullWidth label='Kategori Pekerjaan' {...field} error={!!error} />
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <Controller
            control={control}
            name='templateId'
            render={({ field, fieldState: { error } }) => (
              <FormControl fullWidth>
                <InputLabel id='format-docs'>Format Dokumen</InputLabel>
                <Select {...field} id='format-docs' label='Format Dokumen' error={!!error} placeholder='Pilih Format'>
                  {formatList?.map(item => (
                    <MenuItem key={item.id} value={item.id}>
                      {item.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <Controller
            control={control}
            name='assignedTo'
            render={({ field, fieldState: { error } }) => (
              <Autocomplete
                options={users ?? []}
                freeSolo
                onInputChange={debounce((e, newValue) => {
                  setUserQuery(newValue)
                }, 700)}
                loading={isFetchingUser}
                noOptionsText='User tidak ditemukan'
                getOptionLabel={(option: UserType) => `${option.fullName} | ${option.title}`}
                onChange={(e, newValue: UserType) => {
                  field.onChange(newValue.id)
                }}
                renderInput={params => (
                  <TextField
                    {...params}
                    error={!!error}
                    ref={field.ref}
                    fullWidth
                    label='Dicek oleh'
                    className='bg-white rounded-lg'
                  />
                )}
              />
            )}
          />
        </Grid>
      </Grid>
    </div>
  )
}

export default CheckerField
