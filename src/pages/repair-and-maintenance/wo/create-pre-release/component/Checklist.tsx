import { useEffect, useMemo } from 'react'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { Card, CardContent, Typography } from '@mui/material'
import Table from '@/components/table'
import { parameterTableColumns } from '../config/table'
import { usePreRelease } from '@/pages/repair-and-maintenance/pre-release/context/PreReleaseContext'
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form'
import { PreReleaseDtoType } from '@/types/preReleaseTypes'
import { useQuery } from '@tanstack/react-query'
import { FORMAT_QUERY_LIST_KEY } from '@/api/services/parameter/service'
import ParameterQueryMethods from '@/api/services/parameter/query'

const CheckListTable = () => {
  const { control, reset, getValues } = useFormContext<PreReleaseDtoType>()
  const { fields } = useFieldArray({ control, name: 'checkPoints' })

  const templateId = useWatch({ control, name: 'templateId' })

  const { data: template } = useQuery({
    enabled: !!templateId,
    queryKey: [FORMAT_QUERY_LIST_KEY, templateId],
    queryFn: async () => ParameterQueryMethods.getPreReleaseTemplate(templateId)
  })

  const tableOptions = useMemo(
    () => ({
      data: fields ?? [],
      columns: parameterTableColumns({ control, withoutCheck: true }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [fields]
  )

  const table = useReactTable<any>(tableOptions)

  useEffect(() => {
    if (template) {
      reset({
        ...getValues(),
        checkPoints: template.checkPoints?.map(item => ({
          ...item,
          templateCheckPointId: item.id,
          isChecked: false,
          note: ''
        }))
      })
    }
  }, [template])

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <Typography variant='h5'>Check Point</Typography>
        <div className='shadow-sm rounded-[8px]'>
          <Table
            headerColor='green'
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography>Belum ada Checklist</Typography>
              </td>
            }
            disablePagination
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default CheckListTable
