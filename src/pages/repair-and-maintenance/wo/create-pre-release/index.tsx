import LoadingButton from '@mui/lab/LoadingButton'
import { Breadcrumbs, Grid, Typography } from '@mui/material'
import { Link, useLocation } from 'react-router-dom'
import { useWo } from '../context/WoContext'
import AttentionCard from './component/AttentionCard'
import WorkDetail from './component/WorkDetail'
import NoteCard from './component/NoteCard'
import { FormProvider, SubmitErrorHandler, SubmitHandler, useForm } from 'react-hook-form'
import { PreReleaseDtoType } from '@/types/preReleaseTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import { createPreReleaseSchema } from './config/schema'
import { usePreRelease } from '../../pre-release/context/PreReleaseContext'
import { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useQuery } from '@tanstack/react-query'
import ApprovalListCard from '@/pages/material-request/create/components/ApprovalListCard'
import UnitDetail from './component/UnitDetail'
import CheckListTable from './component/Checklist'
import Permission from '@/core/components/Permission'
import { toast } from 'react-toastify'
import * as Sentry from '@sentry/react'

const CreatePreRelease = () => {
  const { selectedWoId, woDetail, refetchWoList } = useWo()
  const { onSubmitPreRelease, loadingCreatePrerelease } = usePreRelease()
  const { userProfile } = useAuth()
  const { state } = useLocation()

  const methods = useForm<PreReleaseDtoType>({
    resolver: zodResolver(createPreReleaseSchema)
  })

  const { handleSubmit, reset, getValues } = methods

  const scope = DefaultApprovalScope.WorkOrderPreRelease

  const { data: approverList } = useQuery({
    enabled: !!woDetail?.siteId && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, woDetail?.siteId, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope,
        siteId: woDetail?.siteId,
        departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  const onContinueSubmit: SubmitHandler<PreReleaseDtoType> = dto => {
    Sentry.captureMessage(`Submit Pre-Release: ${JSON.stringify(dto)}`)
    onSubmitPreRelease(dto, refetchWoList)
  }

  const onErrorSubmit: SubmitErrorHandler<PreReleaseDtoType> = error => {
    console.error(error)
    Sentry.captureException(error)
    Object.entries(error).forEach(([field, error]) => {
      toast.error(`${field}: ${error?.message}`, {
        autoClose: 5000
      })
    })
    if (!!error.approvals) {
      toast.error('Tidak bisa membuat Pre-Release, default approval tidak ditemukan')
    }
  }

  useEffect(() => {
    if (woDetail?.id) {
      reset({
        ...getValues(),
        workOrderId: woDetail?.id
      })
    }
  }, [woDetail])

  useEffect(() => {
    reset({
      ...getValues(),
      workType: 'UNSCHEDULED',
      approvals: approverList?.map(approver => ({
        userId: approver.user?.id
      }))
    })
  }, [approverList])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Work Order</Typography>
          </Link>
          <Link to='/wo/pre-releases' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Pengajuan Pre-Release</Typography>
          </Link>
          <Link to={'/wo/created/' + selectedWoId} state={{ isFromPreRelease: true }} replace>
            <Typography color='var(--mui-palette-text-disabled)'>Detil Work Order</Typography>
          </Link>
          <Typography>Buat Pre-Release</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>Buat Pre-Release</Typography>
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>
              Isi dokumen pre-release dan ajukan ke supervisor kamu
            </Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <Permission permission={['work-order.write-pre-release-tmp']}>
              <LoadingButton
                loading={loadingCreatePrerelease}
                startIcon={<></>}
                variant='contained'
                className='is-full sm:is-auto'
                onClick={handleSubmit(onContinueSubmit, onErrorSubmit)}
              >
                Buat Pre-Release
              </LoadingButton>
            </Permission>
          </div>
        </div>
      </Grid>
      <FormProvider {...methods}>
        <Grid item xs={12}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <Grid container spacing={4}>
                <Grid item xs={12}>
                  <WorkDetail />
                </Grid>
                <Grid item xs={12}>
                  <CheckListTable />
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} md={6}>
              <Grid container spacing={4}>
                <UnitDetail />
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </FormProvider>
    </Grid>
  )
}

export default CreatePreRelease
