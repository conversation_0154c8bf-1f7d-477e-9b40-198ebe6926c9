import {
  Card,
  CardContent,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography
} from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns, tableColumnsOrigin } from '../config/table'
import Table from '@/components/table'
import { useParams } from 'react-router-dom'
import { useWo } from '../../context/WoContext'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useQuery } from '@tanstack/react-query'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { SrRmOPayload } from '@/types/srTypes'
import { ReactNode, useEffect, useMemo, useState } from 'react'
import { WarehouseItemType } from '@/types/appTypes'
import { useWoPartSwap } from '../../context/WoPartSwapContext'
import { swapTypeOptions } from '../config/utils'
import AddOriginPartDialog from '@/components/dialogs/add-origin-part'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'

const ItemListCard = () => {
  const params = useParams()
  const { activeSegment, woDetail } = useWo()
  const { method } = useWoPartSwap()
  const {
    reset,
    getValues,
    control,
    formState: { errors }
  } = method

  const { fields, update } = useFieldArray({ control, name: 'items' })

  const [dialogItem, setDialogItem] = useState<{ origin: boolean; item: boolean }>({ origin: false, item: false })
  const [activeItem, setActiveItem] = useState<WarehouseItemType | null>(null)
  const [activeOriginIndexItem, setActiveOriginIndexItem] = useState<number | null>(null)

  const { data: segmentDetail } = useQuery({
    enabled: !!activeSegment?.id || !!params?.segmentId,
    queryKey: ['WO_SEGMENT_KEY', woDetail, activeSegment, params],
    queryFn: () => RnMQueryMethods.getWoSegment(woDetail?.id, activeSegment?.id || params?.segmentId)
  })

  const tableOptionsOrigin = useMemo(
    () => ({
      data: fields ?? [],
      columns: tableColumnsOrigin({
        onViewOrigin: itemData => {
          setActiveItem({ ...itemData, itemId: itemData.item.id } as WarehouseItemType)
          setDialogItem(curr => ({ ...curr, item: true }))
        },
        onEditOrigin: item => {
          setActiveOriginIndexItem(item)
          setActiveItem({})
          setDialogItem(curr => ({ ...curr, origin: true }))
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [fields]
  )
  const tableOptions = useMemo(
    () => ({
      data: segmentDetail?.items ?? [],
      columns: tableColumns({
        onView: itemData => {
          setActiveItem({ ...itemData, itemId: itemData?.item?.id } as WarehouseItemType)
          setDialogItem(curr => ({ ...curr, item: true }))
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [segmentDetail]
  )

  const table = useReactTable<any>(tableOptions)
  const tableOrigin = useReactTable<any>(tableOptionsOrigin)

  const handleChangePartType = (event: SelectChangeEvent<unknown>) => {
    const newFields = fields?.map(item => ({ ...item, swapType: event.target.value as any }))
    reset({ ...getValues(), items: newFields })
  }

  const handleUpdateOriginItem = (item: WarehouseItemType & { note: string; serialNumber: string }) => {
    const index = activeOriginIndexItem
    const currentItem = fields?.[index]
    update(index, {
      ...currentItem,
      originItemId: item.itemId,
      originSerialNumber: !!item.serialNumber ? item.serialNumber : null,
      note: item.note,
      originItem: { ...item, serialNumber: { number: item.serialNumber } } as any
    })
    setDialogItem(curr => ({ ...curr, origin: false }))
  }

  useEffect(() => {
    if (segmentDetail?.items?.length > 0) {
      reset({
        ...getValues(),
        items: segmentDetail?.items?.map(segmentItem => ({
          ...segmentItem,
          itemId: segmentItem?.item?.id,
          destinationItemId: segmentItem?.item?.id,
          destinationSerialNumberId: segmentItem?.serialNumber?.id,
          originItem: null,
          originItemId: null,
          originSerialNumber: null
        }))
      })
    } else {
      reset({
        ...getValues(),
        items: []
      })
    }
  }, [segmentDetail?.items])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Parts</Typography>
          </div>
          <FormControl fullWidth>
            <InputLabel id='role-select'>Tipe Part Swap</InputLabel>
            <Select
              label='Tipe Part Swap'
              value={fields[0]?.swapType}
              onChange={handleChangePartType}
              id='select-role'
              labelId='role-select'
              placeholder='Pilih Tipe Part Swap'
              error={!!errors?.items?.[0]?.swapType}
            >
              {swapTypeOptions?.map(role => (
                <MenuItem key={role.value} value={role.value}>
                  {role.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <div className='grid md:grid-cols-2 gap-4'>
            <div className='flex flex-col bg-[#4C4E640D] p-4 rounded-[8px] gap-2'>
              <Typography variant='h5'>Part Asal</Typography>
              <Typography variant='body1'>Part yang akan diambil</Typography>
              <div className='shadow-sm rounded-[8px]'>
                <Table
                  headerColor='green'
                  table={tableOrigin}
                  emptyLabel={
                    <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography>Belum ada Barang</Typography>
                      <Typography className='text-sm text-gray-400'>
                        Tambahkan barang yang ingin dimasukkan dalam Part Swap ini
                      </Typography>
                    </td>
                  }
                  disablePagination
                />
              </div>
            </div>
            <div className='flex flex-col bg-[#4C4E640D] p-4 rounded-[8px] gap-2'>
              <Typography variant='h5'>Part Tujuan</Typography>
              <Typography variant='body1'>Part unit yang sedang kamu service</Typography>
              <div className='shadow-sm rounded-[8px]'>
                <Table
                  headerColor='green'
                  table={table}
                  emptyLabel={
                    <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography>Belum ada Barang</Typography>
                      <Typography className='text-sm text-gray-400'>
                        Tambahkan barang yang ingin dimasukkan dalam dokumen ini
                      </Typography>
                    </td>
                  }
                  disablePagination
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {dialogItem.item && (
        <AddWarehouseItemDialog
          open={dialogItem.item}
          setOpen={bool => setDialogItem(curr => ({ ...curr, item: bool }))}
          onSubmit={() => {}}
          viewOnly
          withoutUnit
          currentItem={activeItem}
        />
      )}
      {dialogItem.origin && (
        <AddOriginPartDialog
          open={dialogItem.origin}
          setOpen={bool => setDialogItem(curr => ({ ...curr, origin: bool }))}
          onSubmit={handleUpdateOriginItem}
          withoutUnit
          currentItem={{}}
        />
      )}
    </>
  )
}

export default ItemListCard
