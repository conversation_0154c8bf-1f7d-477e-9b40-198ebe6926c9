import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useWo } from '../../context/WoContext'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import RnMQueryMethods from '@/api/services/rnm/query'

const SegmentCard = () => {
  const params = useParams()
  const { activeSegment, woDetail } = useWo()

  const { data: segmentDetail } = useQuery({
    enabled: !!activeSegment?.id || !!params?.segmentId,
    queryKey: ['WO_SEGMENT_KEY', woDetail, activeSegment, params],
    queryFn: () => RnMQueryMethods.getWoSegment(woDetail?.id, activeSegment?.id || params?.segmentId)
  })
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Segment</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Job Code</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {segmentDetail?.jobCode
                  ? `${segmentDetail?.jobCode.code} | ${segmentDetail?.jobCode.description}}`
                  : '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>SMCS</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {segmentDetail?.componentCode
                  ? `${segmentDetail?.componentCode.code} | ${segmentDetail?.componentCode.description}`
                  : '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Kode Modifier</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {segmentDetail?.modifierCode
                  ? `${segmentDetail?.modifierCode.code} | ${segmentDetail?.modifierCode.description}`
                  : '-'}
              </Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default SegmentCard
