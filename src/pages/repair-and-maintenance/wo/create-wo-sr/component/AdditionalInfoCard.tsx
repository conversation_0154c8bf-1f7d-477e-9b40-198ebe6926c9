// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'

import { useAuth } from '@/contexts/AuthContext'
import { mrPriorityOptions } from '@/pages/material-request/create/config/enum'
import { SrRmOPayload } from '@/types/srTypes'

const AdditionalInfoCard = () => {
  const { userProfile } = useAuth()
  const { control } = useFormContext<SrRmOPayload>()

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Lainnya</Typography>
        </div>
        <Controller
          control={control}
          name='siteId'
          render={({ field: { value, onChange }, fieldState: { error } }) => (
            <FormControl>
              <InputLabel id='role-select'>Pilih Site</InputLabel>
              <Select
                key={value}
                fullWidth
                id='select-siteId'
                value={value}
                onChange={e => onChange(e.target.value)}
                label='Pilih Site'
                size='medium'
                labelId='siteId-select'
                inputProps={{ placeholder: 'Pilih Site' }}
                defaultValue=''
                error={!!error}
              >
                {userProfile?.sites?.map(site => (
                  <MenuItem key={site.id} value={site.id}>
                    {site.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        />
        <Controller
          control={control}
          name='priority'
          render={({ field: { value, onChange }, fieldState: { error } }) => (
            <FormControl>
              <InputLabel id='role-select'>Pilih Prioritas</InputLabel>
              <Select
                key={value}
                fullWidth
                id='select-priority'
                value={String(value)}
                onChange={e => onChange(+e.target.value)}
                label='Pilih Prioritas'
                size='medium'
                labelId='siteId-select'
                inputProps={{ placeholder: 'Pilih Prioritas' }}
                defaultValue=''
                error={!!error}
              >
                {mrPriorityOptions?.map(priority => (
                  <MenuItem key={priority.value} value={priority.value}>
                    <div className='flex items-center gap-2'>
                      <div className={`size-2 ${priority.color}`} />
                      {priority.label}
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        />
        <Controller
          control={control}
          name='note'
          render={({ field, fieldState: { error } }) => (
            <TextField
              {...field}
              fullWidth
              label='Catatan'
              variant='outlined'
              placeholder='Masukkkan Catatan'
              className='mbe-5'
              {...(!!error && { error: true })}
            />
          )}
        />
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
