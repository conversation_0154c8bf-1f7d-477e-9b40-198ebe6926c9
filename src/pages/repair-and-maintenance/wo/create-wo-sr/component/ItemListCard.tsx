import { Card, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { useParams } from 'react-router-dom'
import { useWo } from '../../context/WoContext'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useQuery } from '@tanstack/react-query'
import { useFormContext, useWatch } from 'react-hook-form'
import { SrRmOPayload } from '@/types/srTypes'
import { useEffect, useMemo, useState } from 'react'
import { WarehouseItemType } from '@/types/appTypes'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { WO_SEGMENT_KEY } from '@/api/services/rnm/service'

const ItemListCard = () => {
  const params = useParams()
  const { activeSegment, woDetail } = useWo()
  const { reset, getValues, control } = useFormContext<SrRmOPayload>()

  const items = useWatch({ control, name: 'items' })

  const [dialogItem, setDialogItem] = useState<boolean>(false)
  const [activeItem, setActiveItem] = useState<WarehouseItemType | null>(null)

  const { data: segmentDetail } = useQuery({
    enabled: !!activeSegment?.id || !!params?.segmentId,
    queryKey: [WO_SEGMENT_KEY, woDetail, activeSegment, params],
    queryFn: () => RnMQueryMethods.getWoSegment(woDetail?.id, activeSegment?.id || params?.segmentId)
  })

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns({
        onView: itemData => {
          setActiveItem({ ...itemData, itemId: itemData.item.id } as WarehouseItemType)
          setDialogItem(true)
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items]
  )

  const table = useReactTable<any>(tableOptions)

  useEffect(() => {
    if (segmentDetail?.items?.length > 0) {
      reset({
        ...getValues(),
        items: segmentDetail?.items
          ?.filter(item => item.type === 'COMPONENT')
          ?.map(item => ({
            ...item,
            itemId: item.item.id,
            serialNumberId: item.serialNumber?.id
          }))
      })
    } else {
      reset({
        ...getValues(),
        items: []
      })
    }
  }, [segmentDetail?.items])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Barang</Typography>
          </div>
          <div className='shadow-sm rounded-[8px]'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan barang yang ingin dimasukkan dalam MR ini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {dialogItem && (
        <AddWarehouseItemDialog
          open={dialogItem}
          setOpen={setDialogItem}
          onSubmit={() => {}}
          viewOnly
          withoutUnit
          currentItem={activeItem}
        />
      )}
    </>
  )
}

export default ItemListCard
