// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports

import { Grid } from '@mui/material'
import { WarehouseDataType, WarehouseItemType } from '@/types/appTypes'
import { UnitType } from '@/types/companyTypes'
import { useWo } from '../../context/WoContext'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { UNIT_QUERY_KEY } from '@/api/services/company/query'
import { DEFAULT_CATEGORY } from '@/data/default/category'

const UnitCard = () => {
  const { woDetail } = useWo()

  const { data: unit } = useQuery({
    enabled: !!woDetail?.unitId,
    queryKey: [UNIT_QUERY_KEY, woDetail?.unitId],
    queryFn: () => CompanyQueryMethods.getUnit(woDetail?.unitId)
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Unit</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Kode Unit</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.number ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Kode Activa</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.asset?.code ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Kategori Unit</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.category?.name ?? DEFAULT_CATEGORY.name}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Jenis Unit</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.subCategory?.name ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Type Equipment</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.equipmentType ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Merk Unit</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.brandName ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Tipe Unit</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.type ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Nomor Rangka</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.hullNumber ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Nomor Mesin</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.engineNumber ?? '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <Typography variant='subtitle2'>Plat Nomor</Typography>
              <Typography variant='subtitle1' className='text-textPrimary'>
                {unit?.plateNumber ?? '-'}
              </Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default UnitCard
