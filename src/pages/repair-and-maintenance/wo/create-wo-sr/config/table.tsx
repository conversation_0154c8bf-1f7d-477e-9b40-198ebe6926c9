import { ItemMrInput } from '@/pages/material-request/create/config/schema'
import { WoSegmentItem } from '@/types/woTypes'
import { IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

type DataTypeWithAction = WoSegmentItem & {
  action?: string
}
type RowActionType = {
  onEdit?: (item: ItemMrInput) => void
  onView?: (item: ItemMrInput) => void
}

const columnHelper = createColumnHelper<DataTypeWithAction>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('item.number', {
    header: 'KODE BARANG',
    cell: ({ row }) => <Typography>{row.original.item?.number}</Typography>
  }),
  columnHelper.accessor('serialNumber.number', {
    header: 'NO SERIAL'
  }),
  columnHelper.accessor('item.name', {
    header: 'NAMA ITEM',
    cell: ({ row }) => <Typography>{row.original.item?.name}</Typography>
  }),
  columnHelper.accessor('item.brandName', {
    header: 'MERK ITEM',
    cell: ({ row }) => <Typography>{row.original.item?.brandName}</Typography>
  }),
  columnHelper.accessor('quantity', {
    header: 'QTY',
    cell: ({ row }) => (
      <Typography>
        {row.original.quantity} {row.original.quantityUnit}
      </Typography>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        {!!rowAction.onEdit && (
          <IconButton size='small' onClick={() => rowAction.onEdit(row.original)}>
            <i className='ic-baseline-edit text-secondary' />
          </IconButton>
        )}
        {!!rowAction.onView && (
          <IconButton size='small' onClick={() => rowAction.onView(row.original)}>
            <i className='ri-eye-line text-secondary' />
          </IconButton>
        )}
      </div>
    ),
    enableSorting: false
  })
]
