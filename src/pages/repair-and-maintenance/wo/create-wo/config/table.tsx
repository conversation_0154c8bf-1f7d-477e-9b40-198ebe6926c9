import { createColumnHelper } from '@tanstack/react-table'
import { SegmentType } from './schema'
import { IconButton, Typography } from '@mui/material'
import { CodeType } from '@/types/codes'
import truncateString from '@/core/utils/truncate'

type RowActionType = {
  detail: (item: SegmentType) => void
}

type SegmentTableProps = {
  jobCodeList: CodeType[]
  componentCodeList: CodeType[]
  modifierCodeList: CodeType[]
} & RowActionType

const columnHelper = createColumnHelper<SegmentType>()

export const segmentTableColumns = (rowAction: SegmentTableProps) => [
  columnHelper.accessor('id', {
    header: 'NO',
    cell: ({ row }) => row.index + 1
  }),
  columnHelper.accessor('jobCodeId', {
    header: 'JOB CODE',
    cell: ({ row }) => {
      const jobCode = rowAction.jobCodeList?.find(j => j.id === row.original?.jobCodeId)
      return jobCode?.code ? `${jobCode.code} | ${truncateString(jobCode.description, 12)}` : '-'
    }
  }),
  columnHelper.accessor('componentCodeId', {
    header: 'SMCS',
    cell: ({ row }) => {
      const componentCode = rowAction.componentCodeList?.find(c => c.id === row.original?.componentCodeId)
      return componentCode?.code ? `${componentCode.code} | ${truncateString(componentCode.description, 12)}` : '-'
    }
  }),
  columnHelper.accessor('modifierCodeId', {
    header: 'MODIFIER',
    cell: ({ row }) => {
      const modifierCode = rowAction.modifierCodeList?.find(m => m.id === row.original?.modifierCodeId)
      return modifierCode?.code ? `${modifierCode.code} | ${truncateString(modifierCode.description, 12)}` : '-'
    }
  }),
  columnHelper.accessor('items', {
    header: 'PARTS',
    cell: ({ row }) => {
      const itemParts = row.original.items?.filter(i => i.type === 'COMPONENT')
      return itemParts.length + `${itemParts?.length > 1 ? ' Parts' : ' Part'}`
    }
  }),
  columnHelper.accessor('items', {
    header: 'MISCELLANEOUS',
    cell: ({ row }) => {
      const itemMisc = row.original.items?.filter(i => i.type !== 'COMPONENT')
      return itemMisc.length + `${itemMisc?.length > 1 ? ' Misc' : ' Misc'}`
    }
  }),
  columnHelper.accessor('docs', {
    header: 'DOKUMEN TERBUAT',
    cell: ({ row }) => {
      return (
        <div className='flex gap-2 items-center justify-between'>
          <Typography color='primary'>Belum ada dokumen</Typography>
          <IconButton>
            <i className='ri-arrow-right-s-line text-primary' />
          </IconButton>
        </div>
      )
    }
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
