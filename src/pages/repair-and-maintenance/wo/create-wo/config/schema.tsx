import { number, z } from 'zod'

const imageSchema = z.object({
  uploadId: z.string().uuid().optional().nullable(),
  filename: z.string().optional().nullable()
})

export const itemSchema = z.object({
  id: z.string().uuid().optional().nullable(),
  itemId: z.string().uuid(),
  type: z.enum(['COMPONENT', 'MISCELLANEOUS']),
  serialNumber: z.string().optional().nullable(),
  quantity: z.number().min(1),
  usedQuantity: z.number().optional().nullable(),
  quantityUnit: z.string().min(1),
  largeUnitQuantity: z.number().nonnegative(),
  note: z.string().optional().nullable(),
  images: z.array(imageSchema),
  name: z.string().optional().nullable(),
  number: z.string().optional().nullable(),
  brandName: z.string().optional().nullable()
})

export const segmentSchema = z.object({
  items: z.array(itemSchema).optional(),
  jobCodeId: z.number().nonnegative(),
  componentCodeId: z.number().nonnegative(),
  modifierCodeId: z.number().nonnegative().optional().nullable(),
  divisionId: z.string().uuid().optional().nullable()
})

export const createWorkOrderSchema = z.object({
  fieldReportId: z.string().uuid(),
  destinationSiteId: z.string().uuid(),
  unitId: z.string().uuid(),
  segments: z.array(segmentSchema).min(1, { message: 'Segmen wajib diisi' }),
  diagnosis: z.string(),
  unitKm: z.number().nonnegative().optional().nullable(),
  unitHm: z.number().nonnegative().optional().nullable()
})

export type CreateWorkOrderDtoType = z.infer<typeof createWorkOrderSchema>
export type SegmentType = z.infer<typeof segmentSchema>
export type ItemType = z.infer<typeof itemSchema>
