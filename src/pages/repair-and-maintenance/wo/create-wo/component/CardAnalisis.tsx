import { Card, CardContent, FormControl, TextField, Typography } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { CreateWorkOrderDtoType } from '../config/schema'

const CardAnalisis = () => {
  const { control } = useFormContext<CreateWorkOrderDtoType>()
  return (
    <Card>
      <CardContent>
        <Typography variant='h5'>Analisis</Typography>
        <div className='flex mt-4 flex-col'>
          <Controller
            control={control}
            name='diagnosis'
            render={({ field: { value, onChange, ref }, fieldState: { error } }) => (
              <FormControl fullWidth>
                <TextField
                  error={!!error}
                  defaultValue={value}
                  onChange={onChange}
                  ref={ref}
                  label='Diagnosa'
                  multiline
                />
              </FormControl>
            )}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default CardAnalisis
