import { <PERSON><PERSON>, <PERSON>, CardContent, FormHelper<PERSON>ext, Icon<PERSON>utton, Typography } from '@mui/material'
import {
  createColumnHelper,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useWo } from '../../context/WoContext'
import Table from '@/components/table'
import { useEffect, useMemo, useState } from 'react'
import DialogAddSegmentWo from '@/components/dialogs/add-segment-wo'
import AddItemSegmentWo, { AddItemDto } from '@/components/dialogs/add-item-segment-wo'
import AddMiscSegmentWo from '@/components/dialogs/add-item-misc-segment-wo'
import { FormProvider, useFieldArray, useForm, useFormContext } from 'react-hook-form'
import { CreateWorkOrderDtoType, SegmentType, segmentSchema } from '../config/schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQuery } from '@tanstack/react-query'
import RnMQueryMethods from '@/api/services/rnm/query'
import { CodeType } from '@/types/codes'
import { segmentTableColumns } from '../config/table'
import SearchSnDialog, { ItemTypeWithSn } from '@/components/dialogs/search-serial-number'
import { ItemType } from '@/types/companyTypes'

const CardSegment = () => {
  const { setActiveSegment } = useWo()
  const [tempParts, setTempParts] = useState<AddItemDto[]>([])

  const [modalState, setModalState] = useState<{
    segment: boolean
    part: boolean
    item: boolean
    misc: boolean
  }>({
    segment: false,
    part: false,
    item: false,
    misc: false
  })
  const [viewSegment, setViewSegment] = useState<boolean>(false)

  const [jobCodeQuery, setJobCodeQuery] = useState<string>('')
  const [componentCodeQuery, setComponentCodeQuery] = useState<string>('')
  const [modifierCodeQuery, setModifierCodeQuery] = useState<string>('')

  const [selectedItemSn, setSelectedItemSn] = useState<ItemType | null>(null)
  const [editMode, setEditMode] = useState(false)

  const {
    control,
    formState: { errors }
  } = useFormContext<CreateWorkOrderDtoType>()
  const { fields, append } = useFieldArray({ control, name: 'segments' })

  const defaultValues = {
    componentCodeId: null,
    jobCodeId: null,
    items: []
  }

  const method = useForm<SegmentType>({
    defaultValues: {
      ...defaultValues,
      items: tempParts ?? []
    },
    resolver: zodResolver(segmentSchema)
  })

  const {
    control: controlSegment,
    reset: resetSegment,
    setValue: setValueSegment,
    getValues: getValuesSegment
  } = method

  const { data: jobCodeList, isFetching: fetchJobCodeLoading } = useQuery({
    enabled: !!jobCodeQuery,
    queryKey: ['JOB_CODE_QUERY_KEY', jobCodeQuery],
    queryFn: async () => {
      const res = await RnMQueryMethods.getCodeList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        type: 'JOB',
        search: jobCodeQuery
      })
      return res.items
    },
    placeholderData: [] as CodeType[]
  })

  const { data: componentCodeList, isFetching: fetchComponentCodeLoading } = useQuery({
    enabled: !!componentCodeQuery,
    queryKey: ['COMPONENT_CODE_QUERY_KEY', componentCodeQuery],
    queryFn: async () => {
      const res = await RnMQueryMethods.getCodeList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        type: 'COMPONENT',
        search: componentCodeQuery
      })
      return res.items
    },
    placeholderData: [] as CodeType[]
  })

  const { data: modifierCodeList, isFetching: fetchModifierCodeLoading } = useQuery({
    enabled: !!modifierCodeQuery,
    queryKey: ['MODIFIER_CODE_QUERY_KEY', modifierCodeQuery],
    queryFn: async () => {
      const res = await RnMQueryMethods.getCodeList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        type: 'MODIFIER',
        search: modifierCodeQuery
      })
      return res.items
    },
    placeholderData: [] as CodeType[]
  })

  const tableOptions = useMemo(
    () => ({
      data: fields ?? [],
      columns: segmentTableColumns({
        jobCodeList,
        componentCodeList,
        modifierCodeList,
        detail: segment => {
          setModalState(curr => ({ ...curr, segment: true }))
          setActiveSegment(segment as any)
          setViewSegment(true)
          resetSegment({
            jobCodeId: segment.jobCodeId,
            componentCodeId: segment.componentCodeId,
            modifierCodeId: segment.modifierCodeId,
            items: segment.items?.map(item => ({
              itemId: item?.itemId,
              number: item?.number,
              name: item?.name,
              brandName: item?.brandName,
              type: item.type,
              serialNumber: item.serialNumber,
              quantity: item.quantity,
              quantityUnit: item.quantityUnit,
              largeUnitQuantity: item.largeUnitQuantity,
              note: item.note
            }))
          })
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [fields, jobCodeList, componentCodeList, modifierCodeList, method]
  )
  const table = useReactTable<any>(tableOptions)

  const handleOpenDialogItem = (item?: ItemTypeWithSn, sn?: boolean) => {
    if (item) setSelectedItemSn({ ...item, ...(sn && { quantity: 1, quantityUnit: 'PCS' }) })
    setModalState(curr => ({ ...curr, part: false, item: true }))
  }

  const handleOpenDialogMisc = () => {
    setModalState(curr => ({ ...curr, segment: false, misc: true }))
  }

  const handleOpenSegmentDialog = () => {
    resetSegment({
      ...defaultValues,
      items: []
    })
    setModalState(curr => ({ ...curr, segment: true }))
  }

  const handleCloseDialogItem = () => {
    setSelectedItemSn(null)
    setModalState(curr => ({ ...curr, item: false, misc: false, segment: true }))
    setEditMode(false)
  }

  const handleCloseDialogMisc = () => {
    setSelectedItemSn(null)
    setModalState(curr => ({ ...curr, misc: false }))
    setEditMode(false)
  }

  const handleAddParts = (data: AddItemDto) => {
    setValueSegment('items', [...getValuesSegment('items'), data])
    setTempParts(curr => [...curr, data])
    setEditMode(false)
    setSelectedItemSn(null)
    handleCloseDialogItem()
  }

  const handleUpdateParts = (data: AddItemDto) => {
    setValueSegment(
      'items',
      getValuesSegment('items')?.map(item => (item.itemId === data.itemId ? data : item))
    )
    setTempParts(curr => curr?.map(item => (item.itemId === data.itemId ? data : item)))
    setSelectedItemSn(null)
    handleCloseDialogItem()
  }

  const handleEditItem = (item: ItemType) => {
    setEditMode(true)
    if (item?.type === 'COMPONENT') {
      handleOpenDialogItem(item)
    } else {
      setSelectedItemSn(item)
      setModalState(curr => ({ ...curr, misc: true }))
    }
  }

  const handleAddSegment = (segment: SegmentType) => {
    append({
      items: segment.items,
      componentCodeId: segment.componentCodeId,
      jobCodeId: segment.jobCodeId,
      modifierCodeId: segment.modifierCodeId
    })
    setModalState(curr => ({ ...curr, segment: false, item: false, misc: false }))
  }

  useEffect(() => {
    if (!modalState.segment && !modalState.item && !modalState.misc && !modalState.part) {
      resetSegment(defaultValues)
    }
  }, [modalState])

  return (
    <>
      <Card>
        <CardContent className='space-y-4'>
          <div className='w-full flex justify-between items-center'>
            <Typography variant='h5'>Segment</Typography>
            <Button
              variant='outlined'
              size='small'
              onClick={() => {
                handleOpenSegmentDialog()
                setTempParts([])
              }}
            >
              Tambah Segment
            </Button>
          </div>
          <div className='shadow-sm rounded-md'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography> Belum ada Segmen</Typography>
                </td>
              }
            />
          </div>
          {errors?.segments && <FormHelperText error>{errors?.segments?.message}</FormHelperText>}
        </CardContent>
      </Card>
      <FormProvider {...method}>
        {modalState.part && (
          <SearchSnDialog
            onAddItemClick={item => handleOpenDialogItem(item, true)}
            open={modalState.part}
            setOpen={open => setModalState(curr => ({ ...curr, part: open }))}
            onClose={() => {
              setModalState(curr => ({ ...curr, part: false, segment: true }))
            }}
          />
        )}
        {modalState.segment && (
          <DialogAddSegmentWo
            {...{
              configOptions: {
                jobCodeQuery,
                setJobCodeQuery,
                componentCodeQuery,
                setComponentCodeQuery,
                modifierCodeQuery,
                setModifierCodeQuery,
                jobCodeList,
                componentCodeList,
                modifierCodeList,
                fetchJobCodeLoading,
                fetchComponentCodeLoading,
                fetchModifierCodeLoading
              }
            }}
            onEditItem={handleEditItem}
            open={modalState.segment}
            parts={tempParts}
            onOpenDialogItem={() => {
              setModalState(curr => ({ ...curr, part: true, segment: false }))
            }}
            onOpenDialogMisc={handleOpenDialogMisc}
            setOpen={open => {
              setModalState(curr => ({ ...curr, segment: open }))
              if (!open) {
                setViewSegment(false)
                setModalState(curr => ({ ...curr, item: false, misc: false }))
              }
            }}
            onAddSegment={handleAddSegment}
            viewOnly={viewSegment}
          />
        )}
        {modalState.item && (
          <AddItemSegmentWo
            edit={editMode}
            selectedItem={selectedItemSn}
            onSuccessfullAdd={handleAddParts}
            onSuccessfullUpdate={handleUpdateParts}
            open={modalState.item}
            setOpen={open => setModalState(curr => ({ ...curr, item: open }))}
            onClose={handleCloseDialogItem}
          />
        )}
        {modalState.misc && (
          <AddMiscSegmentWo
            edit={editMode}
            selectedItem={selectedItemSn}
            open={modalState.misc}
            setOpen={open => setModalState(curr => ({ ...curr, misc: open, ...(!open && { segment: true }) }))}
            onSuccessfullAdd={handleAddParts}
            onSuccessfullUpdate={handleUpdateParts}
            onClose={handleCloseDialogMisc}
          />
        )}
      </FormProvider>
    </>
  )
}

export default CardSegment
