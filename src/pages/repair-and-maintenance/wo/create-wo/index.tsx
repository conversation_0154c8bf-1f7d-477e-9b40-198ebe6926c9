import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Grid, Typo<PERSON> } from '@mui/material'
import { Link } from 'react-router-dom'
import { useWo } from '../context/WoContext'
import CardAnalisis from './component/CardAnalisis'
import CardSegment from './component/CardSegment'
import UnitDetail from '../fr-detail/component/UnitDetail'
import DataUnitDetail from '../fr-detail/component/DataUnitDetail'
import CreatedByCard from '../fr-detail/component/CreatedByCard'
import CardProblem from '../fr-detail/component/CardProblem'
import CardSchedule from '../fr-detail/component/CardSchedule'
import ActivityLogCard from '../fr-detail/component/ActivityLogCard'
import { useFormContext } from 'react-hook-form'
import { CreateWorkOrderDtoType } from './config/schema'
import LoadingButton from '@mui/lab/LoadingButton'
import Permission from '@/core/components/Permission'
import * as Sentry from '@sentry/react'
import { toast } from 'react-toastify'

const CreateWoPage = () => {
  const { selectedFrId, handleAddWo, frDetail, frLogs, creatingWorkOrder } = useWo()
  const { handleSubmit } = useFormContext<CreateWorkOrderDtoType>()
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>WO</Typography>
          </Link>
          <Link to='/wo/list' replace>
            <Typography color='var(--mui-palette-text-disabled)'>FR Masuk</Typography>
          </Link>
          <Link to={'/wo/list/' + selectedFrId} replace>
            <Typography color='var(--mui-palette-text-disabled)'>Detil FR</Typography>
          </Link>
          <Typography>Buat WO</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>Buat WO</Typography>
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>Buat WO dari FR yang sudah diproses</Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <Permission permission={['work-order.create', 'work-order.update']}>
              <LoadingButton
                loading={creatingWorkOrder}
                startIcon={<></>}
                variant='contained'
                className='is-full sm:is-auto'
                onClick={handleSubmit(handleAddWo, errors => {
                  console.error(errors)
                  Sentry.captureException(errors)
                  Object.entries(errors).forEach(([field, error]) => {
                    toast.error(`${field}: ${error?.message}`, {
                      autoClose: 5000
                    })
                  })
                })}
              >
                Buat WO
              </LoadingButton>
            </Permission>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <CardAnalisis />
      </Grid>
      <Grid item xs={12}>
        <CardSegment />
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <UnitDetail unitData={frDetail?.unit} />
          </Grid>
          <Grid item xs={12}>
            <DataUnitDetail />
          </Grid>
          <Grid item xs={12}>
            <CreatedByCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={6}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <CardProblem />
          </Grid>
          <Grid item xs={12}>
            <CardSchedule />
          </Grid>
          <Grid item xs={12}>
            <ActivityLogCard logList={frLogs} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default CreateWoPage
