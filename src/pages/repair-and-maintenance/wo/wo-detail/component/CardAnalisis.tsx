import { Card, CardContent, Typography } from '@mui/material'
import { useWo } from '../../context/WoContext'

const CardAnalisis = () => {
  const { woDetail } = useWo()
  return (
    <Card>
      <CardContent>
        <Typography variant='h5'>Analisis</Typography>
        <div className='flex mt-4 flex-col gap-2'>
          <Typography variant='caption'>Diagnosa</Typography>
          <p>{woDetail?.diagnosis ?? '-'}</p>
        </div>
      </CardContent>
    </Card>
  )
}

export default CardAnalisis
