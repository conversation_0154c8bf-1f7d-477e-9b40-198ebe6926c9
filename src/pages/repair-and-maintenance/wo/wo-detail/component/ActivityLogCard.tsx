// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import TimelineDot from '@mui/lab/TimelineDot'
import TimelineItem from '@mui/lab/TimelineItem'
import TimelineContent from '@mui/lab/TimelineContent'
import TimelineSeparator from '@mui/lab/TimelineSeparator'
import TimelineConnector from '@mui/lab/TimelineConnector'
import Typography from '@mui/material/Typography'

import { Timeline } from '@/components/Timeline'
import { PrLogType, LogTypeStatus } from '@/types/prTypes'
import { Avatar } from '@mui/material'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { WoLogType, WorkOrderLogStatus } from '@/types/woTypes'

type Props = {
  logList?: WoLogType[]
}

const ActivityLogCard = ({ logList = [] }: Props) => {
  return (
    <Card>
      <CardHeader title='Log Aktivitas' />
      <CardContent>
        <Timeline>
          {logList?.map(log => {
            let dotColor: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'inherit' | 'grey' =
              'primary'
            let title = ''
            const changes = ((JSON.parse(log.changes) as string[]) ?? []).map(change =>
              change.replaceAll('"', '').replaceAll('{changed}', '→').replaceAll('{added}', '')
            )
            switch (log.status) {
              case WorkOrderLogStatus.PRE_RELEASED:
                title = 'WO Dibuat Pre-Release'
                dotColor = 'info'
                break
              case WorkOrderLogStatus.CREATED:
                title = 'WO Dibuat'
                break
              case WorkOrderLogStatus.SEGMENT_ITEM_ADDED:
              case WorkOrderLogStatus.SEGMENT_ITEM_UPDATED:
              case WorkOrderLogStatus.UPDATED:
                title = 'Detil Wo Diperbarui'
                break
              case WorkOrderLogStatus.PRE_RELEASE_REJECTED:
                title = 'Pre-Release WO Ditolak'
                dotColor = 'error'
                break
              case WorkOrderLogStatus.READY_TO_RELEASE:
                title = 'WO Unit Siap Release'
                dotColor = 'warning'
                break
              case WorkOrderLogStatus.SEGMENT_ADDED:
                title = 'WO Segment Ditambahkan'
                dotColor = 'info'
                break
              case WorkOrderLogStatus.SEGMENT_UPDATED:
                title = 'WO Segment Diperbarui'
                dotColor = 'info'
                break
              case WorkOrderLogStatus.SEGMENT_ADDED:
                title = 'WO Segment Ditambahkan'
                dotColor = 'info'
                break
              case WorkOrderLogStatus.SEGMENT_COMPLETED:
                title = 'WO Segment Selesai'
                dotColor = 'success'
                break
              case WorkOrderLogStatus.SEGMENT_PROCESSED:
                title = 'WO Segment Diproses'
                dotColor = 'warning'
                break
              case WorkOrderLogStatus.COMPLETED:
                title = 'WO Selesai'
                dotColor = 'success'
                break
              default:
                break
            }
            return title ? (
              <TimelineItem key={log.id} className='pt-2'>
                <TimelineSeparator>
                  <TimelineDot color={dotColor} />
                  <TimelineConnector />
                </TimelineSeparator>
                <TimelineContent>
                  <div className='flex flex-wrap items-center justify-between gap-x-2 mbe-1'>
                    <Typography color='text.primary' className='font-medium text-base'>
                      {title}
                    </Typography>
                    <Typography variant='caption'>
                      {formatDistanceToNow(log.createdAt, {
                        locale: id,
                        addSuffix: true
                      })
                        .replace('sekitar ', '')
                        .replace('kurang dari ', '')}
                    </Typography>
                  </div>
                  {changes?.map(change => (
                    <Typography key={change} className='mbe-2 text-sm'>
                      {change}
                    </Typography>
                  ))}
                  {log.user ? (
                    <div className='flex items-center gap-3'>
                      <Avatar />
                      <div className='flex flex-col'>
                        <Typography color='text.primary' className='font-medium'>
                          {log.user?.fullName}
                        </Typography>
                        <Typography variant='body2'>{log.user?.title}</Typography>
                      </div>
                    </div>
                  ) : null}
                </TimelineContent>
              </TimelineItem>
            ) : null
          })}
        </Timeline>
      </CardContent>
    </Card>
  )
}

export default ActivityLogCard
