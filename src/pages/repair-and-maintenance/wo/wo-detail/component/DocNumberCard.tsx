import { Card, CardContent, Typography } from '@mui/material'

type DocNumberCardProps = {
  docType: 'FR' | 'WO' | 'SCHEDULE'
}

const DocNumberCard = ({ docType }: DocNumberCardProps) => {
  const items = []
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>{{ FR: 'FR Terbuat', WO: 'WO Terbuat', SCHEDULE: 'Schedule' }[docType]}</Typography>
        </div>
        {items.length > 0 ? (
          <></>
        ) : (
          <div className='flex flex-col items-center justify-center p-6'>
            <Typography variant='h5'>Belum ada {{ WO: 'WO', FR: 'FR', SCHEDULE: 'Jadwal' }[docType]}</Typography>
            <Typography variant='caption'>
              {
                {
                  WO: 'WO terbuat untuk unit ini akan ditampilkan di sini',
                  FR: 'FR terbuat untuk unit ini akan ditampilkan di sini',
                  SCHEDULE: '<PERSON>adwal perawatan untuk unit ini akan ditampilkan di sini'
                }[docType]
              }
            </Typography>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default DocNumberCard
