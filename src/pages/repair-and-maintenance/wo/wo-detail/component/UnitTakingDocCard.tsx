import ChevronRight from '@/components/menu/svg/ChevronRight'
import { WorkOrderType } from '@/types/woTypes'
import { Button, Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type DocNumberCardProps = {
  workOrder: WorkOrderType
}

const UnitTakingDocCard = ({ workOrder }: DocNumberCardProps) => {
  return (
    <Card>
      <CardContent>
        <Typography variant='h5'>Pengambilan Unit</Typography>
        <div className='flex mt-4 flex-col gap-2'>
          <div className='grid grid-cols-1 md:grid-cols-2'>
            <div className='flex flex-col gap-2'>
              <small>Di<PERSON><PERSON></small>
              <Typography>
                {workOrder?.takenAt
                  ? formatDate(new Date(workOrder?.takenAt), 'dd/MM/yyyy HH:mm', { locale: id })
                  : '-'}
              </Typography>
            </div>
            <div className='flex flex-col gap-2'>
              <small>Di<PERSON><PERSON></small>
              <Typography>{workOrder?.takenBy ?? '-'}</Typography>
            </div>
          </div>
          <Button
            className='is-fit ml-0 pl-0'
            endIcon={<ChevronRight />}
            color='primary'
            variant='text'
            target='_blank'
            href={workOrder?.takenImageUrl}
            download
          >
            Lihat Foto Bukti Pengambilan
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default UnitTakingDocCard
