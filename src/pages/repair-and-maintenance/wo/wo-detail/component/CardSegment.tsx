import { <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useWo, WoDocumentType } from '../../context/WoContext'
import Table from '@/components/table'
import { useEffect, useMemo, useState } from 'react'

import AddItemSegmentWo, { AddItemDto } from '@/components/dialogs/add-item-segment-wo'
import AddMiscSegmentWo from '@/components/dialogs/add-item-misc-segment-wo'
import DetailDocumentComponentDialog from '@/components/dialogs/detail-document-rnm'
import DetailDocumentCreateDialog from '@/components/dialogs/detail-document-create-rnm'
import DialogAddSegmentWo from '@/components/dialogs/add-segment-wo'
import { tableColumns } from '../config/table'
import { WoSegmentType, WoStatus } from '@/types/woTypes'
import RnMQueryMethods from '@/api/services/rnm/query'
import { useQuery } from '@tanstack/react-query'
import { CodeType } from '@/types/codes'
import { FormProvider, useForm } from 'react-hook-form'
import { segmentSchema, SegmentType } from '../../create-wo/config/schema'
import { zodResolver } from '@hookform/resolvers/zod'
import SearchSnDialog, { ItemTypeWithSn } from '@/components/dialogs/search-serial-number'
import { ItemType } from '@/types/companyTypes'
import { useAddWoSegment } from '@/api/services/rnm/mutation'
import { WO_SEGMENT_KEY } from '@/api/services/rnm/service'

const CardSegment = () => {
  const { woSegments, activeSegment, setActiveSegment, woDetail, handleSubmitSegment, loadingAddSegment } = useWo()

  const [dialog, setDialog] = useState<{
    document: boolean
    segment: boolean
    misc: boolean
    part: boolean
    item: boolean
  }>({
    document: false,
    segment: false,
    misc: false,
    item: false,
    part: false
  })
  const [openCreateDoc, setOpenCreateDoc] = useState(false)
  const [jobCodeQuery, setJobCodeQuery] = useState<string>('')
  const [componentCodeQuery, setComponentCodeQuery] = useState<string>('')
  const [modifierCodeQuery, setModifierCodeQuery] = useState<string>('')

  const [selectedItemSn, setSelectedItemSn] = useState<ItemType | null>(null)

  const defaultValues = {
    componentCodeId: null,
    jobCodeId: null,
    items: []
  }

  const method = useForm<SegmentType>({
    defaultValues: {
      ...defaultValues,
      items: []
    },
    resolver: zodResolver(segmentSchema)
  })

  const { data: jobCodeList, isFetching: fetchJobCodeLoading } = useQuery({
    enabled: !!jobCodeQuery,
    queryKey: ['JOB_CODE_QUERY_KEY', jobCodeQuery],
    queryFn: async () => {
      const res = await RnMQueryMethods.getCodeList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        type: 'JOB',
        search: jobCodeQuery
      })
      return res.items
    },
    placeholderData: [] as CodeType[]
  })

  const { data: componentCodeList, isFetching: fetchComponentCodeLoading } = useQuery({
    enabled: !!componentCodeQuery,
    queryKey: ['COMPONENT_CODE_QUERY_KEY', componentCodeQuery],
    queryFn: async () => {
      const res = await RnMQueryMethods.getCodeList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        type: 'COMPONENT',
        search: componentCodeQuery
      })
      return res.items
    },
    placeholderData: [] as CodeType[]
  })

  const { data: modifierCodeList, isFetching: fetchModifierCodeLoading } = useQuery({
    enabled: !!modifierCodeQuery,
    queryKey: ['MODIFIER_CODE_QUERY_KEY', modifierCodeQuery],
    queryFn: async () => {
      const res = await RnMQueryMethods.getCodeList({
        page: 1,
        limit: Number.MAX_SAFE_INTEGER,
        type: 'MODIFIER',
        search: modifierCodeQuery
      })
      return res.items
    },
    placeholderData: [] as CodeType[]
  })

  const tableOptions = useMemo(
    () => ({
      data: woSegments ?? [],
      columns: tableColumns({
        detail: item => {
          setDialog(curr => ({ ...curr, segment: true }))
          setActiveSegment(item)
          method.reset({
            jobCodeId: item.jobCodeId,
            componentCodeId: item.componentCodeId,
            modifierCodeId: item.modifierCodeId
          })
        },
        documentClick: item => {
          setDialog(curr => ({ ...curr, document: true }))
          setActiveSegment(item)
        }
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [woSegments]
  )

  const table = useReactTable<any>(tableOptions)

  const handleAddSegment = () => {
    setDialog(curr => ({ ...curr, segment: true }))
    setActiveSegment(null)
    method.reset({
      ...defaultValues,
      items: []
    })
  }

  const handleOpenDialogItem = (item?: ItemTypeWithSn, sn?: boolean) => {
    if (item) setSelectedItemSn({ ...item, ...(sn && { quantity: 1, quantityUnit: 'PCS' }) })
    setDialog(curr => ({ ...curr, part: false, item: true }))
  }

  const handleOpenDialogMisc = () => {
    setDialog(curr => ({ ...curr, segment: false, misc: true }))
  }

  const handleCloseDialogItem = () => {
    setSelectedItemSn(null)
    setDialog(curr => ({ ...curr, item: false, misc: false, segment: true }))
  }

  const handleCloseDialogMisc = () => {
    setSelectedItemSn(null)
    setDialog(curr => ({ ...curr, misc: false }))
  }

  const handleAddParts = (data: AddItemDto) => {
    method.setValue('items', [...method.getValues('items'), data])
    setSelectedItemSn(null)
    handleCloseDialogItem()
  }

  const handleUpdateParts = (data: AddItemDto) => {
    method.setValue(
      'items',
      method.getValues('items')?.map(item => (item.itemId === data.itemId ? data : item))
    )
    setSelectedItemSn(null)
    handleCloseDialogItem()
  }

  return (
    <>
      <Card>
        <CardContent className='space-y-4'>
          <div className='flex items-center justify-between'>
            <Typography variant='h5'>Segment</Typography>
            {woDetail?.status === WoStatus.ACTIVE && (
              <Button onClick={handleAddSegment} disabled={loadingAddSegment} variant='outlined' size='small'>
                Tambah Segmen
              </Button>
            )}
          </div>
          <div className='shadow-sm rounded-md'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography> Belum ada Segmen</Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {activeSegment && dialog.document && !openCreateDoc && (
        <DetailDocumentComponentDialog
          segment={activeSegment}
          open={!!activeSegment}
          setOpen={setActiveSegment}
          handleClose={() => {
            setActiveSegment(null)
            setDialog(curr => ({ ...curr, document: false }))
          }}
          onCreateDocument={() => setOpenCreateDoc(true)}
        />
      )}
      <FormProvider {...method}>
        {dialog.segment && !openCreateDoc && (
          <DialogAddSegmentWo
            viewOnly={!!activeSegment}
            open={dialog.segment}
            setOpen={open => setDialog(curr => ({ ...curr, segment: open }))}
            onOpenDialogItem={() => {
              setDialog(curr => ({ ...curr, part: true, segment: false }))
            }}
            onOpenDialogMisc={handleOpenDialogMisc}
            configOptions={{
              segment: activeSegment,
              jobCodeQuery,
              setJobCodeQuery,
              componentCodeQuery,
              setComponentCodeQuery,
              modifierCodeQuery,
              setModifierCodeQuery,
              jobCodeList,
              componentCodeList,
              modifierCodeList,
              fetchJobCodeLoading,
              fetchComponentCodeLoading,
              fetchModifierCodeLoading
            }}
            loading={loadingAddSegment}
            onAddSegment={dto => handleSubmitSegment(dto, () => setDialog(curr => ({ ...curr, segment: false })))}
          />
        )}
        {dialog.part && (
          <SearchSnDialog
            onAddItemClick={item => handleOpenDialogItem(item, true)}
            open={dialog.part}
            setOpen={open => setDialog(curr => ({ ...curr, part: open }))}
            onClose={() => {
              setDialog(curr => ({ ...curr, part: false, segment: true }))
            }}
          />
        )}
        {dialog.item && (
          <AddItemSegmentWo
            selectedItem={selectedItemSn}
            onSuccessfullAdd={handleAddParts}
            onSuccessfullUpdate={handleUpdateParts}
            open={dialog.item}
            setOpen={open => setDialog(curr => ({ ...curr, item: open }))}
            onClose={handleCloseDialogItem}
          />
        )}
        {dialog.misc && (
          <AddMiscSegmentWo
            selectedItem={selectedItemSn}
            open={dialog.misc}
            setOpen={open => setDialog(curr => ({ ...curr, misc: open, ...(!open && { segment: true }) }))}
            onSuccessfullAdd={handleAddParts}
            onSuccessfullUpdate={handleUpdateParts}
            onClose={handleCloseDialogMisc}
          />
        )}
      </FormProvider>

      {openCreateDoc && (
        <DetailDocumentCreateDialog
          segment={activeSegment}
          open={openCreateDoc}
          setOpen={setOpenCreateDoc}
          handleClose={() => setOpenCreateDoc(false)}
        />
      )}
    </>
  )
}

export default CardSegment
