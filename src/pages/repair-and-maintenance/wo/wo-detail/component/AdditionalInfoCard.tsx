// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'
import { WpType } from '@/types/wpTypes'
import { WorkOrderType } from '@/types/woTypes'

const AdditionalInfoCard = ({ woData }: { woData: WorkOrderType }) => {
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Lainnya</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small>Departemen</small>
          <Typography>{woData?.department?.name ?? '-'}</Typography>
        </div>
        <div className='flex flex-col gap-2'>
          <small>Lokasi Workshop</small>
          <Typography>{woData?.site?.name ?? '-'}</Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
