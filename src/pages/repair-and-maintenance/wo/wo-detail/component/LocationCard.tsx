import { Card, CardContent, Typography } from '@mui/material'
import { FrType } from '@/types/frTypes'
import { FR_DETAIL_QUERY_KEY } from '@/api/services/rnm/service'
import { useQuery } from '@tanstack/react-query'
import RnMQueryMethods from '@/api/services/rnm/query'
import { WorkOrderType } from '@/types/woTypes'

const CardLocation = ({ frData, woData }: { frData: FrType; woData?: WorkOrderType }) => {
  const { data: fieldReportData } = useQuery({
    enabled: !!frData?.id,
    queryKey: [FR_DETAIL_QUERY_KEY, frData?.id],
    queryFn: () => RnMQueryMethods.getFrDetail(frData?.id)
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Lokasi</Typography>
        </div>
        <div className='flex flex-col gap-6'>
          <div className='grid grid-cols-1 md:grid-cols-2'>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                  Lokasi Asal Unit
                </small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className=''>{fieldReportData?.site?.name ?? '-'}</p>
            </div>
            <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
              <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
                <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>Pit</small>
              </label>
              <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
              <p className=''>{fieldReportData?.locationPit ? `Pit ${fieldReportData?.locationPit}` : '-'}</p>
            </div>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Detil Lokasi
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className=''>{fieldReportData?.locationNote ?? '-'}</p>
          </div>

          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Diserahkan ke Workshop
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className=''>{fieldReportData?.destinationSite?.name ?? '-'}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CardLocation
