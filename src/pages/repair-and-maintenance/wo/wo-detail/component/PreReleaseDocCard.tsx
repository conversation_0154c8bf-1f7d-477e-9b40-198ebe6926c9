import ChevronRight from '@/components/menu/svg/ChevronRight'
import { Card, CardContent, Typography } from '@mui/material'

type DocNumberCardProps = {
  onClickDoc?: () => void
}

const PreReleaseDocCard = ({ onClickDoc }: DocNumberCardProps) => {
  return (
    <Card>
      <CardContent>
        <Typography variant='h5'>Dokumen Pre-Release</Typography>
        <div className='flex mt-4 flex-col gap-2'>
          <Typography
            color='primary'
            role='button'
            sx={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}
            onClick={onClickDoc}
          >
            Lihat Dokumen Pre-Release <ChevronRight />
          </Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default PreReleaseDocCard
