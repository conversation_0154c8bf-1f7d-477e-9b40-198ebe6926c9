import { createColumnHelper } from '@tanstack/react-table'
import { Chip, IconButton, Typography } from '@mui/material'
import { WoSegmentStatus, WoSegmentType } from '@/types/woTypes'
import { getSegmentStatusConfig } from './utils'
import truncateString from '@/core/utils/truncate'
import { classNames } from '@/utils/helper'

type RowActionType = {
  detail: (item: WoSegmentType) => void
  documentClick: (item: WoSegmentType) => void
}

const columnHelper = createColumnHelper<WoSegmentType>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('id', {
    header: 'NO',
    cell: ({ row }) => row.index + 1
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => {
      return (
        <Chip
          label={getSegmentStatusConfig(row.original.status).label}
          color={getSegmentStatusConfig(row.original.status).color as any}
          variant='tonal'
          size='small'
        />
      )
    }
  }),
  columnHelper.accessor('jobCode.code', {
    header: 'JOB CODE',
    cell: ({ row }) =>
      row.original?.jobCode?.code
        ? `${row.original.jobCode.code} | ${truncateString(row.original.jobCode.description, 12)}`
        : '-'
  }),
  columnHelper.accessor('componentCode.code', {
    header: 'SMCS',
    cell: ({ row }) =>
      row.original?.componentCode?.code
        ? `${row.original.componentCode.code} | ${truncateString(row.original.componentCode.description, 12)}`
        : '-'
  }),
  columnHelper.accessor('modifierCode.code', {
    header: 'MODIFIER',
    cell: ({ row }) =>
      row.original?.modifierCode?.code
        ? `${row.original.modifierCode?.code} | ${truncateString(row.original.modifierCode?.description, 12)}`
        : '-'
  }),
  columnHelper.accessor('componentsCount', {
    header: 'PARTS',
    cell: ({ row }) => (row.original?.componentsCount ? `${row.original?.componentsCount} Part` : '-')
  }),
  columnHelper.accessor('miscellaneousCount', {
    header: 'MISCELLANEOUS',
    cell: ({ row }) => (row.original?.miscellaneousCount ? `${row.original?.miscellaneousCount} Misc` : '-')
  }),
  columnHelper.accessor('createdDocumentsCount', {
    header: 'DOKUMEN TERBUAT',
    cell: ({ row }) => {
      return (
        <div
          role='button'
          onClick={() => rowAction.documentClick(row.original)}
          className={classNames(
            'flex gap-2 items-center justify-center cursor-pointer border border-primary rounded-md px-2 py-1 select-none',
            row.original?.createdDocumentsCount > 0
              ? 'bg-white text-primary'
              : row.original.status === WoSegmentStatus.COMPLETED
                ? 'bg-white border-textDisabled'
                : 'bg-primary text-white'
          )}
        >
          {row.original?.createdDocumentsCount > 0 ? (
            <Typography variant='caption' color='primary'>
              Lihat Dokumen
            </Typography>
          ) : (
            <Typography
              variant='caption'
              color={row.original.status === WoSegmentStatus.COMPLETED ? 'textDisabled' : 'white'}
            >
              {row.original.status === WoSegmentStatus.COMPLETED ? 'Tanpa Dokumen' : 'Buat Dokumen'}
            </Typography>
          )}
        </div>
      )
    }
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
