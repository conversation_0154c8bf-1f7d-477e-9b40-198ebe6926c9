import { WoStatus } from '@/types/woTypes'

export const getStatusConfig = (status: string): { color: string; label: string } => {
  switch (status) {
    case WoStatus.ACTIVE:
      return {
        color: 'info',
        label: 'Aktif'
      }
    case WoStatus.COMPLETED:
      return {
        color: 'success',
        label: 'Sudah Diambil'
      }
    case WoStatus.PRE_RELEASED:
      return {
        color: 'warning',
        label: 'Pre Release diajukan'
      }
    case WoStatus.READY_TO_RELEASE:
      return {
        color: 'warning',
        label: 'Siap Diambil'
      }
    default:
      return {
        color: 'default',
        label: status
      }
  }
}
