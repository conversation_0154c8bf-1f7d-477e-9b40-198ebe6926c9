import { createColumnHelper } from '@tanstack/react-table'
import { WoType } from '../../context/WoContext'
import { Chip, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { WorkOrderType } from '@/types/woTypes'
import truncateString from '@/core/utils/truncate'
import { getStatusConfig } from './utils'
import { srPriorityOptions } from '@/pages/service-requisition/list/config/utils'
import { ServiceRequisitionPriority } from '@/types/serviceRequisitionsTypes'

const columnHelper = createColumnHelper<WorkOrderType>()

type RowActionType = {
  detail: (item: WorkOrderType) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'NO. WO',
    cell: ({ row }) => (
      <Typography
        role='button'
        onClick={() => rowAction.detail(row.original)}
        sx={{ cursor: 'pointer' }}
        color='primary'
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => (
      <Chip
        variant='tonal'
        size='small'
        color={getStatusConfig(row.original.status).color as any}
        label={getStatusConfig(row.original.status).label}
      />
    )
  }),
  columnHelper.accessor('unit.number', {
    header: 'KODE UNIT'
  }),
  columnHelper.accessor('fieldReport.scheduleDate', {
    header: 'TANGGAL SCHEDULE',
    cell: ({ row }) =>
      row.original.fieldReport.scheduleDate
        ? formatDate(new Date(row.original.fieldReport.scheduleDate), 'dd/MM/yyyy', { locale: id })
        : '-'
  }),
  columnHelper.accessor('site.name', {
    header: 'Workshop'
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = srPriorityOptions.find(
        option => option.value === String(row.original.priority ?? ServiceRequisitionPriority.P4)
      )
      return priority ? (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      ) : (
        '-'
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) => formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id })
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
