export const getStatusConfig = (status: string): { color: string; label: string } => {
  switch (status) {
    case 'PROCESSED':
      return {
        color: 'success',
        label: 'Diproses'
      }
    case 'PENDING':
      return {
        color: 'warning',
        label: 'Menunggu'
      }
    case 'CREATED':
      return {
        color: 'default',
        label: 'Dibuat'
      }
    case 'COMPLETED':
      return {
        color: 'success',
        label: '<PERSON>les<PERSON>'
      }
    case 'APPROVED':
      return {
        color: 'success',
        label: 'Disetujui'
      }
    case 'REJECTED':
      return {
        color: 'error',
        label: '<PERSON>tolak'
      }
    case 'CANCELED':
      return {
        color: 'default',
        label: '<PERSON><PERSON>alkan'
      }
    case 'CLOSED':
      return {
        color: 'success',
        label: '<PERSON><PERSON><PERSON>'
      }
    default:
      return {
        color: 'default',
        label: status
      }
  }
}

export enum SrStatus {
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED'
}
