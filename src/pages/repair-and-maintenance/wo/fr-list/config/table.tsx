import { Chip, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { FrType } from '@/types/frTypes'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { getStatusConfig } from './utils'
import truncateString from '@/core/utils/truncate'
import { srPriorityOptions } from '@/pages/service-requisition/list/config/utils'
import { ServiceRequisitionPriority } from '@/types/serviceRequisitionsTypes'

const columnHelper = createColumnHelper<FrType>()

type RowActionType = {
  detail: (fr: FrType) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'NO. FR',
    cell: ({ row }) => (
      <Typography
        color='primary'
        role='button'
        onClick={() => rowAction.detail(row.original)}
        sx={{ cursor: 'pointer' }}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => (
      <Chip
        size='small'
        label={getStatusConfig(row.original.status)?.label}
        variant='tonal'
        color={getStatusConfig(row.original.status)?.color as any}
      />
    )
  }),
  columnHelper.accessor('unit.number', {
    header: 'KODE UNIT'
  }),
  columnHelper.accessor('scheduleDate', {
    header: 'TANGGAL SCHEDULE',
    cell: ({ row }) =>
      row.original.scheduleDate ? formatDate(new Date(row.original.scheduleDate), 'dd/MM/yyyy', { locale: id }) : '-'
  }),
  columnHelper.accessor('destinationSite.name', {
    header: 'WORKSHOP'
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = srPriorityOptions.find(
        option => option.value === String(row.original.priority ?? ServiceRequisitionPriority.P4)
      )
      return priority ? (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      ) : (
        '-'
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) => formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id })
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      )
    }
  })
]
