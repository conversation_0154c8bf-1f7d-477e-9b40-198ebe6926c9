import DebouncedInput from '@/components/DebounceInput'
import { <PERSON><PERSON>, <PERSON>, IconButton, Typography } from '@mui/material'
import { useMemo, useState } from 'react'
import { useWo } from '../../context/WoContext'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'

const FrList = () => {
  const {
    setSelectedFrId,
    navigate,
    frListResponse: { items, totalItems, totalPages },
    isMobile,
    frParams: { search, page },
    setFrParams
  } = useWo()
  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [actionBtn, setBtn] = useState<boolean>(false)
  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns({
        detail: fr => {
          setSelectedFrId(fr.id)
          navigate(`list/${fr.id}`)
        }
      }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: page - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items, totalItems, totalPages, page]
  )

  const table = useReactTable<any>(tableOptions)

  const actionButton = (
    <></>
    // <Button
    //   color='secondary'
    //   variant='outlined'
    //   startIcon={<i className='ri-upload-2-line' />}
    //   className='is-full sm:is-auto'
    // >
    //   Ekspor
    // </Button>
  )

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
        {searchExtend ? (
          <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
            <DebouncedInput
              value={search}
              onChange={value => setFrParams(prev => ({ ...prev, page: 1, search: value as string }))}
              onBlur={() => setSearchExtend(false)}
              placeholder='Cari'
              className='is-full'
            />
          </div>
        ) : !isMobile ? (
          <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
            <DebouncedInput
              value={search}
              onChange={value => setFrParams(prev => ({ ...prev, page: 1, search: value as string }))}
              placeholder='Cari'
              className='is-full sm:is-auto'
            />
          </div>
        ) : (
          <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
            <i className='ri-search-line' />
          </IconButton>
        )}
        {!searchExtend && (
          <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
            {!isMobile ? (
              actionButton
            ) : (
              <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                <i className='pepicons-pop--dots-y' />
              </IconButton>
            )}
          </div>
        )}
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography> Belum ada Data</Typography>
          </td>
        }
      />
    </Card>
  )
}

export default FrList
