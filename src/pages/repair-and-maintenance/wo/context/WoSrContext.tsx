import { useAddWoSr } from '@/api/services/rnm/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { SrRmOPayload } from '@/types/srTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import { createContext, useContext } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { useNavigate } from 'react-router-dom'
import { toast } from 'react-toastify'
import * as z from 'zod'
import * as Sentry from '@sentry/react'

type SrContextProps = {
  handleSubmitServiceRequest: (dto?: SrRmOPayload) => void
  creatingSr: boolean
}

const WoSrContext = createContext<SrContextProps>({} as SrContextProps)

export const useWoSr = () => {
  const context = useContext(WoSrContext)
  if (context === undefined) {
    throw new Error('useWoSr must be used within a WoSrContextProvider')
  }
  return context
}

const CreateWoSrSchema = z
  .object({
    type: z.enum(['INTERNAL', 'EXTERNAL', 'VENDOR']),
    workOrderSegmentId: z.string(),
    items: z.array(
      z.object({
        itemId: z.string(),
        quantity: z.number(),
        quantityUnit: z.string(),
        largeUnitQuantity: z.number(),
        serialNumberId: z.number().optional().nullable(),
        note: z.string(),
        images: z.array(
          z.object({
            uploadId: z.string()
          })
        )
      })
    ),
    approvals: z.array(
      z.object({
        userId: z.string()
      })
    ),
    note: z.string(),
    priority: z.number(),
    documentNumber: z.string(),
    documentUploadId: z.string(),
    documentNote: z.string(),
    vendorId: z.string().optional().nullable(),
    originSiteId: z.string().optional().nullable(),
    siteId: z.string().optional().nullable()
  })
  .superRefine((values, ctx) => {
    if (values.type !== 'INTERNAL') {
      if (values.vendorId === undefined || values.vendorId === null || values.vendorId === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'vendorId harus diisi',
          path: ['vendorId', 'originSiteId']
        })
      }
    }
    if (values.type === 'VENDOR') {
      if (values.originSiteId === undefined || values.originSiteId === null || values.originSiteId === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'originSiteId harus diisi',
          path: ['originSiteId']
        })
      }
    }
  })

export const WoSrProvider = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate()
  const { setConfirmState } = useMenu()
  const methods = useForm<SrRmOPayload>({
    defaultValues: {
      approvals: [],
      items: []
    },
    resolver: zodResolver(CreateWoSrSchema),
    mode: 'all'
  })

  const { mutate: createSrMutate, isLoading: creatingSr } = useAddWoSr()

  const handleSubmitServiceRequest = (dto?: SrRmOPayload) => {
    Sentry.captureMessage(`Submit Service Request: ${JSON.stringify(dto)}`)
    setConfirmState({
      open: true,
      title: 'Buat Service Request',
      content:
        'Apakah kamu yakin akan membuat Service Request ini? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Buat Service Request',
      onConfirm: () => {
        createSrMutate(dto, {
          onSuccess: () => {
            toast.success('Service Request berhasil dibuat')
            setTimeout(() => {
              navigate(`/service-request/list`)
            }, 700)
          }
        })
      }
    })
  }

  const value = {
    handleSubmitServiceRequest,
    creatingSr
  }

  return (
    <WoSrContext.Provider value={value}>
      <FormProvider {...methods}>{children}</FormProvider>
    </WoSrContext.Provider>
  )
}
