import { createContext, ReactNode, useContext } from 'react'
import usePartialState from '@/core/hooks/usePartialState'
import { MrPayload } from '@/types/mrTypes'

export type MrPayloadExtend = {
  priority?: string
  workOrderSegmentId: string
  forSiteId?: string
} & MrPayload

type CreateWoMrContextProps = {
  payload: MrPayloadExtend
  setPartialPayload: (fieldName: keyof MrPayloadExtend, value: any) => void
  setPayload: React.Dispatch<React.SetStateAction<MrPayloadExtend>>
}

const CreateWoMrContext = createContext<CreateWoMrContextProps>({} as CreateWoMrContextProps)

export const useWoMr = () => {
  const context = useContext(CreateWoMrContext)
  if (context) {
    return context
  }
  throw new Error('useWoMr must be used within a CreateWoMrContextProvider')
}

export const WoMrProvider = ({ children }: { children: ReactNode }) => {
  const [payload, setPartialPayload, setPayload] = usePartialState<MrPayloadExtend>({} as MrPayloadExtend)
  const value = {
    payload,
    setPartialPayload,
    setPayload
  }
  return <CreateWoMrContext.Provider value={value}>{children}</CreateWoMrContext.Provider>
}
