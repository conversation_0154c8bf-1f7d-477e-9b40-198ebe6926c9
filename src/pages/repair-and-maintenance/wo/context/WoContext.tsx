import { defaultListData } from '@/api/queryClient'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { ListParams } from '@/types/payload'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters, useQuery } from '@tanstack/react-query'
import React, { useEffect, useMemo, useState } from 'react'
import { NavigateFunction, useLocation, useNavigate, useParams } from 'react-router-dom'
import { FieldReportStatus, FrLogType, FrParams, FrType } from '@/types/frTypes'
import { useMenu } from '@/components/menu/contexts/menuContext'
import RnMQueryMethods from '@/api/services/rnm/query'
import { FormProvider, useForm } from 'react-hook-form'
import { CreateWorkOrderDtoType, createWorkOrderSchema, SegmentType } from '../create-wo/config/schema'
import { zodResolver } from '@hookform/resolvers/zod'
import { useAddWoSegment, useCloseFieldReport, useCreateWorkOrder } from '@/api/services/rnm/mutation'
import { toast } from 'react-toastify'
import { WoLogType, WoParams, WorkOrderType, WoSegmentType, WoStatus } from '@/types/woTypes'
import { useAddMr } from '@/api/services/mr/mutation'
import { MrPayloadExtend } from './WoMrContext'
import {
  FR_DETAIL_LOGS_KEY,
  FR_DETAIL_QUERY_KEY,
  FR_LIST_QUERY_KEY,
  WO_LOGS_QUERY_KEY,
  WO_QUERY_DETAIL_KEY,
  WO_QUERY_LIST_KEY,
  WO_SEGMENTS_QUERY_KEY
} from '@/api/services/rnm/service'
import * as Sentry from '@sentry/react'
import { useAuth } from '@/contexts/AuthContext'
import CloseFRDialog, { CloseFrInput } from '@/components/dialogs/close-field-report-dialog'

interface WoContextProps {
  isMobile: boolean
  woParams: ListParams
  setPartialWoParams: (fieldName: keyof WoParams, value: any) => void
  setWoParams: React.Dispatch<React.SetStateAction<WoParams>>
  frParams: ListParams
  setPartialFrParams: (fieldName: keyof ListParams, value: any) => void
  setFrParams: React.Dispatch<React.SetStateAction<ListParams>>
  woList: ListResponse<WorkOrderType>
  woDetail: WorkOrderType
  woSegments: WoSegmentType[]
  activeSegment: WoSegmentType
  setActiveSegment: React.Dispatch<React.SetStateAction<WoSegmentType>>
  woLogs: WoLogType[]
  frListResponse: ListResponse<FrType>
  frDetail: FrType
  frLogs: FrLogType[]
  navigate: NavigateFunction
  selectedWoId: string
  setSelectedWoId: React.Dispatch<React.SetStateAction<string | undefined>>
  handleProccessFr: () => void
  handleCloseFieldReport: () => void
  handleAddWo: (data: CreateWorkOrderDtoType) => void
  handleSubmitMr: (dto: MrPayloadExtend) => void
  handleSubmitSegment: (dto: SegmentType, cb?: () => void) => void
  selectedFrId: string
  setSelectedFrId: React.Dispatch<React.SetStateAction<string>>
  creatingWorkOrder: boolean
  creatingMr: boolean
  loadingAddSegment: boolean
  refetchWoList: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<ListResponse<WorkOrderType>, unknown>>
  refetchWoDetail: <TPageData>(
    options?: RefetchOptions & RefetchQueryFilters<TPageData>
  ) => Promise<QueryObserverResult<WorkOrderType, unknown>>
}

export type WoType = {
  id: string
  no: string
  active: boolean
  schedule: string
  site: string
  docs: string
  unitCode: string
  hullNumber: string
  createdAt: string
}

export type WoDocumentType = {
  number: string
  status: string
  documentType: string
  createdAt: string
  id: string
}

const WoContext = React.createContext<WoContextProps>({} as WoContextProps)

export const useWo = () => {
  const context = React.useContext(WoContext)
  if (context === undefined) {
    throw new Error('useWo must be used within a WoContextProvider')
  }
  return context
}

export const WoProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const location = useLocation()
  const { setConfirmState } = useMenu()
  const params = useParams()
  const {
    userProfile: { sites }
  } = useAuth()

  const [dialogClose, setDialogClose] = useState<boolean>(false)
  const [activeSegment, setActiveSegment] = useState<WoSegmentType | null>(null)
  const [selectedWoId, setSelectedWoId] = useState<string | undefined>(params?.woId)
  const [selectedFrId, setSelectedFrId] = useState<string | undefined>(params?.frId)
  const [woParams, setPartialWoParams, setWoParams] = usePartialState<WoParams>({
    page: 1,
    limit: 10
  })
  const [frParams, setPartialFrParams, setFrParams] = usePartialState<FrParams>({
    page: 1,
    limit: 10,
    status: FieldReportStatus.CREATED
  })
  const isReportsIn = location.pathname.includes('list')

  const siteWorkshop = useMemo(() => {
    if (sites.filter(site => site.type === 'WORKSHOP').length === 0) return undefined
    return sites
      ?.filter(site => site.type === 'WORKSHOP')
      .map(site => site.id)
      .join(',')
  }, [sites])

  const method = useForm<CreateWorkOrderDtoType>({
    resolver: zodResolver(createWorkOrderSchema)
  })

  const { reset, getValues } = method

  const { mutate: createWorkOrder, isLoading: creatingWorkOrder } = useCreateWorkOrder()
  const { mutate: createMaterialRequest, isLoading: creatingMr } = useAddMr()
  const { mutate: addSegmentMutate, isLoading: loadingAddSegment } = useAddWoSegment()
  const { mutate: closeFrMutate, isLoading: closeFrLoading } = useCloseFieldReport()

  const { data: woList, refetch: refetchWoList } = useQuery({
    queryKey: [WO_QUERY_LIST_KEY, JSON.stringify(woParams), siteWorkshop],
    queryFn: () =>
      RnMQueryMethods.getWoList({
        ...woParams,
        siteIds: siteWorkshop
      }),
    placeholderData: defaultListData as ListResponse<WorkOrderType>
  })

  const { data: woDetail, refetch: refetchWoDetail } = useQuery({
    enabled: !!selectedWoId,
    queryKey: [WO_QUERY_DETAIL_KEY, selectedWoId],
    queryFn: () => RnMQueryMethods.getWoDetail(selectedWoId)
  })

  const { data: frListResponse, refetch: refetchFrList } = useQuery({
    queryKey: [FR_LIST_QUERY_KEY, JSON.stringify(frParams), siteWorkshop],
    queryFn: () => {
      return RnMQueryMethods.getFrList({
        ...frParams,
        ...(isReportsIn && {
          destinationSiteIds: siteWorkshop
        })
      })
    },
    placeholderData: defaultListData as ListResponse<FrType>
  })

  const { data: frDetail, refetch: refetchFrDetail } = useQuery({
    enabled: !!selectedFrId,
    queryKey: [FR_DETAIL_QUERY_KEY, selectedFrId],
    queryFn: () => RnMQueryMethods.getFrDetail(selectedFrId)
  })

  const { data: frLogs, refetch: refetchFrLogs } = useQuery({
    enabled: !!selectedFrId,
    queryKey: [FR_DETAIL_LOGS_KEY, 'WO_CONTEXT', selectedFrId],
    queryFn: async () => {
      const res = await RnMQueryMethods.getFrDetailLogs(selectedFrId)
      return res.items ?? []
    },
    placeholderData: [] as FrLogType[]
  })

  const { data: woLogs, refetch: refetchWoLogs } = useQuery({
    enabled: !!selectedWoId,
    queryKey: [WO_LOGS_QUERY_KEY, selectedWoId],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoDetailLogs(selectedWoId, { page: 1, limit: Number.MAX_SAFE_INTEGER })
      return res.items ?? []
    },
    placeholderData: [] as WoLogType[]
  })

  const { data: woSegments, refetch: refetchSegments } = useQuery({
    enabled: !!selectedWoId,
    queryKey: [WO_SEGMENTS_QUERY_KEY, selectedWoId],
    queryFn: async () => {
      const res = await RnMQueryMethods.getWoSegments(selectedWoId, { page: 1, limit: Number.MAX_SAFE_INTEGER })
      return res.items ?? []
    },
    placeholderData: [] as WoSegmentType[]
  })

  const handleProccessFr = () => {
    setConfirmState({
      title: 'Proses FR',
      open: true,
      content: 'Apakah kamu yakin akan memproses FR ini dan melanjutkan ke pembuatan WO? Action ini tidak dapat diubah',
      confirmText: 'Proses FR',
      onConfirm: () => {
        reset({
          ...getValues(),
          diagnosis: null,
          segments: []
        })
        navigate(location.pathname + '/create-wo')
      }
    })
  }

  const handleCloseFieldReport = () => {
    setDialogClose(true)
  }

  const onCloseFieldReport = (dto: CloseFrInput) => {
    closeFrMutate(
      {
        frId: selectedFrId,
        closeReason: dto.closeReason
      },
      {
        onSuccess: () => {
          toast.success('Field Report berhasil ditutup')
          setDialogClose(false)
          refetchFrList()
          refetchFrDetail()
          refetchFrLogs()
        }
      }
    )
  }

  const handleAddWo = (dto: CreateWorkOrderDtoType) => {
    Sentry.captureMessage(`Submit Work Order: ${JSON.stringify(dto)}`)
    setConfirmState({
      open: true,
      title: 'Buat WO',
      content:
        'Apakah kamu yakin akan menambahkan WO dari FR untuk unit ini? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Buat WO',
      onConfirm: () => {
        createWorkOrder(dto, {
          onSuccess: response => {
            const newWoId = response.data.id
            toast.success('Berhasil membuat WO')
            refetchWoList()
            setTimeout(() => {
              navigate(`/wo/created/${newWoId}`)
            }, 1000)
          }
        })
      }
    })
  }

  const handleSubmitMr = (dto: MrPayloadExtend) => {
    setConfirmState({
      open: true,
      title: 'Buat Material Request',
      content:
        'Apakah kamu yakin akan membuat Material Request ini? Pastikan semua detil yang kamu masukkan untuk Material Request ini sudah benar',
      confirmText: 'Buat Material Request',
      onConfirm: () => {
        createMaterialRequest(dto, {
          onSuccess: () => {
            toast.success('Material Request berhasil dibuat')
            setTimeout(() => {
              navigate(`/mr/list`, {
                replace: true
              })
            }, 700)
          }
        })
      }
    })
  }

  const handleSubmitSegment = (dto: SegmentType, cb?: () => void) => {
    addSegmentMutate(
      {
        woId: selectedWoId,
        jobCodeId: dto.jobCodeId,
        componentCodeId: dto.componentCodeId,
        modifierCodeId: dto.modifierCodeId,
        divisionId: dto.divisionId,
        items: dto.items?.map(item => ({
          itemId: item.itemId,
          serialNumber: item.serialNumber,
          quantity: item.quantity,
          quantityUnit: item.quantityUnit,
          largeUnitQuantity: item.largeUnitQuantity,
          note: item.note,
          type: item.type,
          images: item.images as any
        }))
      },
      {
        onSuccess: () => {
          toast.success('Segmen berhasil ditambahkan')
          refetchWoDetail()
          refetchWoLogs()
          refetchSegments()
          setTimeout(() => {
            cb?.()
          }, 700)
        }
      }
    )
  }

  useEffect(() => {
    if (params?.frId) setSelectedFrId(params.frId)
    if (params?.woId) setSelectedWoId(params.woId)
  }, [params])

  useEffect(() => {
    if (frDetail) {
      reset({
        ...getValues(),
        fieldReportId: frDetail.id,
        destinationSiteId: frDetail.destinationSiteId,
        unitId: frDetail.unitId
      })
    }
  }, [frDetail])

  const value = {
    isMobile,
    woParams,
    setPartialWoParams,
    setWoParams,
    woList,
    woDetail,
    woLogs,
    frListResponse,
    frDetail,
    frLogs,
    navigate,
    selectedWoId,
    setSelectedWoId,
    woSegments,
    activeSegment,
    setActiveSegment,
    frParams,
    setPartialFrParams,
    setFrParams,
    handleProccessFr,
    handleCloseFieldReport,
    handleAddWo,
    handleSubmitMr,
    handleSubmitSegment,
    selectedFrId,
    setSelectedFrId,
    creatingWorkOrder,
    creatingMr,
    loadingAddSegment,
    refetchWoList,
    refetchWoDetail
  }
  return (
    <WoContext.Provider value={value}>
      {dialogClose && (
        <CloseFRDialog
          loading={closeFrLoading}
          open={dialogClose}
          setOpen={setDialogClose}
          onCloseFrHandler={onCloseFieldReport}
        />
      )}
      <FormProvider {...method}>{children}</FormProvider>
    </WoContext.Provider>
  )
}
