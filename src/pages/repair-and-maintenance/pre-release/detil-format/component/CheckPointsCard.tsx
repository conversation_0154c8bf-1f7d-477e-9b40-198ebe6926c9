import { Card, CardContent, Typography } from '@mui/material'
import { useFormatPreRelease } from '../../context/FormatPreReleaseContext'

const CheckPointsCard = () => {
  const { selectedFormat } = useFormatPreRelease()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Check Poin</Typography>
        </div>

        <ul>
          {selectedFormat?.checkPoints?.map((point, idx) => (
            <div className='flex flex-col mb-4' key={point.id}>
              <li className='list-decimal pl-6'>&bull;{` ${point.name}`}</li>
              <Typography variant='caption' className='pl-6'>
                {`${point.description}`}
              </Typography>
            </div>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}

export default CheckPointsCard
