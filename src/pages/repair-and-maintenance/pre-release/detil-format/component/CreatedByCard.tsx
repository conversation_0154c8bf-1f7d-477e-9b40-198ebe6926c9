import { Avatar, Card, CardContent, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { useFormatPreRelease } from '../../context/FormatPreReleaseContext'

const CreatedByCard = () => {
  const { selectedFormat } = useFormatPreRelease()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Dibuat oleh</Typography>
        </div>
        <div className='flex gap-3 items-center w-full pl-4'>
          <Avatar src={selectedFormat?.createdByUser?.profilePictureUrl ?? ''} alt='avatar' />
          <div className='flex flex-col flex-1 gap-0 items-start relative bg-transparent'>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <p className='tracking-[0.2px] leading-6 text-base text-black dark:text-inherit'>
                {selectedFormat?.createdByUser?.fullName ?? '-'}
              </p>
            </div>
            <div className='flex flex-col gap-0 items-start relative bg-transparent'>
              <Typography variant='caption'>{selectedFormat?.createdByUser?.title ?? '-'}</Typography>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CreatedByCard
