import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useFormatPreRelease } from '../../context/FormatPreReleaseContext'

const FormatDetailCard = () => {
  const { selectedFormat } = useFormatPreRelease()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Format</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Judul</small>
              <Typography>{selectedFormat?.title ?? '-'}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Deskripsi</small>
              <Typography>{selectedFormat?.description ?? '-'}</Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default FormatDetailCard
