import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useFormatPreRelease } from '../../context/FormatPreReleaseContext'

const UnitCheckCard = () => {
  const { selectedFormat } = useFormatPreRelease()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Untuk Pengecekan Unit</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Kategori unit</small>
              <Typography>{selectedFormat?.unitCategory?.name ?? '-'}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-1'>
              <small>Jenis unit</small>
              <Typography>{selectedFormat?.unitSubCategory?.name ?? '-'}</Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default UnitCheckCard
