import { WorkOrderType } from '@/types/woTypes'
import { Chip, Typography, IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { getStatusConfig } from './utils'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { PreReleaseType } from '@/types/preReleaseTypes'

const columnHelper = createColumnHelper<PreReleaseType>()

type RowActionType = {
  detail: (item: WorkOrderType) => void
}

export const tableColumns = (rowAction: RowActionType, userId?: string) => [
  columnHelper.accessor('number', {
    header: 'NO. WO',
    cell: ({ row }) => (
      <Typography color='primary' sx={{ cursor: 'pointer' }} onClick={() => rowAction.detail(row.original.workOrder)}>
        {row.original.workOrder.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => {
      const docStatus = row.original.status
      const ownApproval = row.original.approvals?.find(approval => approval.userId === userId)
      return docStatus !== 'CANCELED' ? (
        <Chip
          label={getStatusConfig(ownApproval?.status)?.label}
          color={getStatusConfig(ownApproval?.status)?.color as any}
          variant='tonal'
          size='small'
        />
      ) : (
        <Chip label='Dibatalkan' color='error' variant='tonal' size='small' />
      )
    }
  }),
  columnHelper.accessor('unit.number', {
    header: 'Kode Unit'
  }),
  columnHelper.accessor('unit.brandName', {
    header: 'Unit'
  }),
  columnHelper.accessor('unit.hullNumber', {
    header: 'No Lambung'
  }),
  columnHelper.accessor('checkPointsCount', {
    header: 'JUMLAH CHECKPOINT'
  }),
  columnHelper.accessor('createdAt', {
    header: 'TGL DIBUAT',
    cell: ({ row }) =>
      row.original.createdAt ? formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id }) : '-'
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original.workOrder)}>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
