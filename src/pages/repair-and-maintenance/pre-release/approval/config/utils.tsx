import { WoStatus } from '@/types/woTypes'

export const getStatusConfig = (status: string): { color: string; label: string } => {
  switch (status) {
    case WoStatus.ACTIVE:
      return {
        color: 'success',
        label: 'Aktif'
      }
    case WoStatus.COMPLETED:
      return {
        color: 'success',
        label: '<PERSON><PERSON><PERSON>'
      }
    case WoStatus.PRE_RELEASED:
      return {
        color: 'warning',
        label: 'Pre Release diajukan'
      }
    case WoStatus.READY_TO_RELEASE:
      return {
        color: 'warning',
        label: 'Siap Release'
      }
    case 'ASSIGNED':
      return {
        color: 'default',
        label: 'Pre Release sudah dibuat'
      }
    case 'PROCESSED':
      return {
        color: 'warning',
        label: 'Pre Release diproses'
      }
    case 'APPROVED':
      return {
        color: 'success',
        label: 'Pre Release disetujui'
      }
    case 'REJECTED':
      return {
        color: 'error',
        label: 'Pre Release ditolak'
      }
    case 'CANCELED':
      return {
        color: 'default',
        label: 'Pre Release dibatalkan'
      }
    case 'WAITING':
      return {
        color: 'default',
        label: 'Menunggu'
      }
    default:
      return {
        color: 'default',
        label: status
      }
  }
}

export const woUserStatusOptions = [
  {
    value: 'WAITING',
    label: 'Menunggu'
  },
  {
    value: 'APPROVED',
    label: 'Disetujui'
  },
  {
    value: 'REJECTED',
    label: 'Ditolak'
  }
]
