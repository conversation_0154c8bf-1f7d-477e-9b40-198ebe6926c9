import { Link } from 'react-router-dom'
import { useMemo, useState } from 'react'
import { Box, Button, Card, IconButton, Typography } from '@mui/material'
import DebouncedInput from '@/components/DebounceInput'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { usePreRelease } from '../../context/PreReleaseContext'
import {
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  getPaginationRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'

const UnitTakingList = () => {
  const {
    isMobile,
    navigate,
    woList: { items, totalItems, totalPages },
    woParams: { search, page },
    setWoParams,
    setPartialWoParams
  } = usePreRelease()

  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [actionBtn, setBtn] = useState<boolean>(false)
  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  const actionButton = (
    <></>
    // <Button
    //   color='secondary'
    //   variant='outlined'
    //   startIcon={<i className='ri-upload-2-line' />}
    //   className='is-full sm:is-auto'
    // >
    //   Ekspor
    // </Button>
  )

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns({
        detail: item => {
          navigate(`/wo/unit-taking/${item.id}`)
        }
      }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: page - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setWoParams(prev => ({ ...prev, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setWoParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
              {!isMobile ? (
                actionButton
              ) : (
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              )}
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Data</Typography>
              <Typography className='text-sm text-gray-400'>Semua data Work Order akan ditampilkan di sini</Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setWoParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialWoParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setWoParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialWoParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default UnitTakingList
