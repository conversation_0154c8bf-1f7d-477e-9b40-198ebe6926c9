import { useState, useMemo } from 'react'

import { Button, Card, Grid, IconButton, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useFormatPreRelease } from '../../context/FormatPreReleaseContext'
import { useLocation } from 'react-router-dom'
import { tableColums } from '../config/table'

const FormatList = () => {
  const { pathname } = useLocation()
  const {
    isMobile,
    formatParams: { search, page, limit },
    formatListResponse: { items, totalItems, totalPages },
    setFormatParams,
    setPartialFormatParams,
    navigate
  } = useFormatPreRelease()

  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [actionBtn, setBtn] = useState<boolean>(false)

  const actionButton = (
    <>
      {/* <Button
        color='secondary'
        variant='outlined'
        startIcon={<i className='ri-upload-2-line' />}
        className='is-full sm:is-auto'
      >
        Ekspor
      </Button> */}
      <Button variant='contained' onClick={() => navigate(pathname + '/create')}>
        Buat Format
      </Button>
    </>
  )

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColums({
        detail: format => navigate(`/wo/format-pre-release/${format.id}`)
      }),
      initialState: {
        pagination: {
          pageSize: limit ?? 10,
          pageIndex: page - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setFormatParams(curr => ({ ...curr, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setFormatParams(curr => ({ ...curr, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
              {!isMobile ? (
                actionButton
              ) : (
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              )}
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography>Belum ada Data</Typography>
              <Typography className='text-sm text-gray-400'>Semua data Format akan ditampilkan di sini</Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setFormatParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialFormatParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setFormatParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialFormatParams('page', pageIndex)}
        />
      </Card>
    </>
  )
}

export default FormatList
