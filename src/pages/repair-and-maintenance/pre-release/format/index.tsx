import { Grid, Typography } from '@mui/material'
import { useFormatPreRelease } from '../context/FormatPreReleaseContext'
import FormatList from './component/FormatList'

const FormatPreRelease = () => {
  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <div className='flex justify-between items-end'>
          <div className='flex flex-col'>
            <Typography variant='h4'>Format Pre-Release</Typography>
            <Typography>Buat Format Dokumen Pre-Release sesuai dengan kebutuhan</Typography>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        <FormatList />
      </Grid>
    </Grid>
  )
}

export default FormatPreRelease
