import truncateString from '@/core/utils/truncate'
import { ParameterType } from '@/types/parameterTypes'
import { IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const columnHelper = createColumnHelper<ParameterType>()

type RowActionType = {
  detail: (item: ParameterType) => void
}

export const tableColums = (rowAction: RowActionType) => [
  columnHelper.accessor('title', {
    header: 'JUDUL'
  }),
  columnHelper.accessor('description', {
    header: 'DESKRIPSI',
    cell: ({ row }) => truncateString(row.original.description, 20)
  }),
  columnHelper.accessor('unitCategory.name', {
    header: 'UNTUK PENGECEKAN UNIT'
  }),
  columnHelper.accessor('checkPointsCount', {
    header: 'CHECK POIN',
    cell: ({ row }) => `${row.original.checkPointsCount} poin`
  }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) =>
      row.original.createdAt ? formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id }) : '-'
  }),
  columnHelper.display({
    id: 'action',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
