import { ParameterType, PreReleaseTemplateDto } from '@/types/parameterTypes'
import { createColumnHelper } from '@tanstack/react-table'
import { IconButton, TextField } from '@mui/material'
import { Control, Controller } from 'react-hook-form'

const columnHelper = createColumnHelper<ParameterType>()

type RowActionType = {
  delete: (index: number) => void
  control: Control<PreReleaseTemplateDto, any>
}

export const checkpointColumns = (action: RowActionType) => [
  columnHelper.display({
    id: 'numbering',
    size: 10,
    header: 'NO',
    cell: ({ row }) => row.index + 1
  }),
  columnHelper.accessor('description', {
    header: 'CHECK POIN',
    size: 250,
    cell: ({ row }) => (
      <Controller
        control={action.control}
        name={`checkPoints.${row.index}.name`}
        render={({ field, fieldState: { error } }) => (
          <TextField
            fullWidth
            defaultValue={field.value}
            onBlur={e => field.onChange(e.target.value)}
            ref={field.ref}
            size='small'
            error={!!error}
          />
        )}
      />
    )
  }),
  columnHelper.accessor('description', {
    header: 'PENJELASAN',
    size: 250,
    cell: ({ row }) => (
      <Controller
        control={action.control}
        name={`checkPoints.${row.index}.description`}
        render={({ field, fieldState: { error } }) => (
          <TextField
            fullWidth
            defaultValue={field.value}
            onBlur={e => field.onChange(e.target.value)}
            ref={field.ref}
            size='small'
            error={!!error}
          />
        )}
      />
    )
  }),
  columnHelper.display({
    id: 'action',
    size: 10,
    header: 'ACTION',
    cell: ({ row }) => (
      <IconButton onClick={() => action.delete(row.index)}>
        <i className='ri-delete-bin-7-line text-googlePlus' />
      </IconButton>
    )
  })
]
