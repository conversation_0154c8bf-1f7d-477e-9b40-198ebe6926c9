import CompanyQueryMethods, { UNIT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { PreReleaseTemplateDto } from '@/types/parameterTypes'
import { Card, CardContent, Typography, Grid, FormControl, InputLabel, Select, MenuItem } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { Controller, useFormContext, useWatch } from 'react-hook-form'

const PurposeCard = () => {
  const { control } = useFormContext<PreReleaseTemplateDto>()

  const categoryId = useWatch({ control, name: 'unitCategoryId' })

  const { data: unitCategories } = useQuery({
    queryKey: [UNIT_LIST_QUERY_KEY, 'CATEGORY'],
    queryFn: async () => CompanyQueryMethods.getCategoryList({ type: 'UNIT', limit: Number.MAX_SAFE_INTEGER })
  })

  const { data: unitSubCategories } = useQuery({
    enabled: !!categoryId,
    queryKey: [UNIT_LIST_QUERY_KEY, 'SUB_CATEGORY', categoryId],
    queryFn: async () =>
      CompanyQueryMethods.getCategoryList({ type: 'UNIT', parentId: categoryId, limit: Number.MAX_SAFE_INTEGER })
  })

  return (
    <Card>
      <CardContent className='space-y-1'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Untuk Pengecekan Unit</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='unitCategoryId'
              render={({ field, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='category-unit'>Kategori Unit</InputLabel>
                  <Select
                    {...field}
                    id='category-unit'
                    label='Kategori Unit'
                    error={!!error}
                    placeholder='Pilih Kategori Unit'
                  >
                    {unitCategories?.map(category => (
                      <MenuItem key={category.id} value={category.id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='unitSubCategoryId'
              render={({ field, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='unit-type'>Jenis Unit</InputLabel>
                  <Select {...field} id='unit-type' label='Jenis Unit' error={!!error} placeholder='Pilih Jenis Unit'>
                    {unitSubCategories?.map(category => (
                      <MenuItem key={category.id} value={category.id}>
                        {category.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default PurposeCard
