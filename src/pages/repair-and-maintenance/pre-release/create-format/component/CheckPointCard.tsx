import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, FormHelperText, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useMemo } from 'react'
import Table from '@/components/table'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { FormatPreReleaseDto } from '../../context/FormatPreReleaseContext'
import { checkpointColumns } from '../config/table'
import { PreReleaseTemplateDto } from '@/types/parameterTypes'

const CheckPointCard = () => {
  const {
    control,
    formState: { errors }
  } = useFormContext<PreReleaseTemplateDto>()
  const { fields, append, remove } = useFieldArray({ control, name: 'checkPoints' })

  const handleAddCheckpoint = () => {
    append({ name: '', description: '' })
  }

  const tableOptions = useMemo(
    () => ({
      data: fields ?? [],
      columns: checkpointColumns({
        delete: idx => remove(idx),
        control
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [fields]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Card>
      <CardContent className='space-y-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Check Point</Typography>
          <Button variant='outlined' onClick={handleAddCheckpoint}>
            Tambah Check Poin
          </Button>
        </div>
        <div className='shadow-sm rounded-[8px]'>
          <Table
            table={table}
            headerColor='green'
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography>Belum ada Check Point</Typography>
                <Typography className='text-sm text-gray-400'>Tambahkan item check point untuk dokumen ini</Typography>
              </td>
            }
          />
        </div>
        <Controller
          control={control}
          name='checkPoints'
          render={({ fieldState: { error } }) =>
            !!error && fields.length === 0 && <FormHelperText error>Wajib mengisikan checkpoint</FormHelperText>
          }
        />
      </CardContent>
    </Card>
  )
}

export default CheckPointCard
