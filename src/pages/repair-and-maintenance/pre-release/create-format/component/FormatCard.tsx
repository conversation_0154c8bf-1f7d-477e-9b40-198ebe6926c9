import { Card, CardContent, Grid, TextField, Typography } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { FormatPreReleaseDto } from '../../context/FormatPreReleaseContext'
import { PreReleaseTemplateDto } from '@/types/parameterTypes'

const FormatCard = () => {
  const { control } = useFormContext<PreReleaseTemplateDto>()
  return (
    <Card>
      <CardContent className='space-y-1'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Format</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='title'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  error={!!error}
                  ref={field.ref}
                  fullWidth
                  label='Judul'
                  className='bg-white rounded-lg'
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='description'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  error={!!error}
                  ref={field.ref}
                  fullWidth
                  label='Deskripsi'
                  className='bg-white rounded-lg'
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default FormatCard
