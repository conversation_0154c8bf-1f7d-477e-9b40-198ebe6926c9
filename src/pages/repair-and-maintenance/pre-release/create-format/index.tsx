import * as Sentry from '@sentry/react'
import { Breadcrum<PERSON>, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import { FormProvider, useForm } from 'react-hook-form'
import LoadingButton from '@mui/lab/LoadingButton'
import { useFormatPreRelease } from '../context/FormatPreReleaseContext'
import FormatCard from './component/FormatCard'
import PurposeCard from './component/PurposeCard'
import CheckPointCard from './component/CheckPointCard'
import { PreReleaseTemplateDto } from '@/types/parameterTypes'
import { zodResolver } from '@hookform/resolvers/zod'
import { PreReleaseTemplateDtoSchema } from './config/schema'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect } from 'react'
import { toast } from 'react-toastify'

const CreateFormatPage = () => {
  const { onSubmitFormat, loadingCreatingFormat } = useFormatPreRelease()
  const { userProfile } = useAuth()
  const methods = useForm<PreReleaseTemplateDto>({
    resolver: zodResolver(PreReleaseTemplateDtoSchema)
  })
  const { handleSubmit, reset, getValues } = methods

  useEffect(() => {
    if (userProfile?.company?.id) {
      reset({
        ...getValues(),
        companyId: userProfile?.company?.id
      })
    }
  }, [userProfile])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <Breadcrumbs aria-label='breadcrumb'>
          <Link to='#' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Pre-Release</Typography>
          </Link>
          <Link to='/wo/format-pre-release' replace>
            <Typography color='var(--mui-palette-text-disabled)'>Format Pre-Release</Typography>
          </Link>
          <Typography>Buat Format</Typography>
        </Breadcrumbs>
      </Grid>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
          <div className='flex flex-col'>
            <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
              <Typography variant='h4'>Buat Format Pre-Release</Typography>
            </div>
            <Typography className='max-sm:text-center max-sm:mt-2'>Buat format dokumen Pre-Release baru</Typography>
          </div>
          <div className='flex gap-2 flex-col sm:flex-row is-full sm:is-auto'>
            <Button variant='outlined' color='secondary'>
              Batalkan
            </Button>
            <LoadingButton
              loading={loadingCreatingFormat}
              startIcon={<></>}
              variant='contained'
              className='is-full sm:is-auto'
              onClick={handleSubmit(onSubmitFormat, errors => {
                console.error(errors)
                Sentry.captureException(errors)
                Object.entries(errors).forEach(([field, error]) => {
                  toast.error(`${field}: ${error?.message}`, {
                    autoClose: 5000
                  })
                })
              })}
            >
              Buat Format
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <FormProvider {...methods}>
        <Grid item xs={12}>
          <Grid container spacing={4}>
            <Grid item xs={12} md={6}>
              <FormatCard />
            </Grid>
            <Grid item xs={12} md={6}>
              <PurposeCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12}>
          <CheckPointCard />
        </Grid>
      </FormProvider>
    </Grid>
  )
}

export default CreateFormatPage
