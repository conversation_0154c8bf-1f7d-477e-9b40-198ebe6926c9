import { Card, CardContent, Divider, Typography } from '@mui/material'
import { usePreRelease } from '../../context/PreReleaseContext'
import { PreReleaseType } from '@/types/preReleaseTypes'

type WorkDetailProps = {
  prData: PreReleaseType
}

const WorkDetail = (props: WorkDetailProps) => {
  const { prData } = props

  return (
    <Card>
      <CardContent>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'><PERSON><PERSON></Typography>
        </div>
        <div className='flex flex-col gap-4'>
          <Typography>Unscheduled Service</Typography>
          <Divider />
          <div className='flex flex-col gap-4'>
            <div className='flex flex-col gap-2'>
              <Typography variant='caption'>Kate<PERSON><PERSON></Typography>
              <Typography>{prData?.workName ?? '-'}</Typography>
            </div>
            <div className='flex flex-col gap-2'>
              <Typography variant='caption'>Format Dokumen</Typography>
              <Typography>{prData?.template?.title ?? '-'}</Typography>
            </div>
            <div className='flex flex-col gap-2'>
              <Typography variant='caption'>Dicek Oleh</Typography>
              <Typography>{prData?.assignedToUser?.fullName ?? '-'}</Typography>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default WorkDetail
