import { useMemo } from 'react'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { Card, CardContent, Typography } from '@mui/material'
import Table from '@/components/table'
import { parameterTableColumns } from '../config/table'
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form'
import { PreReleaseChecklistDto, PreReleaseDtoType } from '@/types/preReleaseTypes'

const CheckPointTable = () => {
  const { control, reset, getValues } = useFormContext<PreReleaseChecklistDto>()
  const { fields } = useFieldArray({ control, name: 'checkPoints' })

  const tableOptions = useMemo(
    () => ({
      data: fields ?? [],
      columns: parameterTableColumns({ control }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [fields]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <Typography variant='h5'>Check Point</Typography>
        <div className='shadow-sm rounded-[8px]'>
          <Table
            headerColor='green'
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography>Belum ada Checklist</Typography>
              </td>
            }
            disablePagination
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default CheckPointTable
