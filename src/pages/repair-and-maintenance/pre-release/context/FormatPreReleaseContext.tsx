import * as Sentry from '@sentry/react'
import { defaultListData } from '@/api/queryClient'
import { useCreatePreReleaseTemplate } from '@/api/services/parameter/mutation'
import ParameterQueryMethods from '@/api/services/parameter/query'
import { FORMAT_QUERY_LIST_KEY } from '@/api/services/parameter/service'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { FormatType } from '@/types/formatTypes'
import { ParameterParams, ParameterType, PreReleaseTemplateDto } from '@/types/parameterTypes'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import { createContext, useContext, useEffect, useState } from 'react'
import { NavigateFunction, useNavigate, useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

interface FormatPreReleaseContextProps {
  isMobile: boolean
  loadingCreatingFormat: boolean
  navigate: NavigateFunction
  formatListResponse: ListResponse<ParameterType>
  formatParams: ListParams
  setPartialFormatParams: (fieldName: keyof ListParams, value: any) => void
  setFormatParams: React.Dispatch<React.SetStateAction<ListParams>>
  setSelectedFormatId: React.Dispatch<React.SetStateAction<string>>
  selectedFormatId: string
  selectedFormat: ParameterType
  onSubmitFormat: (dto: PreReleaseTemplateDto) => void
}

export type FormatPreReleaseDto = {
  checkpoint: ParameterType[]
  title: string
  description: string
  categoryUnitId: string
  unitType: string
}

const FormatPreReleaseContext = createContext<FormatPreReleaseContextProps>({} as FormatPreReleaseContextProps)

export const useFormatPreRelease = () => {
  const context = useContext(FormatPreReleaseContext)
  if (context === undefined) {
    throw new Error('useFormatPreRelease must be used within a FormatPreReleaseContextProvider')
  }
  return context
}

export const FormatPreReleaseContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const params = useParams()
  const { setConfirmState } = useMenu()

  const [selectedFormatId, setSelectedFormatId] = useState<string>(params?.formatId ?? '')

  const [formatParams, setPartialFormatParams, setFormatParams] = usePartialState<ParameterParams>({
    page: 1,
    limit: 10
  })

  const { data: formatListResponse, refetch: refetchFormatList } = useQuery({
    queryKey: [FORMAT_QUERY_LIST_KEY, JSON.stringify(formatParams)],
    queryFn: () => ParameterQueryMethods.getPreReleaseTemplates(formatParams),
    placeholderData: defaultListData as ListResponse<ParameterType>
  })

  const { data: selectedFormat } = useQuery({
    enabled: !!selectedFormatId,
    queryKey: [FORMAT_QUERY_LIST_KEY, selectedFormatId],
    queryFn: () => ParameterQueryMethods.getPreReleaseTemplate(selectedFormatId)
  })

  const { mutate: createFormatPreRelease, isLoading: loadingCreatingFormat } = useCreatePreReleaseTemplate()

  const onSubmitFormat = (dto: PreReleaseTemplateDto) => {
    Sentry.captureMessage(`Submit Pre Release: ${JSON.stringify(dto)}`)
    setConfirmState({
      open: true,
      title: 'Buat Format Pre-Release',
      content:
        'Apakah kamu yakin akan membuat format dokumen Pre-Release ini? Pastikan semua data yang kamu masukkan sudah benar',
      confirmText: 'Buat Format',
      onConfirm: () => {
        createFormatPreRelease(dto, {
          onSuccess: () => {
            toast.success('Format Pre-Release Berhasi dibuat')
            refetchFormatList()
            setTimeout(() => {
              navigate('/wo/format-pre-release')
            }, 700)
          }
        })
      }
    })
  }

  useEffect(() => {
    if (params?.formatId) {
      setSelectedFormatId(params?.formatId)
    }
  }, [params])

  const value = {
    isMobile,
    loadingCreatingFormat,
    formatListResponse,
    formatParams,
    selectedFormatId,
    selectedFormat,
    setSelectedFormatId,
    setPartialFormatParams,
    setFormatParams,
    navigate,
    onSubmitFormat
  }

  return <FormatPreReleaseContext.Provider value={value}>{children}</FormatPreReleaseContext.Provider>
}
