import { WorkOrderType } from '@/types/woTypes'
import { Chip, Typography, IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { getStatusConfig } from './utils'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { srPriorityOptions } from '@/pages/service-requisition/list/config/utils'
import { ServiceRequisitionPriority } from '@/types/serviceRequisitionsTypes'

const columnHelper = createColumnHelper<WorkOrderType>()

type RowActionType = {
  detail: (item: WorkOrderType) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'NO WO',
    cell: ({ row }) => (
      <Typography color='primary' sx={{ cursor: 'pointer' }} onClick={() => rowAction.detail(row.original)}>
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'STATUS',
    cell: ({ row }) => (
      <Chip
        label={getStatusConfig(row.original.status).label}
        color={getStatusConfig(row.original.status).color as any}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnHelper.accessor('unit.number', {
    header: 'Kode Unit'
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = srPriorityOptions.find(
        option => option.value === String(row.original.priority ?? ServiceRequisitionPriority.P4)
      )
      return priority ? (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      ) : (
        '-'
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'TGL DIBUAT',
    cell: ({ row }) =>
      row.original.createdAt ? formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id }) : '-'
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)}>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
