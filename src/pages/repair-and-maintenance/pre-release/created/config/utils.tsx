import { WoStatus } from '@/types/woTypes'

export const getStatusConfig = (status: string): { color: string; label: string } => {
  switch (status) {
    case WoStatus.ACTIVE:
      return {
        color: 'default',
        label: 'Pre Release belum dibuat'
      }
    case WoStatus.COMPLETED:
      return {
        color: 'success',
        label: 'Sudah Diambil'
      }
    case WoStatus.PRE_RELEASED:
      return {
        color: 'default',
        label: 'Pre Release belum diajukan'
      }
    case WoStatus.READY_TO_RELEASE:
      return {
        color: 'warning',
        label: 'Belum Diambil'
      }
    default:
      return {
        color: 'default',
        label: status
      }
  }
}
