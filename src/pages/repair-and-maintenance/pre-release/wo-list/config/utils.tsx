import { WoStatus } from '@/types/woTypes'

export const getStatusConfig = (status: string): { color: string; label: string } => {
  switch (status) {
    case WoStatus.ACTIVE:
      return {
        color: 'default',
        label: 'Pre Release belum dibuat'
      }
    case WoStatus.COMPLETED:
      return {
        color: 'success',
        label: 'Sudah Diambil'
      }
    case WoStatus.PRE_RELEASED:
      return {
        color: 'default',
        label: 'Pre Release belum diajukan'
      }
    case WoStatus.READY_TO_RELEASE:
      return {
        color: 'warning',
        label: 'Belum Diambil'
      }
    // ASSIGNED, PROCESSED, APPROVED, REJECTED, CANCELED
    case 'ASSIGNED':
      return {
        color: 'default',
        label: 'Pre Release sudah dibuat'
      }
    case 'PROCESSED':
      return {
        color: 'warning',
        label: 'Pre Release diproses'
      }
    case 'APPROVED':
      return {
        color: 'success',
        label: 'Pre Release disetujui'
      }
    case 'REJECTED':
      return {
        color: 'error',
        label: 'Pre Release ditolak'
      }
    case 'CANCELED':
      return {
        color: 'default',
        label: 'Pre Release dibatalkan'
      }
    default:
      return {
        color: 'default',
        label: status
      }
  }
}
