import { WorkOrderType } from '@/types/woTypes'
import { Chip, Typography, IconButton } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { getStatusConfig } from './utils'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { PreReleaseType } from '@/types/preReleaseTypes'
import { srPriorityOptions } from '@/pages/service-requisition/list/config/utils'
import { ServiceRequisitionPriority } from '@/types/serviceRequisitionsTypes'

const columnHelper = createColumnHelper<PreReleaseType>()

type RowActionType = {
  detail: (item: WorkOrderType) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('number', {
    header: 'NO. WO',
    cell: ({ row }) => (
      <Typography color='primary' sx={{ cursor: 'pointer' }} onClick={() => rowAction.detail(row.original.workOrder)}>
        {row.original.workOrder.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => (
      <Chip
        label={getStatusConfig(row.original.status).label}
        color={getStatusConfig(row.original.status).color as any}
        variant='tonal'
        size='small'
      />
    )
  }),
  columnHelper.accessor('unit.number', {
    header: 'Kode Unit'
  }),
  columnHelper.accessor('checkPointsCount', {
    header: 'Jumlah Checkpoint',
    cell: ({ row }) => <Typography align='center'>{row.original.checkPointsCount}</Typography>
  }),
  columnHelper.accessor('createdAt', {
    header: 'Tgl Dibuat',
    cell: ({ row }) =>
      row.original.createdAt ? formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id }) : '-'
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original.workOrder)}>
          <i className='ri-eye-line text-secondary' />
        </IconButton>
      )
    }
  })
]
