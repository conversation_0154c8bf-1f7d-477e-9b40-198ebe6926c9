import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import { useFrContext } from '../context/FrContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import UnitDetail from './component/UnitDetail'
import DataUnitDetail from './component/DataUnitDetail'
import CreatedByCard from './component/CreatedByCard'
import CardProblem from './component/CardProblem'
import CardSchedule from './component/CardSchedule'
import ActivityLogCard from './component/ActivityLogCard'
import LocationCard from './component/LocationCard'
import { frTableConfig } from '../created/config/table'
import { FieldReportStatus } from '@/types/frTypes'
import CloseReason from './component/CloseReasonCard'

const FrDetailPage = () => {
  const { activeFr, handleCloseFieldReport } = useFrContext()

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>FR</Typography>
            </Link>
            <Link to='/fr/created' replace>
              <Typography color='var(--mui-palette-text-disabled)'>FR Terbuat</Typography>
            </Link>
            <Typography>Detil FR</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>No. FR: {activeFr?.number}</Typography>
                <Chip
                  label={frTableConfig[activeFr?.status]?.label}
                  color={frTableConfig[activeFr?.status]?.color}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(
                  activeFr?.createdAt ? new Date(activeFr?.createdAt) : Date.now(),
                  'eeee, dd/MM/yyyy HH:mm',
                  {
                    locale: id
                  }
                )}
              </Typography>
            </div>
            {activeFr?.status === FieldReportStatus.CREATED && (
              <Button variant='contained' color='error' onClick={handleCloseFieldReport}>
                Tutup Field Report
              </Button>
            )}
          </div>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <UnitDetail />
            </Grid>
            <Grid item xs={12}>
              <DataUnitDetail />
            </Grid>
            <Grid item xs={12}>
              <CreatedByCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={4}>
            {activeFr?.status === FieldReportStatus.CLOSED && (
              <Grid item xs={12}>
                <CloseReason reason={activeFr?.closeReason} />
              </Grid>
            )}
            <Grid item xs={12}>
              <LocationCard />
            </Grid>
            <Grid item xs={12}>
              <CardProblem />
            </Grid>
            <Grid item xs={12}>
              <CardSchedule />
            </Grid>
            <Grid item xs={12}>
              <ActivityLogCard />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  )
}

export default FrDetailPage
