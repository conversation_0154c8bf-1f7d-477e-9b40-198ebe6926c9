import { useAuth } from '@/contexts/AuthContext'
import {
  Card,
  CardContent,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useFrContext } from '../../context/FrContext'
import { mrPriorityOptions } from '@/pages/repair-and-maintenance/wo/create-wo-mr/config/util'

const CardProblem = () => {
  const { userProfile } = useAuth()
  const { activeFr } = useFrContext()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Masalah</Typography>
        </div>
        <div className='flex flex-col gap-6'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Prioritas
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <Typography className='flex gap-2 items-center'>
              <div
                className={`size-2 ${mrPriorityOptions?.find(p => p.value === `${activeFr?.priority ?? 3}`).color}`}
              />
              {`P${activeFr?.priority ?? 3}`}
            </Typography>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>Laporan</small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {activeFr?.initialReport}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Sympton / Gejala
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <div dangerouslySetInnerHTML={{ __html: activeFr?.symptom ?? '-' }} />
          </div>

          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>Penyebab</small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {activeFr?.cause ?? '-'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CardProblem
