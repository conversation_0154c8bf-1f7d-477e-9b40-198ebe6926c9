import { Card, CardContent, Typography } from '@mui/material'
import { useFrContext } from '../../context/FrContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const CardSchedule = () => {
  const { activeFr } = useFrContext()

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Schedule</Typography>
        </div>
        <div className='flex flex-col gap-6'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                <PERSON>gal Pen<PERSON>dwalan
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 pb-[3px] flex flex-col gap-0 items-start relative bg-transparent' />
            <p className=''>
              {activeFr?.scheduleDate ? (
                <p className='tracking-[0.2px] leading-6 text-base text-googlePlus'>
                  <i className='clock mr-2' />
                  {activeFr?.scheduleDate
                    ? formatDate(new Date(activeFr?.scheduleDate), 'eeee, dd/MM/yyyy', { locale: id })
                    : '-'}
                </p>
              ) : (
                '-'
              )}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default CardSchedule
