import DebouncedInput from '@/components/DebounceInput'
import MobileDropDown from '@/components/layout/shared/components/MobileDropDown'
import { Box, Button, Card, IconButton, Typography } from '@mui/material'
import { memo, useState, useCallback, MouseEvent, useMemo, useEffect } from 'react'
import { useFrContext } from '../../context/FrContext'
import { Link } from 'react-router-dom'
import { tableColumns } from '../config/table'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import Permission from '@/core/components/Permission'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { FieldReportStatus } from '@/types/frTypes'
import { useAuth } from '@/contexts/AuthContext'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { UNIT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { mrPriorityOptions } from '@/pages/material-request/create/config/enum'
import { frStatusOptions } from '../config/utils'

const FrList = () => {
  const { departmentList, ownSiteList, userProfile } = useAuth()
  const {
    isMobile,
    navigate,
    setSelectedFrId,
    frList: { items, totalItems, totalPages },
    frParams,
    setFrParams,
    setPartialFrParams
  } = useFrContext()

  const { search, page, departmentId, siteIds, unitId, priority, startDate, endDate, status, destinationSiteIds } =
    frParams

  const [searchExtend, setSearchExtend] = useState<boolean>(false)
  const [actionBtn, setBtn] = useState<boolean>(false)
  const [addAnchorEl, setAddAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(addAnchorEl)

  const [importDialogOpen, setImportDialogOpen] = useState(false)
  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const { data: unitList } = useQuery({
    queryKey: [UNIT_LIST_QUERY_KEY],
    queryFn: async () => (await CompanyQueryMethods.getUnitList({ limit: Number.MAX_SAFE_INTEGER })).items,
    placeholderData: []
  })

  const tableOptions = useMemo(
    () => ({
      data: items ?? [],
      columns: tableColumns({
        detail: item => {
          setSelectedFrId(item.id)
          navigate(`created/${item.id}`)
        }
      }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: page - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items, totalPages, page]
  )

  const table = useReactTable<any>(tableOptions)

  const handleAddClose = () => {
    setAddAnchorEl(null)
  }

  const handleAddClick = useCallback((event: MouseEvent<HTMLButtonElement>) => {
    setAddAnchorEl(event.currentTarget)
    setBtn(false)
  }, [])

  const onFilterChanged = ({ date, status, unit, priority, site, department }: FilterValues) => {
    console.log(site)
    setFrParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        status: status.length > 0 ? (status[0] as FieldReportStatus) : undefined,
        unitId: unit.length > 0 ? unit[0] : undefined,
        priority: priority.length > 0 ? priority[0] : undefined,
        destinationSiteIds: site.length > 0 ? site[0] : undefined,
        departmentId: department.length > 0 ? department[0] : undefined
      }
    })
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      status: {
        options: frStatusOptions,
        values: status ? [status] : []
      },
      unit: {
        options: unitList.map(unit => ({ value: unit.id, label: `${unit.number} - ${unit.brandName}` })),
        values: unitId ? [unitId] : []
      },
      priority: {
        options: mrPriorityOptions,
        values: priority ? [priority] : []
      },
      site: {
        options: userProfile?.sites
          ?.filter(site => site.type === 'WORKSHOP')
          .map(site => ({
            value: site.id,
            label: site.name
          })),
        values: destinationSiteIds ? [destinationSiteIds] : []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: departmentId ? [departmentId] : []
      }
    })
  }, [frParams])

  const actionButton = (
    <></>
    // <Button
    //   color='secondary'
    //   variant='outlined'
    //   startIcon={<i className='ri-upload-2-line' />}
    //   className='is-full sm:is-auto'
    // >
    //   Ekspor
    // </Button>
  )

  return (
    <>
      <Card>
        <div className='flex justify-between gap-4 p-5 flex-row items-start sm:flex-row sm:items-center'>
          {searchExtend ? (
            <div className='flex gap-4 items-center is-full flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setFrParams(prev => ({ ...prev, page: 1, search: value as string }))}
                onBlur={() => setSearchExtend(false)}
                placeholder='Cari'
                className='is-full'
              />
            </div>
          ) : !isMobile ? (
            <div className='flex gap-4 items-center is-full sm:is-auto flex-col sm:flex-row'>
              <DebouncedInput
                value={search}
                onChange={value => setFrParams(prev => ({ ...prev, page: 1, search: value as string }))}
                placeholder='Cari'
                className='is-full sm:is-auto'
              />
              <FilterGroupDialog
                config={filterGroupConfig}
                onFilterApplied={onFilterChanged}
                onRemoveFilter={onFilterChanged}
              />
            </div>
          ) : (
            <IconButton onClick={() => setSearchExtend(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
              <i className='ri-search-line' />
            </IconButton>
          )}
          {!searchExtend && (
            <div className='flex items-center justify-end md:justify-between gap-x-4 max-sm:gap-y-4 is-full flex-row sm:is-auto'>
              {!isMobile ? (
                actionButton
              ) : (
                <IconButton onClick={() => setBtn(true)} className='bg-[#4C4E640D]/5 rounded-[8px]'>
                  <i className='pepicons-pop--dots-y' />
                </IconButton>
              )}
              <Link to='/fr/new-fr' replace>
                <Permission permission={['field-report.create', 'field-report.update']}>
                  <Button variant='contained' className='is-full sm:is-auto' color='primary'>
                    Buat FR
                  </Button>
                </Permission>
              </Link>
            </div>
          )}
        </div>
        <Table
          table={table}
          emptyLabel={
            <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
              <Typography> Belum ada Data</Typography>
              <Typography className='text-sm text-gray-400'>Semua data FR akan ditampilkan di sini</Typography>
            </td>
          }
          onRowsPerPageChange={pageSize => {
            if (pageSize > totalItems) {
              setFrParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
            } else {
              setPartialFrParams('limit', pageSize)

              const maxPage = Math.ceil(totalItems / pageSize)
              if (page > maxPage) {
                setFrParams(prev => ({ ...prev, page: maxPage }))
              }
            }
          }}
          onPageChange={pageIndex => setPartialFrParams('page', pageIndex)}
        />
      </Card>
      <MobileDropDown className='z-1' open={actionBtn} onClose={() => setBtn(false)} onOpen={() => setBtn(true)}>
        <Typography sx={{ marginTop: 2 }} align='center' variant='h5'>
          Action
        </Typography>
        <Box className='flex gap-2 p-4 pb-2 flex-col'>{actionButton}</Box>
      </MobileDropDown>
    </>
  )
}

export default memo(FrList)
