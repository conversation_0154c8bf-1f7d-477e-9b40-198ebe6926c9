import { createColumnHelper } from '@tanstack/react-table'
import { Chip, IconButton, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { FrType } from '@/types/frTypes'
import truncateString from '@/core/utils/truncate'
import { WoStatus } from '@/types/woTypes'
import { srPriorityOptions } from '@/pages/service-requisition/list/config/utils'
import { ServiceRequisitionPriority } from '@/types/serviceRequisitionsTypes'

const columnHelper = createColumnHelper<FrType>()

type RowAction = {
  detail: (fr: FrType) => void
}

export const woTableConfig = (status: string): { color: string; label: string } => {
  switch (status) {
    case WoStatus.ACTIVE:
      return {
        color: 'success',
        label: 'Aktif'
      }
    case WoStatus.COMPLETED:
      return {
        color: 'info',
        label: 'Selesai'
      }
    case WoStatus.PRE_RELEASED:
      return {
        color: 'warning',
        label: 'Pre Release diajukan'
      }
    case WoStatus.READY_TO_RELEASE:
      return {
        color: 'warning',
        label: 'Siap Diambil'
      }
    default:
      return {
        color: 'default',
        label: status
      }
  }
}

export const frTableConfig = {
  CREATED: {
    color: 'default',
    label: 'Dibuat'
  },
  PROCESSED: {
    color: 'success',
    label: 'Diproses'
  },
  CLOSED: {
    color: 'error',
    label: 'Ditutup'
  },
  WAITING_PROCESS: {
    color: 'warning',
    label: 'Menunggu'
  }
}

export const tableColumns = (rowAction: RowAction) => [
  columnHelper.accessor('number', {
    header: 'No. FR',
    cell: ({ row }) => (
      <Typography
        color='primary'
        className='cursor-pointer'
        role='button'
        onClick={() => rowAction.detail(row.original)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ row }) => {
      return (
        <Chip
          variant='tonal'
          size='small'
          label={frTableConfig[row.original.status]?.label}
          color={frTableConfig[row.original.status]?.color}
        />
      )
    }
  }),
  columnHelper.accessor('unit.number', {
    header: 'KODE UNIT'
  }),
  columnHelper.accessor('scheduleDate', {
    header: 'TANGGAL SCHEDULE',
    cell: ({ row }) => {
      return row.original.scheduleDate
        ? formatDate(new Date(row.original.scheduleDate), 'dd/MM/yyyy', { locale: id })
        : '-'
    }
  }),
  columnHelper.accessor('destinationSite.name', {
    header: 'WORKSHOP'
  }),
  columnHelper.accessor('priority', {
    header: 'Prioritas',
    cell: ({ row }) => {
      const priority = srPriorityOptions.find(
        option => option.value === String(row.original.priority ?? ServiceRequisitionPriority.P4)
      )
      return priority ? (
        <div className='flex items-center gap-2'>
          <div className={`size-2 ${priority.color}`} />
          <Typography>{priority.label}</Typography>
        </div>
      ) : (
        '-'
      )
    }
  }),
  columnHelper.accessor('createdAt', {
    header: 'TANGGAL DIBUAT',
    cell: ({ row }) => {
      return formatDate(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id })
    }
  }),
  columnHelper.display({
    id: 'actions',
    header: 'ACTION',
    cell: ({ row }) => {
      return (
        <div className='flex items-center gap-0.5'>
          <IconButton onClick={() => rowAction.detail(row.original)} className='flex items-center gap-2'>
            <i className='ri-eye-line text-textSecondary' />
          </IconButton>
        </div>
      )
    }
  })
]
