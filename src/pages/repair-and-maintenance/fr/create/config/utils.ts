export const hoursToMinutes = (hours: number): [number, number] => {
  if (typeof hours === 'number') {
    const wholeHours: number = Math.floor(hours)
    const decimalHours: number = hours - wholeHours

    const minutesFromDecimalHours: number = Math.round(decimalHours * 60)

    return [wholeHours, minutesFromDecimalHours]
  } else {
    throw new Error('Input must be a number.')
  }
}

export const minutesToHours = (hours: number, minutes: number): number => {
  if (typeof hours !== 'number' || typeof minutes !== 'number') {
    throw new Error('Both hours and minutes must be numbers.')
  }

  const totalHours: number = hours + minutes / 60
  return totalHours
}
