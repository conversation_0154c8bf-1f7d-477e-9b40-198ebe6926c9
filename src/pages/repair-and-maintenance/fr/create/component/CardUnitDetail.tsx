import NumberField from '@/components/numeric/NumberField'
import { <PERSON><PERSON>, Card, CardContent, Grid, TextField, Typography } from '@mui/material'
import { useFrContext } from '../../context/FrContext'
import { Controller, useFormContext } from 'react-hook-form'
import { FrDtoType } from '../config/schema'

const DataUnitDetail = () => {
  const { selectedUnit } = useFrContext()
  const { control } = useFormContext<FrDtoType>()

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Data Unit</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='unitKm'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <>
                  <TextField
                    error={!!error}
                    fullWidth
                    label='KM'
                    value={value}
                    onChange={onChange}
                    InputProps={{
                      endAdornment: 'km',
                      inputComponent: NumberField as any
                    }}
                  />
                  {!!error && error?.message}
                </>
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='unitHm'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  fullWidth
                  label='HM'
                  value={value}
                  onChange={onChange}
                  InputProps={{
                    endAdornment: 'jam',
                    inputComponent: NumberField as any
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='unitMnt'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  fullWidth
                  label='MNT'
                  value={value}
                  onChange={onChange}
                  InputProps={{
                    endAdornment: 'mnt',
                    inputComponent: NumberField as any
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default DataUnitDetail
