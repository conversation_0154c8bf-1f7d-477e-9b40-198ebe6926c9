import { useAuth } from '@/contexts/AuthContext'
import {
  Card,
  CardContent,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { FrDtoType } from '../config/schema'
import { useMemo } from 'react'
import { mrPriorityOptions } from '@/pages/repair-and-maintenance/wo/create-wo-mr/config/util'

const CardProblem = () => {
  const { userProfile } = useAuth()
  const { control } = useFormContext<FrDtoType>()

  const items = useMemo(() => userProfile?.sites?.filter(site => site.type === 'WORKSHOP') ?? [], [userProfile])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil <PERSON></Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='priority'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <FormControl fullWidth>
                  <InputLabel id='priority-select'>Prioritas</InputLabel>
                  <Select
                    fullWidth
                    id='select-priority'
                    value={String(value)}
                    onChange={e => onChange(+e.target.value)}
                    label='Prioritas'
                    size='medium'
                    labelId='priority-select'
                    inputProps={{ placeholder: 'Prioritas' }}
                    defaultValue=''
                    error={!!error}
                  >
                    {mrPriorityOptions?.map(priority => (
                      <MenuItem key={priority.value} value={priority.value}>
                        <div className='flex items-center gap-2'>
                          <div className={`size-2 ${priority.color}`} />
                          {priority.label}
                        </div>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='initialReport'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  label='Laporan Awal'
                  value={value}
                  onChange={onChange}
                  fullWidth
                  multiline
                  rows={3}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='symptom'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  label='Sympton/Gejala (Opsional)'
                  value={value}
                  onChange={onChange}
                  fullWidth
                  multiline
                  rows={3}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='cause'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  error={!!error}
                  label='Penyebab (Opsional)'
                  value={value}
                  onChange={onChange}
                  fullWidth
                  multiline
                  rows={3}
                />
              )}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default CardProblem
