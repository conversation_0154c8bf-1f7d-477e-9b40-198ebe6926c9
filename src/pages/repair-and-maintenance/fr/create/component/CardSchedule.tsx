import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import {
  Card,
  CardContent,
  FormControl,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'
import { FrDtoType } from '../config/schema'
import { toDate } from 'date-fns'

const CardSchedule = () => {
  const { control } = useFormContext<FrDtoType>()
  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Tambah Schedule (Opsional)</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='scheduleDate'
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <AppReactDatepicker
                  boxProps={{ className: 'is-full' }}
                  selected={value ? toDate(value) : undefined}
                  onChange={(date: Date) => onChange(date.toISOString())}
                  dateFormat='eeee dd/MM/yyyy'
                  minDate={new Date()}
                  customInput={
                    <TextField fullWidth label='Tanggal Penjadwalan' placeholder='Pilih Tanggal' className='flex-1' />
                  }
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel id='role-select'>Pilih Pengingat</InputLabel>
              <Controller
                control={control}
                name='scheduleReminderType'
                render={({ field: { value, onChange }, fieldState: { error } }) => (
                  <Select
                    fullWidth
                    id='select-siteId'
                    value={value}
                    onChange={e => onChange(e.target.value)}
                    label='Pilih Pengingat'
                    labelId='siteId-select'
                    inputProps={{ placeholder: 'Pilih Pengingat' }}
                    defaultValue=''
                  >
                    {['ONE_WEEK_BEFORE'].map((date, idx) => (
                      <MenuItem key={idx} value={date}>
                        <ListItemText>{date.replaceAll('_', ' ')}</ListItemText>
                      </MenuItem>
                    ))}
                  </Select>
                )}
              />
            </FormControl>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default CardSchedule
