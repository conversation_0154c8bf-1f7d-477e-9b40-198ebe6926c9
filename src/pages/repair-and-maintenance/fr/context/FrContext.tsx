import * as Sentry from '@sentry/react'
import { defaultListData } from '@/api/queryClient'
import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import { useMenu } from '@/components/menu/contexts/menuContext'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { UnitType } from '@/types/companyTypes'
import { ListParams } from '@/types/payload'
import { useQuery } from '@tanstack/react-query'
import React, { useEffect, useState } from 'react'
import { NavigateFunction, useNavigate, useParams } from 'react-router-dom'
import { FrDtoType } from '../create/config/schema'
import { useCloseFieldReport, useCreateFr } from '@/api/services/rnm/mutation'
import { toast } from 'react-toastify'
import { FrLogType, FrParams, FrType } from '@/types/frTypes'
import RnMQueryMethods from '@/api/services/rnm/query'
import { minutesToHours } from '../create/config/utils'
import { FR_DETAIL_LOGS_KEY, FR_DETAIL_QUERY_KEY, FR_LIST_QUERY_KEY } from '@/api/services/rnm/service'
import { useAuth } from '@/contexts/AuthContext'
import CloseFRDialog, { CloseFrInput } from '@/components/dialogs/close-field-report-dialog'

type FrContextProps = {
  isMobile: boolean
  frParams: FrParams
  setPartialFrParams: (fieldName: keyof FrParams, value: any) => void
  setFrParams: React.Dispatch<React.SetStateAction<FrParams>>
  frList: ListResponse<FrType>
  navigate: NavigateFunction
  selectedFrId: string | undefined
  setSelectedFrId: React.Dispatch<React.SetStateAction<string | undefined>>
  activeFr: FrType
  selectedUnit: UnitType
  setSelectedUnit: React.Dispatch<React.SetStateAction<UnitType>>
  onSubmitFieldReport: (data: FrDtoType) => void
  createFrLoading: boolean
  frLogs: ListResponse<FrLogType>
  handleCloseFieldReport: () => void
}

const FrContext = React.createContext<FrContextProps>({} as FrContextProps)

export const useFrContext = () => {
  const context = React.useContext(FrContext)
  if (context === undefined) {
    throw new Error('useFrContext must be used within a FrProvider')
  }
  return context
}

export const FrProvider = ({ children }) => {
  const { isMobile } = useMobileScreen()
  const navigate = useNavigate()
  const params = useParams()
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const [dialogCloseFr, setDialogCloseFr] = useState<boolean>(false)
  const [frParams, setPartialFrParams, setFrParams] = usePartialState<FrParams>({
    limit: 10,
    page: 1,
    siteIds: userProfile?.sites?.map(site => site.id).join(',')
  })
  const [selectedUnit, setSelectedUnit] = useState<UnitType | null>(null)
  const [selectedFrId, setSelectedFrId] = useState<string | undefined>(params?.frId)
  const [activeFr, setActiveFr] = useState<FrType | null>(null)

  const { data: frList, refetch: refetchFrList } = useQuery({
    queryKey: [FR_LIST_QUERY_KEY, JSON.stringify(frParams)],
    queryFn: () => {
      return RnMQueryMethods.getFrList(frParams)
    },
    placeholderData: defaultListData as ListResponse<FrType>
  })

  const { data: frDetail, refetch: refetchFrDetail } = useQuery({
    enabled: !!selectedFrId,
    queryKey: [FR_DETAIL_QUERY_KEY, selectedFrId],
    queryFn: () => RnMQueryMethods.getFrDetail(selectedFrId)
  })

  const { data: frLogs, refetch: refetchFrLogs } = useQuery({
    enabled: !!selectedFrId,
    queryKey: [FR_DETAIL_LOGS_KEY, 'FR_CONTEXT', selectedFrId],
    queryFn: () => RnMQueryMethods.getFrDetailLogs(selectedFrId),
    placeholderData: defaultListData as ListResponse<FrLogType>
  })

  const { mutate: createFrMutate, isLoading: createFrLoading } = useCreateFr()
  const { mutate: closeFrMutate, isLoading: closeFrLoading } = useCloseFieldReport()

  const onSubmitFieldReport = (data: FrDtoType) => {
    Sentry.captureMessage(`Submit Field Report: ${JSON.stringify(data)}`)
    setConfirmState({
      open: true,
      title: 'Buat FR',
      content:
        'Apakah kamu yakin akan menambahkan FR untuk unit ini? Pastikan semua detil yang kamu masukkan sudah benar',
      confirmText: 'Buat FR',
      onConfirm: () => {
        createFrMutate(
          {
            ...data,
            unitHm: minutesToHours(data.unitHm, data.unitMnt),
            unitMnt: undefined
          },
          {
            onSuccess: response => {
              const newFrId = response.data.id
              toast.success('FR Berhasil dibuat')
              setSelectedUnit(null)
              refetchFrList()
              setTimeout(() => {
                navigate(`/fr/created/${newFrId}`)
              }, 1000)
            }
          }
        )
      }
    })
  }

  const onCloseFieldReport = (dto: CloseFrInput) => {
    closeFrMutate(
      {
        frId: selectedFrId,
        closeReason: dto.closeReason
      },
      {
        onSuccess: () => {
          toast.success('Field Report berhasil ditutup')
          setDialogCloseFr(false)
          refetchFrList()
          refetchFrDetail()
          refetchFrLogs()
        }
      }
    )
  }

  const handleCloseFieldReport = () => {
    setDialogCloseFr(true)
  }

  useEffect(() => {
    if (params?.frId) setSelectedFrId(params.frId)
  }, [params])

  useEffect(() => {
    if (selectedFrId) {
      if (frDetail) {
        setActiveFr(frDetail)
        return
      }
      setActiveFr(frList?.items?.find(fr => fr.id === selectedFrId))
    } else {
      setActiveFr(null)
    }
  }, [selectedFrId, frDetail])

  const value = {
    isMobile,
    frParams,
    setPartialFrParams,
    setFrParams,
    frList,
    navigate,
    selectedFrId,
    setSelectedFrId,
    activeFr,
    selectedUnit,
    setSelectedUnit,
    onSubmitFieldReport,
    handleCloseFieldReport,
    createFrLoading,
    frLogs
  }
  return (
    <FrContext.Provider value={value}>
      <CloseFRDialog
        loading={closeFrLoading}
        open={dialogCloseFr}
        setOpen={setDialogCloseFr}
        onCloseFrHandler={onCloseFieldReport}
      />
      {children}
    </FrContext.Provider>
  )
}
