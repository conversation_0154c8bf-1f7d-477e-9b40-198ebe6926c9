// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import { useMenu } from '@/components/menu/contexts/menuContext'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useUploadImage } from '@/api/services/file/mutation'
import { useAuth } from '@/contexts/AuthContext'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import Table from '@/components/table'
import { WarehouseItemType } from '@/types/appTypes'
import { tableColumns } from '../table'
import { MtItemType } from '@/pages/material-borrow/config/types'
import { useDeleteMtItem, useUpdateMtItem } from '@/api/services/mt/mutation'
import { useMb } from '@/pages/material-borrow/context/MbContext'
import { isNullOrUndefined } from '@/utils/helper'

const ItemListCard = () => {
  const { setConfirmState } = useMenu()
  const { mbData, fetchMbData, fetchLogList, canRemove } = useMb()
  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { isLoading: deleteItemLoading } = useDeleteMtItem()
  const { mutate: updateItemMutate, isLoading: updateItemLoading } = useUpdateMtItem()

  const [mbItems, setMbItems] = useState<MtItemType[]>([])

  const isLoading = uploadLoading || deleteItemLoading || updateItemLoading

  const [{ open: addItemOpen, selectedItem }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as MtItemType
  })

  const handleDeleteItem = (mbItem: MtItemType) => {
    setConfirmState({
      open: true,
      title: 'Hapus Barang',
      content: 'Apakah kamu yakin ingin menghapus barang ini?',
      onConfirm: () => {
        updateItemMutate(
          {
            mtId: mbData.id,
            payload: {
              id: mbItem.id,
              itemId: mbItem.itemId,
              quantity: 0,
              quantityUnit: mbItem.quantityUnit,
              note: mbItem.note,
              largeUnitQuantity: mbItem.largeUnitQuantity,
              isLargeUnit: mbItem.isLargeUnit,
              ...(mbItem.unitId && { unitId: mbItem.unitId }),
              ...(!isNullOrUndefined(mbItem?.unitKm) && { unitKm: mbItem.unitKm }),
              ...(!isNullOrUndefined(mbItem?.unitHm) && { unitHm: mbItem.unitHm })
              // images: [
              //   ...(mbItem.images ?? [])
              //     .filter(image => !image.fileName && !!image.uploadId)
              //     .map(image => ({
              //       uploadId: image.uploadId
              //     }))
              // ]
            }
          },
          {
            onSuccess: () => {
              toast.success('Barang berhasil dihapus')
              fetchMbData()
              fetchLogList()
            }
          }
        )
      },
      confirmText: 'Hapus',
      confirmColor: 'error'
    })
  }

  const handleUpdateItem = (itemData: WarehouseItemType) => {
    Promise.all(
      (itemData.images ?? [])
        .filter(item => !!item.fileName)
        .map(item =>
          uploadMutate({
            fieldName: `item_image_${itemData.itemId}`,
            file: item.content,
            scope: 'public-image',
            fileName: item.fileName
          })
        )
    )
      .then(() => {
        updateItemMutate(
          {
            mtId: mbData.id,
            payload: {
              id: selectedItem?.id,
              itemId: itemData.itemId,
              quantity: itemData.quantity,
              quantityUnit: itemData.quantityUnit,
              note: itemData.note,
              isLargeUnit: itemData.isLargeUnit,
              largeUnitQuantity: itemData.largeUnitQuantity,
              ...(itemData.unitId && { unitId: itemData.unitId }),
              ...(!isNullOrUndefined(itemData?.unitKm) && { unitKm: itemData.unitKm }),
              ...(!isNullOrUndefined(itemData?.unitHm) && { unitHm: itemData.unitHm })
              // images: [
              //   ...(itemData.images ?? [])
              //     .filter(image => !image.fileName && !!image.uploadId)
              //     .map(image => ({
              //       uploadId: image.uploadId
              //     })),
              //   ...uploadIds
              // ]
            }
          },
          {
            onSuccess: () => {
              toast.success('Data barang berhasil diubah')
              fetchMbData()
              fetchLogList()
              setAddItemModalState({
                open: false,
                selectedItem: undefined
              })
            }
          }
        )
      })
      .catch(error => {
        const message = error.response?.data?.message
        if (message) {
          toast.error(message)
        } else {
          toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
        }
      })
  }

  const table = useReactTable({
    data: mbItems,
    columns: tableColumns(
      {
        onEdit: itemData => {
          setAddItemModalState({ open: true, selectedItem: itemData })
        },
        onRemove: handleDeleteItem
      },
      mbData?.requesterSiteId,
      canRemove
    ),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    setMbItems(mbData?.items ?? [])
  }, [mbData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Barang</Typography>
          </div>
          <div className='mb-4'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan barang yang ingin dimasukkan dalam Material Borrow ini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? undefined : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          withoutUnit
          canUpdateAll={false}
          isLoading={isLoading}
          currentItem={selectedItem}
          onSubmit={handleUpdateItem}
          siteId={mbData?.siteId}
        />
      )}
    </>
  )
}

export default ItemListCard
