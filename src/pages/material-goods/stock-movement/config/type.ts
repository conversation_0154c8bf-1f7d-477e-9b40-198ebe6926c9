import { CodeNameType } from '@/types/common'
import { DepartmentType, ItemType } from '@/types/companyTypes'
import { ListParams, UserIdPayload } from '@/types/payload'
import { ApproverType, UserOutlineType } from '@/types/userTypes'
import { SmUserStatus } from './enum'
import { WarehouseDataType, WarehouseLogType } from '@/types/appTypes'
import { SjType } from '@/pages/material-transfer/config/types'

export type StockMovementPayload = {
  originSiteId?: string
  destinationSiteId?: string
  items?: SmItemType[]
  approvals?: UserIdPayload[]
  note?: string
  purchaseOrderId?: string
  incomingMaterialId?: string
  purpose?: string
}

export type SmItemType = {
  id?: number
  isActive?: boolean
  stockMovementItemId?: number
  itemId?: string
  systemStock?: number
  quantity?: number
  quantityUnit?: string
  largeUnitQuantity?: number
  note?: string
  item?: ItemType
  isLargeUnit?: boolean
  remainingQuantity?: number
  receivedQuantity?: number
  receiptNote?: string
  receivedQuantityUnit?: string
}

export type StockMovementType = {
  id?: string
  number?: string
  originSiteId?: string
  destinationSiteId?: string
  itemsCount?: number
  approvalsCount?: number
  remainingQuantity?: number
  note?: string
  cancelationNote?: string
  status?: string
  companyId?: string
  parentCompanyId?: string
  departmentId?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  items?: SmItemType[]
  approvals?: ApproverType[]
  department?: DepartmentType
  originSite?: CodeNameType
  destinationSite?: CodeNameType
  receipts?: string[]
  createdByUser?: UserOutlineType
  purchaseOrderId?: string
  deliveryNotesCount?: number
  receivedQuantity?: number
  purpose?: string
} & WarehouseDataType

export type SmParams = {
  hasRemainingQuantity?: boolean
  destinationSiteId?: string
  originSiteId?: string
  destinationSiteIds?: string
  originSiteIds?: string
  isHasDeliveryNote?: boolean
} & ListParams

export type SmApprovalPayload = {
  smId?: string
  approvalId?: number
  userId?: string
  note?: string
  status?: SmUserStatus
  isRead?: boolean
  takenBy?: string
  takenImageUploadId?: string
}

export type SmReceiptType = {
  id: string
  itemsCount?: number
  deliveryNoteNumber?: string
  deliveryNoteUrl?: string
  deliveryNoteMimeType?: string
  note?: string
  stockMovementId?: string
  createdAt?: string
  updatedAt?: string
  createdBy?: string
  items?: SmItemType[]
  createdByUser?: UserOutlineType
}

export type SmReceiptPayload = {
  smId: string
  items: SmItemType[]
  deliveryNoteNumber?: string
  deliveryNoteUploadId?: string
  note?: string
}

export type SjReceiptPayload = {
  sjId?: string
  items?: SmItemType[]
  receiptNote?: string
}

export type SmSjType = {
  items?: SjItemType[]
} & SjType

export type SjItemType = {
  stockMovementItemId?: number
} & SmItemType

export type SjPayload = {
  mtId?: string
  type?: string
  items?: SjItemType[]
  note?: string
}

export type SmLogType = {
  id?: number
  type?: string
  status?: string
  changes?: string
  attachmentUrl?: string
  attachmentMimeType?: string
  stockMovementId?: string
  userId?: string
  stockMovementItemId?: number
  createdAt?: string
  user?: UserOutlineType
  stockMovementItem?: SmItemType
} & WarehouseLogType

export type SjReceiptType = {
  id?: string
  number?: string
  itemsCount?: number
  note?: string
  receiptNote?: string
  status?: string
  createdAt?: string
  updatedAt?: string
  receivedAt?: string
  createdBy?: string
  receivedBy?: string
  items?: SmItemType[]
  createdByUser?: UserOutlineType
  receivedByUser?: UserOutlineType
}

export type SjReceiptPayloadList = {
  payloads?: SjReceiptPayload[]
}

export enum StockMovementPurpose {
  FOR_RMA = 'FOR_RMA',
  NORMAL_MOVE = 'NORMAL_MOVE'
}
