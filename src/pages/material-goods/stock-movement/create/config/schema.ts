import { array, boolean, coerce, object, string, TypeOf } from 'zod'

export const smItemPayloadSchema = object({
  itemId: string({ message: 'Barang wajib dipilih' }),
  quantity: coerce.number().min(1, { message: '<PERSON><PERSON><PERSON> diisi' }),
  quantityUnit: string().optional().nullable(),
  largeUnitQuantity: coerce.number().optional().nullable()
})

export const smPayloadSchema = object({
  items: array(smItemPayloadSchema).min(1, { message: 'Barang wajib diisi' }),
  originSiteId: string({ message: 'Gudang asal wajib dipilih' }),
  destinationSiteId: string({ message: 'Gudang tujuan wajib dipilih' }),
  note: string().nullable().optional().nullable(),
  purchaseOrderId: string().optional().nullable(),
  incomingMaterialId: string().optional().nullable(),
  purpose: string().optional().nullable()
})

export type CreateSoInput = TypeOf<typeof smPayloadSchema>
