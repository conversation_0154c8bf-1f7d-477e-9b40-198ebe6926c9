import Grid from '@mui/material/Grid'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Typography } from '@mui/material'
import AddSmDataCard from './components/AddSmDataCard'
import { FormProvider, SubmitHandler, useForm, useWatch } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { smPayloadSchema } from './config/schema'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from '@/routes/hooks'
import { useSm } from '../context/SmContext'
import { useEffect } from 'react'
import LoadingButton from '@mui/lab/LoadingButton'
import { Link } from 'react-router-dom'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import TableCard from '../component/table'
import { StockMovementPayload, StockMovementPurpose } from '../config/type'
import ApprovalListCard from './components/ApprovalListCard'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { useQuery } from '@tanstack/react-query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import AddPoDataCard from './components/AddPoDataCard'

const CreateSmPage = () => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const { userProfile } = useAuth()
  const { showLoading, fetchSmList, createSmMutate, setSelectedSiteId } = useSm()
  const method = useForm<StockMovementPayload>({
    resolver: zodResolver(smPayloadSchema),
    mode: 'onChange',
    defaultValues: {
      originSiteId: userProfile?.sites?.[0]?.id ?? '',
      items: [],
      purpose: StockMovementPurpose.NORMAL_MOVE
    }
  })
  const { handleSubmit, setValue, control } = method

  const siteIdWatch = useWatch({
    control,
    name: 'originSiteId',
    defaultValue: ''
  })

  const { data: approverList } = useQuery({
    enabled: !!siteIdWatch && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, DefaultApprovalScope.StockMovement, siteIdWatch, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope: DefaultApprovalScope.StockMovement,
        siteId: siteIdWatch,
        departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  const onSubmitHandler: SubmitHandler<StockMovementPayload> = (formInput: StockMovementPayload) => {
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Tambah Pindah Barang',
      content: 'Apakah kamu yakin akan menambahkan permintaan pindah barang? Action ini tidak bisa diubah',
      confirmText: 'Tambah Pindah Barang',
      onConfirm: () => {
        createSmMutate(
          {
            ...formInput,
            approvals: approverList.map(approver => ({
              userId: approver.user?.id
            }))
          },
          {
            onSuccess: () => {
              toast.success('Pindah Barang berhasil disimpan')
              fetchSmList()
              router.replace('/mg/sm/list')
            }
          }
        )
      }
    })
  }

  useEffect(() => {
    if ((userProfile?.sites?.length ?? 0) > 0) {
      const siteId = userProfile?.sites?.[0]?.id
      setValue('originSiteId', siteId)
      setSelectedSiteId(prev => prev ?? siteId)
    }
  }, [userProfile])

  return (
    <FormProvider {...method}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Material/Barang</Typography>
            </Link>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Stok</Typography>
            </Link>
            <Link to='/mg/sm/list' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Pindah Barang</Typography>
            </Link>
            <Typography>Tambah Pindah Barang</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col max-sm:text-center'>
              <Typography variant='h4'>Tambah Pindah Barang</Typography>
              <Typography>Masukkan data material/barang yang akan dipidahkan ke gudang/warehouse lain</Typography>
            </div>
            <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
              <Button
                color='secondary'
                variant='outlined'
                disabled={showLoading}
                className='is-full sm:is-auto'
                onClick={() => router.replace('/mg/so')}
              >
                Batalkan
              </Button>
              <LoadingButton
                startIcon={<></>}
                loading={showLoading}
                variant='contained'
                className='is-full sm:is-auto'
                onClick={handleSubmit(onSubmitHandler)}
              >
                Simpan
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12} md={5}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <AddPoDataCard />
            </Grid>
            <Grid item xs={12}>
              <AddSmDataCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={7}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <TableCard />
            </Grid>
            <Grid item xs={12}>
              <ApprovalListCard approverList={approverList.map(approver => approver.user) ?? []} />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default CreateSmPage
