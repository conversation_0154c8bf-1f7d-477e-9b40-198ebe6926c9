import NumberField from '@/components/numeric/NumberField'
import { <PERSON><PERSON><PERSON>utton, TextField, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { Control, Controller, UseFormGetValues, useWatch } from 'react-hook-form'
import { SmItemType, StockMovementPayload } from '../../../config/type'
import React from 'react'

type SmItemTypeWithActions = SmItemType & {
  deleteAction?: string
}

type RowActionType = {
  delete: (idx: number, id: string) => void
}

const columnHelper = createColumnHelper<SmItemTypeWithActions>()

export const editableTableColumns = (
  control: Control<StockMovementPayload>,
  getValues: UseFormGetValues<StockMovementPayload>,
  rowAction: RowActionType,
  poId?: string
) => [
  columnHelper.accessor('item.name', {
    header: 'Nama Item',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.item?.name}</Typography>
        <Typography variant='caption'>
          {row.original.item?.brandName} - {row.original.item?.number}
        </Typography>
      </div>
    ),
    enableSorting: false
  }),
  columnHelper.accessor('systemStock', {
    header: 'Stok Tersedia',
    cell: ({ row }) => (
      <Typography>
        {row.original.systemStock} {row.original.item?.smallUnit}
      </Typography>
    ),
    enableSorting: false
  }),
  columnHelper.accessor('quantity', {
    header: 'Jumlah Pindah',
    cell: ({ row }) => {
      const { item } = row.original
      return (
        <Controller
          name={`items.${row.index}.quantity`}
          rules={{ required: true }}
          control={control}
          render={({ field, fieldState: { error } }) => {
            return (
              <TextField
                {...field}
                value={field.value ?? 0}
                className='w-24'
                variant='outlined'
                disabled={!!poId}
                size='small'
                InputProps={{
                  endAdornment: row.original.quantityUnit,
                  inputComponent: NumberField as any,
                  inputProps: {
                    isAllowed: ({ floatValue }) => floatValue <= row.original.systemStock || floatValue === undefined
                  }
                }}
                {...(error && { error: true, helperText: 'Wajib diisi.' })}
              />
            )
          }}
        />
      )
    },
    enableSorting: false
  }),
  columnHelper.accessor('deleteAction', {
    header: '',
    cell: ({ row }) => {
      return !poId ? (
        <IconButton size='medium' onClick={() => rowAction.delete(row.index, row.original.itemId)}>
          <i className='ic-baseline-delete-forever text-red-500' />
        </IconButton>
      ) : (
        <React.Fragment />
      )
    },
    enableSorting: false
  })
]

export const tableColumns = () => [
  columnHelper.accessor('item.name', {
    header: 'Nama Item',
    cell: ({ row }) => (
      <div className='flex flex-col'>
        <Typography>{row.original.item?.name}</Typography>
        <Typography variant='caption'>
          {row.original.item?.brandName} - {row.original.item?.number}
        </Typography>
      </div>
    )
  }),
  columnHelper.accessor('quantity', {
    header: 'Jumlah Pindah',
    cell: ({ row }) => (
      <Typography>
        {row.original.quantity} {row.original.quantityUnit}
      </Typography>
    )
  })
]
