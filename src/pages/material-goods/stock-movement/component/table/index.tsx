import Table from '@/components/table'
import LoadingButton from '@mui/lab/LoadingButton'
import { Card, CardContent, CardHeader, FormHelperText, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useSm } from '../../context/SmContext'
import { editableTableColumns, tableColumns } from './config/table'
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form'
import { useEffect, useMemo, useState } from 'react'
import { ItemType } from '@/types/companyTypes'
import AddGoodsDialog from '../add-goods-dialog'
import { SmItemType, StockMovementPayload } from '../../config/type'

const useFormBoilerplate = (enabled: boolean = true) => {
  if (!enabled) {
    return {
      form: { control: null, getValues: null, trigger: null, errors: null },
      array: { fields: null, append: null, remove: null, update: null }
    }
  }

  const {
    control,
    getValues,
    trigger,
    formState: { errors }
  } = useFormContext<StockMovementPayload>()
  const { fields, append, remove, update } = useFieldArray({
    control: control,
    name: 'items'
  })

  return { form: { control, getValues, trigger, errors }, array: { fields, append, remove, update } }
}

const TableCard = () => {
  const { selectedSmId, showLoading, smData } = useSm()
  const {
    form: { control, getValues, trigger, errors },
    array: { fields, append, remove, update }
  } = useFormBoilerplate(!selectedSmId)

  const poIdWatch = !selectedSmId
    ? useWatch({
        control,
        name: 'purchaseOrderId',
        defaultValue: ''
      })
    : ''

  const originSiteIdWatch = !selectedSmId
    ? useWatch({
        control,
        name: 'originSiteId',
        defaultValue: ''
      })
    : ''

  const [{ open: addItemOpen, selectedItem, selectedIndex }, setAddItemModalState] = useState({
    open: false,
    selectedItem: [] as SmItemType[],
    selectedIndex: []
  })

  const columns = useMemo(() => {
    if (selectedSmId) {
      return tableColumns()
    }
    return editableTableColumns(
      control,
      getValues,
      {
        delete: (idx: number, id: string) => {
          remove(idx)
        }
      },
      poIdWatch
    )
  }, [selectedSmId, control, poIdWatch])

  const initialPagingState = useMemo(() => {
    if (selectedSmId) {
      return { pagination: { pageSize: 10 } }
    }
    return undefined
  }, [selectedSmId])

  const table = useReactTable<SmItemType>({
    data: (selectedSmId ? smData?.items : fields) ?? [],
    columns: columns,
    initialState: initialPagingState,
    manualPagination: !!selectedSmId,
    rowCount: fields?.length,
    pageCount: 1,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    if (selectedItem.length === 0) {
      return
    }
    const tableItemIds = new Set(fields.map(item => item.itemId))
    const newItems = selectedItem.filter(pickedItem => !tableItemIds.has(pickedItem.itemId))
    append(newItems)
    trigger('items')
  }, [selectedItem])

  return (
    <Card>
      <CardHeader
        title='Material/Barang'
        action={
          !selectedSmId &&
          !poIdWatch && (
            <LoadingButton
              variant='outlined'
              onClick={() => {
                setAddItemModalState(current => ({
                  ...current,
                  open: true
                }))
              }}
            >
              Pilih Barang
            </LoadingButton>
          )
        }
      />
      <CardContent>
        <div className='shadow-sm rounded-lg'>
          <Table
            disablePagination={!selectedSmId}
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography variant='h5'>Belum ada material/barang</Typography>
                <Typography className='text-sm text-gray-400'>
                  Material/barang yang kamu pilih akan ditampilkan di sini
                </Typography>
              </td>
            }
          />
        </div>
        {(!!errors?.items?.message || !!errors?.items?.root) && (
          <FormHelperText className='mt-3 ml-0' error>
            Material/Barang wajib dipilih
          </FormHelperText>
        )}
        {addItemOpen && (
          <AddGoodsDialog
            open={addItemOpen}
            setOpen={open => {
              setAddItemModalState(current => ({
                ...current,
                open
              }))
            }}
            setData={data =>
              setAddItemModalState(current => {
                const mapped = data.map(
                  itemType =>
                    ({
                      systemStock: (!originSiteIdWatch
                        ? itemType.stock
                        : itemType.stocks?.filter(stock => stock.siteId === originSiteIdWatch)?.[0]?.stock) ?? 0,
                      itemId: itemType.id,
                      largeUnitQuantity: itemType.largeUnitQuantity,
                      quantityUnit: itemType.smallUnit,
                      item: {
                        ...itemType
                      }
                    }) as SmItemType
                )
                return {
                  ...current,
                  selectedItem: mapped
                }
              })
            }
            excludedData={fields}
            selectedSiteId={originSiteIdWatch}
          />
        )}
      </CardContent>
    </Card>
  )
}

export default TableCard
