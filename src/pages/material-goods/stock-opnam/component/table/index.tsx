import Table from '@/components/table'
import LoadingButton from '@mui/lab/LoadingButton'
import { <PERSON>, Card<PERSON>ontent, CardHeader, Typography } from '@mui/material'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { useSo } from '../../context/SoContext'
import { editableTableColumns, tableColumns } from './config/table'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { SoItemPayload, SoPayload } from '@/types/soTypes'
import { useEffect, useMemo, useState } from 'react'
import AddGoodsDialog from '@/components/dialogs/add-goods-dialog'
import { ItemType } from '@/types/companyTypes'
import { useUpdate, useUpdateEffect } from 'react-use'

const useFormBoilerplate = (enabled: boolean = true) => {
  if (!enabled) {
    return {
      form: { control: null, getValues: null, trigger: null },
      array: { fields: null, append: null, remove: null, update: null }
    }
  }

  const { control, getValues, trigger } = useFormContext<SoPayload>()
  const { fields, append, remove, update } = useFieldArray({
    control: control,
    name: 'items'
  })

  return { form: { control, getValues, trigger }, array: { fields, append, remove, update } }
}

const TableCard = () => {
  const {
    selectedSoId,
    selectedSiteId,
    showLoading,
    setSoItemParams,
    soItemListResponse: { items: soItemList, totalItems, totalPages }
  } = useSo()
  const {
    form: { control, getValues, trigger },
    array: { fields, append, remove, update }
  } = useFormBoilerplate(!selectedSoId)

  const [{ open: addItemOpen, selectedItem, selectedIndex }, setAddItemModalState] = useState({
    open: false,
    selectedItem: [] as SoItemPayload[],
    selectedIndex: []
  })

  const columns = useMemo(() => {
    if (selectedSoId) {
      return tableColumns()
    }
    return editableTableColumns(control, getValues, {
      delete: (idx: number, id: string) => {
        remove(idx)
      }
    })
  }, [selectedSoId, control])

  const initialPagingState = useMemo(() => {
    if (selectedSoId) {
      return { pagination: { pageSize: 10 } }
    }
    return undefined
  }, [selectedSoId])

  const table = useReactTable<SoItemPayload>({
    data: selectedSoId ? soItemList : fields ?? [],
    columns: columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    setSoItemParams({
      limit: 10,
      page: 1
    })
  }, [])

  useEffect(() => {
    if (selectedItem.length === 0) {
      return
    }
    const tableItemIds = new Set(fields.map(item => item.itemId))
    const newItems = selectedItem.filter(pickedItem => !tableItemIds.has(pickedItem.itemId))
    append(newItems)
  }, [selectedItem])

  useUpdateEffect(() => {
    if (selectedItem.length === 0) {
      return
    }
    setAddItemModalState(current => {
      return {
        ...current,
        selectedItem: []
      }
    })
    remove()
  }, [selectedSiteId])

  return (
    <Card>
      <CardHeader
        title='Material/Barang'
        action={
          !selectedSoId && (
            <LoadingButton
              variant='outlined'
              onClick={() => {
                setAddItemModalState(current => ({
                  ...current,
                  open: true
                }))
              }}
            >
              Pilih Barang
            </LoadingButton>
          )
        }
      />
      <CardContent>
        <div className='shadow-sm rounded-lg'>
          <Table
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                <Typography variant='h5'>Belum ada material/barang</Typography>
                <Typography className='text-sm text-gray-400'>
                  Material/barang yang kamu pilih akan ditampilkan di sini
                </Typography>
              </td>
            }
          />
        </div>
        {addItemOpen && (
          <AddGoodsDialog
            open={addItemOpen}
            setOpen={open => {
              setAddItemModalState(current => ({
                ...current,
                open
              }))
            }}
            setData={data =>
              setAddItemModalState(current => {
                const mapped = data.map(
                  itemType =>
                    ({
                      systemStock:
                        (!selectedSiteId
                          ? itemType?.stock
                          : itemType?.stocks?.filter(stock => stock.siteId === selectedSiteId)?.[0]?.totalStock) ?? 0,
                      relationedStock:
                        (!selectedSiteId
                          ? itemType?.stock
                          : (itemType?.stocks?.filter(stock => stock.siteId === selectedSiteId)?.[0]?.totalStock ?? 0) -
                            (itemType?.stocks?.filter(stock => stock.siteId === selectedSiteId)?.[0]?.stock ?? 0)) ?? 0,
                      itemId: itemType.id,
                      largeUnitQuantity: itemType.largeUnitQuantity,
                      item: {
                        ...itemType
                      }
                    }) as SoItemPayload
                )
                return {
                  ...current,
                  selectedItem: mapped
                }
              })
            }
            excludedData={fields}
          />
        )}
      </CardContent>
    </Card>
  )
}

export default TableCard
