import { DEFAULT_CATEGORY } from '@/data/default/category'
import { ItemType } from '@/types/companyTypes'
import { Checkbox, colors, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type ItemTypeWithAction = ItemType & {
  action?: string
}

type RowActionType = {
  showDetail: (id: string) => void
}

// Column Definitions
const columnHelper = createColumnHelper<ItemTypeWithAction>()

export const tableColumns = (rowAction: RowActionType, siteId?: string) => [
  columnHelper.accessor('number', {
    header: 'Kode Barang',
    cell: ({ row }) => (
      <Typography
        color={colors.green.A400}
        className='cursor-pointer'
        onClick={() => rowAction.showDetail(row.original.id)}
      >
        {row.original.number}
      </Typography>
    )
  }),
  columnHelper.accessor('name', {
    header: 'Nama Item',
    cell: ({ row }) => <Typography>{row.original.name}</Typography>
  }),
  columnHelper.accessor('brandName', {
    header: 'Merk Item',
    cell: ({ row }) => <Typography>{row.original.brandName}</Typography>
  }),
  columnHelper.accessor('category.name', {
    header: 'Kategori Item',
    cell: ({ row }) => <Typography>{row.original.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
  }),
  columnHelper.accessor('stock', {
    header: 'Total Stok',
    cell: ({ row }) => (
      <Typography>
        {siteId === 'all'
          ? row.original.stock
          : row.original.stocks.find(stock => stock.siteId === siteId)?.totalStock ?? 0}{' '}
        {row.original.smallUnit}
      </Typography>
    )
  }),
  columnHelper.accessor('stocks', {
    header: 'Lokasi',
    cell: ({ row }) => (
      <Typography>
        {siteId === 'all' ? '-' : row.original.stocks.find(stock => stock.siteId === siteId)?.note ?? 'N/A'}
      </Typography>
    )
  }),
  columnHelper.accessor('updatedAt', {
    header: 'Tanggal Diperbarui',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.updatedAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('action', {
    header: 'Action',
    cell: ({ row }) => (
      <div className='flex items-center gap-0.5'>
        <IconButton size='small' onClick={() => rowAction.showDetail(row.original.id)}>
          <i className='ri-eye-line text-textSecondary' />
        </IconButton>
      </div>
    ),
    enableSorting: false
  })
]

const goodsColumnHelper = createColumnHelper<ItemType>()

export const goodsTableColumns = (selectedSiteId?: string) => [
  {
    id: 'select-col',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllRowsSelected()}
        indeterminate={table.getIsSomeRowsSelected()}
        onChange={table.getToggleAllRowsSelectedHandler()} //or getToggleAllPageRowsSelectedHandler
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        disabled={!row.getCanSelect()}
        onChange={row.getToggleSelectedHandler()}
      />
    )
  },
  goodsColumnHelper.accessor('number', {
    header: 'Kode Barang',
    cell: ({ row }) => <Typography color={colors.green.A400}>{row.original.number}</Typography>
  }),
  goodsColumnHelper.accessor('name', {
    header: 'Nama Item',
    cell: ({ row }) => <Typography>{row.original.name}</Typography>
  }),
  goodsColumnHelper.accessor('brandName', {
    header: 'Merk Item',
    cell: ({ row }) => <Typography>{row.original.brandName}</Typography>
  }),
  goodsColumnHelper.accessor('category.name', {
    header: 'Kategori Item',
    cell: ({ row }) => <Typography>{row.original.category?.name ?? DEFAULT_CATEGORY.name}</Typography>
  }),
  goodsColumnHelper.accessor('stock', {
    header: 'Stok Tersedia',
    cell: ({ row }) => (
      <Typography>
        {(!selectedSiteId
          ? row.original.stock
          : row.original.stocks?.filter(stock => stock.siteId === selectedSiteId)?.[0]?.stock) ?? 0}{' '}
        {row.original.smallUnit}
      </Typography>
    )
  }),
]
