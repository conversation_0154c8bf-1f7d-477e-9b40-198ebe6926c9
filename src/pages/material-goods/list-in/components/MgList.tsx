import { useEffect, useState } from 'react'
import { Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useRouter } from '@/routes/hooks'
import { useMg } from '../context/MgContext'
import { tableColumns } from '../config/table'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { useAuth } from '@/contexts/AuthContext'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ProjectType } from '@/types/projectTypes'
import { useLocation } from 'react-router-dom'

const MgList = () => {
  const router = useRouter()
  const { state } = useLocation()
  const {
    poListResponse: { items: poList, totalItems, totalPages, limit: limitItems, page: pageItems },
    posParams,
    setPartialPosParams,
    setPosParams,
    setSelectedPoId
  } = useMg()

  const { departmentList, ownSiteList } = useAuth()

  const { page, search, startDate, endDate, siteIds, departmentId, projectId } = posParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const { data: projectList } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getProjectList({ limit: Number.MAX_SAFE_INTEGER }),
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  // TODO: MOVE THIS SHIT
  const table = useReactTable({
    data: poList,
    columns: tableColumns({
      showDetail: id => {
        setSelectedPoId(id)
        router.push(`/mg/in/${id}`)
      }
    }),
    initialState: {
      pagination: {
        pageSize: posParams.limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const onFilterChanged = ({ date, site, department, project }: FilterValues) => {
    setPosParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        siteIds: site.length > 0 ? site[0] : undefined,
        departmentId: department.length > 0 ? department[0] : undefined,
        projectId: project?.length > 0 ? project[0] : undefined
      }
    })
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      site: {
        options: ownSiteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: siteIds ? [siteIds] : []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: departmentId ? [departmentId] : []
      },
      ...(projectList?.items?.length > 0 && {
        project: {
          options: projectList?.items.map(project => ({ value: project.id, label: project.name })) ?? [],
          values: projectId ? [projectId] : []
        }
      })
    })
  }, [posParams, projectList])

  useEffect(() => {
    setPosParams({
      limit: 10,
      page: 1
    })
  }, [])

  useEffect(() => {
    if (state?.projectId) {
      setPosParams(prev => ({ ...prev, projectId: state.projectId }))
    }
  }, [state])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setPosParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari Barang Masuk'
            className='is-full sm:is-auto'
          />
          <FilterGroupDialog
            config={filterGroupConfig}
            onFilterApplied={onFilterChanged}
            onRemoveFilter={onFilterChanged}
          />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada barang</Typography>
            <Typography className='text-sm text-gray-400'>
              Cek semua barang yang masuk dari Vendor ke Stok Gudang
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setPosParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialPosParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setPosParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialPosParams('page', pageIndex)}
      />
    </Card>
  )
}

export default MgList
