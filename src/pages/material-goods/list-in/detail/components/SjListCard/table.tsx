import { ImItem, ImType } from '@/types/mgTypes'
import { Chip, IconButton, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type RowActionType = {
  detail: (item: ImType) => void
}

const columnHelper = createColumnHelper<ImType>()
const columnItemHelper = createColumnHelper<ImItem>()

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('deliveryNoteNumber', {
    header: 'NO. SJ'
  }),
  columnHelper.accessor('unbilledQuantity', {
    header: 'STATUS',
    cell: ({ row }) => (
      <Chip
        label={row.original?.unbilledQuantity > 0 ? 'Belum Tertagih' : 'Sudah Tertagih'}
        color={row.original?.unbilledQuantity > 0 ? 'warning' : 'success'}
        size='small'
        variant='tonal'
      />
    )
  }),
  columnHelper.accessor('site.name', {
    header: 'SITE DITERIMA'
  }),
  columnHelper.accessor('updatedAt', {
    header: 'TANGGAL TERIMA',
    cell: ({ row }) => (
      <Typography>{formatDate(row.original.updatedAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}</Typography>
    )
  }),
  columnHelper.accessor('id', {
    header: '',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.detail(row.original)} color='primary'>
          <i className='ri-arrow-right-s-line' />
        </IconButton>
      )
    }
  })
]

export const tableItemsColumns = () => [
  columnItemHelper.accessor('item.name', {
    header: 'NAMA ITEM'
  }),
  columnItemHelper.accessor('quantity', {
    header: 'QTY DITERIMA',
    cell: ({ row }) => {
      return (
        <Typography>
          {row.original.quantity} {row.original.quantityUnit}
        </Typography>
      )
    }
  }),
  columnItemHelper.accessor('item.number', {
    header: 'NOMOR SERI'
  }),
  columnItemHelper.accessor('note', {
    header: 'CATATAN',
    enableSorting: false,
    cell: ({ row }) => row.original.note ?? '-'
  })
]
