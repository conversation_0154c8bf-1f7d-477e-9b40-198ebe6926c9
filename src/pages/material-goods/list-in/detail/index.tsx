// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { B<PERSON>crumbs, Button, Chip, Typography } from '@mui/material'

import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { Link } from 'react-router-dom'
import ItemListDetailCard from '@/pages/material-request/detail/components/ItemListDetailCard'
import AdditionalInfoCard from '@/pages/material-request/detail/components/AdditionalInfoCard'
import ActivityLogCard from './components/ActivityLogCard'
import PrQueryMethods, { PR_QUERY_KEY } from '@/api/services/pr/query'
import { useQuery } from '@tanstack/react-query'
import { useMg } from '../context/MgContext'
import MgInTableCard from './components/mg-in-table'
import ImLogCard from './components/ImLogCard'
import DocNumberCard from '@/pages/material-request/mr-in/component/DocNumberCard'
import SjListCard from './components/SjListCard'

const PoDetailPage = () => {
  const { poData, logList, imList } = useMg()

  const { data: prData } = useQuery({
    enabled: !!poData?.purchaseRequisitionId,
    queryKey: [PR_QUERY_KEY, poData?.purchaseRequisitionId],
    queryFn: () => PrQueryMethods.getPr(poData?.purchaseRequisitionId)
  })

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs aria-label='breadcrumb'>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Material/Barang</Typography>
            </Link>
            <Link to='/mg/in' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Barang Masuk</Typography>
            </Link>
            <Typography>Detil PO</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row max-sm:items-center gap-2'>
            <div className='flex flex-col'>
              <div className='flex items-center gap-2 flex-col sm:flex-row max-sm:text-center'>
                <Typography variant='h4'>NO. PO: {poData?.number}</Typography>
                <Chip
                  label={poData?.closedAt ? 'Semua barang sudah diterima' : 'Barang belum diterima semua'}
                  color={poData?.closedAt ? 'success' : 'secondary'}
                  variant='tonal'
                  size='small'
                />
              </div>
              <Typography className='max-sm:text-center max-sm:mt-2'>
                {formatDate(poData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy, HH:mm', { locale: id })}
              </Typography>
            </div>
            <div className='flex gap-2 is-full sm:is-auto'>
              {/* <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ri-upload-2-line' />}
                className='is-full sm:is-auto'
              >
                Ekspor
              </Button>
              <Button
                color='secondary'
                variant='outlined'
                startIcon={<i className='ic-outline-local-printshop' />}
                className='is-full sm:is-auto'
              >
                Cetak
              </Button> */}
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <MgInTableCard />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={5}>
          <Grid container spacing={4}>
            {!!poData?.references && (
              <>
                {poData.references.map(reference => (
                  <Grid item xs={12}>
                    <DocNumberCard
                      warehouseDoc={reference.purchaseRequisitionChild ?? reference.purchaseRequisition}
                      docType='PR'
                    />
                  </Grid>
                ))}
              </>
            )}
            {poData?.items ? (
              <Grid item xs={12}>
                <ItemListDetailCard warehouseData={poData} />
              </Grid>
            ) : null}
            <Grid item xs={12}>
              <AdditionalInfoCard warehouseData={poData} />
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} md={7}>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <SjListCard />
            </Grid>
            {/* <Grid item xs={12}>
              <ImLogCard />
            </Grid> */}
            <Grid item xs={12}>
              <ActivityLogCard logList={logList} title='Log Aktivitas PO' />
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  )
}

export default PoDetailPage
