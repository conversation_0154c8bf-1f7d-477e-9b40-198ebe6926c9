import { Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { useRouter } from '@/routes/hooks'
import { useMg } from '../context/MgContext'
import { tableColumnsMgOut } from '../config/table'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { MrStatus } from '@/types/mrTypes'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { mgUserStatusOptions } from '../../list-out-approval/config/utils'
import { mgStatusOptions } from '../config/utils'
import { useLocation } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ProjectType } from '@/types/projectTypes'

const MgList = () => {
  const router = useRouter()
  const { state } = useLocation()
  const {
    mgOutParams,
    setMgOutParams,
    mgOutList: {
      items: mgOutList,
      totalItems: mgOutTotalItems,
      totalPages: mgOutTotalPages,
      limit: limitItems,
      page: pageItems
    },
    setPartialMgOutParams,
    setSelectedId
  } = useMg()

  const { departmentList, ownSiteList } = useAuth()

  const { page, search, userStatus, startDate, endDate, siteIds, departmentId, projectId } = mgOutParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const {
    data: { items: projectList }
  } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getProjectList(),
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  const tableMgOut = useReactTable({
    data: mgOutList ?? [],
    columns: tableColumnsMgOut({
      showDetail: mgOutData => {
        setSelectedId({
          mgOutId: mgOutData.id,
          mrId: mgOutData.materialRequestId
        })
        router.push(`/mg/out/request/${mgOutData.id}`)
      }
    }),
    initialState: {
      pagination: {
        pageSize: mgOutParams.limit ?? 10,
        pageIndex: page - 1
      }
    },
    state: {
      pagination: {
        pageSize: limitItems,
        pageIndex: pageItems - 1
      }
    },
    manualPagination: true,
    rowCount: mgOutTotalItems,
    pageCount: mgOutTotalPages,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  const onFilterChanged = ({ date, site, department, status, project }: FilterValues) => {
    setMgOutParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        status: status.length > 0 ? status[0] : undefined,
        siteIds: site.length > 0 ? site[0] : undefined,
        departmentId: department.length > 0 ? department[0] : undefined,
        projectId: project?.length > 0 ? project[0] : undefined
      }
    })
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      site: {
        options: ownSiteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: siteIds ? [siteIds] : []
      },
      status: {
        options: mgStatusOptions,
        values: userStatus ? [userStatus] : []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: departmentId ? [departmentId] : []
      },
      ...(projectList?.length > 0 && {
        project: {
          options: projectList?.map(project => ({ value: project.id, label: project.name })) ?? [],
          values: projectId ? [projectId] : []
        }
      })
    })
  }, [mgOutParams])

  useEffect(() => {
    setMgOutParams({
      limit: 10,
      page: 1
    })
  }, [])

  useEffect(() => {
    if (state?.projectId) {
      setMgOutParams(prev => ({ ...prev, projectId: state.projectId }))
    }
  }, [state])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row sm:items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setMgOutParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari'
            className='is-full sm:is-auto'
          />
          <FilterGroupDialog
            config={filterGroupConfig}
            onFilterApplied={onFilterChanged}
            onRemoveFilter={onFilterChanged}
          />
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
        </div>
      </div>
      <Table
        table={tableMgOut}
        emptyLabel={
          <td colSpan={tableMgOut.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Material Request</Typography>
            <Typography className='text-sm text-gray-400'>
              Belum ada Material Request barang yang bisa dikeluarkan dari gudang
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > mgOutTotalItems) {
            setMgOutParams(prev => ({ ...prev, limit: mgOutTotalItems, page: 1 }))
          } else {
            setPartialMgOutParams('limit', pageSize)

            const maxPage = Math.ceil(mgOutTotalItems / pageSize)
            if (page > maxPage) {
              setMgOutParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialMgOutParams('page', pageIndex)}
      />
    </Card>
  )
}

export default MgList
