// MUI Imports

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports
import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'
import { Controller, useFormContext } from 'react-hook-form'

import { useAuth } from '@/contexts/AuthContext'
import { CreateMrInput } from '../config/schema'
import { useMr } from '../../context/MrContext'
import { mrPriorityOptions } from '../config/enum'
import { CompanySiteType } from '@/types/companyTypes'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useSearchParams } from 'react-router-dom'
import { ProjectStatus, ProjectType } from '@/types/projectTypes'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'

const AdditionalInfoCard = () => {
  const { userProfile } = useAuth()
  const { showLoading } = useMr()
  const [searchParams] = useSearchParams()
  const {
    control,
    formState: { errors }
  } = useFormContext<CreateMrInput>()

  const {
    data: { items: projects }
  } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY],
    queryFn: async () => {
      return CompanyQueryMethods.getProjectList({ limit: Number.MAX_SAFE_INTEGER, status: ProjectStatus.ACTIVE })
    },
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Lainnya</Typography>
        </div>
        <Controller
          name='siteId'
          control={control}
          rules={{ required: true }}
          render={({ field: { value, onChange } }) => (
            <FormControl>
              <InputLabel id='role-select'>Warehouse</InputLabel>
              <Select
                key={value}
                fullWidth
                id='select-siteId'
                value={value}
                onChange={e => onChange((e.target as HTMLInputElement).value)}
                label='Warehouse'
                size='medium'
                labelId='siteId-select'
                inputProps={{ placeholder: 'Warehouse' }}
                defaultValue=''
              >
                {userProfile?.sites
                  ?.filter(site => site.type === CompanySiteType.WAREHOUSE)
                  ?.map(site => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
          )}
        />
        {userProfile?.sites?.filter(site => site.type === CompanySiteType.WORKSHOP).length > 0 && (
          <Controller
            name='forSiteId'
            control={control}
            render={({ field: { value, onChange } }) => (
              <FormControl>
                <InputLabel id='role-select'>Untuk Stok Workshop (Opsional)</InputLabel>
                <Select
                  key={value}
                  fullWidth
                  id='select-siteId'
                  value={value}
                  onChange={e => onChange((e.target as HTMLInputElement).value)}
                  label='Untuk Stok Workshop (Opsional)'
                  size='medium'
                  labelId='siteId-select'
                  inputProps={{ placeholder: 'Untuk Stok Workshop (Opsional)' }}
                  defaultValue=''
                >
                  {userProfile?.sites
                    ?.filter(site => site.type === CompanySiteType.WORKSHOP)
                    ?.map(site => (
                      <MenuItem key={site.id} value={site.id}>
                        {site.name}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            )}
          />
        )}
        <Controller
          control={control}
          name='projectId'
          render={({ field: { value, onChange } }) => (
            <FormControl fullWidth>
              <InputLabel id='select-projectId'>Untuk Proyek</InputLabel>
              <Select
                key={value}
                fullWidth
                id='select-projectId'
                value={value}
                readOnly={!!searchParams.get('projectId')}
                onChange={e => onChange((e.target as HTMLInputElement).value)}
                label='Untuk Proyek'
                labelId='select-projectId'
                inputProps={{ placeholder: 'Untuk Proyek' }}
              >
                {projects?.map(project => (
                  <MenuItem key={project.id} value={project.id}>
                    {project.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        />
        <Controller
          name='priority'
          control={control}
          render={({ field: { value, onChange } }) => (
            <FormControl>
              <InputLabel id='role-select'>Pilih Prioritas</InputLabel>
              <Select
                key={value}
                fullWidth
                id='select-priority'
                value={String(value)}
                onChange={e => onChange((e.target as HTMLInputElement).value)}
                label='Pilih Prioritas'
                size='medium'
                labelId='siteId-select'
                inputProps={{ placeholder: 'Pilih Prioritas' }}
                defaultValue=''
              >
                {mrPriorityOptions.map(priority => (
                  <MenuItem key={priority.value} value={priority.value}>
                    <div className='flex items-center gap-2'>
                      <div className={`size-2 ${priority.color}`} />
                      {priority.label}
                    </div>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        />
        <Controller
          name='note'
          control={control}
          render={({ field }) => (
            <TextField
              {...field}
              disabled={showLoading}
              fullWidth
              label='Catatan'
              variant='outlined'
              placeholder='Masukkkan Catatan'
              className='mbe-5'
              {...(errors.note && { error: true, helperText: 'Catatan wajib diisi.' })}
              InputLabelProps={{ shrink: !!field.value }}
            />
          )}
        />
      </CardContent>
    </Card>
  )
}

export default AdditionalInfoCard
