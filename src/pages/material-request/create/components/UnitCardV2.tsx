import { useEffect, useState } from 'react'
import { Card, CardContent, Checkbox, FormControlLabel, Grid, TextField, Typography } from '@mui/material'
import usePartialState from '@/core/hooks/usePartialState'
import { UnitType } from '@/types/companyTypes'
import NumberField from '@/components/numeric/NumberField'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { CreateMrInput } from '../config/schema'
import { isNullOrUndefined } from '@/utils/helper'
import UnitAutocomplete from '@/components/UnitAutocomplete'

const UniCardV2 = () => {
  const [hasNoUnit, setHasNoUnit] = useState(true)
  const [selectedUnit, setPartialSelectedUnit, setSelectedUnit] = usePartialState<UnitType | null>(null)

  const {
    control,
    formState: { errors },
    getValues,
    setValue,
    reset
  } = useFormContext<CreateMrInput>()
  const unitWatch = useWatch({ control, name: 'unit' })

  useEffect(() => {
    if (unitWatch) {
      setSelectedUnit(unitWatch as UnitType)
    }
  }, [unitWatch])

  useEffect(() => {
    if (hasNoUnit) {
      setSelectedUnit(null)
      reset({
        ...getValues(),
        unit: undefined,
        unitId: undefined,
        unitKm: undefined,
        unitHm: undefined
      })
    }
  }, [hasNoUnit])

  return (
    <Card>
      <CardContent>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Unit</Typography>
          <FormControlLabel
            control={
              <Checkbox onChange={e => setHasNoUnit((e.target as HTMLInputElement).checked)} checked={hasNoUnit} />
            }
            label='Tanpa Unit'
          />
        </div>
        {!hasNoUnit ? (
          <>
            <Grid container spacing={5} className='mt-1'>
              <Grid item xs={12} md={4}>
                <Controller
                  control={control}
                  name='unitId'
                  rules={{
                    validate: value => {
                      if (!hasNoUnit && isNullOrUndefined(value)) {
                        return 'Wajib diisi'
                      }
                      return true
                    }
                  }}
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <UnitAutocomplete
                      value={selectedUnit}
                      onChange={unit => {
                        if (unit) {
                          setValue('unitId', unit.id)
                          setValue('unit', unit)
                          setSelectedUnit(unit)
                        } else {
                          setValue('unitId', undefined)
                          setValue('unit', undefined)
                          setSelectedUnit(null)
                        }
                      }}
                      error={!!error}
                      helperText={error?.message}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Kategori Unit'
                  variant='outlined'
                  value={selectedUnit?.category?.name ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.category?.name }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Jenis Unit'
                  variant='outlined'
                  value={selectedUnit?.subCategory?.name ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.subCategory?.name }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Merk Unit'
                  variant='outlined'
                  value={selectedUnit?.brandName ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.brandName }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Tipe Unit'
                  variant='outlined'
                  value={selectedUnit?.type ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.type }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Nomor Lambung'
                  variant='outlined'
                  value={selectedUnit?.hullNumber ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.hullNumber }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Controller
                  control={control}
                  name='unitKm'
                  rules={{
                    validate: value => {
                      if (!hasNoUnit && isNullOrUndefined(value)) {
                        return 'Wajib diisi'
                      }
                      return true
                    }
                  }}
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <TextField
                      fullWidth
                      label='KM'
                      variant='outlined'
                      value={value}
                      onChange={onChange}
                      InputProps={{
                        endAdornment: 'km',
                        inputComponent: NumberField as any,
                        inputProps: {
                          isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined,
                          decimalScale: 1
                        }
                      }}
                      {...(!!error && { error: true, helperText: error.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Controller
                  control={control}
                  name='unitHm'
                  rules={{
                    validate: value => {
                      if (!hasNoUnit && isNullOrUndefined(value)) {
                        return 'Wajib diisi'
                      }
                      return true
                    }
                  }}
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <TextField
                      fullWidth
                      label='HM'
                      variant='outlined'
                      value={value}
                      onChange={onChange}
                      InputProps={{
                        endAdornment: 'jam',
                        inputComponent: NumberField as any,
                        inputProps: {
                          isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined,
                          decimalScale: 1
                        }
                      }}
                      {...(!!error && { error: true, helperText: error.message })}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </>
        ) : null}
      </CardContent>
    </Card>
  )
}

export default UniCardV2
