import { useEffect, useState } from 'react'
import {
  Autocomplete,
  Card,
  CardContent,
  Checkbox,
  CircularProgress,
  debounce,
  FormControlLabel,
  Grid,
  TextField,
  Typography
} from '@mui/material'
import usePartialState from '@/core/hooks/usePartialState'
import { UnitType } from '@/types/companyTypes'
import NumberField from '@/components/numeric/NumberField'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { UNIT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { CreateMrInput } from '../config/schema'
import { isNullOrUndefined } from '@/utils/helper'

const UniCardV2 = () => {
  const [hasNoUnit, setHasNoUnit] = useState(true)
  const [selectedUnit, setPartialSelectedUnit, setSelectedUnit] = usePartialState<UnitType | null>(null)
  const [unitKmHm, setPartialUnitKmHm, setUnitKmHm] = usePartialState<{ unitKm?: number; unitHm?: number }>({
    unitKm: 0,
    unitHm: 0
  })
  const [unitSearchQuery, setUnitSearchQuery] = useState('')

  const {
    control,
    trigger,
    formState: { errors },
    getValues,
    setValue,
    resetField,
    reset
  } = useFormContext<CreateMrInput>()
  const unitWatch = useWatch({ control, name: 'unit' })

  const {
    data: { items: unitList },
    isLoading: fetchUnitsLoading,
    remove: removeUnitList
  } = useQuery({
    enabled: !!unitSearchQuery,
    queryKey: [UNIT_LIST_QUERY_KEY, unitSearchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getUnitList({
        ...(unitSearchQuery && { number: unitSearchQuery }),
        limit: Number.MAX_SAFE_INTEGER
      })
    },
    placeholderData: defaultListData as ListResponse<UnitType>
  })

  useEffect(() => {
    if (unitWatch) {
      setSelectedUnit(unitWatch as UnitType)
    }
  }, [unitWatch])

  useEffect(() => {
    if (hasNoUnit) {
      setSelectedUnit(null)
      reset({
        ...getValues(),
        unit: undefined,
        unitId: undefined,
        unitKm: undefined,
        unitHm: undefined
      })
    }
  }, [hasNoUnit])

  return (
    <Card>
      <CardContent>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Unit</Typography>
          <FormControlLabel
            control={
              <Checkbox onChange={e => setHasNoUnit((e.target as HTMLInputElement).checked)} checked={hasNoUnit} />
            }
            label='Tanpa Unit'
          />
        </div>
        {!hasNoUnit ? (
          <>
            <Grid container spacing={5} className='mt-1'>
              <Grid item xs={12} md={4}>
                <Controller
                  control={control}
                  name='unitId'
                  rules={{
                    validate: value => {
                      if (!hasNoUnit && isNullOrUndefined(value)) {
                        return 'Wajib diisi'
                      }
                      return true
                    }
                  }}
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <Autocomplete
                      key={JSON.stringify(selectedUnit)}
                      filterOptions={x => x}
                      isOptionEqualToValue={(option, value) => option.id === value.id}
                      onInputChange={debounce((e, newValue, reason) => {
                        if (reason === 'input') {
                          setUnitSearchQuery(newValue)
                        }
                      }, 700)}
                      options={unitList || []}
                      freeSolo
                      onChange={(e, newValue: UnitType) => {
                        if (newValue) {
                          setValue('unitId', newValue.id)
                          setValue('unit', newValue)
                          setSelectedUnit(newValue)
                          removeUnitList()
                        }
                      }}
                      value={selectedUnit}
                      noOptionsText='Unit tidak ditemukan'
                      loading={fetchUnitsLoading}
                      renderInput={params => (
                        <TextField
                          {...params}
                          label='Kode Unit'
                          placeholder='Masukkan kode unit'
                          variant='outlined'
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: <>{fetchUnitsLoading ? <CircularProgress /> : null}</>,
                            onKeyDown: e => {
                              if (e.key === 'Enter') {
                                e.stopPropagation()
                              }
                            }
                          }}
                          {...(!!error && { error: true, helperText: error.message })}
                        />
                      )}
                      getOptionLabel={(option: UnitType) => option?.number}
                      renderOption={(props, option) => {
                        const { key, ...optionProps } = props
                        return (
                          <li key={key} {...optionProps}>
                            <Typography>
                              {option.number} | {option.brandName} | {option.type}
                            </Typography>
                          </li>
                        )
                      }}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Kategori Unit'
                  variant='outlined'
                  value={selectedUnit?.category?.name ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.category?.name }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Jenis Unit'
                  variant='outlined'
                  value={selectedUnit?.subCategory?.name ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.subCategory?.name }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Merk Unit'
                  variant='outlined'
                  value={selectedUnit?.brandName ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.brandName }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Tipe Unit'
                  variant='outlined'
                  value={selectedUnit?.type ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.type }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label='Nomor Lambung'
                  variant='outlined'
                  value={selectedUnit?.hullNumber ?? ''}
                  disabled
                  InputLabelProps={{ shrink: !!selectedUnit?.hullNumber }}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Controller
                  control={control}
                  name='unitKm'
                  rules={{
                    validate: value => {
                      if (!hasNoUnit && isNullOrUndefined(value)) {
                        return 'Wajib diisi'
                      }
                      return true
                    }
                  }}
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <TextField
                      fullWidth
                      label='KM'
                      variant='outlined'
                      value={value}
                      onChange={onChange}
                      InputProps={{
                        endAdornment: 'km',
                        inputComponent: NumberField as any,
                        inputProps: {
                          isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined,
                          decimalScale: 1
                        }
                      }}
                      {...(!!error && { error: true, helperText: error.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <Controller
                  control={control}
                  name='unitHm'
                  rules={{
                    validate: value => {
                      if (!hasNoUnit && isNullOrUndefined(value)) {
                        return 'Wajib diisi'
                      }
                      return true
                    }
                  }}
                  render={({ field: { value, onChange }, fieldState: { error } }) => (
                    <TextField
                      fullWidth
                      label='HM'
                      variant='outlined'
                      value={value}
                      onChange={onChange}
                      InputProps={{
                        endAdornment: 'jam',
                        inputComponent: NumberField as any,
                        inputProps: {
                          isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined,
                          decimalScale: 1
                        }
                      }}
                      {...(!!error && { error: true, helperText: error.message })}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </>
        ) : null}
      </CardContent>
    </Card>
  )
}

export default UniCardV2
