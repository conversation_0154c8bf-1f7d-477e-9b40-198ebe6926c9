import Table from '@/components/table'
import { WarehouseItemType } from '@/types/appTypes'
import { <PERSON><PERSON>, Card, CardContent, Typography } from '@mui/material'
import {
  useReactTable,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'
import { useEffect, useState } from 'react'
import { tableColumns } from '../add-item/table'
import { useFormContext, useWatch } from 'react-hook-form'
import { CreateMrInput } from '../config/schema'
import { EmptyState } from './ItemListCard'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { toast } from 'react-toastify'
import { useMenu } from '@/components/menu/contexts/menuContext'

const ItemListCardV2 = () => {
  const { setConfirmState } = useMenu()
  const [{ open: addItemOpen, selectedItem }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as WarehouseItemType
  })
  const [selectedItems, setSelectedItems] = useState<WarehouseItemType[]>([])

  const { control, getValues, setValue } = useFormContext<CreateMrInput>()
  const selectedUnit = useWatch({ control, name: 'unit' })

  const handleRemoveItem = (selectedItem: WarehouseItemType) => {
    setSelectedItems(current => current.filter(item => item.itemId !== selectedItem.itemId))
  }

  const table = useReactTable({
    data: selectedItems,
    columns: tableColumns(
      {
        onEdit: itemData => {
          setAddItemModalState({
            open: true,
            selectedItem: itemData
          })
        },
        onRemove: handleRemoveItem
      },
      getValues('siteId')
    ),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    if (selectedItems.length > 0) {
      setValue('items', [
        ...selectedItems.map(item => ({
          ...item
          // unit: selectedUnit,
          // unitId: selectedUnit?.id,
          // unitKm: unitKmHm?.unitKm ?? 0,
          // unitHm: unitKmHm?.unitHm ?? 0
        }))
      ])
    }
  }, [selectedItems])

  return (
    <>
      <Card>
        <CardContent>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Daftar Barang</Typography>
            <Button
              color='primary'
              variant='outlined'
              onClick={() => {
                setAddItemModalState({
                  open: true,
                  selectedItem: {}
                })
              }}
            >
              Tambah Barang
            </Button>
          </div>
          {selectedItems.length > 0 ? (
            <div className='mt-4'>
              <Table
                table={table}
                emptyLabel={
                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                    <Typography>Belum ada Barang</Typography>
                    <Typography className='text-sm text-gray-400'>
                      Tambahkan barang yang ingin dimasukkan dalam Material Request ini
                    </Typography>
                  </td>
                }
              />
            </div>
          ) : (
            <EmptyState />
          )}
        </CardContent>
      </Card>
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? {} : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          currentItem={selectedItem}
          onSubmit={(itemData, isEnter) => {
            if (selectedItem?.itemId) {
              const tempItems = [...selectedItems]
              const itemIndex = tempItems.findIndex(item => item.itemId === selectedItem.itemId)
              tempItems[itemIndex] = itemData
              setSelectedItems(tempItems)
            } else {
              const existingItem = [
                ...(getValues('items') ?? []),
                ...selectedItems.map(item => ({
                  ...item,
                  unit: selectedUnit,
                  unitId: selectedUnit?.id
                }))
              ].find(item => item.itemId === itemData.itemId)
              if (existingItem) {
                toast.error('Barang sudah ditambahkan')
              } else {
                setSelectedItems(current => [...current, itemData])
              }
            }
            setAddItemModalState({
              open: false,
              selectedItem: {} as WarehouseItemType
            })
            if (!isEnter) {
              // setConfirmState({
              //   open: true,
              //   title: 'Tambah Barang Lain',
              //   content: (
              //     <>
              //       <Typography>
              //         Barang sudah ditambahkan! Apakah kamu mau menambahkan barang lain untuk Material Request ini?
              //       </Typography>
              //     </>
              //   ) as any,
              //   confirmText: 'Tambah Barang Lain',
              //   onConfirm: () => {
              //     setAddItemModalState({
              //       open: true,
              //       selectedItem: {} as WarehouseItemType
              //     })
              //   },
              //   onCancel: () => {
              //     setAddItemModalState({
              //       open: false,
              //       selectedItem: {} as WarehouseItemType
              //     })
              //   }
              // })
            }
          }}
          onEnterCb={() => {
            setAddItemModalState({
              open: true,
              selectedItem: {} as WarehouseItemType
            })
          }}
          withoutUnit
          parentCodeStrict={false}
          siteId={getValues('siteId')}
        />
      )}
    </>
  )
}

export default ItemListCardV2
