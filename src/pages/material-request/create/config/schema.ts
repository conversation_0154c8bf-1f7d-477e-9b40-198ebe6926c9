import { itemSchema, unitSchema } from '@/types/companyTypes'
import { array, boolean, coerce, object, string, TypeOf } from 'zod'

export const mrItemPayloadSchema = object({
  originalItemId: string().optional().nullable(),
  itemId: string({ message: 'Barang wajib dipilih' }),
  quantity: coerce.number(),
  quantityUnit: string({ message: 'Satuan wajib dipilih' }),
  largeUnitQuantity: coerce.number().optional().nullable(),
  remainingQuantity: coerce.number().optional().nullable(),
  unitId: string().nullable().optional(),
  unitKm: coerce.number().nullable().optional(),
  unitHm: coerce.number().nullable().optional(),
  note: string().nullable().optional(),
  isLargeUnit: boolean().optional().nullable(),
  images: array(
    object({
      uploadId: string().optional().nullable(),
      fileName: string().optional().nullable(),
      content: string().optional().nullable(),
      url: string().optional().nullable()
    })
  )
    .optional()
    .nullable(),
  item: itemSchema.nullable().optional(),
  unit: unitSchema.nullable().optional()
})

export const mrPayloadSchema = object({
  items: array(mrItemPayloadSchema).min(1, { message: 'Barang wajib diisi' }),
  siteId: string({ message: 'Site wajib dipilih' }),
  projectId: string().optional().nullable(),
  forSiteId: string().optional().nullable(),
  unitId: string().nullable().optional(),
  unitKm: coerce.number().nullable().optional(),
  unitHm: coerce.number().nullable().optional(),
  unit: unitSchema.nullable().optional(),
  note: string({ message: 'Catatan wajib diisi' }).min(1, { message: 'Catatan wajib diisi' }),
  priority: string()
})

export type ItemMrInput = TypeOf<typeof mrItemPayloadSchema>
export type CreateMrInput = TypeOf<typeof mrPayloadSchema>
