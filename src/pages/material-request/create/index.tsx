// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import { Button, Typography } from '@mui/material'

import { SubmitError<PERSON><PERSON><PERSON>, SubmitHandler, useFormContext, useWatch } from 'react-hook-form'

import ItemListCardV2 from './components/ItemListCardV2'
import AdditionalInfoCard from './components/AdditionalInfoCard'

import { useRouter } from '@/routes/hooks'
import ApprovalListCard from './components/ApprovalListCard'
import { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'
import { useMr } from '../context/MrContext'
import { MrItemPayload } from '@/types/mrTypes'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import { CreateMrInput } from './config/schema'
import LoadingButton from '@mui/lab/LoadingButton'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { isNullOrUndefined, mergeArrays } from '@/utils/helper'
import UnitCardV2 from './components/UnitCardV2'
import { ItemType } from '@/types/companyTypes'
import { DraftScope } from '@/types/draftsTypes'
import { useLocation, useSearchParams } from 'react-router-dom'
import { DRAFT_QUERY_KEY } from '@/api/services/draft/service'
import DraftQueryMethods from '@/api/services/draft/query'
import { useDeleteDraft, useUpdateDraft } from '@/api/services/draft/mutation'
import { useCreateMr } from '../context/CreateMrContext'

const CreateMrPage = () => {
  const router = useRouter()
  const { search } = useLocation()
  const [searchParams] = useSearchParams()
  const { setConfirmState } = useMenu()
  const { userProfile } = useAuth()
  const { effectExecuted } = useCreateMr()
  const { fetchMrList, showLoading, uploadMutate, createMrMutate, saveAsDraft } = useMr()
  const { mutateAsync: deleteDraft } = useDeleteDraft()
  const { mutate: updateDraft } = useUpdateDraft()

  const { control, setValue, handleSubmit, getValues, reset } = useFormContext<CreateMrInput>()

  const itemsWatch = useWatch({
    control,
    name: 'items'
  })

  const siteIdWatch = useWatch({
    control,
    name: 'siteId',
    defaultValue: ''
  })

  const unitWatch = useWatch({
    control,
    name: ['unit', 'unitKm', 'unitHm'],
    defaultValue: null
  })

  const scope = `material-request`

  const { data: draftData, refetch: refetchDraft } = useQuery({
    enabled: !!searchParams.get('draft'),
    queryKey: [DRAFT_QUERY_KEY, searchParams.get('draft')],
    queryFn: () => DraftQueryMethods.getOneDraft(searchParams.get('draft')),
    cacheTime: 0
  })

  const { data: approverList } = useQuery({
    enabled: !!siteIdWatch && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, siteIdWatch, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope,
        siteId: siteIdWatch,
        departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  const onSaveDraft = () => {
    const { items, ...formInput } = getValues()
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Simpan Draft Material Request',
      content:
        'Apakah kamu yakin akan menyimpan draft Material Request ini? Pastikan semua detil yang kamu masukkan untuk Material Request ini sudah benar',
      confirmText: 'Simpan',
      onConfirm: () => {
        Promise.all(
          (itemsWatch ?? [])
            ?.filter(item => (item.images?.length ?? 0) > 0)
            ?.map(item =>
              Promise.all(
                item.images
                  .filter(image => !!image.content)
                  .map(image =>
                    uploadMutate({
                      fieldName: `item_image_${item.itemId}`,
                      file: image.content,
                      scope: 'public-image',
                      fileName: image.fileName
                    })
                  )
              )
                .then(values => {
                  const uploadIds = values.map(val => ({
                    uploadId: val.data?.id ?? ''
                  }))
                  return {
                    ...item,
                    images: [
                      ...(item.images ?? [])
                        .filter(image => !image.fileName && !!image.uploadId)
                        .map(image => ({
                          uploadId: image.uploadId
                        })),
                      ...uploadIds
                    ]
                  }
                })
                .catch(error => {
                  const message = error.response?.data?.message
                  if (message) {
                    toast.error(message)
                  } else {
                    toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
                  }
                  return {} as CreateMrInput['items'][0]
                })
            )
        )
          .then(values => {
            const itemsValues = values
              .filter(val => !!val.itemId)
              .map(
                val =>
                  ({
                    ...val,
                    itemId: val.itemId,
                    quantity: val.quantity,
                    quantityUnit: val.quantityUnit,
                    note: val.note,
                    images: val.images,
                    largeUnitQuantity: val.largeUnitQuantity,
                    ...(val.unitId && { unitId: val.unitId }),
                    ...(!isNullOrUndefined(val?.unitKm) && { unitKm: val.unitKm }),
                    ...(!isNullOrUndefined(val?.unitHm) && { unitHm: val.unitHm })
                  }) as MrItemPayload
              )
            const stringifyPayload = JSON.stringify({
              ...formInput,
              unit: {
                ...formInput.unit,
                category: undefined,
                categoryId: undefined,
                subCategory: undefined,
                subCategoryId: undefined
              },
              approvals: approverList.map(approver => ({
                userId: approver.user?.id
              })),
              items: mergeArrays(items, itemsValues, 'itemId') as MrItemPayload[]
            })
            if (draftData) {
              updateDraft(
                {
                  draftId: draftData.id,
                  siteId: formInput.siteId,
                  payload: stringifyPayload
                },
                {
                  onSuccess: () => {
                    toast.success('Material Request disimpan sebagai draft.')
                    refetchDraft()
                    fetchMrList()
                    router.replace('/mr/draft')
                  }
                }
              )
            } else {
              saveAsDraft(
                {
                  payload: stringifyPayload,
                  scope: DraftScope['MATERIAL-REQUEST'],
                  siteId: formInput.siteId
                },
                {
                  onSuccess: () => {
                    toast.success('Material Request disimpan sebagai draft.')
                    fetchMrList()
                    router.replace('/mr/draft')
                  }
                }
              )
            }
            // }
          })
          .catch(error => {
            const message = error.response?.data?.message
            if (message) {
              toast.error(message)
            } else {
              toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
            }
          })
      }
    })
  }

  const onSubmitHandler: SubmitHandler<CreateMrInput> = ({ items, ...formInput }: CreateMrInput) => {
    if ((approverList?.length ?? 0) <= 0) {
      toast.error('Default Approval belum tersedia. Silahkan hubungi admin terlebih dahulu.')
      return
    }
    setConfirmState({
      open: true,
      title: 'Buat Material Request',
      content:
        'Apakah kamu yakin akan membuat Material Request ini? Pastikan semua detil yang kamu masukkan untuk Material Request ini sudah benar',
      confirmText: 'Buat Material Request',
      onConfirm: () => {
        Promise.all(
          items
            .filter(item => (item.images?.length ?? 0) > 0)
            .map(item =>
              Promise.all(
                item.images
                  .filter(image => !!image.content)
                  .map(image =>
                    uploadMutate({
                      fieldName: `item_image_${item.itemId}`,
                      file: image.content,
                      scope: 'public-image',
                      fileName: image.fileName
                    })
                  )
              )
                .then(values => {
                  const uploadIds = values.map(val => ({
                    uploadId: val.data?.id ?? ''
                  }))
                  return {
                    ...item,
                    images: [
                      ...(item.images ?? [])
                        .filter(image => !image.fileName && !!image.uploadId)
                        .map(image => ({
                          uploadId: image.uploadId
                        })),
                      ...uploadIds
                    ]
                  }
                })
                .catch(error => {
                  const message = error.response?.data?.message
                  if (message) {
                    toast.error(message)
                  } else {
                    toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
                  }
                  return {} as CreateMrInput['items'][0]
                })
            )
        )
          .then(values => {
            // if (!selectedMrId) {
            const itemsValues = values
              .filter(val => !!val.itemId)
              .map(
                val =>
                  ({
                    itemId: val.itemId,
                    quantity: val.quantity,
                    quantityUnit: val.quantityUnit,
                    note: val.note,
                    images: val.images,
                    largeUnitQuantity: val.largeUnitQuantity,
                    ...(val.unitId && { unitId: val.unitId }),
                    ...(!isNullOrUndefined(val?.unitKm) && { unitKm: val.unitKm }),
                    ...(!isNullOrUndefined(val?.unitHm) && { unitHm: val.unitHm })
                  }) as MrItemPayload
              )

            createMrMutate(
              {
                ...formInput,
                approvals: approverList.map(approver => ({
                  userId: approver.user?.id
                })),
                items: mergeArrays(items, itemsValues, 'itemId') as MrItemPayload[]
              },
              {
                onSuccess: async () => {
                  if (draftData) await deleteDraft(draftData.id)
                  toast.success('Material Request berhasil dibuat')
                  fetchMrList()
                  router.replace('/mr/list')
                }
              }
            )
            // }
          })
          .catch(error => {
            const message = error.response?.data?.message
            if (message) {
              toast.error(message)
            } else {
              toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
            }
          })
      }
    })
  }

  const onErrorHandler: SubmitErrorHandler<CreateMrInput> = errors => {
    console.warn({ errors })
    Object.entries(errors).forEach(([field, error]) => {
      toast.error(`${field}: ${error?.message}`, {
        autoClose: 5000
      })
    })
  }

  useEffect(() => {
    if (draftData && !effectExecuted.current) {
      const parsedPayload: CreateMrInput = JSON.parse(draftData.payload)
      const itemsArray = mergeArrays(parsedPayload.items, getValues('items'), 'itemId') as ItemType[]

      reset({
        ...parsedPayload,
        items: itemsArray,
        forSiteId: !!getValues('forSiteId') ? getValues('forSiteId') : parsedPayload.forSiteId,
        note: !!getValues('note') ? getValues('note') : parsedPayload.note,
        unit: Object.keys(getValues('unit') ?? {}).length > 0 ? getValues('unit') : parsedPayload.unit
      })

      effectExecuted.current = true
    }
  }, [draftData])

  useEffect(() => {
    if (searchParams.get('projectId')) {
      setValue('projectId', searchParams.get('projectId'))
    }
  }, [searchParams])

  return (
    <Grid container spacing={4}>
      <Grid item xs={12}>
        <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
          <div className='flex flex-col max-sm:text-center'>
            <Typography variant='h4'>Buat Material Request</Typography>
            <Typography>Buat Material Request untuk diajukan kepada eselon terkait</Typography>
          </div>
          <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
            <Button
              color='secondary'
              variant='outlined'
              disabled={showLoading}
              onClick={() => router.replace('/mr/list')}
            >
              Batalkan
            </Button>
            <LoadingButton startIcon={<></>} loading={showLoading} variant='outlined' onClick={onSaveDraft}>
              Simpan Draft
            </LoadingButton>
            <LoadingButton
              startIcon={<></>}
              loading={showLoading}
              variant='contained'
              onClick={handleSubmit(onSubmitHandler, onErrorHandler)}
            >
              Buat Material Request
            </LoadingButton>
          </div>
        </div>
      </Grid>
      <Grid item xs={12}>
        {/* <ItemListCard /> */}
        <ItemListCardV2 />
      </Grid>
      <Grid item xs={12} md={7}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <UnitCardV2 />
          </Grid>
          <Grid item xs={12}>
            <AdditionalInfoCard />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12} md={5}>
        <Grid container spacing={4}>
          <Grid item xs={12}>
            <ApprovalListCard approverList={approverList.map(approver => approver.user) ?? []} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default CreateMrPage
