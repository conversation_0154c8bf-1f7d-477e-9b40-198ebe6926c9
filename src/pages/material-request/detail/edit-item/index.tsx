// MUI Imports

import Grid from '@mui/material/Grid'

// Type Imports
import {
  Autocomplete,
  Button,
  Card,
  CardContent,
  Checkbox,
  CircularProgress,
  debounce,
  FormControlLabel,
  TextField,
  Typography
} from '@mui/material'

import { useRouter } from '@/routes/hooks'
import LoadingButton from '@mui/lab/LoadingButton'
import { useEffect, useState } from 'react'
import { ItemType, UnitType } from '@/types/companyTypes'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { UNIT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { useUpdateEffect } from 'react-use'
import { WarehouseItemType } from '@/types/appTypes'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { Controller, useForm, useFormContext } from 'react-hook-form'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from './table'
import Table from '@/components/table'
import { toast } from 'react-toastify'
import NumberField from '@/components/numeric/NumberField'
import usePartialState from '@/core/hooks/usePartialState'
import { CreateMrInput } from '../../create/config/schema'
import { EmptyState } from '../../create/components/ItemListCard'
import { useMr } from '../../context/MrContext'
import { useUpdateMr, useUpdateMrItem } from '@/api/services/mr/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { isNullOrUndefined } from '@/utils/helper'
import { useUploadImage } from '@/api/services/file/mutation'

const AddMrItemPage = () => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const {
    control,
    trigger,
    formState: { errors },
    getValues,
    setValue
  } = useForm<CreateMrInput>()
  const { mrData, fetchMrData, fetchLogList, selectedMrId } = useMr()
  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const {
    mutate: updateItemMutate,
    mutateAsync: updateItemAsyncMutate,
    isLoading: updateItemLoading
  } = useUpdateMrItem()
  const { mutate: updateMrMutate, mutateAsync: updateMrAsyncMutate, isLoading: updateMrLoading } = useUpdateMr()

  const [hasNoUnit, setHasNoUnit] = useState(false)
  const [selectedUnit, setPartialSelectedUnit, setSelectedUnit] = usePartialState<UnitType | null>(null)
  const [unitKmHm, setPartialUnitKmHm, setUnitKmHm] = usePartialState<{ unitKm?: number; unitHm?: number }>({
    unitKm: 0,
    unitHm: 0
  })
  const [unitSearchQuery, setUnitSearchQuery] = useState('')

  const [{ open: addItemOpen, selectedItem }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as WarehouseItemType
  })

  const [selectedItems, setSelectedItems] = useState<WarehouseItemType[]>([])

  const handleRemoveItem = (mrItem: WarehouseItemType) => {
    setConfirmState({
      open: true,
      title: 'Hapus Barang',
      content: 'Apakah kamu yakin ingin menghapus barang ini?',
      onConfirm: () => {
        updateItemMutate(
          {
            mrId: mrData.id,
            payload: {
              id: mrItem.id,
              itemId: mrItem.itemId,
              quantity: 0,
              quantityUnit: mrItem.quantityUnit,
              note: mrItem.note,
              largeUnitQuantity: mrItem.largeUnitQuantity,
              ...(mrItem.unitId && { unitId: mrItem.unitId }),
              ...(!isNullOrUndefined(mrItem?.unitKm) && { unitKm: mrItem.unitKm }),
              ...(!isNullOrUndefined(mrItem?.unitHm) && { unitHm: mrItem.unitHm }),
              images: [
                ...(mrItem.images ?? [])
                  .filter(image => !image.fileName && !!image.uploadId)
                  .map(image => ({
                    uploadId: image.uploadId
                  }))
              ]
            }
          },
          {
            onSuccess: () => {
              toast.success('Barang berhasil dihapus')
              fetchMrData()
              setSelectedItems(current => current.filter(item => item.itemId !== selectedItem.itemId))
            }
          }
        )
      },
      confirmText: 'Hapus',
      confirmColor: 'error'
    })
  }

  const handleSubmitItems = () => {
    const formItems = [
      ...selectedItems.map(item => ({
        ...item,
        unit: selectedUnit
      }))
    ]
    setConfirmState({
      open: true,
      title: 'Ubah Barang',
      content: 'Apakah kamu yakin ingin mengubah barang MR ini?',
      onConfirm: () => {
        Promise.all([
          ...formItems.map(itemData => {
            return Promise.all(
              (itemData.images ?? [])
                .filter(item => !!item.fileName)
                .map(item =>
                  uploadMutate({
                    fieldName: `item_image_${itemData.itemId}`,
                    file: item.content,
                    scope: 'public-image',
                    fileName: item.fileName
                  })
                )
            ).then(async values => {
              const uploadIds = values.map(val => ({
                uploadId: val.data?.id ?? ''
              }))
              await updateItemAsyncMutate({
                mrId: mrData.id,
                payload: {
                  id: itemData.id,
                  itemId: itemData.itemId,
                  quantity: itemData.quantity,
                  quantityUnit: itemData.quantityUnit,
                  note: itemData.note,
                  largeUnitQuantity: itemData.largeUnitQuantity,
                  images: [
                    ...(itemData.images ?? [])
                      .filter(image => !image.fileName && !!image.uploadId)
                      .map(image => ({
                        uploadId: image.uploadId
                      })),
                    ...uploadIds
                  ]
                }
              })
            })
          }),
          updateMrAsyncMutate({
            mrId: mrData?.id,
            unitId: selectedUnit?.id,
            unitKm: unitKmHm?.unitKm ?? 0,
            unitHm: unitKmHm?.unitHm ?? 0
          })
        ]).then(() => {
          setTimeout(() => {
            toast.success('Data barang berhasil diubah')
            fetchMrData()
            router.back()
          }, 1000)
        })
      },
      confirmText: 'Ubah'
    })
  }

  const {
    data: { items: unitList },
    isLoading: fetchUnitsLoading,
    remove: removeUnitList
  } = useQuery({
    enabled: !!unitSearchQuery,
    queryKey: [UNIT_LIST_QUERY_KEY, unitSearchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getUnitList({
        ...(unitSearchQuery && { number: unitSearchQuery }),
        limit: Number.MAX_SAFE_INTEGER
      })
    },
    placeholderData: defaultListData as ListResponse<UnitType>
  })

  const table = useReactTable({
    data: selectedItems,
    columns: tableColumns(
      {
        onEdit: itemData => {
          setAddItemModalState({
            open: true,
            selectedItem: itemData
          })
        },
        onRemove: handleRemoveItem
      },
      getValues('siteId')
    ),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useUpdateEffect(() => {
    if (hasNoUnit) {
      setSelectedUnit(null)
    }
  }, [hasNoUnit])

  useEffect(() => {
    if (selectedUnit) {
      setPartialUnitKmHm('unitKm', selectedUnit?.km ?? 0)
      setPartialUnitKmHm('unitHm', selectedUnit?.hm ?? 0)
    }
  }, [selectedUnit])

  useEffect(() => {
    if (mrData) {
      const existingItems = mrData?.items ?? []
      setSelectedItems(
        existingItems.map(exItem => ({
          ...exItem,
          item: exItem.item as ItemType,
          unit: exItem.unit as UnitType
        }))
      )
      if (existingItems.length > 0) {
        const existingUnit = mrData?.unit
        if (existingUnit) {
          setSelectedUnit({
            ...existingUnit,
            km: mrData?.unitKm ?? 0,
            hm: mrData?.unitHm ?? 0
          })
        } else {
          setHasNoUnit(true)
        }
      }
    } else {
      router.replace(`/mr/list/${selectedMrId}`)
    }
  }, [])

  return (
    <>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <div className='flex justify-between items-end flex-col sm:flex-row gap-2 max-sm:items-center'>
            <div className='flex flex-col max-sm:text-center'>
              <Typography variant='h4'>Ubah Barang</Typography>
              <Typography>Atur barang yang ingin dimasukkan dalam MR ini</Typography>
            </div>
            <div className='flex flex-col sm:flex-row gap-2 is-full sm:is-auto'>
              <Button color='secondary' variant='outlined' onClick={() => router.back()}>
                Batalkan Perubahan
              </Button>
              <LoadingButton
                startIcon={<></>}
                loading={uploadLoading || updateItemLoading}
                variant='contained'
                className='is-full sm:is-auto px-8'
                onClick={handleSubmitItems}
              >
                Simpan Perubahan
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <div className='flex justify-between items-center'>
                <Typography variant='h5'>Detil Unit</Typography>
                <FormControlLabel
                  control={
                    <Checkbox
                      onChange={e => setHasNoUnit((e.target as HTMLInputElement).checked)}
                      checked={hasNoUnit}
                    />
                  }
                  label='Tanpa Unit'
                />
              </div>
              {!hasNoUnit ? (
                <>
                  <Grid container spacing={5} className='mt-1'>
                    <Grid item xs={12} md={4}>
                      <Autocomplete
                        key={JSON.stringify(selectedUnit)}
                        filterOptions={x => x}
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        onInputChange={debounce((e, newValue, reason) => {
                          if (reason === 'input') {
                            setUnitSearchQuery(newValue)
                          }
                        }, 700)}
                        options={unitList || []}
                        freeSolo
                        onChange={(e, newValue: UnitType) => {
                          setSelectedUnit(newValue)
                          removeUnitList()
                        }}
                        value={selectedUnit}
                        noOptionsText='Unit tidak ditemukan'
                        loading={fetchUnitsLoading}
                        renderInput={params => (
                          <TextField
                            {...params}
                            label='Kode Unit'
                            placeholder='Masukkan kode unit'
                            variant='outlined'
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: <>{fetchUnitsLoading ? <CircularProgress /> : null}</>,
                              onKeyDown: e => {
                                if (e.key === 'Enter') {
                                  e.stopPropagation()
                                }
                              }
                            }}
                          />
                        )}
                        getOptionLabel={(option: UnitType) => option?.number}
                        renderOption={(props, option) => {
                          const { key, ...optionProps } = props
                          return (
                            <li key={key} {...optionProps}>
                              <Typography>
                                {option.number} | {option.brandName} | {option.type}
                              </Typography>
                            </li>
                          )
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label='Kategori Unit'
                        variant='outlined'
                        value={selectedUnit?.category?.name ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.category?.name }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label='Jenis Unit'
                        variant='outlined'
                        value={selectedUnit?.subCategory?.name ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.subCategory?.name }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label='Merk Unit'
                        variant='outlined'
                        value={selectedUnit?.brandName ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.brandName }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label='Tipe Unit'
                        variant='outlined'
                        value={selectedUnit?.type ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.type }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label='Nomor Lambung'
                        variant='outlined'
                        value={selectedUnit?.hullNumber ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.hullNumber }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label='KM'
                        variant='outlined'
                        value={unitKmHm?.unitKm}
                        onChange={e => setPartialUnitKmHm('unitKm', Number((e.target as HTMLInputElement).value))}
                        InputProps={{
                          endAdornment: 'km',
                          inputComponent: NumberField as any,
                          inputProps: {
                            isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined,
                            decimalScale: 1
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label='HM'
                        variant='outlined'
                        value={unitKmHm?.unitHm}
                        onChange={e => setPartialUnitKmHm('unitHm', Number((e.target as HTMLInputElement).value))}
                        InputProps={{
                          endAdornment: 'jam',
                          inputComponent: NumberField as any,
                          inputProps: {
                            isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined,
                            decimalScale: 1
                          }
                        }}
                      />
                    </Grid>
                  </Grid>
                </>
              ) : null}
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <div className='flex justify-between items-center'>
                <Typography variant='h5'>Barang</Typography>
                {/* <Button
                  color='primary'
                  variant='outlined'
                  onClick={() => {
                    setAddItemModalState({
                      open: true,
                      selectedItem: {}
                    })
                  }}
                >
                  Tambah List
                </Button> */}
              </div>
              {selectedItems.length > 0 ? (
                <div className='mt-4'>
                  <Table
                    table={table}
                    emptyLabel={
                      <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                        <Typography>Belum ada Barang</Typography>
                        <Typography className='text-sm text-gray-400'>
                          Tambahkan barang yang ingin dimasukkan dalam dokumen ini
                        </Typography>
                      </td>
                    }
                  />
                </div>
              ) : (
                <EmptyState />
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? {} : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          currentItem={selectedItem}
          onSubmit={itemData => {
            if (selectedItem?.itemId) {
              const tempItems = [...selectedItems]
              const itemIndex = tempItems.findIndex(item => item.itemId === selectedItem.itemId)
              tempItems[itemIndex] = itemData
              setSelectedItems(tempItems)
            } else {
              const existingItem = [
                ...(getValues('items') ?? []),
                ...selectedItems.map(item => ({
                  ...item,
                  unit: selectedUnit,
                  unitId: selectedUnit?.id
                }))
              ].find(item => item.itemId === itemData.itemId)
              if (existingItem) {
                toast.error('Barang sudah ditambahkan')
              } else {
                setSelectedItems(current => [...current, itemData])
              }
            }
            setAddItemModalState({
              open: false,
              selectedItem: {} as WarehouseItemType
            })
          }}
          withoutUnit
          parentCodeStrict={false}
          siteId={getValues('siteId')}
        />
      )}
    </>
  )
}

export default AddMrItemPage
