import { useUploadDocument } from '@/api/services/file/mutation'
import { useUploadInvoice } from '@/api/services/general-purchase/mutation'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { GeneralPurchaseStatus, GeneralPurchaseType } from '@/types/appTypes'
import { GeneralPurchaseInvoicePayload } from '@/types/payload'
import { PrStatus, PrType } from '@/types/prTypes'
import { fileNameFromUrl } from '@/utils/helper'
import LoadingButton from '@mui/lab/LoadingButton'
import { Button, Card, CardContent, Grid, TextField, Typography } from '@mui/material'
import { QueryObserverResult, RefetchOptions, RefetchQueryFilters } from '@tanstack/react-query'
import { formatDate, formatISO, toDate } from 'date-fns'
import { useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import { toast } from 'react-toastify'
import { useFilePicker } from 'use-file-picker'

type GeneralPurchaseCardProps = {
  purchase: GeneralPurchaseType
  fetchDetail: () => void
}

const UploadSection = ({
  onChange,
  invoice
}: {
  onChange: (file: Partial<File>) => void
  invoice?: { uploadId: string; url?: string; mimeType?: string }
}) => {
  const { openFilePicker, filesContent, clear } = useFilePicker({
    accept: ['image/*', '.pdf', '.doc', '.docx', 'xls', 'xlsx'],
    readAs: 'DataURL'
  })

  useEffect(() => {
    if (filesContent?.length > 0) {
      onChange(filesContent?.[0])
    }
  }, [filesContent])

  return (
    <div className='flex items-center gap-4'>
      <TextField
        size='small'
        fullWidth
        value={filesContent?.[0]?.name || invoice?.url}
        placeholder='Tidak ada file dipilih'
        aria-readonly
        className='flex-1'
      />
      <Button variant='contained' onClick={() => openFilePicker()}>
        Unggah
      </Button>
    </div>
  )
}

const GeneralPurchaseCard = ({ purchase, fetchDetail }: GeneralPurchaseCardProps) => {
  const [notas, setNotas] = useState<(GeneralPurchaseInvoicePayload & { edit?: boolean }) | null>(null)
  const [fileCollections, setFileCollections] = useState<Partial<File>[]>([])

  const { openFilePicker, filesContent, clear } = useFilePicker({
    accept: ['image/*', '.pdf', '.doc', '.docx', 'xls', 'xlsx'],
    readAs: 'DataURL'
  })

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadDocument()
  const { mutate: uploadInvoice, isLoading: uploadInvoiceLoading } = useUploadInvoice()

  const isLoading = uploadLoading || uploadInvoiceLoading

  const handleUploadNote = () => {
    setNotas(prev => ({
      invoiceDate: !!prev?.invoiceDate ? prev.invoiceDate : new Date().toISOString(),
      invoices: !!prev?.invoices ? prev.invoices : [],
      edit: true
    }))
  }

  const submitInvoice = () => {
    Promise.all(
      fileCollections?.map(item =>
        uploadMutate({
          fieldName: `general_purchase_${purchase.id}`,
          file: (item as any)?.content,
          scope: 'public-document',
          fileName: item.name
        })
      )
    )
      .then(values => {
        const uploadIds = values.map(val => ({
          uploadId: val.data?.id ?? ''
        }))
        return uploadIds
      })
      .then(values => {
        const mergedUploadIds = [
          ...notas.invoices.filter(inv => !!inv.uploadId).map(inv => ({ uploadId: inv.uploadId })),
          ...values
        ]

        uploadInvoice(
          {
            id: purchase.id,
            invoiceDate: notas?.invoiceDate,
            invoices: mergedUploadIds
          },
          {
            onSuccess: () => {
              toast.success('Nota Pembelian berhasil diupload')
              fetchDetail()
            }
          }
        )
      })
      .catch(() => {
        setTimeout(() => {
          toast.error('Terjadi kesalahan, silahkan coba beberapa saat lagi.')
          clear()
          setFileCollections([])
        }, 700)
      })
  }

  useEffect(() => {
    if (purchase?.invoices?.length > 0) {
      setNotas({
        invoiceDate: purchase.invoiceDate,
        invoices: purchase.invoices.map(inv => ({
          uploadId: inv.uploadId,
          url: inv.url,
          mimeType: inv.mimeType
        }))
      })
    }
  }, [purchase])

  return (
    <Card>
      <CardContent className='flex flex-col gap-6'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Pembelian Vendor Umum</Typography>
          {notas && notas.edit ? (
            <LoadingButton loading={isLoading} variant='contained' onClick={submitInvoice}>
              Simpan Nota
            </LoadingButton>
          ) : (
            <Button
              variant='contained'
              onClick={handleUploadNote}
              disabled={[GeneralPurchaseStatus.PROCESSED, GeneralPurchaseStatus.CLOSED].includes(
                purchase?.status as GeneralPurchaseStatus
              )}
            >
              {purchase.status === GeneralPurchaseStatus.WAITING_INVOICE ? 'Unggah Nota' : 'Edit Nota'}
            </Button>
          )}
        </div>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2'>
              <small className='text-secondary'>No. Pembelian</small>
              <Link to={`/po/list/${purchase?.purchaseOrder?.id}`}>
                <Typography color='primary'>{purchase?.purchaseOrder?.number}</Typography>
              </Link>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-2'>
              <small className='text-secondary'>Nama Vendor</small>
              <Typography>{purchase.vendorName}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex justify-between items-center gap-4 p-4 rounded-md bg-[#4C4E640D]'>
              <i className='warning-triangle-red text-error size-12' />
              <Typography variant='body1'>
                Unggah nota pembelian dari vendor. Penerimaan barang tidak akan bisa diproses sampai kamu mengunggah
                nota pembelian
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            {notas ? (
              notas?.edit ? (
                <div className='flex flex-col gap-4'>
                  <Typography className='font-semibold'>Unggah Nota Pembelian</Typography>
                  <AppReactDatepicker
                    boxProps={{ className: 'is-full' }}
                    selected={notas?.invoiceDate ? toDate(notas.invoiceDate) : undefined}
                    onChange={(date: Date) => setNotas(prev => ({ ...prev, invoiceDate: formatISO(date) }))}
                    dateFormat='eeee dd/MM/yyyy'
                    customInput={
                      <TextField
                        fullWidth
                        label='Tanggal'
                        className='flex-1'
                        InputProps={{
                          readOnly: true
                        }}
                      />
                    }
                  />
                  {notas.invoices.map(inv => (
                    <UploadSection invoice={inv} onChange={file => setFileCollections(prev => [...prev, file])} />
                  ))}
                  <Button
                    onClick={() => setNotas(prev => ({ ...prev, invoices: [...prev.invoices, { uploadId: '' }] }))}
                    variant='outlined'
                  >
                    Tambah Nota
                  </Button>
                </div>
              ) : (
                <div className='flex flex-col gap-4'>
                  <Typography className='font-semibold'>Nota Pembelian</Typography>
                  <div className='flex flex-col gap-2'>
                    <small className='text-secondary'>Tanggal Nota</small>
                    <Typography>{formatDate(notas.invoiceDate, 'dd/MM/yyyy')}</Typography>
                  </div>
                  <div className='flex flex-col gap-2'>
                    <small>Nota Pembelian</small>

                    {notas.invoices.map(inv => (
                      <div className='flex justify-between'>
                        <Typography>{fileNameFromUrl(inv?.url ?? '')}</Typography>
                        <Button variant='text' className='underline' href={inv?.url} target='_blank'>
                          Unduh
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )
            ) : (
              <div className='flex flex-col items-center gap-2 p-4'>
                <Typography variant='h5'>Belum ada Nota</Typography>
                <Typography>Unggah nota pembelian dari vendor</Typography>
              </div>
            )}
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default GeneralPurchaseCard
