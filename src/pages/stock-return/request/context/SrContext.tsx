import { create<PERSON>ontext, <PERSON><PERSON>N<PERSON>, useContext, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import usePartialState from '@/core/hooks/usePartialState'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { useParams } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { useUploadImage } from '@/api/services/file/mutation'
import { FileType } from '@/types/fileTypes'
import { UploadPayload } from '@/types/payload'
import { MrLogType, MrParams, MrPayload, MrStatus, MrType } from '@/types/mrTypes'
import { SrType } from '@/types/srTypes'
import { useAddMr } from '@/api/services/mr/mutation'
import MrQueryMethods, { MR_LIST_QUERY_KEY, MR_LOG_LIST_QUERY_KEY, MR_QUERY_KEY } from '@/api/services/mr/query'
import SrQueryMethods, { SR_LIST_QUERY_KEY } from '@/api/services/sr/query'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import {
  QueryFn,
  QueryListResponseFn,
  SetState,
  UseApiResponseAsyncMutation,
  UseApiResponseMutation
} from '@/types/alias'

interface SrContextProps {
  showLoading: boolean
  mrListResponse: ListResponse<MrType>
  mrsParams: MrParams
  setMrsParams: SetState<MrParams>
  fetchMrList: QueryListResponseFn<MrType>
  mrData: MrType
  clearMrData: () => void
  selectedMrId?: string
  setSelectedMrId: SetState<string>
  setPartialMrsParams: (fieldName: string, value: any) => void
  fetchMrData: QueryFn<MrType>
  uploadMutate: UseApiResponseAsyncMutation<FileType, UploadPayload>
  createMrMutate: UseApiResponseMutation<MrType, MrPayload>
  logList: MrLogType[]
  fetchLogList: QueryFn<MrLogType[]>
  refreshData: () => void
  canUpdate: boolean
  srList: SrType[]
  fetchSrList: QueryFn<SrType[]>
}

export const SrContext = createContext<SrContextProps>({} as SrContextProps)

interface SrContextProviderProps {
  children: ReactNode
}

export const useSr = () => {
  return useContext(SrContext)
}

export function SrContextProvider({ children }: SrContextProviderProps) {
  const { accountPermissions } = useAuth()
  const { mrId } = useParams()
  const [selectedMrId, setSelectedMrId] = useState<string>(mrId)

  const [mrsParams, setPartialMrsParams, setMrsParams] = usePartialState<MrParams>({
    limit: 10,
    page: 1
  })

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: createMrMutate, isLoading: createMrLoading } = useAddMr()

  const canUpdate = accountPermissions.includes(`${DefaultApprovalScope.MaterialRequest}.update`)

  const {
    data: mrData,
    refetch: fetchMrData,
    isLoading: fetchMrDataLoading,
    remove: removeMrData
  } = useQuery({
    enabled: !!selectedMrId,
    queryKey: [MR_QUERY_KEY, selectedMrId],
    queryFn: () => MrQueryMethods.getMr(selectedMrId)
  })

  const {
    data: logList,
    refetch: fetchLogList,
    remove: removeMrLogList
  } = useQuery({
    enabled: !!selectedMrId,
    queryKey: [MR_LOG_LIST_QUERY_KEY, selectedMrId],
    queryFn: () => MrQueryMethods.getMrLogList(selectedMrId, { limit: 10000 }),
    placeholderData: []
  })

  const {
    data: mrListResponse,
    refetch: fetchMrList,
    isLoading: fetchMrsLoading
  } = useQuery({
    enabled: !!mrsParams.search,
    queryKey: [MR_LIST_QUERY_KEY, JSON.stringify(mrsParams), 'stock-return'],
    queryFn: () => {
      const { search } = mrsParams
      return MrQueryMethods.getMrList({
        ...(search && { search }),
        limit: 10000,
        status: MrStatus.APPROVED
      })
    },
    placeholderData: defaultListData as ListResponse<MrType>,
    cacheTime: 0
  })

  const { data: srList, refetch: fetchSrList } = useQuery({
    enabled: !!selectedMrId,
    queryKey: [SR_LIST_QUERY_KEY, selectedMrId],
    queryFn: async () => {
      const list = await SrQueryMethods.getSrList({
        limit: 10000,
        materialRequestId: selectedMrId
      })
      return Promise.all(
        list.map(async sr => {
          return await SrQueryMethods.getSr(sr.id)
        })
      )
    },
    placeholderData: []
  })

  const clearMrData = () => {
    removeMrData()
    removeMrLogList()
    setSelectedMrId('')
  }

  const refreshData = () => {
    fetchMrData()
    fetchLogList()
    fetchMrList()
  }

  const value = {
    mrListResponse: mrListResponse ?? defaultListData,
    showLoading: uploadLoading || createMrLoading,
    fetchMrList: fetchMrList,
    mrsParams,
    setPartialMrsParams,
    setMrsParams,
    mrData,
    selectedMrId,
    setSelectedMrId,
    clearMrData,
    fetchMrData,
    uploadMutate,
    createMrMutate,
    logList,
    fetchLogList,
    refreshData,
    canUpdate,
    srList,
    fetchSrList
  }

  return (
    <SrContext.Provider value={value}>
      <>{children}</>
    </SrContext.Provider>
  )
}
