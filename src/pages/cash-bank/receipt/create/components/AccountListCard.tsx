import { <PERSON><PERSON>, <PERSON>, CardContent, FormHelperText, Typography } from '@mui/material'
import { useMemo, useState } from 'react'
import DialogItems from './dialog-items'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { CreateCashReceiptPayload, CreatePaymentItemPayload, CreatePaymentPayload } from '../../config/types'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../config/table'
import Table from '@/components/table'
import { toCurrency } from '@/utils/helper'

const ItemListCard = () => {
  const [dialogItem, setDialogItem] = useState<boolean>(false)
  const { control, reset } = useFormContext<CreateCashReceiptPayload>()
  const { remove, fields, append } = useFieldArray({ control, name: 'items' })

  const tableOptions: any = useMemo(
    () => ({
      data: fields ?? [],
      columns: tableColumns({
        delete: index => remove(index)
      }),
      initialState: {
        pagination: {
          pageSize: 10,
          pageIndex: 0
        }
      },
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [fields, remove]
  )

  const table = useReactTable(tableOptions)

  const handleAddItem = (item: CreatePaymentItemPayload) => {
    append(item)
    setDialogItem(false)
  }

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between'>
            <Typography variant='h5'>List Item</Typography>
            <Button onClick={() => setDialogItem(true)} variant='outlined'>
              Tambah Item
            </Button>
          </div>
          <div>
            {fields?.length > 0 ? (
              <div className='flex flex-col gap-4'>
                <div className='rounded-[8px] shadow-md'>
                  <Table table={table} disablePagination headerColor='green' />
                </div>
                <div className='flex flex-col gap-1 rounded-md p-3 bg-[#DBF7E8]'>
                  <small>Total Penerimaan</small>
                  <Typography color='primary'>
                    {toCurrency(fields.reduce((acc, item) => acc + item.amount, 0))}
                  </Typography>
                </div>
              </div>
            ) : (
              <Controller
                control={control}
                name='items'
                rules={{ required: true }}
                render={({ fieldState: { error } }) => (
                  <>
                    <div className='flex flex-col justify-center items-center gap-2'>
                      <Typography variant='h5'>Belum ada item</Typography>
                      <Typography variant='body1'>
                        Tambahkan detil item yang harus dibayar dengan tombol diatas
                      </Typography>
                    </div>
                    {!!error && <FormHelperText error>Wajib diisi.</FormHelperText>}
                  </>
                )}
              />
            )}
          </div>
        </CardContent>
      </Card>
      {dialogItem && <DialogItems onSubmit={handleAddItem} open={dialogItem} setOpen={setDialogItem} />}
    </>
  )
}

export default ItemListCard
