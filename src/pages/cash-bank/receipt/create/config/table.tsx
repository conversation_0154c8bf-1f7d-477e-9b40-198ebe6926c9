import { createColumnHelper } from '@tanstack/react-table'
import { CreatePaymentItemPayload } from '../../config/types'
import { toCurrency } from '@/utils/helper'
import { IconButton } from '@mui/material'

const columnHelper = createColumnHelper<CreatePaymentItemPayload>()

type RowActionType = {
  delete: (index: number) => void
}

export const tableColumns = (rowAction: RowActionType) => [
  columnHelper.accessor('code', {
    header: 'Kode Akun',
    cell: ({ row }) => row.original.code
  }),
  columnHelper.accessor('name', {
    header: '<PERSON>a <PERSON>kun',
    cell: ({ row }) => row.original.name
  }),
  columnHelper.accessor('amount', {
    header: 'Nominal',
    cell: ({ row }) => toCurrency(row.original.amount)
  }),
  columnHelper.accessor('description', {
    header: 'Memo',
    cell: ({ row }) => row.original?.description ?? '-'
  }),
  columnHelper.display({
    id: 'action',
    header: 'Action',
    cell: ({ row }) => {
      return (
        <IconButton onClick={() => rowAction.delete(row.index)} className='flex items-center gap-0.5'>
          <i className='ri-delete-bin-line text-textSecondary' />
        </IconButton>
      )
    },
    enableSorting: false
  })
]
