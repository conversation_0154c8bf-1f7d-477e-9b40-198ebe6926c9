import { useRouter } from '@/routes/hooks'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Grid, Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import ItemListCard from './components/AccountListCard'
import { FormProvider, useForm } from 'react-hook-form'
import { CreateCashReceiptPayload, CreatePaymentItemPayload, CreatePaymentPayload } from '../config/types'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { APPROVER_USER_LIST_QUERY_KEY, DEFAULT_APPROVER_QUERY_KEY } from '@/api/services/user/query'
import CompanyQueryMethods from '@/api/services/company/query'
import { useAuth } from '@/contexts/AuthContext'
import ApprovalListCard from './components/ApprovalListCard'
import { useEffect } from 'react'
import ReceiptDetailCard from './components/PaymentDetailCard'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useReceipt } from '../context/ReceiptContext'
import { useCreateCashReceipt, useCreatePayment } from '@/api/services/cashbank/mutation'
import { toast } from 'react-toastify'

const PaymentCreatePage = () => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const { ownSiteList, userProfile } = useAuth()
  const methods = useForm<CreateCashReceiptPayload>({
    defaultValues: {
      items: [],
      type: 'GENERAL'
    }
  })
  const { reset, getValues, handleSubmit } = methods
  const { fetchCashReceiptList } = useReceipt()

  const { mutate: createMutate, isLoading: loadingMutate } = useCreateCashReceipt()

  const scope = `cash-receipt`

  const { data: approverList } = useQuery({
    enabled: !!ownSiteList?.[0]?.id && !!userProfile?.departmentId,
    queryKey: [DEFAULT_APPROVER_QUERY_KEY, scope, ownSiteList?.[0]?.id, userProfile?.departmentId],
    queryFn: () =>
      UserQueryMethods.getDefaultApproverList({
        limit: 1000,
        scope,
        siteId: ownSiteList?.[0]?.id,
        departmentId: userProfile?.departmentId
      }),
    placeholderData: []
  })

  const onSubmit = (data: CreateCashReceiptPayload) => {
    setConfirmState({
      open: true,
      title: 'Buat Penerimaan',
      content: 'Apakah kamu yakin akan penerimaan ini? Action ini tidak dapat diubah',
      confirmText: 'Buat Penerimaan',
      onConfirm: () => {
        createMutate(data, {
          onSuccess: () => {
            toast.success('Pencatatan berhasil dibuat')
            fetchCashReceiptList()
            router.push('/cash-bank/receipt')
          }
        })
      }
    })
  }

  useEffect(() => {
    if (approverList?.length > 0) {
      reset({
        ...getValues(),
        approvals: approverList.map(approver => ({
          userId: approver.user?.id
        }))
      })
    }
  }, [approverList])

  return (
    <FormProvider {...methods}>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Link to='#' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Kas & Bank</Typography>
            </Link>
            <Link to='/cash-bank/payments' replace>
              <Typography color='var(--mui-palette-text-disabled)'>Penerimaan</Typography>
            </Link>
            <Typography>Tambah Penerimaan</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Tambah Penerimaan</Typography>
              <Typography>Lengkapi data dan tambahkan pencatatan Penerimaan</Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              <Button
                disabled={loadingMutate}
                onClick={() => router.back()}
                color='secondary'
                variant='outlined'
                className='is-full sm:is-auto'
              >
                Batalkan
              </Button>
              <Button
                variant='contained'
                disabled={loadingMutate}
                onClick={handleSubmit(onSubmit)}
                className='is-full sm:is-auto'
              >
                Tambah Penerimaan
              </Button>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <ItemListCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <ReceiptDetailCard />
        </Grid>
        <Grid item xs={12} md={6}>
          <ApprovalListCard approverList={approverList?.map(approver => approver.user) ?? []} />
        </Grid>
      </Grid>
    </FormProvider>
  )
}

export default PaymentCreatePage
