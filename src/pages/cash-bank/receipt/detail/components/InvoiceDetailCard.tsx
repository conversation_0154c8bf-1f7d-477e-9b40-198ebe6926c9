import SellingInvoiceQueryMethods from '@/api/services/selling-invoice/query'
import { Card, CardContent, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type Props = {
  purchaseInvoiceId?: string
}

const InvoiceDetailCard = ({ purchaseInvoiceId }: Props) => {
  const { data: salesInvoiceData } = useQuery({
    queryKey: ['SALES_INVOICE_DETAIL_QUERY_KEY', purchaseInvoiceId],
    queryFn: () => SellingInvoiceQueryMethods.getSellingInvoice(purchaseInvoiceId)
  })
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Faktur</Typography>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='flex flex-col gap-2'>
            <small>Syarat Bayar</small>
            <Typography>{salesInvoiceData?.paymentTerms}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Tanggal Faktur</small>
            <Typography>
              {salesInvoiceData?.invoiceDate ? formatDate(salesInvoiceData?.invoiceDate, 'dd/MM/yyyy') : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Jatuh Tempo</small>
            <Typography color='error'>
              {salesInvoiceData?.paymentDueDate ? formatDate(salesInvoiceData?.paymentDueDate, 'dd/MM/yyyy') : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Memo</small>
            <Typography>{salesInvoiceData?.note ?? '-'}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default InvoiceDetailCard
