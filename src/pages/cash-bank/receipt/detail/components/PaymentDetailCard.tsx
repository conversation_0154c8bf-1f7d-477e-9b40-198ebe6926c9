import { Card, CardContent, Typography } from '@mui/material'
import { useReceipt } from '../../context/ReceiptContext'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'
import { toCurrency } from '@/utils/helper'

const PaymentDetailCard = () => {
  const { cashReceiptData } = useReceipt()
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Penerimaan</Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small><PERSON><PERSON> Penerimaan</small>
          <Typography>
            {formatDate(cashReceiptData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}
          </Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Diter<PERSON> di Akun</small>
          <Typography>
            {cashReceiptData?.account?.name
              ? `[${cashReceiptData?.account?.code}] ${cashReceiptData?.account?.name}`
              : '-'}
          </Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>No Voucher</small>
          <Typography>{cashReceiptData?.voucherNumber}</Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Memo</small>
          <Typography>{cashReceiptData?.memo ?? '-'}</Typography>
        </div>
        <div className='flex flex-col gap-2 w-full'>
          <small>Nominal Diterima</small>
          <Typography>{toCurrency(cashReceiptData?.totalAmount)}</Typography>
        </div>
      </CardContent>
    </Card>
  )
}

export default PaymentDetailCard
