import { <PERSON><PERSON>, <PERSON>, CardContent, FormHelperText, Typography } from '@mui/material'
import { useMemo, useState } from 'react'
import { Controller, useFieldArray, useFormContext } from 'react-hook-form'
import { CreateCashReceiptPayload, CreatePaymentItemPayload, CreatePaymentPayload } from '../../config/types'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '../../create/config/table'
import Table from '@/components/table'
import { toCurrency } from '@/utils/helper'
import { useReceipt } from '../../context/ReceiptContext'

const ItemListCard = () => {
  const { cashReceiptData } = useReceipt()

  const items = cashReceiptData?.items ?? []

  const tableOptions: any = useMemo(
    () => ({
      data: items,
      columns: [
        {
          accessorKey: 'accountId',
          header: 'Kode Akun',
          cell: ({ row }) => row.original?.account?.code
        },
        {
          accessorKey: 'accountId',
          header: 'Nama Akun',
          cell: ({ row }) => row.original?.account?.name
        },
        {
          accessorKey: 'amount',
          header: 'Nominal',
          cell: ({ row }) => <Typography>{toCurrency(row.original?.amount)}</Typography>
        },
        {
          accessorKey: 'description',
          header: 'Catatan'
        }
      ],
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [items]
  )

  const table = useReactTable(tableOptions)

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between'>
          <Typography variant='h5'>List Item</Typography>
        </div>
        <div>
          {items?.length > 0 ? (
            <div className='flex flex-col gap-4'>
              <div className='rounded-[8px] shadow-md'>
                <Table
                  table={table}
                  emptyLabel={
                    <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                      <Typography>Belum ada Data</Typography>
                      <Typography className='text-sm text-gray-400'>Semua item akan ditampilkan di sini</Typography>
                    </td>
                  }
                  disablePagination
                  headerColor='green'
                />
              </div>
              <div className='flex flex-col gap-1 rounded-md p-3 bg-[#DBF7E8]'>
                <small>Total Penerimaan</small>
                <Typography color='primary'>{toCurrency(cashReceiptData?.totalAmount ?? 0)}</Typography>
              </div>
            </div>
          ) : (
            <div className='flex flex-col justify-center items-center gap-2'>
              <Typography variant='h5'>Belum ada item</Typography>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default ItemListCard
