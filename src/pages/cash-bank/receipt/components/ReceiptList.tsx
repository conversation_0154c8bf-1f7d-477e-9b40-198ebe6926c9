import { useEffect, useMemo, useState } from 'react'
import { Typography } from '@mui/material'
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'

import {
  getCoreRowModel,
  useReactTable,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFacetedMinMaxValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

import DebouncedInput from '@/components/DebounceInput'
import Table from '@/components/table'
import { tableColumns } from '../config/table'
import { useRouter } from '@/routes/hooks'
// import { mrPriorityOptions, mrStatusOptions } from '../config/utils'
import FilterGroupDialog, { FilterGroupConfig, FilterValues } from '@/components/layout/shared/filter/FilterGroup'
import { MrStatus } from '@/types/mrTypes'
import { useAuth } from '@/contexts/AuthContext'
import { useLocation } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { PROJECT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ProjectType } from '@/types/projectTypes'
import { useReceipt } from '../context/ReceiptContext'
import { PaymentStatus } from '../config/types'
import { paymentStatusOptions } from '../config/utils'

const ReceiptList = () => {
  const router = useRouter()
  const { state } = useLocation()

  const {
    cashReceiptListResponse,
    cashReceiptParams,
    setPartialCashReceiptParams,
    setCashReceiptParams,
    setSelectedCashReceiptId
  } = useReceipt()

  const { departmentList, ownSiteList } = useAuth()

  const { items: mrList, totalItems, totalPages, limit: limitItems, page: pageItems } = cashReceiptListResponse
  const { page, search, status, startDate, endDate, siteIds, priority, departmentId, unitId, projectId } =
    cashReceiptParams

  const [filterGroupConfig, setFilterGroupConfig] = useState<FilterGroupConfig>({})

  const { data: projectList } = useQuery({
    queryKey: [PROJECT_LIST_QUERY_KEY],
    queryFn: () => CompanyQueryMethods.getProjectList({ limit: Number.MAX_SAFE_INTEGER }),
    placeholderData: defaultListData as ListResponse<ProjectType>
  })

  // TODO: MOVE THIS SHIT

  const tableOptions: any = useMemo(
    () => ({
      data: mrList ?? [],
      columns: tableColumns({
        onDetail: row => router.push(`/cash-bank/receipt/${row.id}`)
      }),
      initialState: {
        pagination: {
          pageSize: cashReceiptParams.limit ?? 10,
          pageIndex: page - 1
        }
      },
      state: {
        pagination: {
          pageSize: limitItems,
          pageIndex: pageItems - 1
        }
      },
      manualPagination: true,
      rowCount: totalItems,
      pageCount: totalPages,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [cashReceiptParams, page, limitItems, pageItems, totalItems, totalPages]
  )
  const table = useReactTable(tableOptions)

  const onFilterChanged = ({ date, status, unit, priority, site, department, project }: FilterValues) => {
    setCashReceiptParams(prev => {
      return {
        ...prev,
        page: 1,
        startDate: date[0],
        endDate: date[1],
        status: status.length > 0 ? (status[0] as PaymentStatus) : undefined,
        unitId: unit.length > 0 ? unit[0] : undefined,
        priority: priority.length > 0 ? priority[0] : undefined,
        siteIds: site.length > 0 ? site[0] : undefined,
        departmentId: department.length > 0 ? department[0] : undefined,
        projectId: project?.length > 0 ? project[0] : undefined
      }
    })
  }

  useEffect(() => {
    setFilterGroupConfig({
      date: {
        options: [],
        values: [startDate, endDate]
      },
      status: {
        options: paymentStatusOptions,
        values: status ? [status] : []
      },
      // priority: {
      //   options: mrPriorityOptions,
      //   values: priority ? [priority] : []
      // },
      site: {
        options: ownSiteList.map(site => {
          return { value: site.id, label: site.name }
        }),
        values: siteIds ? [siteIds] : []
      },
      department: {
        options: departmentList?.map(department => ({ value: department.id, label: department.name })) ?? [],
        values: departmentId ? [departmentId] : []
      },
      ...(projectList?.items?.length > 0 && {
        project: {
          options: projectList?.items.map(project => ({ value: project.id, label: project.name })) ?? [],
          values: projectId ? [projectId] : []
        }
      })
    })
  }, [cashReceiptParams, projectList])

  useEffect(() => {
    setCashReceiptParams(prev => ({ ...prev, status: undefined }))
  }, [])

  return (
    <Card>
      <div className='flex justify-between gap-4 p-5 flex-col items-start sm:flex-row items-center'>
        <div className='flex gap-4 items-center flex-col sm:flex-row is-full sm:is-auto'>
          <DebouncedInput
            value={search}
            onChange={value => setCashReceiptParams(prev => ({ ...prev, page: 1, search: value as string }))}
            placeholder='Cari'
            className='is-full sm:w-[240px]'
          />
          {/* <FilterGroupDialog
            config={filterGroupConfig}
            onFilterApplied={onFilterChanged}
            onRemoveFilter={onFilterChanged}
          /> */}
        </div>
        <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
          {/* <Button
            color='secondary'
            variant='outlined'
            startIcon={<i className='ri-upload-2-line' />}
            className='is-full sm:is-auto'
          >
            Ekspor
          </Button> */}
          <Button
            variant='contained'
            onClick={() => router.push('/cash-bank/receipt/create')}
            className='is-full sm:is-auto'
          >
            Tambah Penerimaan
          </Button>
        </div>
      </div>
      <Table
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada Pencatatan Pembayaran</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua pencatatan pembayaran yang telah kamu buat akan ditampilkan di sini
            </Typography>
          </td>
        }
        onRowsPerPageChange={pageSize => {
          if (pageSize > totalItems) {
            setCashReceiptParams(prev => ({ ...prev, limit: totalItems, page: 1 }))
          } else {
            setPartialCashReceiptParams('limit', pageSize)

            const maxPage = Math.ceil(totalItems / pageSize)
            if (page > maxPage) {
              setCashReceiptParams(prev => ({ ...prev, page: maxPage }))
            }
          }
        }}
        onPageChange={pageIndex => setPartialCashReceiptParams('page', pageIndex)}
      />
    </Card>
  )
}

export default ReceiptList
