import { toTitleCase } from '@/utils/helper'

export const paymentStatusOptions = [
  { value: 'PROCESSED', label: 'Diproses' },
  { value: 'APPROVED', label: 'Disetujui' },
  { value: 'REJECTED', label: '<PERSON><PERSON><PERSON>' },
  { value: 'CANCELED', label: '<PERSON><PERSON>alk<PERSON>' }
]

export const paymentTypesOptions = [
  { value: 'GENERAL', label: 'General' },
  { value: 'PURCHASE', label: 'Pembelian' }
]

export const getStatusConfig = (status: string) => {
  switch (status) {
    case 'PROCESSED':
      return {
        label: 'Diproses',
        color: 'warning'
      }
      break
    case 'APPROVED':
      return {
        label: 'Disetujui',
        color: 'success'
      }
      break
    case 'REJECTED':
      return {
        label: '<PERSON><PERSON><PERSON>',
        color: 'error'
      }
      break
    case 'CANCELED':
      return {
        label: '<PERSON>batalkan',
        color: 'error'
      }
      break
    default:
      return {
        label: toTitleCase(status),
        color: 'default'
      }
  }
}
