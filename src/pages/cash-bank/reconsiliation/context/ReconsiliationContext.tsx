import { defaultListData } from '@/api/queryClient'
import AccountsQueryMethods, { JOURNAL_LINE_LIST_QUERY_KEY } from '@/api/services/account/query'
import usePartialState from '@/core/hooks/usePartialState'
import { AccountType, JournalLineParams, JournalLineType } from '@/types/accountTypes'
import { QueryFn } from '@/types/alias'
import { ListResponse } from '@/types/api'
import { useQuery } from '@tanstack/react-query'
import React, { createContext, useContext, useEffect, useState } from 'react'
import { ReconciliationType } from '../config/types'
import { ListParams } from '@/types/payload'
import CashBankQueryMethods, {
  RECONCILIATION_LIST_QUERY_KEY,
  RECONCILIATION_QUERY_KEY
} from '@/api/services/cashbank/query'
import { useParams } from 'react-router-dom'

type ReconsiliationContextProps = {
  journalLines: ListResponse<JournalLineType>
  fetchJournalLines: QueryFn<ListResponse<JournalLineType>>
  loadingJournalLines: boolean
  reconciliationList: ListResponse<ReconciliationType>
  fetchReconciliationList: QueryFn<ListResponse<ReconciliationType>>
  reconciliationData: ReconciliationType
  fetchReconciliation: QueryFn<ReconciliationType>
  removeJournalLines: () => void
  loadingReconciliation: boolean
  journalLineParams: JournalLineParams
  setJournalLineParams: React.Dispatch<React.SetStateAction<JournalLineParams>>
  setPartialJournalLineParams: (key: keyof JournalLineParams, value: any) => void
  reconciliationParams: ListParams
  setPartialReconciliationParams: (key: keyof ListParams, value: any) => void
  setReconciliationParams: React.Dispatch<React.SetStateAction<ListParams>>
  handleSearch: () => void
  selectedAccountCashbank: AccountType
  setSelectedAccountCashbank: React.Dispatch<React.SetStateAction<AccountType>>
}

const ReconsiliationContext = createContext<ReconsiliationContextProps>({} as ReconsiliationContextProps)

export const useReconsiliation = () => {
  const context = useContext(ReconsiliationContext)
  if (context === undefined) {
    throw new Error('useReconsiliation must be used within a ReconsiliationProvider')
  }
  return context
}

export const ReconsiliationProvider = ({ children }: { children: React.ReactNode }) => {
  const { reconciliationId } = useParams()
  const [isSearching, setSearching] = useState<boolean>(false)
  const [selectedAccountCashbank, setSelectedAccountCashbank] = useState<AccountType>(null)
  const [selectedReconciliationId, setSelectedReconciliationId] = useState<string>(reconciliationId ?? '')
  const [journalLineParams, setPartialJournalLineParams, setJournalLineParams] = usePartialState<JournalLineParams>({
    accountId: '',
    isReconciliated: false
  })
  const [reconciliationParams, setPartialReconciliationParams, setReconciliationParams] = usePartialState<ListParams>({
    limit: 10,
    page: 1
  })

  const {
    data: journalLines,
    refetch: fetchJournalLines,
    isFetching: loadingJournalLines,
    remove: removeJournalLines
  } = useQuery({
    enabled: !!journalLineParams.accountId && !!isSearching,
    queryKey: [JOURNAL_LINE_LIST_QUERY_KEY, JSON.stringify(journalLineParams)],
    queryFn: () => {
      setSearching(false)
      return AccountsQueryMethods.getJournalLines(journalLineParams)
    },
    placeholderData: defaultListData as ListResponse<JournalLineType>
  })

  const {
    data: reconciliationList,
    refetch: fetchReconciliationList,
    isFetching: loadingReconciliation
  } = useQuery({
    queryKey: [RECONCILIATION_LIST_QUERY_KEY, JSON.stringify(reconciliationParams)],
    queryFn: () => {
      setSearching(false)
      return CashBankQueryMethods.getReconciliationList(reconciliationParams)
    },
    placeholderData: defaultListData as ListResponse<ReconciliationType>
  })

  const { data: reconciliationData, refetch: fetchReconciliation } = useQuery({
    enabled: !!selectedReconciliationId,
    queryKey: [RECONCILIATION_QUERY_KEY, selectedReconciliationId],
    queryFn: () => {
      return CashBankQueryMethods.getReconciliation(selectedReconciliationId)
    }
  })

  const handleSearch = () => {
    setSearching(true)
  }

  useEffect(() => {
    if (!!reconciliationId) {
      setSelectedReconciliationId(reconciliationId)
    }
  }, [reconciliationId])

  const value = {
    journalLines,
    fetchJournalLines,
    removeJournalLines,
    loadingJournalLines,
    journalLineParams,
    setJournalLineParams,
    setPartialJournalLineParams,
    reconciliationParams,
    setReconciliationParams,
    setPartialReconciliationParams,
    handleSearch,
    selectedAccountCashbank,
    setSelectedAccountCashbank,
    reconciliationList,
    fetchReconciliationList,
    reconciliationData,
    fetchReconciliation,
    loadingReconciliation
  }

  return <ReconsiliationContext.Provider value={value}>{children}</ReconsiliationContext.Provider>
}
