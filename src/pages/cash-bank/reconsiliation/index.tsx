import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton, Grid, Typo<PERSON> } from '@mui/material'
import ReconciliationFormCard from './components/ReconciliationFormCard'
import ReconciliationCardResult from './components/ReconciliationResultCard'
import ReconciliationBalanceCard from './components/ReconciliationBalanceCard'
import ReconciliationSummary from './components/ReconciliationSummaryCard'
import { useReconsiliation } from './context/ReconsiliationContext'
import { FormProvider, useForm } from 'react-hook-form'
import { CreateReconciliationPayload } from './config/types'
import { useCreateReconciliation } from '@/api/services/cashbank/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { toast } from 'react-toastify'
import { useRouter } from '@/routes/hooks'
import { Link } from 'react-router-dom'

const ReconsiliationPage = () => {
  const router = useRouter()
  const { setConfirmState } = useMenu()
  const {
    journalLines,
    fetchReconciliationList,
    selectedAccountCashbank,
    setSelectedAccountCashbank,
    removeJournalLines
  } = useReconsiliation()
  const methods = useForm<CreateReconciliationPayload>({
    defaultValues: {
      actualBalance: 0
    }
  })

  const { handleSubmit, reset } = methods

  const { mutate: createReconciliation, isLoading: isLoadingReconciliation } = useCreateReconciliation()

  const onSubmitReconsiliation = (data: CreateReconciliationPayload) => {
    setConfirmState({
      open: true,
      title: 'Buat Rekonsiliasi',
      content:
        'Apakah kamu yakin akan membuat Rekonsiliasi ini dan menetapkan saldo aktual untuk akun kas & bank ini? Pastikan saldo aktual sudah benar',
      confirmText: 'Buat Rekonsiliasi',
      onConfirm: () => {
        createReconciliation(
          {
            actualBalance: data.actualBalance,
            accountId: data.accountId,
            checklists: data.checklists,
            accountBalance: selectedAccountCashbank?.balance,
            note: selectedAccountCashbank?.note,
            date: data.date
          },
          {
            onSuccess: () => {
              toast.success('Rekonsiliasi berhasil dibuat')
              reset({ actualBalance: 0 })
              setSelectedAccountCashbank(null)
              removeJournalLines()
              fetchReconciliationList()
              router.push('/reconciliation')
            }
          }
        )
      }
    })
  }

  return (
    <FormProvider {...methods}>
      <Grid className='relative' container spacing={4}>
        <Grid item xs={12}>
          <Breadcrumbs>
            <Link to='#'>
              <Typography color='var(--color-text-disabled)'>Kas & Bank</Typography>
            </Link>
            <Link to='/reconciliation'>
              <Typography color='var(--color-text-disabled)'>Rekonsiliasi Bank</Typography>
            </Link>
            <Typography>Buat Rekonsiliasi</Typography>
          </Breadcrumbs>
        </Grid>
        <Grid item xs={12}>
          <div className='flex justify-between items-end'>
            <div className='flex flex-col'>
              <Typography variant='h4'>Rekonsiliasi Bank</Typography>
              <Typography>Pilih akun kas/bank dan lakukan rekonsiliasi</Typography>
            </div>
            <div className='flex items-center gap-x-4 max-sm:gap-y-4 is-full flex-col sm:is-auto sm:flex-row'>
              <LoadingButton
                loading={isLoadingReconciliation}
                onClick={handleSubmit(onSubmitReconsiliation)}
                variant='contained'
              >
                Buat Rekonsiliasi
              </LoadingButton>
            </div>
          </div>
        </Grid>
        <Grid item xs={12}>
          <ReconciliationFormCard />
        </Grid>
        {journalLines?.items?.length > 0 && (
          <>
            <Grid item xs={12}>
              <ReconciliationCardResult />
            </Grid>
            {/* <Grid item xs={12} md={6}>
              <ReconciliationBalanceCard />
            </Grid> */}
            <Grid item xs={12} className='sticky bottom-2'>
              <ReconciliationSummary />
            </Grid>
          </>
        )}
      </Grid>
    </FormProvider>
  )
}

export default ReconsiliationPage
