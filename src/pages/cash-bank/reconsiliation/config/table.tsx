import { JournalLineType } from '@/types/accountTypes'
import { toCurrency } from '@/utils/helper'
import { Checkbox, Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { JournalRefDocType } from './types'

type RowActionType = {
  detail: (row: JournalLineType, type: JournalRefDocType) => void
  withChecklist?: boolean
}

const columnHelper = createColumnHelper<JournalLineType>()

const refDocTypeConfig = (type: JournalRefDocType) => {
  switch (type) {
    case JournalRefDocType.Payment:
      return 'Pembayaran'
    case JournalRefDocType.CashReceipt:
      return 'Penerimaan'
    default:
      return '-'
  }
}

export const journalLineColumns = ({ withChecklist = true, ...rowAction }: RowActionType) => [
  columnHelper.accessor('voucherNumber', {
    header: 'No Voucher',
    cell: ({ row }) => row.original?.voucherNumber ?? '-'
  }),
  columnHelper.accessor('journalId', {
    header: 'No Referensi',
    cell: ({ row }) => {
      const data = row.original
      return (
        <div className='flex flex-col space-y-1'>
          <Typography
            onClick={() => rowAction.detail(data, data?.journal?.refDocType as JournalRefDocType)}
            className='cursor-pointer text-sm font-medium'
            color='primary'
          >
            {data?.journal?.refDocNumber ?? data?.journal?.number}
          </Typography>
        </div>
      )
    },
    size: 200
  }),
  columnHelper.accessor('debit', {
    header: 'Debit',
    cell: ({ getValue }) => {
      const value = getValue<number>()
      return (
        <div className='flex flex-col gap-1'>
          <span className={`text-sm font-medium ${value > 0 ? 'text-primary' : 'text-gray-700'}`}>
            {toCurrency(value)}
          </span>
        </div>
      )
    }
  }),
  columnHelper.accessor('credit', {
    header: 'Kredit',
    cell: ({ getValue }) => {
      const value = getValue<number>()
      return (
        <div className='flex flex-col gap-1'>
          <span className={`text-sm font-medium ${value > 0 ? 'text-error' : 'text-gray-700'}`}>
            {toCurrency(value)}
          </span>
        </div>
      )
    }
  }),
  ...(!!withChecklist
    ? [
        {
          id: 'select',
          header: 'Checklist',
          size: 20,
          cell: ({ row }) => (
            <Checkbox
              checked={row.getIsSelected()}
              onChange={row.getToggleSelectedHandler()}
              disabled={!row.getCanSelect()}
              indeterminate={row.getIsSomeSelected()}
            />
          ),
          enableSorting: false
        }
      ]
    : [])
]
