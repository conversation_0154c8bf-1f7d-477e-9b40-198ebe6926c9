import { Card, CardContent, Grid, Typography } from '@mui/material'
import { useState } from 'react'
import { useReconsiliation } from '../../context/ReconsiliationContext'
import { formatDate } from 'date-fns'

const ReconciliationFormCard = () => {
  const { reconciliationData } = useReconsiliation()
  const [queries, setQueries] = useState<string>()

  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between'>
          <Typography variant='h5'>Akun Rekonsiliasi</Typography>
        </div>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <div className='flex flex-col gap-2'>
              <small>Akun Kas/Bank</small>
              <Typography>
                {reconciliationData?.account?.name
                  ? `[${reconciliationData?.account?.code}] ${reconciliationData?.account?.name}`
                  : '-'}
              </Typography>
            </div>
          </Grid>
          <Grid item xs={12} md={6}>
            <div className='flex flex-col gap-2'>
              <small>Tgl Rekonsiliasi</small>
              <Typography>
                {reconciliationData?.date ? formatDate(reconciliationData?.date, 'dd/MM/yyyy') : '-'}
              </Typography>
            </div>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default ReconciliationFormCard
