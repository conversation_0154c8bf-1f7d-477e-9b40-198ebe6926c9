import { toCurrency } from '@/utils/helper'
import { Card, CardContent, TextField, Typography } from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useMemo } from 'react'
import CurrencyField from '@/components/numeric/CurrencyField'
import { useReconsiliation } from '../../context/ReconsiliationContext'

const ReconciliationSummary = () => {
  const { selectedAccountCashbank, reconciliationData } = useReconsiliation()

  return (
    <Card>
      <CardContent>
        <div className='flex justify-between gap-4 items-center'>
          <div className='flex flex-col rounded-[8px] px-4 py-2 bg-[#DBF7E8] is-full'>
            <small>Total Saldo</small>
            <Typography variant='h5' color='primary'>
              {toCurrency(reconciliationData?.accountBalance ?? 0)}
            </Typography>
          </div>
          <div className='flex flex-col rounded-[8px] px-4 py-2 bg-[#4C4E640D] is-full'>
            <small>Saldo Aktual</small>
            <Typography variant='h5' color='primary'>
              {toCurrency(reconciliationData?.actualBalance ?? 0)}
            </Typography>
          </div>
          <div className='flex flex-col rounded-[8px] px-4 py-2 bg-[#4C4E640D] is-full'>
            <small>Selisih Saldo</small>
            <Typography variant='h5' color='error'>
              {toCurrency(reconciliationData?.balanceDiffence ?? 0)}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default ReconciliationSummary
