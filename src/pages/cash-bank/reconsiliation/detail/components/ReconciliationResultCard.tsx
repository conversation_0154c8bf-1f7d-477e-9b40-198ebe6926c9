import { toCurrency } from '@/utils/helper'
import { Autocomplete, Card, CardContent, CardHeader, debounce, TextField, Typography } from '@mui/material'
import { formatDate } from 'date-fns'
import { useState } from 'react'
import CashbankListTableSelection from './CashbankListTableSelection'
import { useReconsiliation } from '../../context/ReconsiliationContext'
import DebouncedInput from '@/components/DebounceInput'

const ReconciliationCardResult = () => {
  const { journalLineParams, reconciliationData } = useReconsiliation()
  const [itemSearchQuery, setItemSearchQuery] = useState('')
  const { search } = journalLineParams

  return (
    <Card>
      <CardHeader title={<Typography variant='h5'>Jurnal Equalindo360</Typography>}></CardHeader>
      <CardContent className='flex flex-col gap-4'>
        <CashbankListTableSelection />
      </CardContent>
    </Card>
  )
}

export default ReconciliationCardResult
