import React, { useMemo } from 'react'
import { useReactTable, getCoreRowModel, RowSelectionState } from '@tanstack/react-table'
import { journalLineColumns } from '../../config/table'
import { useReconsiliation } from '../../context/ReconsiliationContext'
import { Typography } from '@mui/material'
import { useRouter } from '@/routes/hooks'
import Table from '@/components/table'
import { JournalRefDocType } from '../../config/types'
import { useQuery } from '@tanstack/react-query'
import AccountsQueryMethods, { JOURNAL_LINE_LIST_QUERY_KEY } from '@/api/services/account/query'

const CashbankListTableSelection: React.FC = () => {
  const router = useRouter()
  const { reconciliationData } = useReconsiliation()

  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})

  const { data: journalLines } = useQuery({
    enabled: !!reconciliationData?.accountId && !!reconciliationData?.id,
    queryKey: [JOURNAL_LINE_LIST_QUERY_KEY, JSON.stringify(reconciliationData)],
    queryFn: () => {
      return AccountsQueryMethods.getJournalLines({
        limit: Number.MAX_SAFE_INTEGER,
        bankReconciliationId: reconciliationData?.id,
        accountId: reconciliationData?.accountId
      })
    }
  })

  const tableOptions: any = useMemo(
    () => ({
      data: journalLines?.items ?? [],
      columns: journalLineColumns({
        detail: (row, type) => {
          if (type === JournalRefDocType.Payment) {
            window.open(`/cash-bank/payments/${row.journal.refDocId}`, '_blank')
          } else if (type === JournalRefDocType.CashReceipt) {
            window.open(`/cash-bank/receipt/${row.journal.refDocId}`, '_blank')
          }
        },
        withChecklist: false
      }),
      state: {
        rowSelection
      },
      onRowSelectionChange: setRowSelection,
      getCoreRowModel: getCoreRowModel(),
      enableRowSelection: true,
      enableMultiRowSelection: true
    }),
    [journalLines, rowSelection]
  )

  const table = useReactTable(tableOptions)

  const selectedRows = table.getSelectedRowModel().rows

  return (
    <div className='rounded-[8px] shadow-md'>
      <Table
        headerColor='green'
        table={table}
        emptyLabel={
          <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
            <Typography>Belum ada checklist</Typography>
            <Typography className='text-sm text-gray-400'>
              Semua checklist yang telah kamu buat akan ditampilkan di sini
            </Typography>
          </td>
        }
        disablePagination
      />
    </div>
  )
}

export default CashbankListTableSelection
