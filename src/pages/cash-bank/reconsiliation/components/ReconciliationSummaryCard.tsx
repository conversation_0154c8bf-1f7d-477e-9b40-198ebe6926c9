import { toCurrency } from '@/utils/helper'
import { Card, CardContent, TextField, Typography } from '@mui/material'
import { useReconsiliation } from '../context/ReconsiliationContext'
import { CreateReconciliationPayload } from '../config/types'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useMemo } from 'react'
import CurrencyField from '@/components/numeric/CurrencyField'

const ReconciliationSummary = () => {
  const { selectedAccountCashbank } = useReconsiliation()
  const { control } = useFormContext<CreateReconciliationPayload>()
  const actualBalanceWatch = useWatch({ control, name: 'actualBalance' })

  const { totalBalance, difference } = useMemo(() => {
    const totalBalance = selectedAccountCashbank?.balance ?? 0
    const difference = Math.abs(totalBalance) - actualBalanceWatch

    return {
      totalBalance,
      actualBalance: actualBalanceWatch,
      difference: Math.abs(difference)
    }
  }, [actualBalanceWatch, selectedAccountCashbank])

  return (
    <Card>
      <CardContent>
        <div className='flex justify-between gap-4 items-center'>
          <div className='flex flex-col rounded-[8px] px-4 py-2 bg-[#DBF7E8] is-full'>
            <small>Total Saldo Jurnal</small>
            <Typography variant='h5' color='primary'>
              {toCurrency(totalBalance ?? 0)}
            </Typography>
          </div>
          <div className='flex flex-col rounded-[8px] px-4 py-2 bg-[#4C4E640D] is-full'>
            <small>Saldo Aktual</small>
            <Controller
              control={control}
              name='actualBalance'
              rules={{
                validate: value => {
                  if (value <= 0) {
                    return 'Wajib diisi'
                  }
                  return true
                }
              }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  InputProps={{ inputComponent: CurrencyField as any }}
                  {...field}
                  error={!!error}
                  size='small'
                  className='w-full bg-white'
                />
              )}
            />
          </div>
          <div className='flex flex-col rounded-[8px] px-4 py-2 bg-[#4C4E640D] is-full'>
            <small>Selisih Saldo</small>
            <Typography variant='h5' color='error'>
              {toCurrency(difference ?? 0)}
            </Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default ReconciliationSummary
