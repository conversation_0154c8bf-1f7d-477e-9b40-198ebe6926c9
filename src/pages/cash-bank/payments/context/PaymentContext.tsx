import useMobileScreen from '@/components/dialogs/hooks/useMobileScreen'
import usePartialState from '@/core/hooks/usePartialState'
import React, { useEffect, useState } from 'react'
import { PaymentParams, PaymentType } from '../config/types'
import { useQuery } from '@tanstack/react-query'
import CashBankQueryMethods, { PAYMENT_QUERY_KEY, PAYMENT_QUERY_LIST_KEY } from '@/api/services/cashbank/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { QueryFn } from '@/types/alias'
import { useLocation, useParams } from 'react-router-dom'

type PaymentContextProps = {
  isMobile: boolean
  paymentParams: PaymentParams
  setPartialPaymentParams: (key: keyof PaymentParams, value: PaymentParams[keyof PaymentParams]) => void
  setPaymentParams: React.Dispatch<React.SetStateAction<PaymentParams>>
  selectedPaymentId: string
  setSelectedPaymentId: React.Dispatch<React.SetStateAction<string>>
  paymentListResponse: ListResponse<PaymentType>
  fetchPaymentList: QueryFn<ListResponse<PaymentType>>
  paymentData: PaymentType
  fetchPaymentData: QueryFn<PaymentType>
}

const PaymentContext = React.createContext<PaymentContextProps>({} as PaymentContextProps)

export const usePayment = () => {
  const context = React.useContext(PaymentContext)
  if (context === undefined) {
    throw new Error('usePayment must be used within a PaymentProvider')
  }
  return context
}

export const PaymentProvider = ({ children }: React.PropsWithChildren<unknown>) => {
  const { isMobile } = useMobileScreen()
  const params = useParams()
  const { pathname } = useLocation()
  const [paymentParams, setPartialPaymentParams, setPaymentParams] = usePartialState<PaymentParams>({
    limit: 10,
    page: 1
  })
  const [selectedPaymentId, setSelectedPaymentId] = useState<string>('')

  const isApprovals = pathname.includes('approval')

  const { data: paymentListResponse, refetch: fetchPaymentList } = useQuery({
    queryKey: [PAYMENT_QUERY_LIST_KEY, JSON.stringify(paymentParams), isApprovals],
    queryFn: () => {
      if (isApprovals) {
        return CashBankQueryMethods.getPaymentToMe(paymentParams)
      } else {
        return CashBankQueryMethods.getPaymentsList(paymentParams)
      }
    },
    placeholderData: defaultListData as ListResponse<PaymentType>
  })

  const { data: paymentData, refetch: fetchPaymentData } = useQuery({
    enabled: !!params?.paymentId,
    queryKey: [PAYMENT_QUERY_KEY, params?.paymentId],
    queryFn: () => CashBankQueryMethods.getPayment(params?.paymentId)
  })

  useEffect(() => {
    if (params.paymentId) {
      setSelectedPaymentId(params.paymentId)
    }
  }, [params])

  const value = {
    isMobile,
    paymentParams,
    setPartialPaymentParams,
    setPaymentParams,
    selectedPaymentId,
    setSelectedPaymentId,
    paymentListResponse,
    fetchPaymentList,
    paymentData,
    fetchPaymentData
  }

  return (
    <PaymentContext.Provider value={value}>
      <div>{children}</div>
    </PaymentContext.Provider>
  )
}
