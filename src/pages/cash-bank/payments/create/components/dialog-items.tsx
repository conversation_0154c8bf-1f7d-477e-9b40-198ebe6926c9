import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import CurrencyField from '@/components/numeric/CurrencyField'
import { AccountType } from '@/types/accountTypes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Button,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { CreatePaymentItemPayload } from '../../config/types'

type DialogItemsProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit: (item: CreatePaymentItemPayload) => void
}

const DialogItems = (props: DialogItemsProps) => {
  const { open, setOpen } = props
  const [query, setQuery] = useState('')
  const [selectedAccount, setSelectedAccount] = useState<AccountType>()

  const { control, getValues, handleSubmit, reset } = useForm<CreatePaymentItemPayload>()

  const { data: inventoryAccounts, remove: removeAccounts } = useQuery({
    enabled: !!query,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, query],
    queryFn: () => AccountsQueryMethods.getAccountList({ limit: Number.MAX_SAFE_INTEGER, level: 1, search: query })
  })

  const handleClose = () => {
    setOpen(false)
  }

  return (
    <Dialog scroll='body' open={open} onClose={setOpen} maxWidth='sm'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Tambah item Pembayaran
        <Typography component='span' className='flex flex-col text-center'>
          Lengkapi detil Pembayaran
        </Typography>
      </DialogTitle>
      <form onSubmit={e => e.preventDefault()}>
        <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
          <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
            <i className='ri-close-line text-textSecondary' />
          </IconButton>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='accountId'
                rules={{ required: true }}
                render={({ field: { onChange, value }, fieldState: { error } }) => (
                  <Autocomplete
                    value={selectedAccount}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setQuery(newValue as string)
                      }
                    }, 700)}
                    options={inventoryAccounts?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!query}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        onChange(newValue.id)
                        reset({
                          ...getValues(),
                          code: newValue.code,
                          name: newValue.name
                        })
                        setSelectedAccount(newValue)
                        removeAccounts()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        fullWidth
                        error={!!error}
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        placeholder='Cari kode akun perkiraan'
                        label='Akun Perkiraan'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='amount'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Nominal'
                    error={!!error}
                    InputProps={{
                      inputComponent: CurrencyField as any
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='description'
                render={({ field, fieldState: { error } }) => (
                  <TextField {...field} error={!!error} fullWidth label='Memo' />
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
      </form>
      <DialogActions className='justify-center pbs-0 sm:pbe-16 sm:px-16'>
        <Button variant='outlined' onClick={handleClose}>
          Batalkan
        </Button>
        <LoadingButton onClick={handleSubmit(props.onSubmit)} variant='contained' color='primary'>
          Simpan
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogItems
