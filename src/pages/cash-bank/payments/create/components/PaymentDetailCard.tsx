import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import AppReactDatepicker from '@/components/libs/styles/AppReactDatepicker'
import { AccountType } from '@/types/accountTypes'
import {
  Autocomplete,
  Button,
  Card,
  CardContent,
  Checkbox,
  debounce,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { Controller, useFormContext, useWatch } from 'react-hook-form'
import { useFilePicker } from 'use-file-picker'
import { CreatePaymentPayload } from '../../config/types'
import { isNullOrUndefined } from '@/utils/helper'
import { paymentTypeOptions } from '../config/utils'

const PaymentDetailCard = () => {
  const [selectedItem, setSelectedItem] = useState<AccountType>()
  const [itemQuery, setItemQuery] = useState('')
  const { control } = useFormContext<CreatePaymentPayload>()
  const { openFilePicker, filesContent } = useFilePicker({
    readAs: 'DataURL',
    accept: 'image/jpeg, image/png'
  })

  const useCheckNumber = useWatch({ control, name: 'useCheckNumber' })

  const { data: itemsListResponse, remove: removeItemsListResponse } = useQuery({
    enabled: !!itemQuery,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, itemQuery],
    queryFn: () =>
      AccountsQueryMethods.getAccountList({
        limit: Number.MAX_SAFE_INTEGER,
        level: 1,
        search: itemQuery,
        accountTypeCodes: 'CASH_BANK'
      })
  })

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-4'>
          <div className='flex justify-between'>
            <Typography variant='h5'>Detil Pembayaran</Typography>
          </div>
          <Grid container spacing={4}>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='type'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='payment-type'>Tipe Pembayaran</InputLabel>
                    <Select
                      key={field.value}
                      {...field}
                      labelId='payment-type'
                      id='payment-type'
                      label='Tipe Pembayaran'
                      error={!!error}
                    >
                      {paymentTypeOptions.map(type => (
                        <MenuItem key={type.value} value={type.value}>
                          {type.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='accountId'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <Autocomplete
                    key={JSON.stringify(selectedItem)}
                    value={selectedItem}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setItemQuery(newValue as string)
                      }
                    }, 700)}
                    options={itemsListResponse?.items ?? []}
                    getOptionLabel={(option: AccountType) => `[${option.code}] ${option.name}`}
                    freeSolo={!itemQuery}
                    noOptionsText='Akun tidak ditemukan'
                    onChange={(e, newValue: AccountType) => {
                      if (newValue) {
                        setSelectedItem(newValue)
                        field.onChange(newValue.id)
                        removeItemsListResponse()
                      }
                    }}
                    renderInput={params => (
                      <TextField
                        {...params}
                        InputProps={{
                          ...params.InputProps,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                        error={!!error}
                        placeholder='Cari akun perkiraan'
                        label='Pembayaran Dengan'
                      />
                    )}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='memo'
                render={({ field }) => <TextField {...field} fullWidth label='Memo' />}
              />
            </Grid>
            {/* Currently not used */}
            {/* <Grid item xs={12}>
              <AppReactDatepicker
                boxProps={{ className: 'is-full' }}
                // selected={value ? toDate(value) : undefined}
                // onChange={(date: Date) => onChange(formatISO(date))}
                dateFormat='eeee dd/MM/yyyy'
                customInput={
                  <TextField
                    fullWidth
                    label='Tanggal Bayar'
                    className='flex-1'
                    InputProps={{
                      readOnly: true
                    }}
                  />
                }
              />
            </Grid> */}
            <Grid item xs={12}>
              <Controller
                control={control}
                name='useCheckNumber'
                render={({ field }) => (
                  <FormControlLabel
                    label={<Typography>Gunakan Nomor Cek</Typography>}
                    control={<Checkbox {...field} />}
                  />
                )}
              />
            </Grid>
            {!!useCheckNumber && (
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='checkNumber'
                  rules={{
                    validate: value => {
                      if (isNullOrUndefined(value) && !!useCheckNumber) {
                        return 'Silahkan isi nomor cek'
                      }
                      return true
                    }
                  }}
                  render={({ field, fieldState: { error } }) => (
                    <TextField fullWidth label='Nomor Cek' {...field} error={!!error} />
                  )}
                />
              </Grid>
            )}
            {/* Currently not used */}
            {/* <Grid item xs={12}>
              <div className='flex flex-col gap-2'>
                <Typography variant='subtitle1' marginBottom={2}>
                  Unggah Bukti Transaksi
                </Typography>
                <div className='flex items-center gap-4'>
                  <TextField
                    size='small'
                    fullWidth
                    value={filesContent?.[0]?.name}
                    placeholder='Tidak ada file dipilih'
                    aria-readonly
                    className='flex-1'
                  />
                  <Button variant='contained' onClick={() => openFilePicker()}>
                    Unggah
                  </Button>
                </div>
              </div>
            </Grid> */}
          </Grid>
        </CardContent>
      </Card>
    </>
  )
}

export default PaymentDetailCard
