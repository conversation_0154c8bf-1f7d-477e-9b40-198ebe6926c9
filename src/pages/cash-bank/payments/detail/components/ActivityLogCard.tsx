// MUI Imports
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import TimelineDot from '@mui/lab/TimelineDot'
import TimelineItem from '@mui/lab/TimelineItem'
import TimelineContent from '@mui/lab/TimelineContent'
import TimelineSeparator from '@mui/lab/TimelineSeparator'
import TimelineConnector from '@mui/lab/TimelineConnector'
import Typography from '@mui/material/Typography'

import { Timeline } from '@/components/Timeline'
import { PrLogType, LogTypeStatus } from '@/types/prTypes'
import { Avatar } from '@mui/material'
import { formatDistanceToNow } from 'date-fns'
import { id } from 'date-fns/locale'
import { WarehouseLogType } from '@/types/appTypes'

type Props = {
  logList?: WarehouseLogType[]
}

const ActivityLogCard = ({ logList = [] }: Props) => {
  return (
    <Card>
      <CardHeader title='Log Aktivitas' />
      <CardContent>
        <Timeline>
          {logList?.map(log => {
            let dotColor: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success' | 'inherit' | 'grey' =
              'primary'
            let title = ''
            const changes = ((JSON.parse(log.changes) as string[]) ?? []).map(change =>
              change.replaceAll('"', '').replaceAll('{changed}', '→').replaceAll('{added}', '')
            )
            switch (log.status) {
              case LogTypeStatus.CANCELED:
                title = 'MB Dibatalkan'
                dotColor = 'error'
                break
              case LogTypeStatus.CREATED:
                title = 'MB Dibuat'
                break
              case LogTypeStatus.ITEM_UPDATED:
              case LogTypeStatus.UPDATED:
                title = 'Detil Barang'
                break
              case LogTypeStatus.CLOSED:
                title = 'MB ditutup'
                dotColor = 'error'
                break
              case LogTypeStatus.APPROVAL_UPDATED:
                title = 'Penerima Pengajuan diganti'
                break
              case LogTypeStatus.APPROVAL_APPROVED:
                title = 'MB Disetujui'
                break
              case LogTypeStatus.APPROVAL_REJECTED:
                title = 'MB Ditolak'
                dotColor = 'error'
                break
              case LogTypeStatus.ITEM_RETURNED:
                title = 'Barang Dikembalikan'
                dotColor = 'error'
                break
              case LogTypeStatus.ITEM_RECEIVED:
                title = 'Barang Diterima'
                break
              case LogTypeStatus.ITEM_OUT:
                title = 'Barang Keluar'
                break
              case LogTypeStatus.CANCEL_REQUESTED:
                title = 'Permintaan Pembatalan MB'
                break
              case LogTypeStatus.CANCEL_APPROVED:
                title = 'Pembatalan MB Disetujui'
                break
              case LogTypeStatus.DELIVERY_NOTE_CREATED:
                title = 'Surat Jalan Dibuat'
                break
              case LogTypeStatus.DELIVERY_NOTE_DELIVERED:
                title = 'Barang Diterima'
                break
              case LogTypeStatus.CANCEL_REJECTED:
                title = 'Pembatalan MB Ditolak'
                dotColor = 'error'
                break
              case LogTypeStatus.REJECTED:
                title = 'MB Ditolak'
                dotColor = 'error'
                break
              default:
                break
            }
            return title ? (
              <TimelineItem key={log.id} className='pt-2'>
                <TimelineSeparator>
                  <TimelineDot color={dotColor} />
                  <TimelineConnector />
                </TimelineSeparator>
                <TimelineContent>
                  <div className='flex flex-wrap items-center justify-between gap-x-2 mbe-1'>
                    <Typography color='text.primary' className='font-medium text-base'>
                      {title}
                    </Typography>
                    <Typography variant='caption'>
                      {formatDistanceToNow(log.createdAt, {
                        locale: id,
                        addSuffix: true
                      })
                        .replace('sekitar ', '')
                        .replace('kurang dari ', '')}
                    </Typography>
                  </div>
                  {changes.map(change => (
                    <Typography key={change} className='mbe-2 text-sm'>
                      {change}
                    </Typography>
                  ))}
                  {log.user ? (
                    <div className='flex items-center gap-3'>
                      <Avatar />
                      <div className='flex flex-col'>
                        <Typography color='text.primary' className='font-medium'>
                          {log.user?.fullName}
                        </Typography>
                        <Typography variant='body2'>{log.user?.title}</Typography>
                      </div>
                    </div>
                  ) : null}
                </TimelineContent>
              </TimelineItem>
            ) : null
          })}
        </Timeline>
      </CardContent>
    </Card>
  )
}

export default ActivityLogCard
