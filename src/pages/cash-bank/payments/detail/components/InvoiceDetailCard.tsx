import PurchaseInvoiceQueryMethods, { PURCHASE_INVOICE_DETAIL_QUERY_KEY } from '@/api/services/purchase-invoice/query'
import { Card, CardContent, Typography } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type Props = {
  purchaseInvoiceId?: string
}

const InvoiceDetailCard = ({ purchaseInvoiceId }: Props) => {
  const { data: purchaseInvoiceData } = useQuery({
    enabled: !!purchaseInvoiceId,
    queryKey: [PURCHASE_INVOICE_DETAIL_QUERY_KEY, purchaseInvoiceId],
    queryFn: () => PurchaseInvoiceQueryMethods.getPurchaseInvoice(purchaseInvoiceId)
  })
  return (
    <Card>
      <CardContent className='flex flex-col gap-4'>
        <div className='flex justify-between items-center'>
          <Typography variant='h5'>Detil Faktur</Typography>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='flex flex-col gap-2'>
            <small>Vendor</small>
            <Typography>{purchaseInvoiceData?.vendor?.name}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Syarat Bayar</small>
            <Typography>{purchaseInvoiceData?.paymentTerms}</Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Tanggal Faktur</small>
            <Typography>
              {purchaseInvoiceData?.invoiceDate ? formatDate(purchaseInvoiceData?.invoiceDate, 'dd/MM/yyyy') : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-2'>
            <small>Jatuh Tempo</small>
            <Typography color='error'>
              {purchaseInvoiceData?.paymentDueDate
                ? formatDate(purchaseInvoiceData?.paymentDueDate, 'dd/MM/yyyy')
                : '-'}
            </Typography>
          </div>
          <div className='flex flex-col gap-2 col-span-2'>
            <small>Memo</small>
            <Typography>{purchaseInvoiceData?.note}</Typography>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default InvoiceDetailCard
