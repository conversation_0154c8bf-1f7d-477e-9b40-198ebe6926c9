// MUI Imports
import { useEffect, useState } from 'react'

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

// Type Imports

// Component Imports
import { Controller, useFieldArray, useFormContext, useWatch } from 'react-hook-form'

import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import { CreatePrInput } from '../config/schema'
import { WarehouseItemType } from '@/types/appTypes'
import { useMrList } from '@/pages/material-request/context/MrListContext'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { ItemMrInput } from '@/pages/material-request/create/config/schema'
import { tableColumns } from '@/pages/material-request/create/config/table'
import Table from '@/components/table'
import { id } from 'date-fns/locale'
import { formatDate } from 'date-fns'
import { Checkbox, FormControlLabel, TextField } from '@mui/material'

const AddItemCard = () => {
  const { showLoading, mrData } = useMrList()
  const {
    control,
    trigger,
    formState: { errors },
    watch
  } = useFormContext<CreatePrInput>()

  const isGeneralPurchase = useWatch({ control, name: 'isGeneralPurchase' })

  const { fields, update, replace, remove } = useFieldArray({
    control,
    name: 'items',
    keyName: '_id'
  })

  const [{ open: addItemOpen, selectedItem, selectedIndex }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as CreatePrInput['items'][0],
    selectedIndex: 0
  })

  const table = useReactTable({
    data: fields as ItemMrInput[],
    columns: tableColumns(
      {
        onEdit: itemData => {
          setAddItemModalState({
            open: true,
            selectedItem: itemData as unknown as WarehouseItemType,
            selectedIndex: fields.findIndex(item => item.itemId === itemData.itemId)
          })
        },
        onRemove: itemData => {
          remove(fields.findIndex(item => item.itemId === itemData.itemId))
          trigger()
        }
      },
      mrData?.site
    ),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    replace(
      mrData?.items
        ?.map(item => {
          const remainingQty = item.isLargeUnit
            ? item.remainingQuantity / item.largeUnitQuantity
            : item.remainingQuantity
          return { ...item, quantity: remainingQty, originalItemId: item.itemId }
        })
        .filter(item => item.itemId && item.remainingQuantity > 0 && item.quantity > 0) ?? []
    )
  }, [mrData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex flex-col gap-2 rounded-md p-4 bg-[#4C4E640D]'>
            <Typography variant='h5'>No. MR {mrData?.number}</Typography>
            <Typography className='font-light'>
              {formatDate(mrData?.createdAt ?? Date.now(), 'eeee, dd/MM/yyyy', { locale: id })}
            </Typography>
          </div>
          <Controller
            control={control}
            name='isGeneralPurchase'
            render={({ field }) => (
              <FormControlLabel control={<Checkbox {...field} />} label='Pembelian di Vendor Umum' />
            )}
          />
          {!!isGeneralPurchase && (
            <Controller
              control={control}
              name='vendorName'
              render={({ field }) => (
                <TextField
                  {...field}
                  label='Nama Vendor'
                  variant='outlined'
                  placeholder='Nama Vendor'
                  error={!!errors.vendorName}
                  helperText={errors.vendorName?.message}
                />
              )}
            />
          )}
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Barang</Typography>
          </div>
          <div className='shadow-sm rounded-md'>
            <Table
              headerColor='green'
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan barang yang ingin dimasukkan dalam dokumen ini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? {} : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          currentItem={selectedItem as WarehouseItemType}
          canUpdateAll={false}
          hasMaxQty
          withoutUnit
          onSubmit={itemData => {
            update(selectedIndex, itemData)
            trigger()
            setAddItemModalState({
              open: false,
              selectedItem: {} as CreatePrInput['items'][0],
              selectedIndex: undefined
            })
          }}
          siteId={mrData?.siteId}
        />
      )}
    </>
  )
}

export default AddItemCard
