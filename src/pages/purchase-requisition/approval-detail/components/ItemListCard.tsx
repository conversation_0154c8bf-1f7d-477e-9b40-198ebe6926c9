// MUI Imports
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Typography from '@mui/material/Typography'

import type { LedgerType } from '@/types/apps/ledgerTypes'
import PhotoPicker from '@/components/PhotoPicker'
import { IconButton } from '@mui/material'
import { useDeletePrItem, useUpdatePrItem } from '@/api/services/pr/mutation'
import { useMenu } from '@/components/menu/contexts/menuContext'
import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import { useUploadImage } from '@/api/services/file/mutation'
import { PrItemType, PrUserStatus } from '@/types/prTypes'
import { useAuth } from '@/contexts/AuthContext'
import { usePr } from '../../context/PrContext'
import { CreateMrInput } from '@/pages/material-request/create/config/schema'
import AddWarehouseItemDialog from '@/components/dialogs/add-warehouse-item'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from '@/pages/material-request/approval-detail/table'
import Table from '@/components/table'
import { isNullOrUndefined } from '@/utils/helper'

const ItemListCard = () => {
  const { userProfile } = useAuth()
  const { setConfirmState } = useMenu()
  const { prData, fetchPrData, fetchLogList, canRemove } = usePr()
  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: deleteItemMutate, isLoading: deleteItemLoading } = useDeletePrItem()
  const { mutate: updateItemMutate, isLoading: updateItemLoading } = useUpdatePrItem()

  const [prItems, setPrItems] = useState<PrItemType[]>([])

  const isLoading = uploadLoading || deleteItemLoading || updateItemLoading

  const [{ open: addItemOpen, selectedItem }, setAddItemModalState] = useState({
    open: false,
    selectedItem: {} as PrItemType
  })

  const handleDeleteItem = (prItem: PrItemType) => {
    setConfirmState({
      open: true,
      title: 'Hapus Barang',
      content: 'Apakah kamu yakin ingin menghapus barang ini?',
      onConfirm: () => {
        updateItemMutate(
          {
            prId: prData.id,
            payload: {
              id: prItem.id,
              itemId: prItem.itemId,
              quantity: 0,
              quantityUnit: prItem.quantityUnit,
              note: prItem.note,
              largeUnitQuantity: prItem.largeUnitQuantity,
              ...(prItem.unitId && { unitId: prItem.unitId }),
              ...(!isNullOrUndefined(prItem?.unitKm) && { unitKm: prItem.unitKm }),
              ...(!isNullOrUndefined(prItem?.unitHm) && { unitHm: prItem.unitHm }),
              images: [
                ...(prItem.images ?? [])
                  .filter(image => !image.fileName && !!image.uploadId)
                  .map(image => ({
                    uploadId: image.uploadId
                  }))
              ]
            }
          },
          {
            onSuccess: () => {
              toast.success('Barang berhasil dihapus')
              fetchPrData()
              fetchLogList()
            }
          }
        )
      },
      confirmText: 'Hapus',
      confirmColor: 'error'
    })
  }

  const handleUpdateItem = (itemData: CreateMrInput['items'][0]) => {
    Promise.all(
      (itemData.images ?? [])
        .filter(item => !!item.fileName)
        .map(item =>
          uploadMutate({
            fieldName: `item_image_${itemData.itemId}`,
            file: item.content,
            scope: 'public-image',
            fileName: item.fileName
          })
        )
    )
      .then(values => {
        const uploadIds = values.map(val => ({
          uploadId: val.data?.id ?? ''
        }))
        updateItemMutate(
          {
            prId: prData.id,
            payload: {
              id: selectedItem.id,
              itemId: itemData.itemId,
              quantity: itemData.quantity,
              quantityUnit: itemData.quantityUnit,
              note: itemData.note,
              largeUnitQuantity: itemData.largeUnitQuantity,
              ...(itemData.unitId && { unitId: itemData.unitId }),
              ...(!isNullOrUndefined(itemData?.unitKm) && { unitKm: itemData.unitKm }),
              ...(!isNullOrUndefined(itemData?.unitHm) && { unitHm: itemData.unitHm }),
              images: [
                ...(itemData.images ?? [])
                  .filter(image => !image.fileName && !!image.uploadId)
                  .map(image => ({
                    uploadId: image.uploadId
                  })),
                ...uploadIds
              ]
            }
          },
          {
            onSuccess: () => {
              toast.success('Data barang berhasil diubah')
              fetchPrData()
              fetchLogList()
              setAddItemModalState({
                open: false,
                selectedItem: {}
              })
            }
          }
        )
      })
      .catch(error => {
        const message = error.response?.data?.message
        if (message) {
          toast.error(message)
        } else {
          toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
        }
      })
  }

  const table = useReactTable({
    data: prItems,
    columns: tableColumns(
      {
        onEdit: itemData => {
          setAddItemModalState({ open: true, selectedItem: itemData })
        },
        onRemove: handleDeleteItem
      },
      prData?.siteId,
      canRemove
    ),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  useEffect(() => {
    setPrItems(prData?.items ?? [])
  }, [prData])

  return (
    <>
      <Card>
        <CardContent className='flex flex-col gap-6'>
          <div className='flex justify-between items-center'>
            <Typography variant='h5'>Detil Barang</Typography>
          </div>
          <div>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada Barang</Typography>
                  <Typography className='text-sm text-gray-400'>
                    Tambahkan barang yang ingin dimasukkan dalam Purchase Request ini
                  </Typography>
                </td>
              }
            />
          </div>
        </CardContent>
      </Card>
      {addItemOpen && (
        <AddWarehouseItemDialog
          open={addItemOpen}
          setOpen={open => {
            setAddItemModalState(current => ({
              open,
              selectedItem: !open ? undefined : current.selectedItem,
              selectedIndex: undefined
            }))
          }}
          withoutUnit
          canUpdateAll={false}
          isLoading={isLoading}
          currentItem={selectedItem}
          onSubmit={handleUpdateItem}
          siteId={prData?.siteId}
        />
      )}
    </>
  )
}

export default ItemListCard
