import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet, RouteObject } from 'react-router-dom'
import { PayrollProvider } from '@/pages/accounting/payroll/context/PayrollContext'
import { AccountProvider } from '@/pages/accounting/accounts/context/AccountContext'
import { TaxProvider } from '@/pages/accounting/tax/context/TaxContext'
import { CurrenciesProvider } from '@/pages/accounting/currency/context/CurrencyContext'
import { CustomerProvider } from '@/pages/accounting/customer/context/CustomerContext'
import { CarrierProvider } from '@/pages/accounting/carrier/context/CarrierContext'

const PayrollList = retryDynamicImport(() => import('@/pages/accounting/payroll/index'))
const AccountsList = retryDynamicImport(() => import('@/pages/accounting/accounts/index'))
const TaxList = retryDynamicImport(() => import('@/pages/accounting/tax/index'))
const CurrencyList = retryDynamicImport(() => import('@/pages/accounting/currency/index'))
const CustomerList = retryDynamicImport(() => import('@/pages/accounting/customer/index'))
const CarrierList = retryDynamicImport(() => import('@/pages/accounting/carrier/index'))

export const companyDataRoutes = [
  {
    path: '/accounting',
    children: [
      {
        path: 'salary',
        element: (
          <PayrollProvider>
            <Outlet />
          </PayrollProvider>
        ),
        children: [
          {
            index: true,
            element: <PayrollList />
          }
        ]
      },
      {
        path: 'accounts',
        element: (
          <AccountProvider>
            <Outlet />
          </AccountProvider>
        ),
        children: [
          {
            index: true,
            element: <AccountsList />
          }
        ]
      },
      {
        path: 'tax',
        element: (
          <TaxProvider>
            <Outlet />
          </TaxProvider>
        ),
        children: [
          {
            index: true,
            element: <TaxList />
          }
        ]
      },
      {
        path: 'currency',
        element: (
          <CurrenciesProvider>
            <Outlet />
          </CurrenciesProvider>
        ),
        children: [
          {
            index: true,
            element: <CurrencyList />
          }
        ]
      },
      {
        path: 'customer',
        element: (
          <CustomerProvider>
            <Outlet />
          </CustomerProvider>
        ),
        children: [
          {
            index: true,
            element: <CustomerList />
          }
        ]
      },
      {
        path: 'carrier',
        element: (
          <CarrierProvider>
            <Outlet />
          </CarrierProvider>
        ),
        children: [
          {
            index: true,
            element: <CarrierList />
          }
        ]
      }
    ]
  }
] as RouteObject[]
