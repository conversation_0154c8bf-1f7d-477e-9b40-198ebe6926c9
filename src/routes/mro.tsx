import { JobContextProvider } from '@/pages/repair-and-maintenance/code/job-code/context/JobContext'
import { CodeContextProvider } from '@/pages/repair-and-maintenance/code/component/context/CodeContext'
import { ModifierProvider } from '@/pages/repair-and-maintenance/code/modifier/context/ModifierContext'
import { UnitContextProvider } from '@/pages/repair-and-maintenance/unit/context/UnitContext'
import { WpContextProvider } from '@/pages/repair-and-maintenance/wp/context/WpContext'
import { FrProvider } from '@/pages/repair-and-maintenance/fr/context/FrContext'
import { WoProvider } from '@/pages/repair-and-maintenance/wo/context/WoContext'
import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet } from 'react-router-dom'
import { WoMrProvider } from '@/pages/repair-and-maintenance/wo/context/WoMrContext'
import { WoSrProvider } from '@/pages/repair-and-maintenance/wo/context/WoSrContext'
import { PreReleaseProvider } from '@/pages/repair-and-maintenance/pre-release/context/PreReleaseContext'
import { FormatPreReleaseContextProvider } from '@/pages/repair-and-maintenance/pre-release/context/FormatPreReleaseContext'
import { CreatePartSwapProvider } from '@/pages/repair-and-maintenance/wo/context/WoPartSwapContext'
import { StuffContextProvider } from '@/pages/repair-and-maintenance/stuff-request/context/StuffContext'

const JobCode = retryDynamicImport(() => import('@/pages/repair-and-maintenance/code/job-code/list'))
const CreateJobCode = retryDynamicImport(() => import('@/pages/repair-and-maintenance/code/job-code/create'))

const CodeComponent = retryDynamicImport(() => import('@/pages/repair-and-maintenance/code/component/list'))
const CreateCodeComponent = retryDynamicImport(() => import('@/pages/repair-and-maintenance/code/component/create'))

const ModifierList = retryDynamicImport(() => import('@/pages/repair-and-maintenance/code/modifier/list'))
const CreateModifierComponent = retryDynamicImport(() => import('@/pages/repair-and-maintenance/code/modifier/create'))

const FRCreatedListPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/fr/created'))
const FRCreatedDetailPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/fr/detail'))
const FRNewFR = retryDynamicImport(() => import('@/pages/repair-and-maintenance/fr/create'))

const UnitList = retryDynamicImport(() => import('@/pages/repair-and-maintenance/unit/list'))
const UnitDetailPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/unit/unit-detail'))
const CreateFrPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/unit/create-fr'))

const CreatedWo = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wo/created'))
const WoDetailPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wo/wo-detail'))
const CreateWoMr = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wo/create-wo-mr'))
const CreateWoSr = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wo/create-wo-sr'))
const CreateWoPartSwap = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wo/create-wo-part-swap'))
const CreatePreRelease = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wo/create-pre-release'))

const FRListPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wo/fr-list'))
const FRDetailPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wo/fr-detail'))
const CreateWoPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wo/create-wo'))

const WpCreated = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wp/created'))
const WpDetilPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wp/detail'))
const CreateNewWp = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wp/new-wp'))
const WpReviewPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wp/review-wp'))

const CreatedPreRelease = retryDynamicImport(() => import('@/pages/repair-and-maintenance/pre-release/created'))
const WoPreReleasePage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/pre-release/wo-list'))
const PreReleaseApproval = retryDynamicImport(() => import('@/pages/repair-and-maintenance/pre-release/approval'))
const FormatPreRelease = retryDynamicImport(() => import('@/pages/repair-and-maintenance/pre-release/format'))
const CreateFormatPreRelease = retryDynamicImport(
  () => import('@/pages/repair-and-maintenance/pre-release/create-format')
)
const FormatPreReleaseDetail = retryDynamicImport(
  () => import('@/pages/repair-and-maintenance/pre-release/detil-format')
)
const UnitTakingListPage = retryDynamicImport(() => import('@/pages/repair-and-maintenance/pre-release/unit-taking'))
const DetilUnitTaking = retryDynamicImport(
  () => import('@/pages/repair-and-maintenance/pre-release/unit-taking-detail')
)
const PreReleaseChecklist = retryDynamicImport(() => import('@/pages/repair-and-maintenance/pre-release/checklist'))
const StuffRequestDetail = retryDynamicImport(() => import('@/pages/repair-and-maintenance/wp/stuff-req-detail/index'))
const StuffRequestApproval = retryDynamicImport(
  () => import('@/pages/repair-and-maintenance/stuff-request/approval/index')
)
const StuffApprovalDetail = retryDynamicImport(
  () => import('@/pages/repair-and-maintenance/stuff-request/detail/index')
)

export const mroRoutes = [
  {
    path: '/rnm',
    children: [
      {
        path: 'job-code',
        element: (
          <JobContextProvider>
            <Outlet />
          </JobContextProvider>
        ),
        children: [
          {
            element: <JobCode />,
            index: true
          },
          {
            path: 'add',
            element: <CreateJobCode />
          }
        ]
      },
      {
        path: 'component',
        element: (
          <CodeContextProvider>
            <Outlet />
          </CodeContextProvider>
        ),
        children: [
          {
            element: <CodeComponent />,
            index: true
          },
          {
            path: 'add',
            element: <CreateCodeComponent />
          }
        ]
      },
      {
        path: 'modifier',
        element: (
          <ModifierProvider>
            <Outlet />
          </ModifierProvider>
        ),
        children: [
          {
            element: <ModifierList />,
            index: true
          },
          {
            path: 'create',
            element: <CreateModifierComponent />
          }
        ]
      },
      {
        path: 'unit',
        element: (
          <UnitContextProvider>
            <Outlet />
          </UnitContextProvider>
        ),
        children: [
          {
            element: <UnitList />,
            index: true
          },
          {
            path: ':unitId',
            element: <UnitDetailPage />
          },
          {
            path: ':unitId/create-fr',
            element: <CreateFrPage />
          }
        ]
      }
    ]
  },
  {
    path: 'fr',
    element: (
      <FrProvider>
        <Outlet />
      </FrProvider>
    ),
    children: [
      {
        path: 'created',
        children: [
          {
            element: <FRCreatedListPage />,
            index: true
          },
          {
            path: ':frId',
            element: <FRCreatedDetailPage />
          }
        ]
      },
      {
        path: 'new-fr',
        element: <FRNewFR />
      }
    ]
  },
  {
    path: 'wo',
    element: (
      <WoProvider>
        <Outlet />
      </WoProvider>
    ),
    children: [
      {
        path: 'created',
        element: (
          <PreReleaseProvider>
            <Outlet />
          </PreReleaseProvider>
        ),
        children: [
          {
            element: <CreatedWo />,
            index: true
          },
          {
            path: ':woId',
            element: <WoDetailPage />
          },
          {
            path: ':woId/create-pre-release',
            element: <CreatePreRelease />
          },
          {
            path: ':woId/:segmentId/create-mr',
            element: (
              <WoMrProvider>
                <CreateWoMr />
              </WoMrProvider>
            )
          },
          {
            path: ':woId/:segmentId/create-sr',
            element: (
              <WoSrProvider>
                <CreateWoSr />
              </WoSrProvider>
            )
          },
          {
            path: ':woId/:segmentId/create-part-swap',
            element: (
              <CreatePartSwapProvider>
                <CreateWoPartSwap />
              </CreatePartSwapProvider>
            )
          }
        ]
      },
      {
        path: 'list',
        children: [
          {
            element: <FRListPage />,
            index: true
          },
          {
            path: ':frId',
            element: <FRDetailPage />
          },
          {
            path: ':frId/create-wo',
            element: <CreateWoPage />
          }
        ]
      },
      {
        path: 'format-pre-release',
        element: (
          <FormatPreReleaseContextProvider>
            <Outlet />
          </FormatPreReleaseContextProvider>
        ),
        children: [
          {
            element: <FormatPreRelease />,
            index: true
          },
          {
            path: ':formatId',
            element: <FormatPreReleaseDetail />
          },
          {
            path: 'create',
            element: <CreateFormatPreRelease />
          }
        ]
      },
      {
        path: 'pre-release-created',
        element: (
          <PreReleaseProvider>
            <Outlet />
          </PreReleaseProvider>
        ),
        children: [
          {
            element: <CreatedPreRelease />,
            index: true
          },
          {
            path: ':woId',
            element: <WoDetailPage />
          },
          {
            path: ':woId/create-pre-release',
            element: <CreatePreRelease />
          }
        ]
      },
      {
        path: 'pre-releases',
        element: (
          <PreReleaseProvider>
            <Outlet />
          </PreReleaseProvider>
        ),
        children: [
          {
            element: <WoPreReleasePage />,
            index: true
          },
          {
            path: ':woId',
            element: <WoDetailPage />
          },
          {
            path: ':woId/checklist',
            element: <PreReleaseChecklist />
          }
        ]
      },
      {
        path: 'approval-pre-releases',
        element: (
          <PreReleaseProvider>
            <Outlet />
          </PreReleaseProvider>
        ),
        children: [
          {
            element: <PreReleaseApproval />,
            index: true
          },
          {
            path: ':woId',
            element: <WoDetailPage />
          }
        ]
      },
      {
        path: 'unit-taking',
        element: (
          <PreReleaseProvider>
            <Outlet />
          </PreReleaseProvider>
        ),
        children: [
          {
            element: <UnitTakingListPage />,
            index: true
          },
          {
            path: ':woId',
            element: <DetilUnitTaking />
          }
        ]
      }
    ]
  },
  {
    path: 'wp-in',
    element: (
      <WpContextProvider>
        <Outlet />
      </WpContextProvider>
    ),
    children: [
      {
        element: <WpCreated alternateTitle='Work Process Masuk' />,
        index: true
      },
      {
        path: ':wpId',
        element: <WpDetilPage />
      }
    ]
  },
  {
    path: 'wp',
    element: (
      <WpContextProvider>
        <Outlet />
      </WpContextProvider>
    ),
    children: [
      {
        path: 'list',
        children: [
          {
            element: <WpCreated />,
            index: true
          },
          {
            path: ':wpId',
            element: <WpDetilPage />
          },
          {
            path: ':wpId/:stuffReqId',
            element: <StuffRequestDetail />
          }
        ]
      },
      {
        path: 'create',
        element: <CreateNewWp />
      },
      {
        path: 'review',
        element: <WpReviewPage />
      }
    ]
  },
  {
    path: 'stuff-request',
    element: (
      <StuffContextProvider>
        <Outlet />
      </StuffContextProvider>
    ),
    children: [
      {
        path: 'approvals-take',
        children: [
          {
            index: true,
            element: <StuffRequestApproval type='TAKE' />
          },
          {
            path: ':stuffReqId',
            element: <StuffApprovalDetail />
          }
        ]
      },
      {
        path: 'approvals-return',
        children: [
          {
            index: true,
            element: <StuffRequestApproval type='RETURN' />
          },
          {
            path: ':stuffReqId',
            element: <StuffApprovalDetail />
          }
        ]
      }
    ]
  }
]
