import { GoodContextProvider } from '@/pages/asset-management/goods/context/GoodContext'
import { CategoryContextProvider } from '@/pages/company-data/category/context/CategoryContext'
import { DepartmentContextProvider } from '@/pages/company-data/department/context/DepartmentContext'
import { ItemContextProvider } from '@/pages/company-data/item/context/ItemContext'
import { SiteContextProvider } from '@/pages/company-data/site/context/SiteContext'
import { UnitContextProvider } from '@/pages/company-data/unit/context/UnitContext'
import { UnitContextProvider as AssetUnitProvider } from '@/pages/asset-management/units/context/UnitContext'
import { VendorContextProvider } from '@/pages/company-data/vendor/context/VendorContext'
import { retryDynamicImport } from '@/utils/retryDynamicImport'
import { Outlet, RouteObject } from 'react-router-dom'
import { CodeActivaContextProvider } from '@/pages/asset-management/code-activa/context/ActivaContext'
import { FormUnitContextProvider } from '@/pages/company-data/unit/context/FormUnitContext'
import { DocumentContextProvider } from '@/pages/asset-management/documents/context/DocumentContext'
import { ProjectContextProvider } from '@/pages/company-data/projects/context/ProjectContext'

const ItemListPage = retryDynamicImport(() => import('@/pages/company-data/item'))
const ItemDetailPage = retryDynamicImport(() => import('@/pages/company-data/item/detail'))
const VendorListPage = retryDynamicImport(() => import('@/pages/company-data/vendor'))
const VendorDetailPage = retryDynamicImport(() => import('@/pages/company-data/vendor/detail'))
const UnitListPage = retryDynamicImport(() => import('@/pages/company-data/unit'))
const UnitDetailPage = retryDynamicImport(() => import('@/pages/company-data/unit/detail'))
const CreateEditPage = retryDynamicImport(() => import('@/pages/company-data/unit/edit-create'))
const SiteListPage = retryDynamicImport(() => import('@/pages/company-data/site'))
const SiteDetailPage = retryDynamicImport(() => import('@/pages/company-data/site/detail'))
const DepartmentListPage = retryDynamicImport(() => import('@/pages/company-data/department'))
const ItemCategoryListPage = retryDynamicImport(() => import('@/pages/company-data/category/item-category'))
const UnitCategoryListPage = retryDynamicImport(() => import('@/pages/company-data/category/unit-category'))
const UnitCategoryDetailPage = retryDynamicImport(() => import('@/pages/company-data/category/unit-category/detail'))
const VendorCategoryListPage = retryDynamicImport(() => import('@/pages/company-data/category/vendor-category'))
const GoodAssetManagementListPage = retryDynamicImport(() => import('@/pages/asset-management/goods/list/index'))
const GoodDetailPage = retryDynamicImport(() => import('@/pages/asset-management/goods/detail'))
const UnitAssetManagementListPage = retryDynamicImport(() => import('@/pages/asset-management/units/index'))
const UnitAssetDetailPage = retryDynamicImport(() => import('@/pages/asset-management/units/detail'))
const UnitAssetNewPage = retryDynamicImport(() => import('@/pages/asset-management/units/new'))
const KodeActivaListPage = retryDynamicImport(() => import('@/pages/asset-management/code-activa/index'))
const DocumentListPage = retryDynamicImport(() => import('@/pages/asset-management/documents/index'))
const InsuranceListPage = retryDynamicImport(() => import('@/pages/asset-management/documents/insurance/index'))
const ProjectList = retryDynamicImport(() => import('@/pages/company-data/projects/index'))
const ProjectDetailPage = retryDynamicImport(() => import('@/pages/company-data/projects/detail'))

export const companyDataRoutes = [
  {
    path: '/company-data',
    children: [
      {
        path: 'projects',
        element: (
          <ProjectContextProvider>
            <Outlet />
          </ProjectContextProvider>
        ),
        children: [
          {
            index: true,
            element: <ProjectList />
          },
          {
            path: ':projectId',
            element: <ProjectDetailPage />
          }
        ]
      },
      {
        path: 'category',
        children: [
          {
            path: 'item',
            element: (
              <CategoryContextProvider type='ITEM'>
                <Outlet />
              </CategoryContextProvider>
            ),
            children: [{ element: <ItemCategoryListPage />, index: true }]
          },
          {
            path: 'unit',
            element: (
              <CategoryContextProvider type='UNIT'>
                <Outlet />
              </CategoryContextProvider>
            ),
            children: [
              { element: <UnitCategoryListPage />, index: true },
              {
                path: ':categoryId',
                children: [
                  {
                    element: <UnitCategoryDetailPage />,
                    index: true
                  }
                ]
              }
            ]
          },
          {
            path: 'vendor',
            element: (
              <CategoryContextProvider type='VENDOR'>
                <Outlet />
              </CategoryContextProvider>
            ),
            children: [{ element: <VendorCategoryListPage />, index: true }]
          }
        ]
      },
      {
        path: 'item',
        element: (
          <ItemContextProvider>
            <Outlet />
          </ItemContextProvider>
        ),
        children: [
          {
            element: <ItemListPage />,
            index: true
          },
          {
            path: ':itemId',
            element: <ItemDetailPage />
          }
        ]
      },
      {
        path: 'vendor',
        element: (
          <VendorContextProvider>
            <Outlet />
          </VendorContextProvider>
        ),
        children: [
          {
            element: <VendorListPage />,
            index: true
          },
          {
            path: ':vendorId',
            element: <VendorDetailPage />
          }
        ]
      },
      {
        path: 'unit',
        element: (
          <UnitContextProvider>
            <Outlet />
          </UnitContextProvider>
        ),
        children: [
          {
            element: <UnitListPage />,
            index: true
          },
          {
            path: ':unitId',
            element: <UnitDetailPage />
          }
        ]
      },
      {
        path: 'site',
        element: (
          <SiteContextProvider>
            <Outlet />
          </SiteContextProvider>
        ),
        children: [
          {
            element: <SiteListPage />,
            index: true
          },
          {
            path: ':unitId',
            element: <SiteDetailPage />
          }
        ]
      },
      {
        path: 'department',
        element: (
          <DepartmentContextProvider>
            <DepartmentListPage />
          </DepartmentContextProvider>
        )
      },
      {
        path: 'assets',
        element: <Outlet />,
        children: [
          {
            path: 'goods',
            element: (
              <ItemContextProvider>
                <GoodContextProvider>
                  <Outlet />
                </GoodContextProvider>
              </ItemContextProvider>
            ),
            children: [
              {
                element: <ItemListPage />,
                index: true
              },
              {
                path: ':itemId',
                element: <ItemDetailPage />
              }
            ]
          },
          {
            path: 'unit',
            element: (
              <UnitContextProvider>
                <AssetUnitProvider>
                  <Outlet />
                </AssetUnitProvider>
              </UnitContextProvider>
            ),
            children: [
              {
                element: <UnitListPage />,
                index: true
              },
              {
                path: ':unitId',
                element: <UnitDetailPage />
              },
              {
                path: ':unitId/:mode',
                element: (
                  <FormUnitContextProvider>
                    <CreateEditPage />
                  </FormUnitContextProvider>
                )
              }
            ]
          },
          {
            path: 'code-activa',
            element: (
              <CodeActivaContextProvider>
                <Outlet />
              </CodeActivaContextProvider>
            ),
            children: [
              {
                element: <KodeActivaListPage />,
                index: true
              }
            ]
          },
          {
            path: 'document',
            element: (
              <DocumentContextProvider>
                <Outlet />
              </DocumentContextProvider>
            ),
            children: [
              {
                element: <DocumentListPage />,
                index: true
              }
            ]
          },
          {
            path: 'insurance',
            element: (
              <DocumentContextProvider>
                <Outlet />
              </DocumentContextProvider>
            ),
            children: [
              {
                element: <InsuranceListPage alternativeTitle='List Asuransi' />,
                index: true
              }
            ]
          }
        ]
      }
    ]
  }
] as RouteObject[]
