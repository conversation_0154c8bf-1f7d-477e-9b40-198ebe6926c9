import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

/* eslint-disable @typescript-eslint/no-explicit-any */
export const randomStr = (len = 16) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  const charsLength = chars.length
  for (let i = 0; i < len; i++) {
    result += chars.charAt(Math.floor(Math.random() * charsLength))
  }

  return result
}

export const randomNum = (min = 1, max = 99) => {
  min = Math.ceil(min)
  max = Math.floor(max)
  return Math.floor(Math.random() * (max - min) + min) // The maximum is exclusive and the minimum is inclusive
}

export const sleep = (ms: number) => new Promise(resolver => setTimeout(resolver, ms))

export const FV = (rate: number, nper: number, payment: number, value: number, type: number) => {
  let result

  if (rate === 0) {
    result = value + payment * nper
  } else {
    const term = Math.pow(1 + rate, nper)

    result =
      type === 1
        ? value * term + (payment * (1 + rate) * (term - 1)) / rate
        : value * term + (payment * (term - 1)) / rate
  }

  return -result
}

export const PMT = (rate: number, nper: number, pv: number) => {
  /*
   * rate   - interest rate per month
   * nper   - number of periods (months)
   * pv   - present value
   */
  return (rate * pv * Math.pow(1 + rate, nper)) / (1 - Math.pow(1 + rate, nper))
}

export const CUMPRINC = (rate: number, nper: number, pv: number, start_period: number, end: number, type: number) => {
  // Compute cumulative principal
  const payment = PMT(rate, nper, pv)
  let principal = 0

  if (start_period === 1) {
    principal = type === 0 ? payment + pv * rate : payment

    start_period++
  }

  for (let i = start_period; i <= end; i++) {
    principal +=
      type > 0
        ? payment - (FV(rate, i - 2, payment, pv, 1) - payment) * rate
        : payment - FV(rate, i - 1, payment, pv, 0) * rate
  }

  // Return cumulative principal
  return principal
}

export const toCurrency = (value?: number, keepZero = false, currency = 'IDR') => {
  try {
    const formatter = new Intl.NumberFormat(currency === 'USD' ? 'en-US' : 'id-ID', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2
    })
    return formatter.format(value ?? 0).replace(keepZero ? '' : /(\.|,)00$/g, '')
  } catch (error) {
    // Fallback to IDR if currency is invalid
    const fallbackFormatter = new Intl.NumberFormat(currency === 'USD' ? 'en-US' : 'id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 2
    })
    return fallbackFormatter.format(value ?? 0).replace(keepZero ? '' : /(\.|,)00$/g, '')
  }
}

export function toTitleCase(str: string) {
  if (!str) return ''
  let stringResult = str.replace(/\w\S*/g, function (txt) {
    return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  })
  if (stringResult.length < 4) {
    stringResult = stringResult.toUpperCase()
  }
  return stringResult
}

export function toErrorMessage(str?: string) {
  if (!str) return ''
  return (str.charAt(0).toUpperCase() + str.substring(1).toLowerCase()).replace(/_/g, ' ')
}

export function roundDecimal(value?: number) {
  return value ? value.toFixed(1).replace(/[.,]0$/, '') : 0
}

export function isNullOrUndefined(value?: any) {
  return value === undefined || value === null
}

export function classNames(...classes: unknown[]): string {
  return classes.filter(Boolean).join(' ')
}

export const formatThousandSeparator = (value: number, options = {}) =>
  new Intl.NumberFormat('id-Id', {
    ...options
  }).format(value)

export function bufferToHex(buffer: ArrayBuffer) {
  return Array.from(new Uint8Array(buffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
}

/**
 * Uses Web Crypto API to create a SHA256 HMAC hex string.
 */
export async function simpleHmac({ key, data }: { key: string; data: string }) {
  const encoder = new TextEncoder()
  const encodedKey = encoder.encode(key)
  const encodedData = encoder.encode(data)
  /**
   * @see https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/importKey
   */
  const hmacKey = await window.crypto.subtle.importKey(
    'raw',
    encodedKey,
    {
      name: 'HMAC',
      hash: 'SHA-256'
    },
    true,
    ['sign', 'verify']
  )
  /**
   * @see https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/sign#hmac_2
   */
  const signature = await window.crypto.subtle.sign('HMAC', hmacKey, encodedData)
  //   base64
  //   const buf1 = Buffer.from(signature).toString('base64')
  //   const buf2 = window.btoa(String.fromCharCode(...new Uint8Array(signature)))

  const hex = bufferToHex(signature)

  return { hex }
}

export function getRandomInt(min: number, max: number) {
  // Use Math.floor to round down to the nearest whole number
  // Use Math.random() to generate a random decimal between 0 (inclusive) and 1 (exclusive)
  // Multiply by the range (max - min + 1) to cover the entire range
  // Add the minimum value to shift the range to [min, max]
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export function formatCompactNumber(number?: number) {
  if (!number) return '0'
  if (number < 1000) {
    return number
  } else if (number >= 1000 && number < 1_000_000) {
    return parseFloat((number / 1000).toFixed(2)) + 'K'
  } else if (number >= 1_000_000 && number < 1_000_000_000) {
    return parseFloat((number / 1_000_000).toFixed(2)) + 'M'
  } else if (number >= 1_000_000_000 && number < 1_000_000_000_000) {
    return parseFloat((number / 1_000_000_000).toFixed(2)) + 'B'
  } else if (number >= 1_000_000_000_000 && number < 1_000_000_000_000_000) {
    return parseFloat((number / 1_000_000_000_000).toFixed(2)) + 'T'
  }
}

export function formatCompactNumberIdn(number?: number, longSuffix?: boolean) {
  if (!number) return '0'
  if (number < 1000) {
    return number
  } else if (number >= 1000 && number < 1_000_000) {
    return parseFloat((number / 1000).toFixed(2)) + `${longSuffix ? ' ribu' : 'rb'}`
  } else if (number >= 1_000_000 && number < 1_000_000_000) {
    return parseFloat((number / 1_000_000).toFixed(2)) + `${longSuffix ? ' juta' : 'jt'}`
  } else if (number >= 1_000_000_000 && number < 1_000_000_000_000) {
    return parseFloat((number / 1_000_000_000).toFixed(2)) + `${longSuffix ? ' milyar' : 'M'}`
  } else if (number >= 1_000_000_000_000 && number < 1_000_000_000_000_000) {
    return parseFloat((number / 1_000_000_000_000).toFixed(2)) + `${longSuffix ? ' trilyun' : 'T'}`
  }
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function convertBase64ToBlob(base64Image: string) {
  // Split into two parts
  const parts = base64Image.split(';base64,')

  // Hold the content type
  const imageType = parts[0].split(':')[1]

  // Decode Base64 string
  const decodedData = window.atob(parts[1])

  // Create UNIT8ARRAY of size same as row data length
  const uInt8Array = new Uint8Array(decodedData.length)

  // Insert all character code into uInt8Array
  for (let i = 0; i < decodedData.length; ++i) {
    uInt8Array[i] = decodedData.charCodeAt(i)
  }

  // Return BLOB image after conversion
  return new Blob([uInt8Array], { type: imageType })
}

export function fileNameFromUrl(url) {
  if (!url) return 'Dokumen'
  var matches = url.match(/\/([^\/?#]+)[^\/]*$/)
  if ((matches?.length ?? 0) > 1) {
    return matches[1]
  }
  return 'Dokumen'
}

export function getPrefix(str: string, delimiter = ' '): string {
  if (!str) return ''
  const parts = str.split(delimiter)
  return parts[0] || ''
}

export const getFirstHref = (menuData: any[]) => {
  for (const menu of menuData) {
    if (menu.href) return menu.href
    if (menu.children) {
      for (const child of menu.children) {
        if (child.href) return child.href
        if (child.children) {
          for (const grandchild of child.children) {
            if (grandchild.href) return grandchild.href
          }
        }
      }
    }
  }
  return '/'
}

export function isDistinctList(array) {
  return array.every((item, index) => array.indexOf(item) === index)
}

export function allElementsAreSame(array) {
  if (array.length === 0) return true // An empty array is considered to have "same" elements
  const firstElement = array[0]
  return array.every(item => item === firstElement)
}

export function objectsEqual(obj1: any, obj2: any): boolean {
  if (obj1 === obj2) return true

  if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
    return false
  }

  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  if (keys1.length !== keys2.length) return false

  for (const key of keys1) {
    if (!keys2.includes(key) || !objectsEqual(obj1[key], obj2[key])) {
      return false
    }
  }

  return true
}

export const mergeArrays = <T extends { [key: string]: any }>(arr1: T[], arr2: T[], key: keyof T): T[] => {
  const merged: T[] = [...arr1]
  const seen = new Set()

  // First pass: handle duplicates in arr1
  merged.forEach((item, index) => {
    const itemKey = item[key]
    if (seen.has(itemKey)) {
      // Find all items with the same key
      const duplicates = merged.filter(i => i[key] === itemKey)
      // Merge all properties from duplicates
      const mergedItem = duplicates.reduce((acc, curr) => ({ ...acc, ...curr }), {})
      // Remove duplicates and add merged item
      merged.splice(index, 1)
      merged.push(mergedItem as T)
    }
    seen.add(itemKey)
  })

  // Second pass: handle arr2 items
  arr2?.forEach(item2 => {
    const index = merged.findIndex(item1 => item1[key] === item2[key])
    if (index !== -1) {
      // Update existing item with new values
      merged[index] = { ...merged[index], ...item2 }
    } else {
      // Add new item
      merged.push(item2)
    }
  })

  return merged
}

export const formatDurationInHoursAndMinutes = (seconds: number): string => {
  if (isNaN(seconds) || seconds < 0) {
    return '-'
  }

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  const parts: string[] = []

  if (hours > 0) {
    parts.push(`${hours} jam`)
  }

  if (minutes > 0) {
    parts.push(`${minutes} menit`)
  }

  if (parts.length === 0) {
    return '0 menit'
  }

  return parts.join(' ')
}
