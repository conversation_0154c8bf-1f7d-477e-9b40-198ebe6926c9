import React from 'react'
import { LineSeries, LineSvgProps, PointTooltipProps, ResponsiveLine } from '@nivo/line'
import { ResponsiveBar, ResponsiveBarSvgProps } from '@nivo/bar'
import CustomPointSymbol from './point-symbol'
import { Typography } from '@mui/material'
import { toCurrency } from '@/utils/helper'

export interface LineDataType {
  id: string
  data: Array<{ x: string; y: number }>
}

export interface BarDataType {
  id?: string
  date?: string
  label?: string
  [key: string]: number | string
}

export interface BarChartProps extends Partial<ResponsiveBarSvgProps<any>> {
  bottomTickRotation?: number
}

export interface LineChartProps extends Partial<LineSvgProps<LineSeries>> {
  bottomTickRotation?: number
}

type ChartType = 'line' | 'bar'

type ChartProps = {
  chartType: ChartType
  data: any
  format?: 'currency' | 'nominal'
  keys?: string[] // for bar chart
  indexBy?: string // for bar chart
  barProps?: BarChartProps
  lineProps?: LineChartProps
}

type ChartPoint = {
  id: string
  indexInSeries: number
  absIndex: number
  seriesIndex: number
  seriesId: string
  seriesColor: string
  x: number
  y: number
  data: {
    x: string
    y: number
    yStacked: number
    xFormatted: string
    yFormatted: string
  }
  color: string
  borderColor: string
}

type ChartBarTooltipProps = {
  index: number
  x: number
  y: number
  absX: number
  absY: number
  width: number
  height: number
  color: string
  label: string
  id: string
  value: number
  formattedValue: string
  hidden: boolean
  indexValue: string
  data: {
    id: string
    label: string
    [key: string]: any
  }
}

type TooltipProps = {
  point: ChartPoint
  format: ChartProps['format']
  type: 'line' | 'bar'
} & ChartBarTooltipProps

const Container = ({ children }: { children: React.ReactNode }) => <div className='h-[350px]'>{children}</div>

const Tooltip = (props: TooltipProps) => {
  return (
    <div className='flex w-max p-3 gap-2 rounded-sm items-center bg-white shadow-md'>
      {props.type === 'bar' ? (
        <>
          <div className='size-4' style={{ backgroundColor: props.color }}></div>
          <Typography>{`${props?.label}: ${props.format === 'nominal' ? props.value : toCurrency(props.value)}`}</Typography>
        </>
      ) : (
        <>
          <div className='size-4' style={{ backgroundColor: props.point.color }}></div>
          <Typography>{`${props?.point.seriesId} - ${props.point.data.x}: ${props.format === 'nominal' ? props.point.data.y : toCurrency(props.point.data.y)}`}</Typography>
        </>
      )}
    </div>
  )
}

const RawChart = (props: ChartProps) => {
  const { chartType, data, keys, indexBy, format = 'nominal' } = props

  if (chartType === 'line') {
    return (
      <Container>
        <ResponsiveLine
          data={data}
          margin={{ top: 50, right: 50, bottom: 50, left: 60 }}
          xScale={{ type: 'point' }}
          yScale={{
            type: 'linear',
            min: 'auto',
            max: 'auto',
            stacked: true,
            reverse: false
          }}
          yFormat=' >-.2f'
          curve='cardinal'
          axisTop={null}
          axisRight={null}
          axisBottom={{
            tickSize: 5,
            tickPadding: 5,
            tickRotation: props.lineProps?.bottomTickRotation ?? 0,
            legend: '',
            legendOffset: 36,
            legendPosition: 'middle',
            truncateTickAt: 0
          }}
          axisLeft={{
            tickSize: 4,
            tickPadding: 4,
            tickRotation: 0,
            legend: '',
            legendOffset: -30,
            legendPosition: 'middle',
            truncateTickAt: 0
          }}
          colors={{ scheme: 'set2' }}
          lineWidth={1}
          pointSize={6}
          pointBorderWidth={5}
          pointLabelYOffset={-12}
          pointColor={{ from: 'series.color', modifiers: [] }}
          pointBorderColor={{
            from: 'color',
            modifiers: [['opacity', 0.5]]
          }}
          useMesh
          pointSymbol={CustomPointSymbol as any}
          enableArea={true}
          areaOpacity={0.3}
          enableTouchCrosshair={true}
          tooltip={props => Tooltip({ ...props, format, type: 'line' } as unknown as TooltipProps)}
          legends={[
            {
              anchor: 'bottom',
              direction: 'row',
              justify: false,
              translateX: 4,
              translateY: 50,
              itemWidth: 90,
              itemHeight: 20,
              itemsSpacing: 4,
              symbolSize: 7,
              symbolShape: 'circle',
              itemDirection: 'left-to-right',
              itemTextColor: '#777',
              effects: [
                {
                  on: 'hover',
                  style: {
                    itemBackground: 'rgba(0, 0, 0, .03)',
                    itemOpacity: 1
                  }
                }
              ]
            }
          ]}
          role='application'
          {...props.lineProps}
        />
      </Container>
    )
  }

  if (chartType === 'bar') {
    return (
      <Container>
        <ResponsiveBar
          enableLabel={false}
          data={data}
          keys={keys || []}
          indexBy={indexBy || ''}
          margin={{ top: 50, right: 50, bottom: 80, left: 50 }}
          padding={0.3}
          groupMode='grouped'
          valueScale={{ type: 'linear' }}
          indexScale={{ type: 'band', round: true }}
          colors={{ scheme: 'nivo' }}
          borderColor={{
            from: 'color',
            modifiers: [['darker', 1.6]]
          }}
          tooltip={props => Tooltip({ ...props, format, type: 'bar' } as unknown as TooltipProps)}
          axisTop={null}
          axisRight={null}
          axisBottom={{
            tickSize: 5,
            tickPadding: 2,
            tickRotation: props.barProps?.bottomTickRotation ?? -45,
            legend: '',
            legendPosition: 'middle',
            legendOffset: 32,
            truncateTickAt: 0
          }}
          axisLeft={{
            tickSize: 5,
            tickPadding: 5,
            tickRotation: 0,
            legend: '',
            legendPosition: 'middle',
            legendOffset: -40,
            truncateTickAt: 0
          }}
          labelSkipWidth={12}
          labelSkipHeight={12}
          labelTextColor={{
            from: 'color',
            modifiers: [['darker', 1.6]]
          }}
          legends={[
            {
              dataFrom: 'keys',
              anchor: 'bottom',
              direction: 'row',
              justify: false,
              translateX: 25,
              translateY: 46,
              itemsSpacing: 2,
              itemWidth: 100,
              itemHeight: 20,
              itemDirection: 'left-to-right',
              itemOpacity: 0.85,
              symbolSize: 12,
              effects: [
                {
                  on: 'hover',
                  style: {
                    itemOpacity: 1
                  }
                }
              ]
            }
          ]}
          role='application'
          ariaLabel='bar chart demo'
          {...props.barProps}
        />
      </Container>
    )
  }

  return null
}

export default RawChart
