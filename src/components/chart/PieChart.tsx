import truncateString from '@/core/utils/truncate'
import { MgInStatisticsChartType } from '@/types/dashboardTypes'
import { toCurrency } from '@/utils/helper'
import { Typography } from '@mui/material'
import { ComputedDatum, DefaultRawDatum, PieSvgProps, ResponsivePie } from '@nivo/pie'
import { useMemo } from 'react'

export type PieDataType = {
  color?: string
} & MgInStatisticsChartType

type Props = {
  data: PieDataType[]
  value?: 'numeric' | 'currency' | 'percent'
  format?: 'numeric' | 'currency'
  withSummary?: boolean
} & Omit<PieSvgProps<DefaultRawDatum>, 'width' | 'height'>

const PieChart = ({ withSummary = true, ...props }: Props) => {
  const { data, value = 'numeric', format = 'numeric', ...rest } = props
  const retValue = useMemo(() => {
    const totalValue = data?.reduce((acc, cur) => acc + cur?.value, 0) ?? 0
    switch (value) {
      case 'currency':
        return truncateString(toCurrency(totalValue), 20)
      case 'percent':
        return truncateString(`${totalValue}%`, 20)
      default:
        return truncateString(`${totalValue}`, 5)
    }
  }, [data, value])
  return (
    <div className='h-[300px] relative'>
      {withSummary && (
        <div className='absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 flex flex-col overflow-hidden items-center justify-center w-[100px] h-fit'>
          <div className='flex-1'>
            <Typography color='secondary' variant='h4' sx={{ ...(value !== 'currency' ? {} : { fontSize: '11pt' }) }}>
              {retValue}
            </Typography>
          </div>
        </div>
      )}
      <ResponsivePie
        arcLinkLabel={datum => `${datum.label}\r\n ${format === 'numeric' ? datum.value : toCurrency(datum.value)}`}
        arcLinkLabelsThickness={2}
        arcLinkLabelsTextOffset={0}
        arcLinkLabelsSkipAngle={13}
        arcLinkLabelsColor={{ from: 'color' }}
        margin={{ bottom: 50, top: 50, left: 50, right: 50 }}
        enableArcLabels={false}
        innerRadius={0.4}
        colors={{ scheme: 'nivo' }}
        data={data ?? []}
        {...rest}
      />
    </div>
  )
}

export default PieChart
