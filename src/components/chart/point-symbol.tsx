interface Datum {
  x: string
  y: number
  yStacked: number
  xFormatted: string
  yFormatted: string
}

interface SvgProps {
  borderColor: string
  borderWidth: number
  color: string
  datum: Datum
  size: number
  x: any
  y: any
}

const CustomPointSymbol = (props: SvgProps) => {
  const { color, size, borderWidth, borderColor } = props

  return (
    <circle
      r={size / 2.5}
      cx={1}
      cy={1}
      fill={color}
      opacity={1}
      strokeWidth={borderWidth}
      stroke={borderColor}
      style={{
        pointerEvents: 'none'
      }}
    />
  )
}

export default CustomPointSymbol
