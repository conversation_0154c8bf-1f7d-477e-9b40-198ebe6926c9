import React, { useEffect, useState } from 'react'
import { IconButton, TextField, TextFieldProps } from '@mui/material'
import { isNullOrUndefined } from '@/utils/helper'

const DebouncedInput = ({
  value: initialValue,
  onChange,
  debounce = 500,
  size = 'small',
  ...props
}: {
  value: string | number
  onChange: (value: string | number) => void
  debounce?: number
} & Omit<TextFieldProps, 'onChange'>) => {
  // States
  const [inputValue, setInputValue] = useState(initialValue)

  useEffect(() => {
    setInputValue(initialValue)
  }, [initialValue])

  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(inputValue)
    }, debounce)

    return () => clearTimeout(timeout)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [inputValue])

  return (
    <TextField
      {...props}
      value={isNullOrUndefined(inputValue) ? '' : inputValue}
      onChange={e => setInputValue(e.target.value)}
      size={size}
      InputProps={{
        startAdornment: <i className='ri-search-line text-textSecondary size-5 mr-2' />,
        endAdornment: inputValue && (
          <IconButton onClick={() => setInputValue('')}>
            <i className='ri-close-line' />
          </IconButton>
        )
      }}
    />
  )
}

export default DebouncedInput
