import React, { useEffect, useState, useCallback } from 'react'
import { Autocomplete, CircularProgress, TextField, Typography, debounce, AutocompleteProps } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { UNIT_LIST_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { UnitType } from '@/types/companyTypes'

// Props interface for the UnitAutocomplete component
export interface UnitAutocompleteProps {
  // Form control props
  value?: UnitType | null
  onChange?: (unit: UnitType | null) => void
  onUnitSelect?: (unit: UnitType | null) => void
  error?: boolean
  helperText?: string

  // UI customization props
  label?: string
  placeholder?: string
  disabled?: boolean
  required?: boolean
  fullWidth?: boolean

  // Behavior props
  clearOnSelect?: boolean
  debounceMs?: number

  // Additional autocomplete props
  autoCompleteProps?: Partial<AutocompleteProps<UnitType, false, false, true>>
}

const UnitAutocomplete: React.FC<UnitAutocompleteProps> = ({
  value = null,
  onChange,
  onUnitSelect,
  error = false,
  helperText = '',
  label = 'Kode Unit',
  placeholder = 'Masukkan kode unit',
  disabled = false,
  required = false,
  fullWidth = true,
  clearOnSelect = true,
  debounceMs = 700,
  autoCompleteProps = {}
}) => {
  // State management
  const [selectedUnit, setSelectedUnit] = useState<UnitType | null>(value)
  const [unitSearchQuery, setUnitSearchQuery] = useState('')

  // Sync external value changes
  useEffect(() => {
    setSelectedUnit(value)
  }, [value])

  // Query for fetching units
  const {
    data: unitListResponse = defaultListData as ListResponse<UnitType>,
    isLoading: fetchUnitsLoading,
    remove: removeUnitList
  } = useQuery({
    enabled: !!unitSearchQuery,
    queryKey: [UNIT_LIST_QUERY_KEY, unitSearchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getUnitList({
        ...(unitSearchQuery && { number: unitSearchQuery }),
        limit: Number.MAX_SAFE_INTEGER
      })
    },
    placeholderData: defaultListData as ListResponse<UnitType>
  })

  // Debounced search handler
  const handleInputChange = useCallback(
    debounce((event: any, newValue: string, reason: string) => {
      if (reason === 'input') {
        setUnitSearchQuery(newValue)
      }
    }, debounceMs),
    [debounceMs]
  )

  // Handle unit selection
  const handleUnitChange = (event: any, newValue: UnitType | null) => {
    if (newValue) {
      setSelectedUnit(newValue)

      // Call the onChange callback if provided
      if (onChange) {
        onChange(newValue)
      }

      // Call the onUnitSelect callback if provided
      if (onUnitSelect) {
        onUnitSelect(newValue)
      }

      // Clear unit list data after selection to optimize memory
      if (clearOnSelect) {
        removeUnitList()
      }
    } else {
      setSelectedUnit(null)
      if (onChange) {
        onChange(null)
      }
      if (onUnitSelect) {
        onUnitSelect(null)
      }
    }
  }

  return (
    <Autocomplete<UnitType, false, false, true>
      {...autoCompleteProps}
      key={JSON.stringify(selectedUnit)}
      filterOptions={x => x}
      isOptionEqualToValue={(option, value) => {
        if (typeof option === 'string' || typeof value === 'string') {
          return false
        }
        return option.id === value.id
      }}
      onInputChange={handleInputChange}
      options={unitListResponse.items || []}
      freeSolo
      onChange={(event, newValue) => {
        // Handle string values from freeSolo
        if (typeof newValue === 'string') {
          return
        }
        handleUnitChange(event, newValue)
      }}
      value={selectedUnit}
      noOptionsText='Unit tidak ditemukan'
      loading={fetchUnitsLoading}
      disabled={disabled}
      fullWidth={fullWidth}
      renderInput={params => (
        <TextField
          {...params}
          label={label}
          placeholder={placeholder}
          variant='outlined'
          required={required}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {fetchUnitsLoading ? <CircularProgress size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
            onKeyDown: e => {
              // Prevent Enter key from submitting form
              if (e.key === 'Enter') {
                e.stopPropagation()
              }
            },
            className: 'bg-white'
          }}
          {...(error && { error: true, helperText: helperText })}
        />
      )}
      getOptionLabel={option => {
        if (typeof option === 'string') {
          return option
        }
        return option?.number || ''
      }}
      renderOption={(props, option) => {
        const { key, ...optionProps } = props
        if (typeof option === 'string') {
          return null
        }
        return (
          <li key={key} {...optionProps}>
            <Typography>
              {option.number} | {option.brandName} | {option.type}
            </Typography>
          </li>
        )
      }}
    />
  )
}

export default UnitAutocomplete
