import {
  Dialog,
  DialogContent,
  DialogProps,
  DialogTitle,
  IconButton,
  Typography,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Divider
} from '@mui/material'
import LoadingButton from '@mui/lab/LoadingButton'
import { useState } from 'react'
import { Grid } from '@mui/material'
import NumberField from '@/components/numeric/NumberField'
import CurrencyField from '@/components/numeric/CurrencyField'
import { ImageItemType } from '@/types/companyTypes'
import { defaultImageList } from '../add-item-dialog'
import PhotoPicker from '@/components/PhotoPicker'

type AddItemDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const DialogItemAsset = (props: AddItemDialogProps) => {
  const { open, setOpen } = props
  const [imageList, setImageList] = useState<ImageItemType[]>(defaultImageList)

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  return (
    <Dialog PaperProps={{ className: 'rounded-t-[16px] md:rounded-t-none' }} open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Tambah Barang
        <Typography component='span' className='flex flex-col text-center'>
          Tambahkan barang untuk didaftarkan ke list
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12} md={6}>
            <TextField placeholder='Kode Induk' label='Kode Induk' />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField required placeholder='Kode Barang' label='Kode Barang' />
          </Grid>
          <Grid item xs={12}>
            <TextField fullWidth required placeholder='Kode Eksternal' label='Kode Eksternal' />
          </Grid>
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel id='unit-category-label'>Kategori Item</InputLabel>
              <Select
                labelId='unit-category-label'
                id='unit-category'
                label='Kategori Item'
                defaultValue='Alat Berat'
              ></Select>
              <FormHelperText>
                Kategori Belum ada?{' '}
                <span className='text-primary cursor-pointer' role='button'>
                  Tambah Kategori
                </span>
              </FormHelperText>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField fullWidth required placeholder='Nama Item' label='Nama Item' />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField fullWidth required placeholder='Merk Item' label='Merk Item' />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField fullWidth required placeholder='Satuan Besar' label='Satuan Besar' />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField required placeholder='Satuan Kecil' label='Satuan Kecil' />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              required
              type='number'
              label='Konversi Satuan'
              placeholder='1 Dus = 10 Pcs'
              InputProps={{ inputComponent: NumberField as any }}
            />
          </Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid item xs={12}>
            <Typography variant='body2'>Informasi Vendor (opsional)</Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Kode Vendor' placeholder='Masukkan kode vendor' />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField fullWidth label='Nama Vendor' placeholder='Masukkan nama vendor' />
          </Grid>
          <Grid item xs={12}>
            <Divider />
          </Grid>
          <Grid item xs={12}>
            <Typography variant='body2'>Informasi Harga (opsional)</Typography>
          </Grid>
          <Grid item xs={12}>
            <TextField fullWidth label='Harga Satuan Kecil' InputProps={{ inputComponent: CurrencyField as any }} />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id='tax-amount'>Pajak</InputLabel>
              <Select labelId='tax-amount' id='tax-amount' label='Pajak'></Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label='Jumlah Pajak'
              InputProps={{ inputComponent: NumberField as any, endAdornment: '%' }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id='discount-type'>Jenis Diskon</InputLabel>
              <Select labelId='discount-type' id='discount-type' label='Jenis Diskon'></Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label='Jumlah Diskon'
              InputProps={{ inputComponent: NumberField as any, endAdornment: '%' }}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography variant='caption' mt={4} mb={3}>
              Foto Barang (opsional)
            </Typography>
            <div className='flex gap-5 overflow-y-hidden max-sm:px-2'>
              {imageList?.map((item, index) => (
                <PhotoPicker
                  key={`${item.content}_${index}`}
                  content={item.content}
                  // disabled={isLoading}
                  onPicked={(content, fileName) => {
                    setImageList(current => {
                      const tempCurrent = [...current]
                      tempCurrent[index] = { content, fileName }
                      return tempCurrent
                    })
                  }}
                  onRemoved={() => {
                    setImageList(current => {
                      const tempCurrent = [...current]
                      tempCurrent[index] = { content: '', fileName: '' }
                      return tempCurrent
                    })
                  }}
                />
              ))}
            </div>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' className='is-full sm:is-auto'>
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          // loading={isLoading}
          loadingPosition='start'
          variant='contained'
          // onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          TAMBAHKAN
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default DialogItemAsset
