// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { Autocomplete, Grid } from '@mui/material'
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form'

import { CategoryType } from '@/types/companyTypes'
import { object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import { useAddUnit, useUpdateUnit } from '@/api/services/company/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useUnit } from '@/pages/company-data/unit/context/UnitContext'
import CompanyQueryMethods, { CATEGORY_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'

type CloseFRDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onCloseFrHandler: (data: CloseFrInput) => void
  loading?: boolean
}

const closeFrSchema = object({
  closeReason: string({ message: 'Wajib diisi' }).min(1)
})

export type CloseFrInput = Required<TypeOf<typeof closeFrSchema>>

const CloseFRDialog = ({ open, setOpen, onCloseFrHandler, loading = false }: CloseFRDialogProps) => {
  const { control, handleSubmit } = useForm<CloseFrInput>({
    resolver: zodResolver(closeFrSchema),
    defaultValues: {
      closeReason: ''
    }
  })

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Tutup Field Report
        <Typography component='span' className='flex flex-col text-center'>
          Masukkan alasan penutupan Field Report, Action ini tidak dapat diubah lagi
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-7 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='closeReason'
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  value={value}
                  onChange={onChange}
                  multiline
                  rows={2}
                  label='Alasan Ditutup'
                  error={!!error}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} disabled={loading} variant='outlined' className='is-full sm:is-auto'>
          Kembali
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={loading}
          loadingPosition='start'
          variant='contained'
          color='error'
          onClick={handleSubmit(onCloseFrHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          TUTUP FIELD REPORT
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default CloseFRDialog
