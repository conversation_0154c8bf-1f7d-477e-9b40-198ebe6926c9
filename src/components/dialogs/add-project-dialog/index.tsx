import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { TypeOf, z } from 'zod'
import { useCurrencies } from '@/pages/accounting/currency/context/CurrencyContext'
import { useAddCurrency, useCreateProject, useUpdateCurrency, useUpdateProject } from '@/api/services/company/mutation'
import { ProjectType } from '@/types/projectTypes'
import { useProject } from '@/pages/company-data/projects/context/ProjectContext'
import { useAuth } from '@/contexts/AuthContext'

type AddProjectDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  projectData?: ProjectType
  readonly?: boolean
}

const addProjectSchema = z.object({
  code: z.string({ message: 'Wajib diisi' }),
  name: z.string({ message: 'Wajib diisi' }),
  siteId: z.string({ message: 'Wajib diisi' })
})
type AddProjectInput = Required<TypeOf<typeof addProjectSchema>>

const AddProjecsDialog = ({ open, setOpen, projectData, readonly = false }: AddProjectDialogProps) => {
  const { fetchProjectList, fetchProjectData } = useProject()
  const { control, handleSubmit, reset } = useForm<AddProjectInput>({
    resolver: zodResolver(addProjectSchema)
  })
  const {
    userProfile: { sites }
  } = useAuth()

  const { mutate: addMutate, isLoading: addLoading } = useCreateProject()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateProject()

  const isLoading = addLoading || updateLoading

  const onSubmitHandler: SubmitHandler<AddProjectInput> = (inputValues: AddProjectInput) => {
    if (projectData) {
      updateMutate(
        {
          ...inputValues,
          id: projectData.id
        },
        {
          onSuccess: () => {
            toast.success('Data Proyek berhasil diubah')
            fetchProjectData()
            fetchProjectList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Data Proyek berhasil ditambahkan')
            fetchProjectList()
            setOpen(false)
          }
        }
      )
    }
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  useEffect(() => {
    if (projectData) {
      reset(projectData)
    } else {
      reset({
        code: null,
        name: null,
        siteId: null
      })
    }
  }, [projectData])

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {readonly ? '' : projectData ? 'Ubah Proyek' : 'Tambah Proyek'}
        {readonly ? (
          <Typography component='span' className='flex flex-col text-center'>
            Lihat detil proyek
          </Typography>
        ) : (
          !projectData && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan proyek di perusahaan kamu
            </Typography>
          )
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          {readonly ? (
            <>
              {/* <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Nama</small>
                  <Typography>{projectData?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Kode Mata Uang</small>
                  <Typography>{projectData?.code}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Default Mata Uang</small>
                  <Typography color={projectData?.isDefault ? 'primary' : 'error'}>
                    {projectData?.isDefault ? 'Ya' : 'Tidak'}
                  </Typography>
                </div>
              </Grid> */}
            </>
          ) : (
            <>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='code'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Kode Proyek'
                      variant='outlined'
                      placeholder='Contoh: Rp'
                      disabled={isLoading}
                      InputLabelProps={{ shrink: !!field.value }}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='name'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      label='Nama Proyek'
                      placeholder='Contoh: Proyek Pembangunan'
                      fullWidth
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='siteId'
                  render={({ field }) => (
                    <FormControl fullWidth>
                      <InputLabel>Site</InputLabel>
                      <Select {...field} label='Site' placeholder='Select Site'>
                        {sites.map(site => (
                          <MenuItem key={site.id} value={site.id}>
                            {site.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        {readonly ? (
          <>
            {/* <Button onClick={() => handleDeleteRow(projectData.id)} variant='outlined' color='error'>
              Hapus
            </Button>
            <Button onClick={handleDetailEdit} variant='outlined'>
              Edit
            </Button> */}
          </>
        ) : (
          <>
            <Button
              onClick={() => setOpen(false)}
              variant='outlined'
              disabled={isLoading}
              className='is-full sm:is-auto'
            >
              BATALKAN
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={isLoading}
              loadingPosition='start'
              variant='contained'
              onClick={handleSubmit(onSubmitHandler, console.error)}
              className='px-8 is-full !ml-0 sm:is-auto'
            >
              {projectData ? 'UBAH DATA' : 'TAMBAHKAN'}
            </LoadingButton>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default AddProjecsDialog
