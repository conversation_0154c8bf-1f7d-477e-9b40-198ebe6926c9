import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { string, TypeOf, z } from 'zod'
import { useAddCustomer, useUpdateCustomer } from '@/api/services/company/mutation'
import { CustomerType } from '@/types/customerTypes'
import { useCustomer } from '@/pages/accounting/customer/context/CustomerContext'

type AddCustomerDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  customerData?: CustomerType
  readonly?: boolean
}

const addCustomerSchema = z.object({
  name: string({ message: 'Wajib diisi' }),
  address: string({ message: 'Wajib diisi' }),
  picName: string({ message: 'Wajib diisi' }),
  picPhoneNumber: string({ message: 'Wajib diisi' }),
  email: string().email().optional().nullable(),
  phoneNumber: string().optional().nullable(),
  faxNumber: string().optional().nullable(),
  taxplayerNumber: string().optional().nullable()
})
type AddCustomerInput = Required<TypeOf<typeof addCustomerSchema>>

const AddCustomerDialog = ({ open, setOpen, customerData, readonly = false }: AddCustomerDialogProps) => {
  const { handleDeleteRow, handleDetailEdit, fetchCustomerList } = useCustomer()
  const { control, handleSubmit, reset } = useForm<AddCustomerInput>({
    resolver: zodResolver(addCustomerSchema)
  })

  const { mutate: addMutate, isLoading: addLoading } = useAddCustomer()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateCustomer()

  const isLoading = addLoading || updateLoading

  const onSubmitHandler: SubmitHandler<AddCustomerInput> = (inputValues: AddCustomerInput) => {
    if (customerData) {
      updateMutate(
        {
          ...inputValues,
          id: customerData.id
        },
        {
          onSuccess: () => {
            toast.success('Pelanggan berhasil diubah')
            fetchCustomerList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Pelanggan berhasil ditambahkan')
            fetchCustomerList()
            setOpen(false)
          }
        }
      )
    }
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  useEffect(() => {
    if (customerData) {
      reset(customerData)
    } else {
      reset({
        name: '',
        address: '',
        picName: '',
        picPhoneNumber: '',
        email: undefined,
        phoneNumber: undefined,
        faxNumber: undefined,
        taxplayerNumber: undefined
      })
    }
  }, [customerData])

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {readonly ? 'Customer/Pelanggan' : customerData ? 'Ubah Pelanggan / Customer' : 'Tambah Pelanggan / Customer'}
        {readonly ? (
          <Typography component='span' className='flex flex-col text-center'>
            Lihat detil Pelanggan / Customer
          </Typography>
        ) : (
          !customerData && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan Pelanggan / Customer
            </Typography>
          )
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          {readonly ? (
            <>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Nama Customer / Pelanggan</small>
                  <Typography>{customerData?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Alamat</small>
                  <Typography>{customerData?.address}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Nama PIC</small>
                  <Typography>{customerData?.picName}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>No Telepon PIC</small>
                  <Typography>{customerData?.picPhoneNumber}</Typography>
                </div>
              </Grid>
            </>
          ) : (
            <>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='name'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      label='Nama Customer / Pelanggan'
                      placeholder='Contoh: PT Lorem Ipsum'
                      fullWidth
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='address'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Alamat'
                      variant='outlined'
                      placeholder='Contoh: Jl. Lorem Ipsum'
                      disabled={isLoading}
                      InputLabelProps={{ shrink: !!field.value }}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='picName'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Nama PIC'
                      variant='outlined'
                      placeholder='Contoh: Lorem Ipsum'
                      disabled={isLoading}
                      InputLabelProps={{ shrink: !!field.value }}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='picPhoneNumber'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='No. Telepon PIC'
                      variant='outlined'
                      placeholder='Contoh: 08123456789'
                      disabled={isLoading}
                      InputLabelProps={{ shrink: !!field.value }}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        {readonly ? (
          <>
            <Button onClick={() => handleDeleteRow(customerData.id)} variant='outlined' color='error'>
              Hapus
            </Button>
            <Button onClick={handleDetailEdit} variant='outlined'>
              Edit
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => setOpen(false)}
              variant='outlined'
              disabled={isLoading}
              className='is-full sm:is-auto'
            >
              BATALKAN
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={isLoading}
              loadingPosition='start'
              variant='contained'
              onClick={handleSubmit(onSubmitHandler, console.error)}
              className='px-8 is-full !ml-0 sm:is-auto'
            >
              {customerData ? 'UBAH DATA' : 'TAMBAHKAN'}
            </LoadingButton>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default AddCustomerDialog
