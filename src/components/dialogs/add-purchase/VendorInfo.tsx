import React, { use<PERSON><PERSON>back, useEffect, useState } from 'react'

import {
  Autocomplete,
  Button,
  CircularProgress,
  debounce,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { Controller, useFormContext, useWatch } from 'react-hook-form'

import { VendorAdressesType, VendorType } from '@/types/companyTypes'
import CompanyQueryMethods, {
  CURRENCIES_LIST_QUERY_KEY,
  VENDOR_LIST_QUERY_KEY,
  VENDOR_QUERY_KEY
} from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { PoType, PurchasePayload } from '@/pages/purchase-order/config/types'
import { useFilePicker } from 'use-file-picker'
import { useUpdateEffect } from 'react-use'
import <PERSON>urrency<PERSON>ield from '@/components/numeric/CurrencyField'
import { useAddPurchase } from './context'
import { useAuth } from '@/contexts/AuthContext'
import { PrType } from '@/types/prTypes'
import { formatDate } from 'date-fns'
import { fileNameFromUrl } from '@/utils/helper'

type Props = {
  prData?: PrType
}

const VendorInfo: React.FC<Props> = props => {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedVendor, setSelectedVendor] = useState<VendorType | null>()
  const [selectedAdress, setSelectedAdress] = useState<VendorAdressesType | null>()
  const { control, getValues, setValue, resetField, reset } = useFormContext<PurchasePayload>()
  const { currenciesList } = useAuth()

  const { currentCurrency, setCurrentCurrency } = useAddPurchase()

  const currencyId = useWatch({
    control,
    name: 'currencyId'
  })

  const itemsWatch = useWatch({
    control,
    name: `items`,
    defaultValue: []
  })

  const isGeneralPurchaseWatch = useWatch({
    control,
    name: 'isGeneralPurchase'
  })

  const vendorId = useWatch({
    control,
    name: 'vendorId'
  })

  const { data: vendorData, isFetching: loadingVendorData } = useQuery({
    enabled: !!vendorId || !!selectedVendor?.id,
    queryKey: [VENDOR_QUERY_KEY, selectedVendor?.id, vendorId],
    queryFn: () => CompanyQueryMethods.getVendor(vendorId || selectedVendor?.id)
  })

  const {
    data: { items: vendorList },
    isLoading: fetchVendorsLoading
  } = useQuery({
    enabled: !!searchQuery,
    queryKey: [VENDOR_LIST_QUERY_KEY, searchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getVendorList({
        ...(searchQuery && { search: searchQuery }),
        limit: 100000
      })
    },
    placeholderData: defaultListData as ListResponse<VendorType>
  })

  const { openFilePicker, filesContent, clear } = useFilePicker({
    multiple: false,
    accept: ['.pdf', '.docx', '.doc', 'xls', 'xlsx', 'image/*'],
    readAs: 'DataURL'
  })

  useUpdateEffect(() => {
    if ((filesContent?.length ?? 0) > 0) {
      setValue('tenderDocumentContent', filesContent[0]?.content)
      setValue('tenderDocumentName', filesContent[0]?.name)
    }
  }, [filesContent])

  useEffect(() => {
    if (currencyId && currenciesList.length > 0) {
      const currentCurrency = currenciesList.find(currency => currency.id === currencyId)
      setCurrentCurrency(currentCurrency)
      if (currentCurrency?.isDefault) {
        resetField('exchangeRate', { defaultValue: 1 })
      }
    }
  }, [currencyId, currenciesList])

  useEffect(() => {
    if (!!props.prData?.generalPurchase) {
      reset({
        ...getValues(),
        isGeneralPurchase: true,
        vendorName: props.prData?.generalPurchase?.vendorName
      })
    }
  }, [props.prData?.generalPurchase])

  const invoiceSection = useCallback(() => {
    const poData = getValues() as unknown as PoType
    return (
      <>
        <Grid item xs={12}>
          <Typography color='GrayText' className='font-bold'>
            Nota Pembelian
          </Typography>
        </Grid>
        <Grid item xs={12}>
          <div className='flex flex-col gap-2'>
            <small>Tanggal Nota</small>
            <Typography>
              {poData?.generalPurchase?.invoiceDate
                ? formatDate(poData?.generalPurchase?.invoiceDate, 'dd/MM/yyyy')
                : '-'}
            </Typography>
          </div>
        </Grid>
        <Grid item xs={12}>
          <div className='flex flex-col gap-2'>
            <small>Nota Pembelian</small>
            {poData?.generalPurchase?.invoices?.map(invoice => (
              <div key={JSON.stringify(invoice)} className='flex justify-between items-center'>
                <Typography>{fileNameFromUrl(invoice?.url)}</Typography>
                <Button className='underline' href={invoice?.url} target='_blank'>
                  Unduh
                </Button>
              </div>
            ))}
          </div>
        </Grid>
      </>
    )
  }, [getValues()])

  return (
    <div className='flex flex-col mt-8 w-full max-w-[780px] max-md:max-w-full'>
      <h2 className='self-start text-sm tracking-normal text-center text-gray-600 text-opacity-60'>
        <Typography color='GrayText' className='font-bold'>
          Informasi Vendor
        </Typography>
      </h2>
      <Grid container spacing={3} className='mt-2'>
        {!!isGeneralPurchaseWatch ? (
          <>
            <Grid item xs={12} md={6}>
              <div className='flex flex-col gap-2'>
                <small>Pembelian di</small>
                <Typography>Vendor Umum</Typography>
              </div>
            </Grid>
            <Grid item xs={12} md={6}>
              <div className='flex flex-col gap-2'>
                <small>Nama Vendor</small>
                <Typography>{getValues('vendorName')}</Typography>
              </div>
            </Grid>
            {!!(getValues() as unknown as PoType)?.generalPurchase?.invoiceDate && invoiceSection()}
          </>
        ) : (
          <>
            <Grid item xs={12} md={6}>
              <Controller
                control={control}
                name='vendorId'
                rules={{ required: true }}
                render={({ field: { onChange, value }, formState, fieldState: { error } }) => (
                  <Autocomplete
                    filterOptions={x => x}
                    isOptionEqualToValue={(option, value) => option.id === value.id}
                    onInputChange={debounce((e, newValue, reason) => {
                      if (reason === 'input') {
                        setSearchQuery(newValue)
                      }
                    }, 700)}
                    options={vendorList || []}
                    freeSolo
                    onChange={(e, newValue: VendorType) => {
                      if (newValue) {
                        setSelectedVendor(newValue)
                        resetField('vendor', { defaultValue: newValue })
                        resetField('vendorId', { defaultValue: newValue.id })
                        setValue('vendor', newValue)
                        setValue('vendorId', newValue?.id)
                      }
                    }}
                    value={getValues('vendor')}
                    noOptionsText='Vendor tidak ditemukan'
                    loading={fetchVendorsLoading}
                    disabled={itemsWatch.some(prItem => !!prItem.item?.vendor)}
                    renderInput={params => (
                      <TextField
                        {...params}
                        label='Kode Vendor'
                        placeholder='Masukkan kode vendor'
                        variant='outlined'
                        {...(!!error && { error: true, helperText: 'Wajib diisi' })}
                        InputProps={{
                          ...params.InputProps,
                          endAdornment: <>{fetchVendorsLoading ? <CircularProgress /> : null}</>,
                          onKeyDown: e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation()
                            }
                          }
                        }}
                      />
                    )}
                    getOptionLabel={(option: VendorType) => option?.code}
                    className='flex-1'
                    renderOption={(props, option) => {
                      const { key, ...optionProps } = props
                      return (
                        <li key={key} {...optionProps}>
                          <Typography>
                            {option.code} - {option.name}, {option.address}
                          </Typography>
                        </li>
                      )
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name='vendor'
                control={control}
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    value={field.value?.name}
                    fullWidth
                    label='Nama Vendor'
                    placeholder='Masukkan nama vendor'
                    className='flex-1'
                    disabled
                    InputLabelProps={{ shrink: !!field.value }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                control={control}
                name='vendorAddressId'
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <FormControl fullWidth>
                    <InputLabel id='alamat-vendor'>Alamat Vendor</InputLabel>
                    <Select
                      {...field}
                      disabled={loadingVendorData}
                      fullWidth
                      label='Alamat Vendor'
                      placeholder='Alamat Vendor'
                      className='bg-white'
                      onChange={e => {
                        field.onChange(e)
                        const activeAdress = vendorData.addresses.find(addr => addr.id === e.target.value)
                        setSelectedAdress(activeAdress)
                        resetField('vendorPicName', { defaultValue: activeAdress?.picName ?? '' })
                        resetField('vendorPicPhoneNumber', { defaultValue: activeAdress?.picPhoneNumber ?? '' })
                        setValue('vendorPicName', activeAdress?.picName ?? '')
                        setValue('vendorPicPhoneNumber', activeAdress?.picPhoneNumber ?? '')
                      }}
                      error={!!error}
                    >
                      {vendorData?.addresses?.map(addr => (
                        <MenuItem key={addr.id} value={addr.id}>
                          {addr.address}
                        </MenuItem>
                      ))}
                    </Select>
                    {!!error && <FormHelperText error>Wajib diisi</FormHelperText>}
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name='vendorPicName'
                control={control}
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='PIC Vendor'
                    placeholder='Masukkan PIC vendor'
                    InputLabelProps={{ shrink: !!field.value }}
                    {...(!!error && { error: true, helperText: 'Wajib diisi.' })}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name='vendorPicPhoneNumber'
                control={control}
                rules={{ required: true }}
                render={({ field, fieldState: { error } }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label='Nomor Telepon PIC'
                    type='tel'
                    placeholder='Masukkan Nomor Telepon PIC'
                    InputLabelProps={{ shrink: !!field.value }}
                    {...(!!error && { error: true, helperText: 'Wajib diisi.' })}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <div className='flex flex-col gap-3 flex-1'>
                <Typography className='font-semibold'>Unggah Dokumen Penawaran Vendor (Opsional)</Typography>
                <Controller
                  control={control}
                  name='tenderNumber'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      placeholder='Nomor Dokumen'
                      label='Nomor Dokumen'
                      {...(!!error && { error: true, helperText: 'Wajib diisi' })}
                    />
                  )}
                />
                <div className='flex items-center gap-4'>
                  <TextField
                    key={JSON.stringify(filesContent)}
                    size='small'
                    fullWidth
                    value={filesContent?.[0]?.name ?? getValues('tenderDocumentName')}
                    placeholder='Tidak ada file dipilih'
                    aria-readonly
                    className='flex-1'
                  />
                  <Button variant='contained' onClick={() => openFilePicker()}>
                    Pilih File
                  </Button>
                </div>
              </div>
            </Grid>
          </>
        )}
        <Grid item xs={12} />
        <Grid item xs={12} md={6}>
          <Controller
            control={control}
            name='currencyId'
            rules={{ required: true }}
            render={({ field, fieldState: { error } }) => (
              <FormControl fullWidth>
                <InputLabel id='mata-uang-field'>Mata Uang</InputLabel>
                <Select
                  {...field}
                  fullWidth
                  label='Mata Uang'
                  placeholder='Mata Uang'
                  className='bg-white'
                  {...(!!error && { error: true })}
                >
                  {currenciesList?.map(curr => (
                    <MenuItem key={curr.id} value={curr.id}>
                      {curr.name}
                    </MenuItem>
                  ))}
                </Select>
                {!!error && <FormHelperText error>Wajib diisi</FormHelperText>}
              </FormControl>
            )}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Controller
            control={control}
            name='exchangeRate'
            render={({ field, fieldState: { error } }) => (
              <TextField
                {...field}
                disabled={!!currentCurrency?.isDefault}
                fullWidth
                aria-readonly
                label='Konversi ke Indonesia Rupiah'
                InputLabelProps={{
                  shrink: !!field.value
                }}
                InputProps={{
                  inputComponent: CurrencyField as any,
                  inputProps: {
                    prefix: 'Rp',
                    name: 'exchangeRate',
                    value: field.value,
                    allowLeadingZeros: false
                  }
                }}
                {...(!!error && { error: true, helperText: 'Wajib diisi' })}
              />
            )}
          />
        </Grid>
      </Grid>
    </div>
  )
}

export default VendorInfo
