import { CurrenciesType } from '@/types/currenciesTypes'
import { createContext, ReactNode, useContext, useState } from 'react'

type AddPurchaseContextProps = {
  currentCurrency: CurrenciesType | null
  setCurrentCurrency: React.Dispatch<React.SetStateAction<CurrenciesType | null>>
}

const AddPurchaseContext = createContext<AddPurchaseContextProps>({} as AddPurchaseContextProps)

export const useAddPurchase = () => {
  const context = useContext(AddPurchaseContext)
  if (context === undefined) {
    throw new Error('useAddPurchase must be used within a AddPurchaseProvider')
  }
  return context
}

export function AddPurchaseProvider({ children }: { children: ReactNode }) {
  const [currentCurrency, setCurrentCurrency] = useState<CurrenciesType | null>(null)
  const state = {
    currentCurrency,
    setCurrentCurrency
  }

  return <AddPurchaseContext.Provider value={state}>{children}</AddPurchaseContext.Provider>
}
