import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  IconButton,
  TextField,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Typography,
  Autocomplete,
  CircularProgress
} from '@mui/material'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { TypeOf, z } from 'zod'
import { AssetType, AssetMobilizationPayload } from '@/types/assetTypes'
import { useAuth } from '@/contexts/AuthContext'
import { useMobilizeAsset } from '@/api/services/asset-management/mutation'
import { useQuery } from '@tanstack/react-query'
import UserQueryMethods, { USER_LIST_QUERY_KEY } from '@/api/services/user/query'
import { UserType } from '@/types/userTypes'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { useState } from 'react'
import { toast } from 'react-toastify'

type AddMobilizationDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  assetData?: AssetType
  onSuccess?: () => void
}

const addMobilizationSchema = z.object({
  siteId: z.string().min(1, 'Site harus dipilih'),
  picId: z.string().optional(),
  changePic: z.boolean().default(false)
})

type AddMobilizationInput = TypeOf<typeof addMobilizationSchema>

const AddMobilizationDialog = ({ open, setOpen, assetData, onSuccess }: AddMobilizationDialogProps) => {
  const { allSites } = useAuth()
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null)

  const { control, handleSubmit, reset, watch, setValue } = useForm<AddMobilizationInput>({
    resolver: zodResolver(addMobilizationSchema),
    defaultValues: {
      siteId: '',
      picId: '',
      changePic: false
    }
  })

  const changePic = watch('changePic')
  const mobilizeMutation = useMobilizeAsset()

  // Fetch user list
  const { data: userList, isLoading: isLoadingUsers } = useQuery({
    queryKey: [USER_LIST_QUERY_KEY],
    queryFn: () => UserQueryMethods.getUserList({ limit: Number.MAX_SAFE_INTEGER }),
    placeholderData: defaultListData as ListResponse<UserType>,
    enabled: changePic
  })

  const onSubmitHandler: SubmitHandler<AddMobilizationInput> = async (inputValues: AddMobilizationInput) => {
    if (!assetData?.id) {
      toast.error('Asset data tidak ditemukan')
      return
    }

    const payload: AssetMobilizationPayload = {
      siteId: inputValues.siteId
    }

    if (inputValues.changePic && inputValues.picId) {
      payload.picId = inputValues.picId
    }

    try {
      await mobilizeMutation.mutateAsync({
        id: assetData.id,
        data: payload
      })

      toast.success('Mobilisasi aset berhasil dibuat')
      reset()
      setSelectedUser(null)
      setOpen(false)
      onSuccess?.()
    } catch (error) {
      toast.error('Gagal membuat mobilisasi aset')
      console.error('Mobilization error:', error)
    }
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    reset()
    setSelectedUser(null)
    setOpen(false)
  }

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Buat Mobilisasi
        <Typography variant='body2' color='textSecondary'>
          Pindahkan aset ini ke site atau PIC lain
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4 min-w-[500px]'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>

        <div className='flex flex-col gap-4'>
          {/* Site Selection */}
          <Controller
            control={control}
            name='siteId'
            render={({ field, fieldState: { error } }) => (
              <TextField
                {...field}
                select
                fullWidth
                label='Pindah ke Site'
                error={!!error}
                helperText={error?.message}
                placeholder='DKB'
              >
                {allSites
                  ?.filter(site => site.id !== assetData?.ownerSiteId)
                  ?.map(site => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
              </TextField>
            )}
          />

          {/* Change PIC Checkbox */}
          <Controller
            control={control}
            name='changePic'
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Checkbox
                    {...field}
                    checked={field.value}
                    onChange={e => {
                      field.onChange(e.target.checked)
                      if (!e.target.checked) {
                        setValue('picId', '')
                        setSelectedUser(null)
                      }
                    }}
                    color='primary'
                  />
                }
                label='Ubah PIC aset'
              />
            )}
          />

          {/* PIC Selection - Only show when changePic is true */}
          {changePic && (
            <Controller
              control={control}
              name='picId'
              render={({ field, fieldState: { error } }) => (
                <Autocomplete
                  {...field}
                  options={userList?.items?.filter(user => user.id !== assetData?.picId) || []}
                  getOptionLabel={(option: UserType) => option.fullName || ''}
                  isOptionEqualToValue={(option: UserType, value: UserType) => option.id === value.id}
                  onChange={(_, newValue) => {
                    setSelectedUser(newValue)
                    field.onChange(newValue?.id || '')
                  }}
                  value={userList?.items?.find(user => user.id === field.value) || null}
                  loading={isLoadingUsers}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='PIC Baru'
                      placeholder='Marin Calloway'
                      error={!!error}
                      helperText={error?.message}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isLoadingUsers ? <CircularProgress size={20} /> : null}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                  filterOptions={(options, { inputValue }) => {
                    return options.filter(
                      option =>
                        option?.fullName?.toLowerCase().includes(inputValue.toLowerCase()) ||
                        option?.email?.toLowerCase().includes(inputValue.toLowerCase())
                    )
                  }}
                />
              )}
            />
          )}
        </div>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={e => setOpen(false)} variant='outlined' className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <LoadingButton
          loading={mobilizeMutation.isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
          disabled={mobilizeMutation.isLoading}
        >
          BUAT MOBILISASI
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddMobilizationDialog
