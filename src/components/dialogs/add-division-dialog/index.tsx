import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useFieldArray, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { TypeOf, z } from 'zod'
import { useAuth } from '@/contexts/AuthContext'
import { DivisionType } from '@/types/accountTypes'
import { useAddDivision, useUpdateDivision } from '@/api/services/account/mutation'
import { DivisionPayload } from '@/types/payload'
import { useDivision } from '@/pages/company-data/division/context/DivisionContext'

type AddDivisionDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  data?: DivisionType
  readonly?: boolean
}

const divisionSchema = z.object({
  code: z.string({ message: 'Wajib diisi' }),
  name: z.string({ message: 'Wajib diisi' })
})

const addDivisionSchema = z.object({
  departmentId: z.string({ message: 'Wajib diisi' }),
  companyId: z.string({ message: 'Wajib diisi' }),
  divisions: z.array(divisionSchema).min(1)
})
type AddDivisionInput = Required<TypeOf<typeof addDivisionSchema>>

const AddDivisionDialog = ({ open, setOpen, data, readonly = false }: AddDivisionDialogProps) => {
  const { fetchDivisionList, fetchDivisionData, departmentList } = useDivision()
  const { control, handleSubmit, reset } = useForm<AddDivisionInput>({
    resolver: zodResolver(addDivisionSchema)
  })

  const {
    userProfile: { companyId }
  } = useAuth()

  const { fields, append, remove } = useFieldArray({ control, name: 'divisions' })

  const { mutate: addMutate, isLoading: addLoading } = useAddDivision()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateDivision()

  const isLoading = addLoading || updateLoading

  const onSubmitHandler: SubmitHandler<AddDivisionInput> = (inputValues: AddDivisionInput) => {
    if (data) {
      updateMutate(
        {
          code: inputValues.divisions[0].code,
          name: inputValues.divisions[0].name,
          id: data.id
        },
        {
          onSuccess: () => {
            toast.success('Data Proyek berhasil diubah')
            fetchDivisionList()
            fetchDivisionData()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          ...(inputValues as DivisionPayload)
        },
        {
          onSuccess: () => {
            toast.success('Data Proyek berhasil ditambahkan')
            fetchDivisionList()
            setOpen(false)
          }
        }
      )
    }
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  useEffect(() => {
    if (data) {
      reset({ ...data, divisions: [{ code: data.code, name: data.name }] })
    } else {
      reset({
        departmentId: null,
        companyId: companyId,
        divisions: [{ code: null, name: null }]
      })
    }
  }, [data])

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {readonly ? '' : data ? 'Ubah Divisi' : 'Tambah Divisi'}
        {readonly ? (
          <Typography component='span' className='flex flex-col text-center'>
            Lihat detil Divisi
          </Typography>
        ) : (
          !data && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan Divisi di perusahaan kamu
            </Typography>
          )
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          {readonly ? (
            <>
              {/* <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Nama</small>
                  <Typography>{projectData?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Kode Mata Uang</small>
                  <Typography>{projectData?.code}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Default Mata Uang</small>
                  <Typography color={projectData?.isDefault ? 'primary' : 'error'}>
                    {projectData?.isDefault ? 'Ya' : 'Tidak'}
                  </Typography>
                </div>
              </Grid> */}
            </>
          ) : (
            <>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='departmentId'
                  render={({ field, fieldState: { error } }) => (
                    <FormControl fullWidth>
                      <InputLabel required id='role-select'>
                        Departemen
                      </InputLabel>
                      <Select
                        {...field}
                        key={field.value}
                        fullWidth
                        required
                        label='Departemen'
                        variant='outlined'
                        disabled={isLoading}
                        {...(!!error && { error: true, helperText: error?.message })}
                      >
                        {departmentList?.map(department => (
                          <MenuItem key={department.id} value={department.id}>
                            {department.code} - {department.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-2'>
                  <Typography>Divisi</Typography>
                  {fields.map((_, idx) => (
                    <>
                      <div className='flex gap-2'>
                        <Controller
                          control={control}
                          name={`divisions.${idx}.code`}
                          render={({ field, fieldState: { error } }) => (
                            <TextField
                              {...field}
                              required
                              label='Kode Divisi'
                              {...(!!error && { error: true, helperText: error?.message })}
                            />
                          )}
                        />
                        <Controller
                          control={control}
                          name={`divisions.${idx}.name`}
                          render={({ field, fieldState: { error } }) => (
                            <TextField
                              {...field}
                              required
                              fullWidth
                              label='Nama Divisi'
                              {...(!!error && { error: true, helperText: error?.message })}
                            />
                          )}
                        />
                        {!data && fields.length > 1 && (
                          <IconButton onClick={() => remove(idx)} className='!text-red-500'>
                            <i className='ri-delete-bin-2-fill' />
                          </IconButton>
                        )}
                      </div>
                    </>
                  ))}
                  {!data && (
                    <Button variant='outlined' onClick={() => append({ code: null, name: null })}>
                      Tambah Divisi
                    </Button>
                  )}
                </div>
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        {readonly ? (
          <>
            {/* <Button onClick={() => handleDeleteRow(projectData.id)} variant='outlined' color='error'>
              Hapus
            </Button>
            <Button onClick={handleDetailEdit} variant='outlined'>
              Edit
            </Button> */}
          </>
        ) : (
          <>
            <Button
              onClick={() => setOpen(false)}
              variant='outlined'
              disabled={isLoading}
              className='is-full sm:is-auto'
            >
              BATALKAN
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={isLoading}
              loadingPosition='start'
              variant='contained'
              onClick={handleSubmit(onSubmitHandler, console.error)}
              className='px-8 is-full !ml-0 sm:is-auto'
            >
              {data ? 'UBAH DATA' : 'SIMPAN'}
            </LoadingButton>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default AddDivisionDialog
