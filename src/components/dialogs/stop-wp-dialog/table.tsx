import { UserType } from '@/types/userTypes'
import { Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'

const columnHelper = createColumnHelper<UserType>()

export const tableColumns = [
  columnHelper.display({
    id: 'number',
    size: 20,
    cell: ({ row }) => <Typography>{row.index + 1}</Typography>
  }),
  columnHelper.accessor('fullName', {
    header: 'Nama'
  }),
  columnHelper.accessor('title', {
    header: '<PERSON><PERSON><PERSON>'
  })
]
