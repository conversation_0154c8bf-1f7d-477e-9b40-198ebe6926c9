import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { string, TypeOf, z } from 'zod'
import { useAddCustomer, useUpdateCustomer } from '@/api/services/company/mutation'
import { CustomerType } from '@/types/customerTypes'
import { useCustomer } from '@/pages/accounting/customer/context/CustomerContext'
import { UserType } from '@/types/userTypes'
import { useStopTimerWp } from '@/api/services/wp/mutation'
import { useWp } from '@/pages/repair-and-maintenance/wp/context/WpContext'
import { formatTime } from '@/pages/repair-and-maintenance/wp/detail/config/utils'
import Table from '@/components/table'
import {
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  getCoreRowModel,
  getFacetedUniqueValues,
  useReactTable
} from '@tanstack/react-table'
import { tableColumns } from './table'

type StopWpDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  labors: UserType[]
  wpId: string
  timer: number
  callback?: () => void
}

const stopTimer = z.object({
  note: string({ message: 'Wajib diisi' })
})
type StopTimerInput = Required<TypeOf<typeof stopTimer>>

const StopWpTimerDialog = ({ open, setOpen, labors, timer, wpId, callback }: StopWpDialogProps) => {
  const { control, handleSubmit, reset } = useForm<StopTimerInput>({
    resolver: zodResolver(stopTimer)
  })
  const { refetchWpDetail } = useWp()

  const { mutate: updateMutate, isLoading: updateLoading } = useStopTimerWp()

  const isLoading = updateLoading

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onStopTimer = (dto: StopTimerInput) => {
    updateMutate(
      {
        workProcessId: wpId,
        note: dto.note
      },
      {
        onSuccess: () => {
          toast.success('Waktu berhenti')
          refetchWpDetail()
          callback?.()
          setOpen(false)
        }
      }
    )
  }

  const table = useReactTable({
    data: labors,
    columns: tableColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues()
  })

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Stop Timer
        <Typography component='span' className='flex flex-col text-center'>
          Stop Waktu Pengerjaan Work Process
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <div className='flex  flex-col bg-[#F5F5F7] rounded-[8px] p-[24px_16px]'>
              <small>Timer Pengerjaan</small>
              <Typography>{formatTime(timer)}</Typography>
            </div>
          </Grid>
          <Grid item xs={12}>
            <div className='flex flex-col gap-3'>
              <Typography variant='h6'>Labor</Typography>
              <div className='rounded-md shadow-md'>
                <Table
                  table={table}
                  disablePagination
                  stickyHeader
                  containerClassName='max-h-64 overflow-y-auto'
                  headerColor='green'
                />
              </div>
            </div>
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Catatan'
                  variant='outlined'
                  placeholder='Contoh: Jam Makan'
                  disabled={isLoading}
                  InputLabelProps={{ shrink: !!field.value }}
                  {...(!!error && { error: true, helperText: error?.message })}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onStopTimer)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          Stop Timer
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default StopWpTimerDialog
