// MUI Imports
import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import { IconButton, Typography } from '@mui/material'
import { useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { createColumnHelper, getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'

import { AssetType, AssetValueLogType } from '@/types/assetTypes'
import { ASSET_VALUE_LOGS_LIST_KEY } from '@/api/services/asset-management/services'
import AssetManagementQueryMethods from '@/api/services/asset-management/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ListParams } from '@/types/payload'
import Table from '@/components/table'
import { toCurrency } from '@/utils/helper'

type AssetValueLogsDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  assetData?: AssetType
}

// Create column helper for value logs table
const columnHelper = createColumnHelper<AssetValueLogType>()

// Define table columns based on Figma design
const valueLogsColumns = [
  columnHelper.accessor('type', {
    header: 'PERUBAHAN',
    cell: ({ row }) => (row.original.type === 'EDIT' ? 'Edit Aset' : 'End Period')
  }),
  columnHelper.accessor('createdAt', {
    header: 'TGL PERUBAHAN',
    cell: ({ row }) => {
      if (!row.original.createdAt) return '-'
      return format(new Date(row.original.createdAt), 'dd/MM/yyyy', { locale: id })
    }
  }),
  columnHelper.accessor('previousValue', {
    header: 'NILAI SEMULA',
    cell: ({ row }) => {
      const value = row.original.previousValue
      const currency = row.original.currency?.code || 'IDR'
      return value ? toCurrency(value, false, currency) : '-'
    }
  }),
  columnHelper.accessor('currentValue', {
    header: 'NILAI SAAT INI',
    cell: ({ row }) => {
      const value = row.original.currentValue
      const currency = row.original.currency?.code || 'IDR'
      return (
        <Typography color='primary' fontWeight='medium'>
          {value ? toCurrency(value, false, currency) : '-'}
        </Typography>
      )
    }
  }),
  columnHelper.display({
    id: 'percentage',
    header: '%',
    cell: ({ row }) => {
      const previous = row.original.previousValue || 0
      const current = row.original.currentValue || 0

      if (previous === 0) return '-'

      const percentage = ((current - previous) / previous) * 100
      const isPositive = percentage >= 0

      return (
        <Typography color={isPositive ? 'success.main' : 'error.main'} fontWeight='medium'>
          {isPositive ? '+' : ''}
          {percentage.toFixed(1)}%
        </Typography>
      )
    }
  })
]

const AssetValueLogsDialog = ({ open, setOpen, assetData }: AssetValueLogsDialogProps) => {
  const [valueLogsParams, setValueLogsParams] = useState<ListParams>({
    page: 1,
    limit: 10
  })

  // Fetch value logs data
  const { data: valueLogsResponse, isLoading } = useQuery({
    queryKey: [ASSET_VALUE_LOGS_LIST_KEY, assetData?.id, JSON.stringify(valueLogsParams)],
    queryFn: () => AssetManagementQueryMethods.getPaginatedValueLogs(assetData?.id!, valueLogsParams),
    placeholderData: defaultListData as ListResponse<AssetValueLogType>,
    enabled: !!assetData?.id && open
  })

  const valueLogsList = valueLogsResponse?.items || []
  const totalItems = valueLogsResponse?.totalItems || 0
  const totalPages = valueLogsResponse?.totalPages || 0

  // Setup table
  const table = useReactTable({
    data: valueLogsList,
    columns: valueLogsColumns,
    initialState: {
      pagination: {
        pageSize: valueLogsParams.limit || 10,
        pageIndex: (valueLogsParams.page || 1) - 1
      }
    },
    state: {
      pagination: {
        pageSize: valueLogsParams.limit || 10,
        pageIndex: (valueLogsParams.page || 1) - 1
      }
    },
    manualPagination: true,
    rowCount: totalItems,
    pageCount: totalPages,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel()
  })

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth='md' scroll='body'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Riwayat Perubahan
        <Typography variant='body2' color='textSecondary'>
          Lihat detil perubahan nilai aset ini
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 p-8'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>

        {/* Current Asset Value Display */}
        <div className='mb-6 bg-gray-100 p-4 rounded-lg'>
          <Typography variant='body2' color='textSecondary' className='mb-1'>
            Nilai Aset Saat Ini
          </Typography>
          <Typography variant='h5' color='primary' fontWeight='bold'>
            {toCurrency(assetData?.lcValue)}
          </Typography>
        </div>

        {/* Value Logs Table */}
        {isLoading ? (
          <div className='flex justify-center items-center p-8'>
            <Typography>Loading...</Typography>
          </div>
        ) : valueLogsList.length === 0 ? (
          <div className='flex flex-col items-center justify-center p-8'>
            <Typography variant='h6'>Belum ada riwayat perubahan</Typography>
            <Typography variant='body2' color='textSecondary'>
              Riwayat perubahan nilai aset akan ditampilkan di sini
            </Typography>
          </div>
        ) : (
          <Table
            table={table}
            headerColor='green'
            onPageChange={pageIndex => {
              setValueLogsParams(prev => ({ ...prev, page: pageIndex }))
            }}
            onRowsPerPageChange={pageSize => {
              setValueLogsParams(prev => ({ ...prev, limit: pageSize, page: 1 }))
            }}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}

export default AssetValueLogsDialog
