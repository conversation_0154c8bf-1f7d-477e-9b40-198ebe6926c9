import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  IconButton,
  Typography,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Checkbox,
  Button,
  CircularProgress,
  Box,
  Chip
} from '@mui/material'
import { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { id } from 'date-fns/locale'

import { PoType } from '@/pages/purchase-order/config/types'
import PoQueryMethods, { PO_LIST_QUERY_KEY } from '@/api/services/po/query'
import MgQueryMethods, { MG_IM_LIST_QUERY_KEY } from '@/api/services/mg/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { formatThousandSeparator } from '@/utils/helper'
import { PurchaseOrderStatus } from '@/pages/purchase-order/config/enum'
import { ImType } from '@/types/mgTypes'
import DebouncedInput from '@/components/DebounceInput'
import DateRangePicker from '@/components/DateRangePicker'

type AddPurchaseOrderDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit?: (poList?: PoType[], imList?: ImType[]) => void
  vendorId?: string
  isDownPayment?: boolean
  isGeneralPurchase?: boolean
}

const AddPurchaseOrderDialog = ({
  open,
  setOpen,
  onSubmit,
  vendorId,
  isDownPayment = false,
  isGeneralPurchase = false
}: AddPurchaseOrderDialogProps) => {
  const [selectedPOs, setSelectedPOs] = useState<PoType[]>([])
  const [selectedIMs, setSelectedIMs] = useState<ImType[]>([])
  const [searchQuery, setSearchQuery] = useState<string>('')
  const [dateRange, setDateRange] = useState<string[]>([])

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const { data: poListResponse = defaultListData as ListResponse<PoType>, isLoading: fetchPOsLoading } = useQuery({
    enabled: open && (!!vendorId || !!isGeneralPurchase) && isDownPayment,
    queryKey: [PO_LIST_QUERY_KEY, vendorId, searchQuery, JSON.stringify(dateRange), isGeneralPurchase, 'invoice'],
    queryFn: () => {
      return PoQueryMethods.getPoList({
        limit: Number.MAX_SAFE_INTEGER,
        status: PurchaseOrderStatus.APPROVED,
        vendorId,
        isGeneralPurchase,
        ...(searchQuery && { search: searchQuery }),
        ...(dateRange?.length > 1 && { startDate: dateRange[0], endDate: dateRange[1] })
      })
    },
    placeholderData: defaultListData as ListResponse<PoType>
  })

  const { data: imList = [], isLoading: fetchIMsLoading } = useQuery({
    enabled: open && (!!vendorId || !!isGeneralPurchase) && !isDownPayment,
    queryKey: [MG_IM_LIST_QUERY_KEY, vendorId, searchQuery, JSON.stringify(dateRange), isGeneralPurchase, 'invoice'],
    queryFn: () => {
      return MgQueryMethods.getImList({
        limit: Number.MAX_SAFE_INTEGER,
        vendorId,
        isGeneralPurchase,
        ...(searchQuery && { search: searchQuery }),
        ...(dateRange?.length > 1 && { startDate: dateRange[0], endDate: dateRange[1] })
      })
    },
    placeholderData: []
  })

  // Use appropriate data based on isDownPayment flag
  const filteredPOs = poListResponse.items || []
  const filteredIMs = imList?.filter(im => im.itemsCount > 0) || []
  const isLoading = !isDownPayment ? fetchIMsLoading : fetchPOsLoading

  const handlePOSelection = (po: PoType, checked: boolean) => {
    if (checked) {
      setSelectedPOs(prev => [...prev, po])
    } else {
      setSelectedPOs(prev => prev.filter(selectedPO => selectedPO.id !== po.id))
    }
  }

  const handleIMSelection = (im: ImType, checked: boolean) => {
    if (checked) {
      setSelectedIMs(prev => [...prev, im])
    } else {
      setSelectedIMs(prev => prev.filter(selectedIM => selectedIM.id !== im.id))
    }
  }

  const handleSubmit = () => {
    if (!isDownPayment) {
      onSubmit?.([], selectedIMs)
    } else {
      onSubmit?.(selectedPOs, [])
    }
    setOpen(false)
    setSelectedPOs([])
    setSelectedIMs([])
  }

  const handleSelectAll = (checked: boolean) => {
    if (!isDownPayment) {
      if (checked) {
        setSelectedIMs(filteredIMs)
      } else {
        setSelectedIMs([])
      }
    } else {
      if (checked) {
        setSelectedPOs(filteredPOs)
      } else {
        setSelectedPOs([])
      }
    }
  }

  const handleCancel = () => {
    setOpen(false)
    setSelectedPOs([])
    setSelectedIMs([])
    setSearchQuery('')
  }

  // Reset selection and search when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setSelectedPOs([])
      setSelectedIMs([])
      setSearchQuery('')
    }
  }, [open])

  // Calculate selection states based on current mode
  const currentList = !isDownPayment ? filteredIMs : filteredPOs
  const currentSelected = !isDownPayment ? selectedIMs : selectedPOs
  const isAllSelected = currentList.length > 0 && currentSelected.length === currentList.length
  const isIndeterminate = currentSelected.length > 0 && currentSelected.length < currentList.length

  return (
    <Dialog open={open} maxWidth='lg' onClose={handleClose} fullWidth>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Tambah {!isDownPayment ? 'Penerimaan Barang' : 'Purchase Order'}
        <Typography variant='body2' color='textSecondary'>
          Pilih {!isDownPayment ? 'penerimaan barang' : 'purchase order'} yang akan dimasukkan ke faktur ini
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={handleCancel} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>

        {/* Search Input */}
        <div className='flex gap-4 mt-4 mb-4'>
          <DebouncedInput
            value={searchQuery}
            onChange={value => setSearchQuery(value as string)}
            placeholder={`Cari Nomor PO...`}
            className='w-full flex-1'
          />
          <div className='flex-1'>
            <DateRangePicker
              inputSize='small'
              startDate={dateRange[0]}
              endDate={dateRange[1]}
              onChange={(start, end) => {
                setDateRange([start, end])
              }}
            />
          </div>
        </div>

        {isLoading ? (
          <Box display='flex' justifyContent='center' alignItems='center' minHeight='200px'>
            <CircularProgress />
          </Box>
        ) : (
          <div className='mt-4'>
            <Table>
              <TableHead>
                <TableRow className='bg-gray-50'>
                  <TableCell padding='checkbox'>
                    <Checkbox
                      checked={isAllSelected}
                      indeterminate={isIndeterminate}
                      onChange={e => handleSelectAll(e.target.checked)}
                    />
                  </TableCell>
                  {!isDownPayment && (
                    <TableCell>
                      <strong>NO. PENERIMAAN</strong>
                    </TableCell>
                  )}
                  <TableCell>
                    <strong>NO. PO</strong>
                  </TableCell>
                  {!isDownPayment && (
                    <TableCell>
                      <strong>STATUS</strong>
                    </TableCell>
                  )}
                  <TableCell>
                    <strong>{!isDownPayment ? 'NO. SJ/DO' : 'TOTAL HARGA'}</strong>
                  </TableCell>
                  <TableCell>
                    <strong>TGL DOKUMEN</strong>
                  </TableCell>
                  <TableCell>
                    <strong>{!isDownPayment ? 'TGL SHIPPING' : 'EST SHIPPING'}</strong>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {currentList.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align='center' className='py-8'>
                      <Typography color='textSecondary'>
                        {!vendorId
                          ? 'Pilih vendor terlebih dahulu'
                          : `Tidak ada ${!isDownPayment ? 'Penerimaan Barang' : 'Purchase Order'} yang tersedia untuk vendor ini`}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : !isDownPayment ? (
                  // Render IM rows
                  filteredIMs.map(im => {
                    const isSelected = selectedIMs.some(selectedIM => selectedIM.id === im.id)
                    return (
                      <TableRow key={im.id} hover>
                        <TableCell padding='checkbox'>
                          <Checkbox checked={isSelected} onChange={e => handleIMSelection(im, e.target.checked)} />
                        </TableCell>
                        <TableCell>
                          <Typography color='primary' className='font-medium'>
                            {im.number}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>{im.purchaseOrder?.number}</Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={im.unbilledQuantity > 0 ? 'Belum Tertagih' : 'Sudah Tertagih'}
                            color={im.unbilledQuantity > 0 ? 'warning' : 'success'}
                            size='small'
                            variant='tonal'
                          />
                        </TableCell>
                        <TableCell>
                          <Typography>{im.deliveryNoteNumber ?? '-'}</Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>{format(new Date(im.createdAt), 'dd/MM/yyyy', { locale: id })}</Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>
                            {im.shipDate ? format(new Date(im.shipDate), 'dd/MM/yyyy', { locale: id }) : '-'}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )
                  })
                ) : (
                  // Render PO rows
                  filteredPOs.map(po => {
                    const isSelected = selectedPOs.some(selectedPO => selectedPO.id === po.id)
                    return (
                      <TableRow key={po.id} hover>
                        <TableCell padding='checkbox'>
                          <Checkbox checked={isSelected} onChange={e => handlePOSelection(po, e.target.checked)} />
                        </TableCell>
                        <TableCell>
                          <Typography color='primary' className='font-medium'>
                            {po.number}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>Rp {formatThousandSeparator(po.grandTotal)}</Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>{format(new Date(po.createdAt), 'dd/MM/yyyy', { locale: id })}</Typography>
                        </TableCell>
                        <TableCell>
                          <Typography>
                            {po.estimatedDeliveryTime
                              ? format(new Date(po.estimatedDeliveryTime), 'dd/MM/yyyy', { locale: id })
                              : '-'}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    )
                  })
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button variant='outlined' color='secondary' onClick={handleCancel} className='is-full sm:is-auto'>
          BATALKAN
        </Button>
        <Button
          variant='contained'
          onClick={handleSubmit}
          disabled={currentSelected.length === 0}
          className='is-full sm:is-auto'
        >
          TAMBAHKAN
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default AddPurchaseOrderDialog
