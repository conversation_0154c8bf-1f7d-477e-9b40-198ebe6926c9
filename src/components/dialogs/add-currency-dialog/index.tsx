import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormControlLabel,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { TypeOf, z } from 'zod'
import { useCurrencies } from '@/pages/accounting/currency/context/CurrencyContext'
import { useAddCurrency, useUpdateCurrency } from '@/api/services/company/mutation'
import { CurrenciesType } from '@/types/currenciesTypes'

type AddCurrenciesDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  currenciesData?: CurrenciesType
  readonly?: boolean
}

const addCurrenciesSchema = z.object({
  code: z.string({ message: 'Wajib diisi' }),
  name: z.string({ message: 'Wajib diisi' }),
  symbol: z.string().optional().nullable(),
  isDefault: z.boolean().optional().nullable(),
  companyId: z.string().optional().nullable()
})
type AddCurrenciesInput = Required<TypeOf<typeof addCurrenciesSchema>>

const AddCurrenciesDialog = ({ open, setOpen, currenciesData, readonly = false }: AddCurrenciesDialogProps) => {
  const { handleDeleteRow, handleDetailEdit, fetchCurrenciesList } = useCurrencies()
  const { control, handleSubmit, reset } = useForm<AddCurrenciesInput>({
    resolver: zodResolver(addCurrenciesSchema)
  })

  const { mutate: addMutate, isLoading: addLoading } = useAddCurrency()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateCurrency()

  const isLoading = addLoading || updateLoading

  const onSubmitHandler: SubmitHandler<AddCurrenciesInput> = (inputValues: AddCurrenciesInput) => {
    if (currenciesData) {
      updateMutate(
        {
          ...inputValues,
          symbol: inputValues.code,
          isDefault: inputValues?.isDefault ?? false,
          id: currenciesData.id
        },
        {
          onSuccess: () => {
            toast.success('Data akun berhasil diubah')
            fetchCurrenciesList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          ...inputValues,
          isDefault: inputValues?.isDefault ?? false,
          symbol: inputValues.code
        },
        {
          onSuccess: () => {
            toast.success('Data akun berhasil ditambahkan')
            fetchCurrenciesList()
            setOpen(false)
          }
        }
      )
    }
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  useEffect(() => {
    if (currenciesData) {
      reset(currenciesData)
    } else {
      reset({
        code: '',
        name: '',
        isDefault: false,
        companyId: undefined
      })
    }
  }, [currenciesData])

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {readonly ? 'Syarat Pembayaran' : currenciesData ? 'Ubah Mata Uang' : 'Tambah Mata Uang'}
        {readonly ? (
          <Typography component='span' className='flex flex-col text-center'>
            Lihat detil syarat pembayaran
          </Typography>
        ) : (
          !currenciesData && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan mata uang untuk pencatatan transaksi
            </Typography>
          )
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          {readonly ? (
            <>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Nama</small>
                  <Typography>{currenciesData?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Kode Mata Uang</small>
                  <Typography>{currenciesData?.code}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Default Mata Uang</small>
                  <Typography color={currenciesData?.isDefault ? 'primary' : 'error'}>
                    {currenciesData?.isDefault ? 'Ya' : 'Tidak'}
                  </Typography>
                </div>
              </Grid>
            </>
          ) : (
            <>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='name'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      label='Nama'
                      placeholder='Contoh: Rupiah'
                      fullWidth
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='code'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Kode Mata Uang'
                      variant='outlined'
                      placeholder='Contoh: Rp'
                      disabled={isLoading}
                      InputLabelProps={{ shrink: !!field.value }}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='isDefault'
                  render={({ field }) => (
                    <FormControlLabel control={<Checkbox {...field} />} label='Jadikan default Mata Uang' />
                  )}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        {readonly ? (
          <>
            <Button onClick={() => handleDeleteRow(currenciesData.id)} variant='outlined' color='error'>
              Hapus
            </Button>
            <Button onClick={handleDetailEdit} variant='outlined'>
              Edit
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => setOpen(false)}
              variant='outlined'
              disabled={isLoading}
              className='is-full sm:is-auto'
            >
              BATALKAN
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={isLoading}
              loadingPosition='start'
              variant='contained'
              onClick={handleSubmit(onSubmitHandler, console.error)}
              className='px-8 is-full !ml-0 sm:is-auto'
            >
              {currenciesData ? 'UBAH DATA' : 'TAMBAHKAN'}
            </LoadingButton>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default AddCurrenciesDialog
