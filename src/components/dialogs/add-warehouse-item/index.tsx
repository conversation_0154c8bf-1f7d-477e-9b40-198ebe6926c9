// React Imports

// MUI Imports
import {
  Autocomplete,
  Checkbox,
  CircularProgress,
  debounce,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select
} from '@mui/material'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'

import { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, {
  ITEM_LIST_QUERY_KEY,
  ITEM_QUERY_KEY,
  UNIT_LIST_QUERY_KEY,
  UNIT_QUERY_KEY
} from '@/api/services/company/query'
import { defaultListData, queryClient } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { ImageItemType, ItemType, UnitType } from '@/types/companyTypes'
import { useKeyPressEvent, useUpdateEffect } from 'react-use'
import usePartialState from '@/core/hooks/usePartialState'
import { defaultImageList } from '../add-item-dialog'
import PhotoPicker from '@/components/PhotoPicker'
import LoadingButton from '@mui/lab/LoadingButton'
import { WarehouseItemType } from '@/types/appTypes'
import NumberField from '@/components/numeric/NumberField'
import { useAuth } from '@/contexts/AuthContext'

type AddWarehouseItemProps = {
  open: boolean
  setOpen: (open: boolean) => void
  currentItem?: WarehouseItemType
  onSubmit: (itemData: WarehouseItemType, isEnter?: boolean) => void
  onEnterCb?: () => void
  isLoading?: boolean
  canUpdateAll?: boolean
  siteId?: string
  withoutUnit?: boolean
  hasMaxQty?: boolean
  viewOnly?: boolean
  parentCodeStrict?: boolean
  editItemOnly?: boolean
}

const AddWarehouseItemDialog = ({
  open,
  setOpen,
  currentItem = {},
  onSubmit,
  isLoading,
  canUpdateAll = true,
  siteId,
  withoutUnit,
  hasMaxQty,
  viewOnly,
  parentCodeStrict = true,
  editItemOnly,
  onEnterCb = () => {}
}: AddWarehouseItemProps) => {
  const { itemId, unitId, images } = currentItem
  const [imageList, setImageList] = useState<ImageItemType[]>(images ? (images as ImageItemType[]) : defaultImageList)
  const [itemData, setPartialItemData] = usePartialState<typeof currentItem>(currentItem)
  const [selectedItem, setSelectedItem] = useState<ItemType | null>(itemId ? (currentItem.item as ItemType) : null)
  const [selectedUnit, setSelectedUnit] = useState<UnitType | null>(unitId ? (currentItem.unit as UnitType) : null)
  const [hasNoUnit, setHasNoUnit] = useState(withoutUnit)
  const [unitOptions, setUnitOptions] = useState<string[]>([])
  const [{ item: itemSearchQuery, unit: unitSearchQuery }, setSearchQuery] = useState({
    item: '',
    unit: ''
  })

  const { ownSiteList } = useAuth()

  const { data: itemDetail } = useQuery({
    enabled: !!itemId,
    queryKey: [ITEM_QUERY_KEY, itemId],
    queryFn: () => CompanyQueryMethods.getItem(itemId)
  })

  const { data: unitDetail } = useQuery({
    enabled: !!unitId,
    queryKey: [UNIT_QUERY_KEY, unitId],
    queryFn: () => CompanyQueryMethods.getUnit(unitId)
  })

  const {
    data: { items: itemList },
    isLoading: fetchItemsLoading,
    remove: removeItemList
  } = useQuery({
    enabled: !!itemSearchQuery,
    queryKey: [ITEM_LIST_QUERY_KEY, itemSearchQuery, itemSearchQuery, currentItem?.item?.parentCode],
    queryFn: () => {
      return CompanyQueryMethods.getItemList({
        ...(itemSearchQuery && { search: itemSearchQuery }),
        ...(currentItem?.item?.parentCode && parentCodeStrict && { parentCode: currentItem?.item?.parentCode })
      })
    },
    placeholderData: defaultListData as ListResponse<ItemType>
  })

  const {
    data: { items: unitList },
    isLoading: fetchUnitsLoading,
    remove: removeUnitList
  } = useQuery({
    enabled: !!unitSearchQuery,
    queryKey: [UNIT_LIST_QUERY_KEY, unitSearchQuery],
    queryFn: () => {
      return CompanyQueryMethods.getUnitList({
        ...(unitSearchQuery && { number: unitSearchQuery }),
        limit: 10000
      })
    },
    placeholderData: defaultListData as ListResponse<UnitType>
  })

  const handleClose = (_?: any, reason?: string) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onsubmit = (isEnter?: boolean) => {
    onSubmit(
      {
        ...itemData,
        largeUnitQuantity: selectedItem?.largeUnitQuantity ?? 0,
        quantity: Number(itemData.quantity ?? 0),
        images: imageList,
        item: selectedItem,
        unit: selectedUnit
      },
      isEnter
    )
    setTimeout(() => {
      onEnterCb()
    }, 10)
  }

  useUpdateEffect(() => {
    if (hasNoUnit) {
      setSelectedUnit(null)
      setPartialItemData('unitId', '')
    }
  }, [hasNoUnit])

  useEffect(() => {
    if (Object.values(currentItem).length > 0 && currentItem.images) {
      if (viewOnly || editItemOnly) {
        setImageList([
          ...currentItem.images
            .filter(image => !!image.content || !!image.url)
            .map(image => ({
              content: image.url || image.content,
              uploadId: image.uploadId,
              fileName: image.fileName ?? ''
            }))
        ])
      } else {
        setImageList([
          ...currentItem.images.map(image => ({
            content: image.url || image.content,
            uploadId: image.uploadId,
            fileName: image.fileName ?? ''
          })),
          ...(new Array(5 - (itemData.images?.length ?? 0)).fill({
            content: '',
            fileName: ''
          }) as ImageItemType[])
        ])
      }
    }
  }, [currentItem])

  useEffect(() => {
    if (itemDetail) {
      setSelectedItem(itemDetail)
    }
  }, [itemDetail])

  useEffect(() => {
    if (unitDetail) {
      setSelectedUnit(unitDetail)
    }
  }, [unitDetail])

  useEffect(() => {
    if (!open) {
      removeUnitList()
      removeItemList()
      queryClient.invalidateQueries([ITEM_LIST_QUERY_KEY, itemSearchQuery])
      queryClient.invalidateQueries([UNIT_LIST_QUERY_KEY, unitSearchQuery])
    }
  }, [open])

  useEffect(() => {
    if (selectedItem?.largeUnit && selectedItem?.smallUnit) {
      const units = [selectedItem.largeUnit, selectedItem.smallUnit]
      setUnitOptions(units)
      if (!itemData?.quantityUnit || !units.includes(itemData?.quantityUnit)) {
        setPartialItemData('quantityUnit', null)
      }
    } else {
      setUnitOptions([])
    }
  }, [selectedItem])

  useEffect(() => {
    if (Object.keys(currentItem).length === 0) {
      setSelectedItem(null)
      setSelectedUnit(null)
      setPartialItemData('quantity', 0)
    }
  }, [currentItem])

  useKeyPressEvent('Enter', () => {
    if (!(!itemData?.itemId || !itemData?.quantity || !itemData?.quantityUnit)) {
      onsubmit(true)
    }
  })

  const itemStock = !siteId
    ? selectedItem?.stock
    : selectedItem?.stocks?.filter(stock => stock.siteId === siteId)?.[0]?.stock ?? 0

  return (
    <Dialog fullWidth open={open} onClose={handleClose} maxWidth='md' scroll='body'>
      <DialogTitle variant='h4' className='flex gap-2 flex-col items-center sm:pbs-16 sm:pbe-6 sm:px-16'>
        <div className='max-sm:is-[80%] max-sm:text-center'>
          {itemId ? (viewOnly ? 'Detil Barang' : 'Ubah Barang') : 'Tambah Barang'}
        </div>
        <Typography component='span' className='flex flex-col text-center'>
          {!itemId ? 'Masukkan data barang yang akan ditambahkan ke dokumen' : ''}
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-10'>
        <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='flex flex-col max-h-[60vh] overflow-auto sm:px-16 max-sm:text-center max-sm:px-0'>
          <Typography variant='subtitle1' className='font-semibold' marginY={4}>
            Detil Barang
          </Typography>
          <Grid container spacing={5}>
            {!viewOnly && (
              <Grid item xs={12}>
                <Autocomplete
                  filterOptions={x => x}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setSearchQuery(current => ({ ...current, item: newValue }))
                    }
                  }, 700)}
                  options={itemList || []}
                  freeSolo
                  onChange={(e, newValue: ItemType) => {
                    if (!newValue) return
                    setSelectedItem(newValue)
                    setPartialItemData('itemId', newValue.id)
                    removeItemList()
                  }}
                  noOptionsText='Barang tidak ditemukan'
                  loading={fetchItemsLoading}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label=''
                      placeholder='Cari kode barang, kode eksternal, nama barang, atau merk barang'
                      variant='outlined'
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: <i className='ri-search-line text-textSecondary size-5 mx-2' />,
                        endAdornment: <>{fetchItemsLoading ? <CircularProgress /> : null}</>,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                    />
                  )}
                  getOptionLabel={() => ''}
                  renderOption={(props, option) => {
                    const { key, ...optionProps } = props
                    return (
                      <li key={key} {...optionProps}>
                        <Typography>
                          {option.number} | {option.name} | {option.brandName}
                        </Typography>
                      </li>
                    )
                  }}
                />
              </Grid>
            )}
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label='Kode Induk'
                variant='outlined'
                value={selectedItem?.parentCode}
                disabled
                InputLabelProps={{ shrink: !!selectedItem?.parentCode }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label='Kode Barang'
                variant='outlined'
                value={selectedItem?.number ?? ''}
                disabled
                InputLabelProps={{ shrink: !!selectedItem?.number }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label='Kode Eksternal'
                variant='outlined'
                value={selectedItem?.vendorNumber ?? ''}
                disabled
                InputLabelProps={{ shrink: !!selectedItem?.vendorNumber }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label='Nama Barang'
                variant='outlined'
                value={selectedItem?.name ?? ''}
                disabled
                InputLabelProps={{ shrink: !!selectedItem?.name }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Kategori Barang'
                variant='outlined'
                value={selectedItem?.category?.name ?? ''}
                disabled
                InputLabelProps={{ shrink: !!selectedItem?.category?.name }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label='Merk Barang'
                variant='outlined'
                value={selectedItem?.brandName ?? ''}
                disabled
                InputLabelProps={{ shrink: !!selectedItem?.brandName }}
              />
            </Grid>
            {!editItemOnly && (
              <>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label={`Stok ${ownSiteList?.find(site => site?.id === siteId)?.name ?? 'Sistem'}`}
                    variant='outlined'
                    value={selectedItem ? itemStock : null}
                    disabled
                    InputLabelProps={{ shrink: !!selectedItem }}
                    InputProps={{
                      endAdornment: selectedItem?.smallUnit
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    label='Quantity'
                    variant='outlined'
                    defaultValue={itemData?.quantity}
                    placeholder='Masukkkan Quantity'
                    disabled={viewOnly}
                    onChange={e => setPartialItemData('quantity', e.target.value)}
                    InputProps={{
                      inputComponent: NumberField as any,
                      inputProps: {
                        isAllowed: ({ floatValue }) =>
                          (hasMaxQty && (itemData?.remainingQuantity ?? 0) > 0
                            ? floatValue <= itemData?.remainingQuantity
                            : true) || floatValue === undefined
                      }
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <FormControl fullWidth>
                    <InputLabel id='country' disabled={viewOnly}>
                      Satuan
                    </InputLabel>
                    <Select
                      label='Satuan'
                      sx={{ textAlign: 'left' }}
                      placeholder='Pilih Satuan'
                      value={itemData?.quantityUnit}
                      disabled={viewOnly}
                      onChange={e => setPartialItemData('quantityUnit', e.target.value)}
                    >
                      {unitOptions.map(unit => (
                        <MenuItem key={unit} value={unit}>
                          {unit}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label={viewOnly ? 'Keterangan Barang' : 'Keterangan Barang (opsional)'}
                    variant='outlined'
                    placeholder='Masukkkan Keterangan Barang'
                    defaultValue={itemData?.note ?? (viewOnly ? '-' : '')}
                    disabled={viewOnly}
                    onChange={e => setPartialItemData('note', e.target.value)}
                  />
                </Grid>
              </>
            )}
          </Grid>
          {!withoutUnit && (
            <>
              <FormControlLabel
                control={
                  <Checkbox
                    disabled={!canUpdateAll && !viewOnly}
                    className='my-3'
                    onChange={e => setHasNoUnit(e.target.checked)}
                  />
                }
                label='Tanpa Unit'
              />
              {!hasNoUnit ? (
                <>
                  <Typography variant='subtitle1' className='font-semibold' marginY={4}>
                    Detil Unit
                  </Typography>
                  <Grid container spacing={5}>
                    <Grid item xs={12} sm={6}>
                      <Autocomplete
                        key={JSON.stringify(selectedUnit)}
                        filterOptions={x => x}
                        isOptionEqualToValue={(option, value) => option.id === value.id}
                        onInputChange={debounce((e, newValue, reason) => {
                          if (reason === 'input') {
                            setSearchQuery(current => ({ ...current, unit: newValue }))
                          }
                        }, 700)}
                        options={unitList || []}
                        freeSolo
                        disabled={!canUpdateAll && !viewOnly}
                        onChange={(e, newValue: UnitType) => {
                          setSelectedUnit(newValue)
                          setPartialItemData('unitKm', newValue?.km ?? 0)
                          setPartialItemData('unitHm', newValue?.hm ?? 0)
                          setPartialItemData('unitId', newValue.id)
                          removeUnitList()
                        }}
                        value={selectedUnit}
                        noOptionsText='Unit tidak ditemukan'
                        loading={fetchUnitsLoading}
                        renderInput={params => (
                          <TextField
                            {...params}
                            label='Kode Unit'
                            placeholder='Masukkan kode unit'
                            variant='outlined'
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: <>{fetchUnitsLoading ? <CircularProgress /> : null}</>,
                              onKeyDown: e => {
                                if (e.key === 'Enter') {
                                  e.stopPropagation()
                                }
                              }
                            }}
                          />
                        )}
                        getOptionLabel={(option: UnitType) => option?.number}
                        renderOption={(props, option) => {
                          const { key, ...optionProps } = props
                          return (
                            <li key={key} {...optionProps}>
                              <Typography>
                                {option.number} | {option.brandName} | {option.type}
                              </Typography>
                            </li>
                          )
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Kategori Unit'
                        variant='outlined'
                        value={selectedUnit?.category?.name ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.category?.name }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Jenis Unit'
                        variant='outlined'
                        value={selectedUnit?.subCategory?.name ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.subCategory?.name }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Merk Unit'
                        variant='outlined'
                        value={selectedUnit?.brandName ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.brandName }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Tipe Unit'
                        variant='outlined'
                        value={selectedUnit?.type ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.type }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='Nomor Lambung'
                        variant='outlined'
                        value={selectedUnit?.hullNumber ?? ''}
                        disabled
                        InputLabelProps={{ shrink: !!selectedUnit?.hullNumber }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='KM'
                        variant='outlined'
                        value={selectedUnit ? itemData?.unitKm ?? '' : null}
                        disabled={!selectedUnit || (!canUpdateAll && !viewOnly)}
                        onChange={e => setPartialItemData('unitKm', Number(e.target.value))}
                        InputProps={{
                          endAdornment: 'km',
                          inputComponent: NumberField as any,
                          inputProps: {
                            isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined,
                            decimalScale: 1
                          }
                        }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        label='HM'
                        variant='outlined'
                        value={selectedUnit ? itemData?.unitHm ?? '' : null}
                        disabled={!selectedUnit || (!canUpdateAll && !viewOnly)}
                        onChange={e => setPartialItemData('unitHm', Number(e.target.value))}
                        InputProps={{
                          endAdornment: 'jam',
                          inputComponent: NumberField as any,
                          inputProps: {
                            isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined,
                            decimalScale: 1
                          }
                        }}
                      />
                    </Grid>
                  </Grid>
                </>
              ) : null}
            </>
          )}
          {!!currentItem.images && imageList?.length > 0 && (
            <>
              <Typography variant='subtitle1' className='font-semibold' marginBottom={2} marginTop={4}>
                {viewOnly || editItemOnly ? 'Foto Barang' : 'Foto Barang (Opsional)'}
              </Typography>
              <div>
                <div className='flex gap-5 max-sm:px-2 overflow-y-hidden'>
                  {imageList?.map((item, index) => (
                    <PhotoPicker
                      key={`${item.content}_${index}`}
                      content={item.content}
                      onPicked={(content, fileName) => {
                        setImageList(current => {
                          const tempCurrent = [...current]
                          tempCurrent[index] = { content, fileName }
                          return tempCurrent
                        })
                      }}
                      onRemoved={() => {
                        setImageList(current => {
                          const tempCurrent = [...current]
                          tempCurrent[index] = { content: '', fileName: '' }
                          return tempCurrent
                        })
                      }}
                      isPreview={viewOnly || editItemOnly}
                      disabled={viewOnly || editItemOnly}
                    />
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      </DialogContent>
      <DialogActions className='justify-center pbs-0 sm:pbe-10 sm:px-16 flex-col sm:flex-row is-full sm:is-auto gap-2 sm:gap-6'>
        <Button variant='outlined' color='secondary' type='reset' onClick={handleClose} className='is-full sm:is-auto'>
          {viewOnly ? 'TUTUP' : 'BATAL'}
        </Button>
        {!viewOnly && (
          <LoadingButton
            startIcon={<></>}
            loading={isLoading}
            loadingPosition='start'
            variant='contained'
            onClick={() => onsubmit(false)}
            disabled={!itemData?.itemId || !itemData?.quantity || !itemData?.quantityUnit}
            className='px-8 is-full !ml-0 sm:is-auto'
          >
            {itemId ? 'UBAH BARANG' : 'TAMBAH BARANG'}
          </LoadingButton>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default AddWarehouseItemDialog
