import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Checkbox,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormControlLabel,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { TypeOf, z } from 'zod'
import { useCurrencies } from '@/pages/accounting/currency/context/CurrencyContext'
import { useAddCarrier, useAddCurrency, useUpdateCarrier, useUpdateCurrency } from '@/api/services/company/mutation'
import { CurrenciesType } from '@/types/currenciesTypes'
import { CarrierStatus, CarrierType } from '@/types/companyTypes'
import { useCarrier } from '@/pages/accounting/carrier/context/CarrierContext'
import { getStatusConfig } from '@/pages/accounting/carrier/config/utils'

type AddCarriersDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  carrierData?: CarrierType
  readonly?: boolean
}

const addCarrierSchema = z.object({
  name: z.string({ message: 'Wajib diisi' }),
  status: z.string({ message: 'Wajib diisi' }),
  companyId: z.string().optional().nullable()
})
type AddCarrierInput = Required<TypeOf<typeof addCarrierSchema>>

const AddCarrierDialog = ({ open, setOpen, carrierData, readonly = false }: AddCarriersDialogProps) => {
  const { handleDeleteRow, handleDetailEdit, fetchCarrierList } = useCarrier()
  const { control, handleSubmit, reset, getValues } = useForm<AddCarrierInput>({
    resolver: zodResolver(addCarrierSchema)
  })

  const { mutate: addMutate, isLoading: addLoading } = useAddCarrier()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateCarrier()

  const isLoading = addLoading || updateLoading

  const onSubmitHandler: SubmitHandler<AddCarrierInput> = (inputValues: AddCarrierInput) => {
    if (carrierData) {
      updateMutate(
        {
          ...inputValues,
          status: inputValues.status as CarrierStatus,
          id: carrierData.id
        },
        {
          onSuccess: () => {
            toast.success('Data akun berhasil diubah')
            fetchCarrierList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          ...inputValues,
          status: inputValues.status as CarrierStatus
        },
        {
          onSuccess: () => {
            toast.success('Data akun berhasil ditambahkan')
            fetchCarrierList()
            setOpen(false)
          }
        }
      )
    }
  }

  const toggleStatus = () => {
    updateMutate(
      {
        ...getValues(),
        id: carrierData.id,
        status: carrierData.status === CarrierStatus.ACTIVE ? CarrierStatus.INACTIVE : CarrierStatus.ACTIVE
      },
      {
        onSuccess: () => {
          toast.success('Status berhasil diubah')
          fetchCarrierList()
          setOpen(false)
        }
      }
    )
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  useEffect(() => {
    if (carrierData) {
      reset(carrierData)
    } else {
      reset({
        name: '',
        status: CarrierStatus.ACTIVE,
        companyId: undefined
      })
    }
  }, [carrierData])

  return (
    <Dialog open={open} maxWidth='md' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {readonly ? 'Pengiriman' : carrierData ? 'Ubah Pengiriman' : 'Tambah Pengiriman'}
        {readonly ? (
          <Typography component='span' className='flex flex-col text-center'>
            Lihat detil jasa pengiriman
          </Typography>
        ) : (
          !carrierData && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan detil jasa pengiriman
            </Typography>
          )
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4 min-w-[600px]'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          {readonly ? (
            <>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Nama</small>
                  <Typography>{carrierData?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Status</small>
                  <div className='flex justify-between items-center'>
                    <Chip
                      label={getStatusConfig[carrierData?.status].label}
                      color={getStatusConfig[carrierData?.status].color as any}
                      variant='tonal'
                      size='small'
                    />
                    <Button
                      size='small'
                      variant='outlined'
                      color={carrierData?.status === CarrierStatus.ACTIVE ? 'error' : 'success'}
                      onClick={toggleStatus}
                    >
                      {carrierData?.status === CarrierStatus.ACTIVE ? 'Nonaktifkan' : 'Aktifkan'}
                    </Button>
                  </div>
                </div>
              </Grid>
            </>
          ) : (
            <>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='name'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      label='Nama'
                      placeholder='Contoh: JNE'
                      fullWidth
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='status'
                  render={({ field, fieldState: { error } }) => {
                    return (
                      <FormControlLabel
                        control={<Checkbox {...field} checked={field.value === CarrierStatus.ACTIVE} />}
                        label='Aktifkan Status'
                      />
                    )
                  }}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        {readonly ? (
          <>
            <Button onClick={() => handleDeleteRow(carrierData.id)} variant='outlined' color='error'>
              Hapus
            </Button>
            <Button onClick={handleDetailEdit} variant='outlined'>
              Edit
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => setOpen(false)}
              variant='outlined'
              disabled={isLoading}
              className='is-full sm:is-auto'
            >
              BATALKAN
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={isLoading}
              loadingPosition='start'
              variant='contained'
              onClick={handleSubmit(onSubmitHandler, console.error)}
              className='px-8 is-full !ml-0 sm:is-auto'
            >
              {carrierData ? 'UBAH DATA' : 'TAMBAHKAN'}
            </LoadingButton>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default AddCarrierDialog
