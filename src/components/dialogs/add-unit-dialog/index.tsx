// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { Autocomplete, Grid } from '@mui/material'
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form'

import { CategoryType } from '@/types/companyTypes'
import { object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import { useAddUnit, useUpdateUnit } from '@/api/services/company/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useUnit } from '@/pages/company-data/unit/context/UnitContext'
import CompanyQueryMethods, { CATEGORY_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'

type AddUnitDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
}

const addUnitSchema = object({
  number: string({ message: 'Wajib diisi' }),
  brandName: string({ message: 'Wajib diisi' }),
  type: string({ message: 'Wajib diisi' }),
  hullNumber: string({ message: 'Wajib diisi' }),
  categoryId: string({ message: 'Wajib dipilih' }),
  subCategoryId: string({ message: 'Wajib dipilih' })
})

type AddUnitInput = Required<TypeOf<typeof addUnitSchema>>

const AddUnitDialog = ({ open, setOpen }: AddUnitDialogProps) => {
  const { unitData, categoryList, fetchUnitList, fetchUnitData } = useUnit()
  const { control, handleSubmit } = useForm<AddUnitInput>({
    resolver: zodResolver(addUnitSchema),
    defaultValues: {
      number: unitData?.number,
      brandName: unitData?.brandName,
      type: unitData?.type,
      hullNumber: unitData?.hullNumber,
      categoryId: unitData?.categoryId,
      subCategoryId: unitData?.subCategoryId
    }
  })

  const categoryIdWatch = useWatch({
    control,
    name: 'categoryId',
    defaultValue: unitData?.categoryId ?? ''
  })

  const { data: subCategoryList } = useQuery({
    enabled: !!categoryIdWatch,
    queryKey: [CATEGORY_LIST_QUERY_KEY, 'UNIT', categoryIdWatch],
    queryFn: () => CompanyQueryMethods.getCategoryList({ limit: 1000, type: 'UNIT', parentId: categoryIdWatch }),
    placeholderData: []
  })

  const { mutate: addMutate, isLoading: addLoading } = useAddUnit()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateUnit()

  const isLoading = addLoading || updateLoading

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<AddUnitInput> = (inputValues: AddUnitInput) => {
    if (unitData) {
      updateMutate(
        {
          unitId: unitData.id,
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Data unit berhasil diubah')
            fetchUnitData()
            fetchUnitList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(inputValues, {
        onSuccess: () => {
          toast.success('Data unit berhasil ditambahkan')
          fetchUnitList()
          setOpen(false)
        }
      })
    }
  }

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {unitData ? 'Ubah' : 'Tambah'} Unit
        {!unitData && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan unit untuk didaftarkan ke list
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-16 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={6}>
            <Controller
              name='categoryId'
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange }, formState: { errors } }) => (
                <Autocomplete
                  key={JSON.stringify(categoryList)}
                  selectOnFocus
                  clearOnBlur
                  handleHomeEndKeys
                  freeSolo
                  value={categoryList?.find(category => category.id === value) || null}
                  getOptionLabel={option => (option as CategoryType).name}
                  options={categoryList}
                  disabled={isLoading}
                  onChange={(e, newValue) => {
                    if (newValue) {
                      onChange((newValue as CategoryType).id)
                    } else {
                      onChange(null)
                    }
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      required
                      label='Kategori Unit'
                      {...(errors.categoryId && { error: true, helperText: errors.categoryId?.message })}
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='number'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Unit'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Unit'
                  disabled={isLoading}
                  {...(errors.number && { error: true, helperText: errors.number?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='subCategoryId'
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange }, formState: { errors } }) => (
                <Autocomplete
                  key={JSON.stringify(subCategoryList)}
                  selectOnFocus
                  clearOnBlur
                  handleHomeEndKeys
                  freeSolo
                  value={subCategoryList?.find(category => category.id === value) || null}
                  getOptionLabel={option => (option as CategoryType).name}
                  options={subCategoryList}
                  disabled={isLoading}
                  onChange={(e, newValue) => {
                    if (newValue) {
                      onChange((newValue as CategoryType).id)
                    } else {
                      onChange(null)
                    }
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      required
                      label='Jenis Unit'
                      {...(errors.subCategoryId && { error: true, helperText: errors.subCategoryId?.message })}
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='brandName'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Merk Unit'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Merk Unit'
                  disabled={isLoading}
                  {...(errors.brandName && { error: true, helperText: errors.brandName?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='type'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Tipe Unit'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Tipe Unit'
                  disabled={isLoading}
                  {...(errors.type && { error: true, helperText: errors.type?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='hullNumber'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nomor Lambung'
                  variant='outlined'
                  disabled={isLoading}
                  required
                  placeholder='Masukkkan Nomor Lambung Unit'
                  {...(errors.hullNumber && { error: true, helperText: errors.hullNumber?.message })}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {unitData ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddUnitDialog
