// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { Autocomplete, Grid, InputLabel } from '@mui/material'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'

import { CategoryType, VendorAdressesType } from '@/types/companyTypes'
import { object, string, TypeOf, boolean } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import * as Sentry from '@sentry/react'
import {
  useAddVendor,
  useAddVendorAdresses,
  useUpdateVendor,
  useUpdateVendorAddress
} from '@/api/services/company/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useVendor } from '@/pages/company-data/vendor/context/VendorContext'
import { VendorAddressPayload } from '@/types/payload'
import { useEffect } from 'react'

type AddVendorAddressDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  vendorAdress?: VendorAdressesType
}

const addVendorAddressSchema = object({
  address: string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
  phoneNumber: string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
  faxNumber: string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
  picName: string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
  picPhoneNumber: string({ message: 'Wajib diisi' }).min(1, { message: 'Wajib diisi' }),
  isDefault: boolean()
})

type AddVendorInput = Required<TypeOf<typeof addVendorAddressSchema>>

const AddVendorAddress = ({ open, setOpen, vendorAdress = null }: AddVendorAddressDialogProps) => {
  const { selectedVendorId, fetchVendorData, vendorData } = useVendor()
  const { control, handleSubmit, reset, getValues } = useForm<VendorAddressPayload>({
    resolver: zodResolver(addVendorAddressSchema),
    defaultValues: {
      address: '',
      faxNumber: '',
      isDefault: false,
      phoneNumber: '',
      picName: '',
      picPhoneNumber: ''
    }
  })

  const { mutate: addMutate, isLoading: addLoading } = useAddVendorAdresses()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateVendorAddress()

  const isLoading = addLoading || updateLoading

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<AddVendorInput> = (inputValues: AddVendorInput) => {
    if (vendorAdress) {
      updateMutate(
        {
          vendorId: selectedVendorId,
          addressId: vendorAdress.id,
          ...inputValues
        },
        {
          onSuccess: () => {
            toast.success('Alamat vendor berhasil diubah')
            fetchVendorData()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        { vendorId: selectedVendorId, ...inputValues },
        {
          onSuccess: () => {
            toast.success('Alamat vendor berhasil ditambahkan')
            fetchVendorData()
            setOpen(false)
          }
        }
      )
    }
  }

  useEffect(() => {
    if (vendorAdress) {
      reset({
        ...getValues(),
        faxNumber: vendorAdress.faxNumber,
        phoneNumber: vendorAdress.phoneNumber,
        address: vendorAdress.address,
        picName: vendorAdress.picName,
        picPhoneNumber: vendorAdress.picPhoneNumber
      })
    }
  }, [vendorAdress])

  return (
    <Dialog PaperProps={{ className: 'rounded-t-[16px] md:rounded-t-none' }} open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {vendorAdress ? 'Ubah' : 'Tambah'} Alamat Vendor
        {!vendorAdress && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan alamat baru untuk vendor ini
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-16 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name='address'
              control={control}
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Alamat Vendor'
                  multiline
                  rows={3}
                  required
                  variant='outlined'
                  disabled={isLoading}
                  {...(!!error && { error: true, helperText: error.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            {vendorAdress ? (
              <div className='flex flex-col gap-2'>
                <InputLabel>Nomor Telepon Vendor</InputLabel>
                <Typography>{vendorAdress.phoneNumber ?? '-'}</Typography>
              </div>
            ) : (
              <Controller
                control={control}
                name='phoneNumber'
                rules={{ required: true }}
                render={({ field, fieldState }) => (
                  <TextField
                    label='Nomor Telepon vendor'
                    required
                    {...field}
                    {...(!!fieldState.error && { error: true, helperText: fieldState.error.message })}
                  />
                )}
              />
            )}
          </Grid>
          <Grid item xs={12} md={6}>
            {vendorAdress ? (
              <div className='flex flex-col gap-2'>
                <InputLabel>Nomor Fax Vendor</InputLabel>
                <Typography>{vendorAdress.faxNumber ?? '-'}</Typography>
              </div>
            ) : (
              <Controller
                control={control}
                name='faxNumber'
                render={({ field, fieldState }) => (
                  <TextField
                    label='Nomor Fax vendor'
                    required
                    {...field}
                    {...(!!fieldState.error && { error: true, helperText: fieldState.error.message })}
                  />
                )}
              />
            )}
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='picName'
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange }, fieldState: { error } }) => (
                <TextField
                  label='Nama PIC'
                  required
                  value={value}
                  onChange={onChange}
                  {...(!!error && { error: true, helperText: error.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Controller
              name='picPhoneNumber'
              control={control}
              rules={{ required: true }}
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nomor Telepon PIC'
                  required
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Nomor Telepon PIC'
                  {...(!!error && { error: true, helperText: error.message })}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler, errors => {
            console.error(errors)
            Sentry.captureException(errors)
            Object.entries(errors).forEach(([field, error]) => {
              toast.error(`${field}: ${error?.message}`, {
                autoClose: 5000
              })
            })
          })}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {vendorAdress ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddVendorAddress
