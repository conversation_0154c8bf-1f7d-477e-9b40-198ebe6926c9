// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import { Autocomplete, Grid } from '@mui/material'
import { Controller, SubmitHandler, useForm, useWatch } from 'react-hook-form'

import { CategoryType } from '@/types/companyTypes'
import { object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'react-toastify'
import { useAddUnit, useUpdateUnit } from '@/api/services/company/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { useUnit } from '@/pages/company-data/unit/context/UnitContext'
import CompanyQueryMethods, { CATEGORY_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'
import { AssetDtoType, AssetType } from '@/types/assetTypes'
import { useCreateAsset, useUpdateAsset } from '@/api/services/asset-management/mutation'

type AddKodeActiva = {
  open: boolean
  setOpen: (open: boolean) => void
  onSuccesfullCb?: () => void
  data?: AssetType
}

const addCodeActivaSchema = object({
  code: string({ message: 'Wajib diisi' }),
  name: string({ message: 'Wajib diisi' }),
  note: string().optional().nullable()
})

const AddKodeActiva = ({ open, setOpen, ...props }: AddKodeActiva) => {
  const { control, handleSubmit, reset } = useForm<AssetDtoType>({
    resolver: zodResolver(addCodeActivaSchema)
  })

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const { mutate: createMutate, isLoading: createLoading } = useCreateAsset()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdateAsset()

  const isLoading = createLoading || updateLoading

  const onSubmitHandler: SubmitHandler<AssetDtoType> = (inputValues: AssetDtoType) => {
    if (!!props.data) {
      updateMutate(
        { id: props.data.id, ...inputValues },
        {
          onSuccess: () => {
            toast.success('Data kode aktiva berhasil diubah')
            handleClose({}, '' as any)
            props?.onSuccesfullCb?.()
          }
        }
      )
    } else {
      createMutate(inputValues, {
        onSuccess: () => {
          toast.success('Data kode aktiva berhasil ditambahkan')
          handleClose({}, '' as any)
          props?.onSuccesfullCb?.()
        }
      })
    }
  }

  useEffect(() => {
    if (!!props.data) {
      reset(props.data)
    } else {
      reset({ code: '', name: '', note: '' })
    }
  }, [props.data])

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {props.data ? 'Ubah' : 'Tambah'} Kode Activa
        {!props.data && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan kode aktiva untuk aset perusahaan kamu
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-16 sm:px-12'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='code'
              render={({ field, fieldState: { error } }) => (
                <TextField {...field} ref={field.ref} error={!!error} fullWidth label='Kode Activa' />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='name'
              render={({ field, fieldState: { error } }) => (
                <TextField {...field} ref={field.ref} error={!!error} fullWidth label='Nama' />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field, fieldState: { error } }) => (
                <TextField {...field} ref={field.ref} error={!!error} fullWidth label='Keterangan (Opsional)' />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} disabled={isLoading} variant='outlined' className='is-full sm:is-auto'>
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {props.data ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddKodeActiva
