import { useMemo } from 'react'
import Table from '@/components/table'
import { Dialog, DialogContent, DialogTitle, IconButton, Typography } from '@mui/material'
import { documentColumns } from './config'
import { useReactTable } from '@tanstack/react-table'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'

type DetailHistoryUnitDocProps = {
  open: boolean
  setOpen: (open: boolean) => void
  title?: string
  type: string
}

const DetailHistoryUnitDoc = (props: DetailHistoryUnitDocProps) => {
  const { open, setOpen, title, type } = props

  const handleClose = () => {
    setOpen(false)
  }

  const tableOptions = useMemo(
    () => ({
      data: [],
      columns: documentColumns(type),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    []
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Dialog fullWidth maxWidth='md' scroll='body' open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-16'>
        Riwayat {type}
        <Typography component='span' className='flex flex-col text-center'>
          Lihat riwayat perubahan data {type} untuk Unit ini
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 sm:pbe-6 sm:px-16'>
        <IconButton onClick={handleClose} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>

        <div className='shadow-sm rounded-md'>
          <Table
            headerColor='green'
            table={table}
            emptyLabel={
              <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60 space-y-2'>
                <Typography> Belum ada Dokumen</Typography>
                <Typography className='text-sm text-gray-400'>
                  Semua dokumen yang telah kamu buat akan ditampilkan di sini
                </Typography>
              </td>
            }
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default DetailHistoryUnitDoc
