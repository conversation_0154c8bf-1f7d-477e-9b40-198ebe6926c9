import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

type DocumentType = {
  number: string
  startAt: string
  expiredAt: string
  updatedAt: string
  id: string
}

const documentColumnHelper = createColumnHelper<DocumentType>()

export const documentColumns = (type: string) => [
  documentColumnHelper.accessor('number', {
    header: `Dokumen ${type}`
  }),
  documentColumnHelper.accessor('startAt', {
    header: 'Tanggal Mulai Berlaku',
    cell: ({ row }) => formatDate(new Date(row.original.startAt), 'dd/MM/yyyy', { locale: id })
  }),
  documentColumnHelper.accessor('expiredAt', {
    header: 'Tanggal Habis Berlaku',
    cell: ({ row }) => formatDate(new Date(row.original.expiredAt), 'dd/MM/yyyy', { locale: id })
  }),
  documentColumnHelper.accessor('updatedAt', {
    header: '<PERSON><PERSON> Diperbarui',
    cell: ({ row }) => formatDate(new Date(row.original.updatedAt), 'dd/MM/yyyy', { locale: id })
  })
]
