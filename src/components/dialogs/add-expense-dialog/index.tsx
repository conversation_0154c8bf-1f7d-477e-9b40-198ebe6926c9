import { zodResolver } from '@hookform/resolvers/zod'
import {
  Autocomplete,
  Button,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useState } from 'react'
import { Controller, useForm } from 'react-hook-form'
import { TypeOf, z } from 'zod'
import { useQuery } from '@tanstack/react-query'
import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import { AccountType } from '@/types/accountTypes'
import { PurchaseInvoiceExpense } from '@/types/purchaseInvoiceTypes'
import Currency<PERSON>ield from '@/components/numeric/CurrencyField'

type AddExpenseDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  onSubmit?: (expense: PurchaseInvoiceExpense) => void
}

const addExpenseSchema = z.object({
  amount: z.number({ message: 'Wajib diisi' }),
  accountId: z.string({ message: 'Wajib diisi' }),
  account: z
    .object({
      code: z.string({ message: 'Wajib diisi' }),
      name: z.string({ message: 'Wajib diisi' }),
      id: z.string({ message: 'Wajib diisi' })
    })
    .optional()
    .nullable(),
  note: z.string({ message: 'Wajib diisi' })
})

type AddExpenseInput = Required<TypeOf<typeof addExpenseSchema>>

const AddExpenseDialog = ({ open, setOpen, onSubmit }: AddExpenseDialogProps) => {
  const [accountQuery, setAccountQuery] = useState<string>('')

  const { control, handleSubmit, setValue } = useForm<AddExpenseInput>({
    resolver: zodResolver(addExpenseSchema)
  })

  const { data: accountTypelist, remove: removeAccountTypeList } = useQuery({
    enabled: !!accountQuery,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, accountQuery],
    queryFn: async () =>
      (
        await AccountsQueryMethods.getAccountList({
          page: 1,
          limit: Number.MAX_SAFE_INTEGER,
          search: accountQuery
        })
      ).items,
    placeholderData: []
  })

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth='xl'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Tambah Biaya Lain-Lain
        <Typography component='span' className='flex flex-col text-center'>
          Tambahkan kode perkiraan untuk faktur ini
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-6 w-[600px]'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container gap={3}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='accountId'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <Autocomplete
                  key={JSON.stringify(value)}
                  selectOnFocus
                  clearOnBlur
                  handleHomeEndKeys
                  freeSolo
                  value={accountTypelist?.find(acc => acc.id === value) || null}
                  getOptionLabel={option => (option as AccountType).name}
                  options={accountTypelist}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setAccountQuery(newValue)
                    }
                  }, 700)}
                  onChange={(e, newValue) => {
                    if (newValue) {
                      removeAccountTypeList()
                      setValue('account', {
                        code: (newValue as AccountType).code,
                        name: (newValue as AccountType).name,
                        id: (newValue as AccountType).id
                      })
                      onChange((newValue as AccountType).id)
                    } else {
                      onChange(null)
                    }
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='Akun Perkiraan'
                      placeholder='Cari Akun'
                      {...(!!error && { error: true, helperText: error.message })}
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='amount'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  fullWidth
                  label='Nominal'
                  error={!!error}
                  helperText={error?.message}
                  value={field.value || ''}
                  InputProps={{
                    inputComponent: CurrencyField as any,
                    inputProps: {
                      prefix: 'Rp',
                      name: 'amount',
                      onChange: (e: any) => {
                        field.onChange(e.target.value)
                      },
                      value: field.value,
                      allowLeadingZeros: false,
                      allowNegative: true
                    },
                    className: 'bg-white'
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='note'
              render={({ field, fieldState: { error } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Memo'
                  placeholder='Masukkan Memo'
                  error={!!error}
                  helperText={error?.message}
                  InputLabelProps={{
                    shrink: !!field.value
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' color='error'>
          BATALKAN
        </Button>
        <Button onClick={handleSubmit(onSubmit)} variant='outlined'>
          SIMPAN
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default AddExpenseDialog
