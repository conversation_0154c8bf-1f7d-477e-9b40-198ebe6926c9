import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { PaymentTermsType } from '@/types/accountTypes'
import { CreatePaymentTermsPayload } from '@/types/payload'
import { usePaymentTerms } from '@/pages/accounting/payment-terms/context/PaymentTermsContext'
import { useCreatePaymentTerms, useUpdatePaymentTerms } from '@/api/services/account/mutation'
import NumberField from '@/components/numeric/NumberField'

type AddPaymentTermDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  paymentTerm?: PaymentTermsType
  readonly?: boolean
}

const AddPaymentTermsDialog = ({
  open,
  setOpen,
  paymentTerm: paymentTerm,
  readonly = false
}: AddPaymentTermDialogProps) => {
  const { handleDeleteRow, handleDetailEdit, fetchPaymentTermsList } = usePaymentTerms()
  const { control, handleSubmit, reset } = useForm<CreatePaymentTermsPayload>()

  const { mutate: addMutate, isLoading: addLoading } = useCreatePaymentTerms()
  const { mutate: updateMutate, isLoading: updateLoading } = useUpdatePaymentTerms()

  const isLoading = addLoading || updateLoading

  const handleDefault = () => {
    updateMutate(
      {
        ...paymentTerm,
        isDefault: true,
        id: paymentTerm?.id
      },
      {
        onSuccess: () => {
          toast.success('Syarat Pembayaran berhasil dijadikan default')
          fetchPaymentTermsList()
          setOpen(false)
        }
      }
    )
  }

  const onSubmitHandler: SubmitHandler<CreatePaymentTermsPayload> = (inputValues: CreatePaymentTermsPayload) => {
    if (paymentTerm) {
      updateMutate(
        {
          ...inputValues,
          isDefault: inputValues?.isDefault ?? false,
          id: paymentTerm.id
        },
        {
          onSuccess: () => {
            toast.success('Syarat Pembayaran berhasil diubah')
            fetchPaymentTermsList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          ...inputValues,
          isDefault: inputValues?.isDefault ?? false
        },
        {
          onSuccess: () => {
            toast.success('Syarat Pembayaran berhasil ditambahkan')
            fetchPaymentTermsList()
            setOpen(false)
          }
        }
      )
    }
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  useEffect(() => {
    if (paymentTerm) {
      reset(paymentTerm)
    } else {
      reset({
        isDefault: false,
        companyId: undefined
      })
    }
  }, [paymentTerm])

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {readonly ? 'Syarat Pembayaran' : paymentTerm ? 'Ubah Syarat Pembayaran' : 'Tambah Syarat Pembayaran'}
        {readonly ? (
          <Typography component='span' className='flex flex-col text-center'>
            Lihat detil syarat pembayaran
          </Typography>
        ) : (
          !paymentTerm && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan detil data syarat pembayaran
            </Typography>
          )
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          {readonly ? (
            <>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Nama</small>
                  <Typography>{paymentTerm?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Jika Membayar dalam</small>
                  <Typography>{paymentTerm?.discountDays} Hari</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Dapat Diskon</small>
                  <Typography>{paymentTerm?.discountPercent}%</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Jatuh Tempo</small>
                  <Typography>{paymentTerm?.dueDays} Hari</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Keterangan</small>
                  <Typography>{paymentTerm?.remarks ?? '-'}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex justify-between items-center'>
                  <div className='flex flex-col gap-1'>
                    <small>Default Pembayaran</small>
                    <Typography color={paymentTerm?.isDefault ? 'primary' : 'default'}>
                      {paymentTerm?.isDefault ? 'Ya' : 'Tidak'}
                    </Typography>
                  </div>
                  <Button
                    variant='outlined'
                    onClick={handleDefault}
                    disabled={!!paymentTerm?.isDefault || isLoading}
                    size='small'
                  >
                    Jadikan Default
                  </Button>
                </div>
              </Grid>
            </>
          ) : (
            <>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='name'
                  rules={{ required: true }}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      label='Nama'
                      placeholder='Contoh: NET 60'
                      fullWidth
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  control={control}
                  name='discountDays'
                  rules={{ required: true }}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Jika Membayar Dalam'
                      variant='outlined'
                      disabled={isLoading}
                      InputLabelProps={{ shrink: !!field.value }}
                      InputProps={{ endAdornment: 'Hari', inputComponent: NumberField as any }}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Controller
                  control={control}
                  name='discountPercent'
                  rules={{ required: true }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      label='Dapat Diskon'
                      InputProps={{ inputComponent: NumberField as any, endAdornment: '%' }}
                      error={!!fieldState.error}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='dueDays'
                  rules={{ required: true }}
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Jatuh Tempo'
                      variant='outlined'
                      disabled={isLoading}
                      InputLabelProps={{ shrink: !!field.value }}
                      InputProps={{ endAdornment: 'Hari', inputComponent: NumberField as any }}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='remarks'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Keterangan (Opsional)'
                      variant='outlined'
                      disabled={isLoading}
                      InputLabelProps={{ shrink: !!field.value }}
                    />
                  )}
                />
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        {readonly ? (
          <>
            <Button onClick={() => handleDeleteRow(paymentTerm.id)} variant='outlined' color='error'>
              Hapus
            </Button>
            <Button onClick={handleDetailEdit} variant='outlined'>
              Edit
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => setOpen(false)}
              variant='outlined'
              disabled={isLoading}
              className='is-full sm:is-auto'
            >
              BATALKAN
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={isLoading}
              loadingPosition='start'
              variant='contained'
              onClick={handleSubmit(onSubmitHandler, console.error)}
              className='px-8 is-full !ml-0 sm:is-auto'
            >
              {paymentTerm ? 'UBAH DATA' : 'TAMBAHKAN'}
            </LoadingButton>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default AddPaymentTermsDialog
