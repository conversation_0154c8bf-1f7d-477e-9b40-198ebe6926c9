// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogActions from '@mui/material/DialogActions'
import Typography from '@mui/material/Typography'
import IconButton from '@mui/material/IconButton'
import Button from '@mui/material/Button'
import TextField from '@mui/material/TextField'
import {
  Autocomplete,
  CircularProgress,
  colors,
  debounce,
  FormControl,
  Grid,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select
} from '@mui/material'
import { Controller, SubmitHandler, useFieldArray, useForm, useWatch } from 'react-hook-form'

import PhotoPicker from '@/components/PhotoPicker'
import { useItem } from '@/pages/company-data/item/context/ItemContext'
import { CategoryType, ImageItemType, ItemType, VendorType } from '@/types/companyTypes'
import { array, boolean, coerce, object, string, TypeOf } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { largeUnitOptions, smallUnitOptions } from './config'
import { useEffect, useMemo, useState } from 'react'
import { useUploadImage } from '@/api/services/file/mutation'
import { toast } from 'react-toastify'
import { useAddItem, useUpdateItem } from '@/api/services/company/mutation'
import LoadingButton from '@mui/lab/LoadingButton'
import { id } from 'date-fns/locale'
import { coaList } from '@/data/coaList'
import { useQuery } from '@tanstack/react-query'
import CompanyQueryMethods, { VENDOR_LIST_QUERY_KEY, VENDOR_QUERY_KEY } from '@/api/services/company/query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { discountTypeOptions, taxTypeOptions } from '@/pages/purchase-order/config/options'
import NumberField from '@/components/numeric/NumberField'
import { PurchaseOrderDiscountType, PurchaseOrderTaxType } from '@/pages/purchase-order/config/enum'
import Separator from '@/components/Separator'
import CurrencyField from '@/components/numeric/CurrencyField'
import { isNullOrUndefined } from '@/utils/helper'
import {
  accountSchema,
  QueriesCollectionType as SelectedAccounts
} from '@/pages/company-data/category/item-category/detail/components/dialog-accounts-category'
import DialogAccountsItem from '@/pages/company-data/item/detail/components/dialog-accounts-item'

type AddItemDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  withAccount?: boolean
}

export const defaultImageList = new Array(5).fill({
  content: '',
  fileName: ''
}) as ImageItemType[]

const addItemSchema = object({
  parentCode: string().optional().nullable(),
  number: string({ message: 'Wajib diisi' }),
  vendorNumber: string({ message: 'Wajib diisi' }),
  name: string({ message: 'Wajib diisi' }),
  brandName: string({ message: 'Wajib diisi' }),
  largeUnit: string({ message: 'Wajib diisi' }),
  smallUnit: string({ message: 'Wajib diisi' }),
  categoryId: string({ message: 'Wajib dipilih' }),
  largeUnitQuantity: coerce.number({ message: 'Konversi Satuan wajib diisi' }).gt(0, 'Konversi Satuan wajib diisi'),
  vendorId: string().optional().nullable(),
  taxType: string().optional().nullable(),
  taxPercentage: coerce.number().optional().nullable(),
  pricePerUnit: coerce.number().optional().nullable(),
  discountType: string().optional().nullable(),
  discountValue: coerce.number().optional().nullable(),
  isDiscountAfterTax: boolean().optional().nullable(),
  accounts: accountSchema.optional().nullable()
  // coa: array(object({ number: string(), name: string(), type: string() })).optional().nullable()
  // inventoryCoa: array(object({ number: string(), name: string(), type: string() })).optional().nullable(),
  // expenseCoa: array(object({ number: string(), name: string(), type: string() })).optional().nullable()
})

type AddItemInput = Required<TypeOf<typeof addItemSchema>>

const AddItemDialog = ({ open, setOpen, withAccount }: AddItemDialogProps) => {
  const {
    itemData,
    categoryList,
    accountItemData,
    fetchAccountItemData,
    fetchItemList,
    fetchItemData,
    setAddCategoryOpen
  } = useItem()
  const [imageList, setImageList] = useState<ImageItemType[]>(defaultImageList)
  const { resetField, control, handleSubmit, reset, getValues } = useForm<AddItemInput>({
    resolver: zodResolver(addItemSchema),
    defaultValues: {
      brandName: itemData?.brandName,
      categoryId: itemData?.categoryId,
      largeUnit: itemData?.largeUnit,
      largeUnitQuantity: itemData?.largeUnitQuantity,
      name: itemData?.name,
      number: itemData?.number,
      smallUnit: itemData?.smallUnit,
      vendorNumber: itemData?.vendorNumber,
      vendorId: itemData?.vendorId,
      taxType: itemData?.taxType,
      taxPercentage: itemData?.taxPercentage,
      pricePerUnit: itemData?.pricePerUnit,
      discountType: itemData?.discountType,
      discountValue: itemData?.discountValue,
      isDiscountAfterTax: itemData?.isDiscountAfterTax
    }
  })

  const [vendorQuery, setVendorQuery] = useState('')
  const [selectedVendor, setSelectedVendor] = useState<VendorType>()
  const [selectedAccount, setSelectedAccount] = useState<SelectedAccounts['selectedItem']>(null)
  const [dialogAccount, setDialogAccount] = useState<boolean>(false)

  const { mutateAsync: uploadMutate, isLoading: uploadLoading } = useUploadImage()
  const { mutate: addItemMutate, isLoading: addItemLoading } = useAddItem()
  const { mutate: updateItemMutate, isLoading: updateItemLoading } = useUpdateItem()

  const isLoading = uploadLoading || addItemLoading || updateItemLoading

  const largeUnitWatch = useWatch({
    control,
    name: 'largeUnit',
    defaultValue: itemData?.largeUnit ?? ''
  })

  const smallUnitWatch = useWatch({
    control,
    name: 'smallUnit',
    defaultValue: itemData?.smallUnit ?? ''
  })

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const onSubmitHandler: SubmitHandler<AddItemInput> = (inputValues: AddItemInput) => {
    Promise.all(
      imageList
        .filter(item => !!item.fileName)
        .map(item =>
          uploadMutate({
            fieldName: `item_image_${inputValues.number}`,
            file: item.content,
            scope: 'public-image',
            fileName: item.fileName
          })
        )
    )
      .then(values => {
        const uploadIds = values.map(val => ({
          uploadId: val.data?.id ?? ''
        }))
        if (itemData) {
          updateItemMutate(
            {
              itemId: itemData.id,
              ...inputValues,
              images: [
                ...(imageList ?? [])
                  .filter(image => !image.fileName && !!image.uploadId)
                  .map(image => ({
                    uploadId: image.uploadId
                  })),
                ...uploadIds
              ],
              accounts: {
                inventoryAccountId: accountItemData?.inventoryAccount?.id,
                expenseAccountId: accountItemData?.expenseAccount?.id,
                salesAccountId: accountItemData?.salesAccount?.id,
                salesReturnAccountId: accountItemData?.salesReturnAccount?.id,
                salesDiscountAccountId: accountItemData?.salesDiscountAccount?.id,
                goodsShippedAccountId: accountItemData?.goodsShippedAccount?.id,
                cogsAccountId: accountItemData?.cogsAccount?.id,
                purchaseReturnAccountId: accountItemData?.purchaseReturnAccount?.id,
                unbilledPurchaseAccountId: accountItemData?.unbilledPurchaseAccount?.id
              }
            },
            {
              onSuccess: () => {
                toast.success('Data barang berhasil diubah')
                fetchAccountItemData()
                fetchItemData()
                fetchItemList()
                setOpen(false)
              }
            }
          )
        } else {
          addItemMutate(
            {
              ...inputValues,
              images: uploadIds
            },
            {
              onSuccess: () => {
                toast.success('Data barang berhasil ditambahkan')
                fetchAccountItemData()
                fetchItemList()
                setOpen(false)
              }
            }
          )
        }
      })
      .catch(error => {
        const message = error.response?.data?.message
        if (message) {
          toast.error(message)
        } else {
          toast.error('Oops telah terjadi kesalahan, silahkan coba beberapa saat lagi.')
        }
      })
  }

  const {
    data: { items: vendorList },
    isLoading: fetchVendorsLoading
  } = useQuery({
    enabled: !!vendorQuery,
    queryKey: [VENDOR_LIST_QUERY_KEY, vendorQuery],
    queryFn: () => {
      return CompanyQueryMethods.getVendorList({
        ...(vendorQuery && { search: vendorQuery }),
        limit: 100000
      })
    },
    placeholderData: defaultListData as ListResponse<VendorType>
  })

  const { data: vendorData } = useQuery({
    enabled: !!itemData?.vendorId,
    queryKey: [VENDOR_QUERY_KEY, itemData?.vendorId],
    queryFn: () => CompanyQueryMethods.getVendor(itemData?.vendorId)
  })

  const handleOpenDialog = () => {
    setDialogAccount(true)
  }

  const handleCloseDialog = () => {
    setDialogAccount(false)
  }

  const handleSubmitAccounts = (acc: SelectedAccounts['selectedItem']) => {
    setSelectedAccount(acc)
    reset({
      ...getValues(),
      accounts: {
        inventoryAccountId: acc?.inventory?.id,
        expenseAccountId: acc?.expense?.id,
        salesAccountId: acc?.sales?.id,
        salesReturnAccountId: acc?.salesReturn?.id,
        salesDiscountAccountId: acc?.salesDiscount?.id,
        goodsShippedAccountId: acc?.goodsDelivered?.id,
        cogsAccountId: acc?.costOfGoodsSold?.id,
        purchaseReturnAccountId: acc?.purchaseReturn?.id,
        unbilledPurchaseAccountId: acc?.unbilledPurchases?.id
      }
    })
    handleCloseDialog()
  }

  const accounts = useMemo(() => {
    return Object.values(selectedAccount ?? {}).filter(obj => obj?.id)
  }, [selectedAccount])

  useEffect(() => {
    if (itemData) {
      setImageList([
        ...itemData.images.map(image => ({
          content: image.url,
          uploadId: image.uploadId
        })),
        ...(new Array(5 - (itemData.images?.length ?? 0)).fill({
          content: '',
          fileName: ''
        }) as ImageItemType[])
      ])
    }
  }, [itemData])

  useEffect(() => {
    if (vendorData) {
      setSelectedVendor(vendorData)
    }
  }, [vendorData])

  return (
    <Dialog PaperProps={{ className: 'rounded-t-[16px] md:rounded-t-none' }} open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {itemData ? 'Ubah' : 'Tambah'} Barang
        {!itemData && (
          <Typography component='span' className='flex flex-col text-center'>
            Tambahkan barang untuk didaftarkan ke list
          </Typography>
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={4}>
            <Controller
              name='parentCode'
              control={control}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Induk'
                  variant='outlined'
                  placeholder='Masukkkan Kode Induk'
                  disabled={isLoading}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Controller
              name='number'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Barang'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Barang'
                  disabled={isLoading}
                  {...(errors.number && { error: true, helperText: errors.number?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <Controller
              name='vendorNumber'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Kode Eksternal'
                  required
                  variant='outlined'
                  placeholder='Masukkkan Kode Eksternal'
                  disabled={isLoading}
                  {...(errors.vendorNumber && { error: true, helperText: errors.vendorNumber?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='categoryId'
              control={control}
              rules={{ required: true }}
              render={({ field: { value, onChange }, formState: { errors } }) => (
                <Autocomplete
                  key={JSON.stringify(categoryList)}
                  selectOnFocus
                  clearOnBlur
                  handleHomeEndKeys
                  freeSolo
                  value={categoryList?.find(category => category.id === value) || null}
                  getOptionLabel={option => (option as CategoryType).name}
                  options={categoryList}
                  disabled={isLoading}
                  onChange={(e, newValue) => {
                    if (newValue) {
                      onChange((newValue as CategoryType).id)
                    } else {
                      onChange(null)
                    }
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      required
                      label='Pilih Kategori'
                      {...(errors.categoryId && { error: true, helperText: errors.categoryId?.message })}
                    />
                  )}
                />
              )}
            />
            <div className='flex ml-3'>
              <div>
                <Typography variant='caption' mr={1}>
                  Kategori belum ada?
                </Typography>
              </div>
              <div className='cursor-pointer' onClick={() => setAddCategoryOpen(true)}>
                <Typography variant='caption' color={colors.green.A400}>
                  Tambah Kategori
                </Typography>
              </div>
            </div>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='name'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Nama Item'
                  required
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Nama Item'
                  {...(errors.name && { error: true, helperText: errors.name?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='brandName'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  fullWidth
                  label='Merk Item'
                  required
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Merk Item'
                  {...(errors.brandName && { error: true, helperText: errors.brandName?.message })}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='largeUnit'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <Autocomplete
                  {...field}
                  value={field.value || null}
                  selectOnFocus
                  clearOnBlur
                  freeSolo
                  options={largeUnitOptions}
                  onChange={(_, newValue) => {
                    field.onChange(newValue)
                  }}
                  disabled={isLoading}
                  renderInput={params => (
                    <TextField
                      {...params}
                      required
                      label='Pilih Satuan Besar'
                      {...(errors.largeUnit && { error: true, helperText: errors.largeUnit?.message })}
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Controller
              name='smallUnit'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <Autocomplete
                  {...field}
                  value={field.value || null}
                  selectOnFocus
                  clearOnBlur
                  freeSolo
                  options={smallUnitOptions}
                  onChange={(_, newValue) => {
                    field.onChange(newValue)
                  }}
                  disabled={isLoading}
                  renderInput={params => (
                    <TextField
                      {...params}
                      required
                      label='Pilih Satuan Kecil'
                      {...(errors.smallUnit && { error: true, helperText: errors.smallUnit?.message })}
                    />
                  )}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              name='largeUnitQuantity'
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  value={field.value || 0}
                  fullWidth
                  label='Konversi Satuan'
                  required
                  variant='outlined'
                  disabled={isLoading}
                  placeholder='Masukkkan Konversi'
                  InputProps={{
                    startAdornment: !!largeUnitWatch && (
                      <Typography className='whitespace-nowrap'>1 {largeUnitWatch}&nbsp;=&nbsp;</Typography>
                    ),
                    endAdornment: <InputAdornment position='end'>{smallUnitWatch}</InputAdornment>,
                    inputComponent: NumberField as any,
                    inputProps: {
                      isAllowed: ({ floatValue }) => floatValue >= 0 || floatValue === undefined
                    }
                  }}
                  {...(errors.largeUnitQuantity && { error: true, helperText: errors.largeUnitQuantity?.message })}
                />
              )}
            />
          </Grid>
        </Grid>

        <Typography variant='caption' mt={4} mb={2}>
          Informasi Vendor (opsional)
        </Typography>
        <Grid container spacing={5}>
          <Grid item xs={12} md={6}>
            <Controller
              control={control}
              name='vendorId'
              render={({ field: { onChange, value } }) => (
                <Autocomplete
                  key={JSON.stringify(selectedVendor)}
                  filterOptions={x => x}
                  isOptionEqualToValue={(option, value) => option.id === value.id}
                  onInputChange={debounce((e, newValue, reason) => {
                    if (reason === 'input') {
                      setVendorQuery(newValue)
                    }
                  }, 700)}
                  options={vendorList || []}
                  freeSolo
                  onChange={(e, newValue: VendorType) => {
                    onChange(newValue.id)
                    setSelectedVendor(newValue)
                  }}
                  value={selectedVendor || vendorList?.find(v => v.id === value)}
                  noOptionsText='Vendor tidak ditemukan'
                  loading={fetchVendorsLoading}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label='Kode Vendor'
                      placeholder='Masukkan kode vendor'
                      variant='outlined'
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: <>{fetchVendorsLoading ? <CircularProgress /> : null}</>,
                        onKeyDown: e => {
                          if (e.key === 'Enter') {
                            e.stopPropagation()
                          }
                        }
                      }}
                    />
                  )}
                  getOptionLabel={(option: VendorType) => option?.code}
                  className='flex-1'
                  renderOption={(props, option) => {
                    const { key, ...optionProps } = props
                    return (
                      <li key={key} {...optionProps}>
                        <Typography>
                          {option.code} - {option.name}, {option.address}
                        </Typography>
                      </li>
                    )
                  }}
                />
              )}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label='Nama Vendor'
              placeholder='Masukkan nama vendor'
              className='flex-1'
              disabled
              value={selectedVendor?.name ?? ''}
              InputLabelProps={{ shrink: !!selectedVendor }}
            />
          </Grid>
        </Grid>
        <Typography variant='caption' mt={4} mb={2}>
          Informasi Harga (opsional)
        </Typography>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              name={`pricePerUnit`}
              control={control}
              rules={{ required: true }}
              render={({ field, formState: { errors } }) => (
                <TextField
                  {...field}
                  label='Harga Satuan Kecil'
                  fullWidth
                  InputProps={{
                    className: 'bg-white dark:bg-inherit',
                    inputComponent: CurrencyField as any
                  }}
                />
              )}
            />
          </Grid>
          <Controller
            name='taxType'
            control={control}
            render={({ field: taxTypeField, formState: { errors } }) => (
              <>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel id='permissionGroupId'>Pajak</InputLabel>
                    <Select
                      label='Pajak'
                      {...taxTypeField}
                      placeholder='Pilih Pajak'
                      className='bg-white dark:bg-inherit'
                    >
                      {taxTypeOptions.map(role => (
                        <MenuItem key={role.value} value={role.value}>
                          {role.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name={`taxPercentage`}
                    control={control}
                    render={({ field }) => {
                      return (
                        <TextField
                          {...field}
                          label='Jumlah Pajak'
                          fullWidth
                          disabled={taxTypeField.value !== PurchaseOrderTaxType.EXCLUDE_TAX}
                          InputProps={{
                            className: 'bg-white dark:bg-inherit',
                            endAdornment: '%',
                            inputComponent: NumberField as any,
                            inputProps: {
                              isAllowed: ({ floatValue }) => floatValue <= 100 || floatValue === undefined
                            }
                          }}
                        />
                      )
                    }}
                  />
                </Grid>
              </>
            )}
          />
          <Controller
            name={`discountType`}
            control={control}
            render={({ field: { value: discountTypeValue, ...field } }) => (
              <>
                <Grid item xs={12} sm={6}>
                  <FormControl className='flex-1' fullWidth>
                    <InputLabel id='discountType'>Jenis Diskon</InputLabel>
                    <Select
                      label='Jenis Diskon'
                      {...field}
                      value={discountTypeValue}
                      placeholder='Pilih Jenis Diskon'
                      className='bg-white dark:bg-inherit'
                      onChange={e => {
                        field.onChange(e.target.value)
                        resetField(`discountValue`)
                      }}
                    >
                      {discountTypeOptions.map(role => (
                        <MenuItem key={role.value} value={role.value}>
                          {role.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Controller
                    name={`discountValue`}
                    control={control}
                    render={({ field }) => {
                      const isPercentage = discountTypeValue === PurchaseOrderDiscountType.PERCENTAGE
                      return (
                        <TextField
                          {...field}
                          label='Jumlah Diskon'
                          fullWidth
                          InputProps={{
                            className: 'bg-white dark:bg-inherit',
                            endAdornment: isPercentage ? '%' : '',
                            inputComponent: (!isPercentage ? CurrencyField : NumberField) as any,
                            inputProps: {
                              isAllowed: ({ floatValue }) =>
                                floatValue <= (isPercentage ? 100 : Number.MAX_SAFE_INTEGER) || floatValue === undefined
                            }
                          }}
                        />
                      )
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Controller
                    name={`isDiscountAfterTax`}
                    control={control}
                    render={({ field: { onChange, value, ...field }, formState: { errors } }) => (
                      <FormControl fullWidth>
                        <InputLabel id='isDiscountAfterTax'>Pajak Diskon</InputLabel>
                        <Select
                          {...field}
                          onChange={e => onChange(e.target.value === 1)}
                          value={!isNullOrUndefined(value) ? (value ? 1 : 2) : undefined}
                          label='Pajak Diskon'
                          placeholder='Pilih Pajak Diskon'
                          className='bg-white dark:bg-inherit'
                        >
                          <MenuItem key={2} value={2}>
                            Sebelum Pajak
                          </MenuItem>
                          <MenuItem key={1} value={1}>
                            Sesudah Pajak
                          </MenuItem>
                        </Select>
                      </FormControl>
                    )}
                  />
                </Grid>
              </>
            )}
          />
        </Grid>
        {withAccount && !itemData && (
          <>
            <Grid item xs={12}>
              <div className='flex justify-between items-center'>
                <Typography variant='caption' mt={4} mb={3}>
                  Akun Perkiraan (opsional)
                </Typography>
                {accounts?.length > 0 && (
                  <Typography
                    role='button'
                    onClick={handleOpenDialog}
                    variant='body1'
                    color={colors.green.A400}
                    mt={3}
                    className='underline cursor-pointer'
                  >
                    Ubah Akun Perkiraan
                  </Typography>
                )}
              </div>
            </Grid>
            <Grid item xs={12}>
              {accounts?.length > 0 ? (
                <Typography>{accounts.map(acc => `[${acc.code}] ${acc.name}`).join(', ')}</Typography>
              ) : (
                <div className='flex justify-between items-center'>
                  <Typography>Belum ada akun perkiraan</Typography>
                  <Button onClick={handleOpenDialog} variant='outlined' size='small'>
                    Atur Akun Perkiraan
                  </Button>
                </div>
              )}
            </Grid>
          </>
        )}
        <Typography variant='caption' mt={4} mb={3}>
          Foto Barang (opsional)
        </Typography>
        <div className='flex gap-5 overflow-y-hidden max-sm:px-2'>
          {imageList?.map((item, index) => (
            <PhotoPicker
              key={`${item.content}_${index}`}
              content={item.content}
              disabled={isLoading}
              onPicked={(content, fileName) => {
                setImageList(current => {
                  const tempCurrent = [...current]
                  tempCurrent[index] = { content, fileName }
                  return tempCurrent
                })
              }}
              onRemoved={() => {
                setImageList(current => {
                  const tempCurrent = [...current]
                  tempCurrent[index] = { content: '', fileName: '' }
                  return tempCurrent
                })
              }}
            />
          ))}
        </div>
        {dialogAccount && (
          <DialogAccountsItem
            onSubmitAccounts={handleSubmitAccounts}
            selectedAccounts={selectedAccount}
            itemData={{} as ItemType}
            open={dialogAccount}
            setOpen={setDialogAccount}
          />
        )}
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button onClick={() => setOpen(false)} variant='outlined' disabled={isLoading} className='is-full sm:is-auto'>
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={isLoading}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          {itemData ? 'UBAH DATA' : 'TAMBAHKAN'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default AddItemDialog
