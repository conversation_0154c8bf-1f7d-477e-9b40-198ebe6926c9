// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import { IconButton, Typography } from '@mui/material'
import { AssetMobilization, AssetType } from '@/types/assetTypes'
import { useRouter } from '@/routes/hooks'

type MobilizationDetailDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  assetData?: AssetType
  mobilization?: AssetMobilization
}

const MobilizationDetailDialog = ({ open, setOpen, assetData, mobilization }: MobilizationDetailDialogProps) => {
  const router = useRouter()
  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth='sm' scroll='body'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Detil Mobilisasi
        <Typography variant='body2' color='textSecondary'>
          Lihat detil perpindahan aset
        </Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 p-8'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='bg-gray-100 p-4 flex rounded-lg'>
          <div className='flex flex-col gap-1 flex-1'>
            <Typography className='text-textPrimary text-base'>{assetData?.name || '-'}</Typography>
            <Typography
              className='text-primary text-sm cursor-pointer'
              onClick={() => router.push(`/accounting/assets/list/${assetData?.id}`)}
            >
              Kode Aset {assetData?.code || '-'}
            </Typography>
          </div>
          <IconButton className='size-10' onClick={() => router.push(`/accounting/assets/list/${assetData?.id}`)}>
            <i className='ri-arrow-right-s-line' />
          </IconButton>
        </div>
        <div className='flex flex-col gap-4 mt-6 p-4 border-solid border-[1px] rounded-lg'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Lokasi Awal
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {mobilization?.previousSite?.name || '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>PIC Aset</small>
            </label>
            <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {mobilization?.previousPic?.fullName || '-'}
            </p>
          </div>
        </div>

        <div className='flex flex-col gap-4 mt-6 p-4 border-solid border-[1px] rounded-lg'>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>
                Pindah ke Site
              </small>
            </label>
            <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {mobilization?.currentSite?.name || '-'}
            </p>
          </div>
          <div className='flex flex-col gap-1 items-start self-stretch relative w-full bg-transparent'>
            <label className='rounded-[10px] flex gap-2.5 items-center relative bg-transparent'>
              <small className='tracking-[0.2px] leading-3 text-xs text-[#4c4e64]/60 dark:text-inherit'>PIC Aset</small>
            </label>
            <div className='rounded-[10px] pt-0.5 flex flex-col gap-0 items-start relative bg-transparent' />
            <p className='tracking-[0.2px] leading-6 text-base text-[#4c4e64]/[87%] dark:text-inherit'>
              {mobilization?.currentPic?.fullName || '-'}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default MobilizationDetailDialog
