import { JobType, useJob } from '@/pages/repair-and-maintenance/code/job-code/context/JobContext'
import { JobCodeType } from '@/pages/repair-and-maintenance/code/job-code/create/config/schema'
import { CodeType } from '@/types/codes'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  TextField,
  Typography
} from '@mui/material'
import React from 'react'
import { Controller, useForm } from 'react-hook-form'

interface EditCodeComponentDialogProps {
  open: boolean
  setOpen: (value: React.SetStateAction<CodeType>) => void
  handleClose: () => void
  jobCode?: CodeType
  onSubmitHandler: (data: CodeType) => void
}

const EditCodeComponentDialog = (props: EditCodeComponentDialogProps) => {
  const { open, setOpen, handleClose, jobCode: component, onSubmitHandler } = props
  const { loadingEditJobCode } = useJob()
  const { control, handleSubmit } = useForm<CodeType>({
    defaultValues: {
      code: component?.code,
      description: component?.description,
      usage: component?.usage,
      createdAt: component?.createdAt,
      newLifeRecognition: component?.newLifeRecognition,
      isUseStock: component?.isUseStock,
      id: component?.id
    }
  })
  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Edit
        <Typography component='span' className='flex flex-col text-center'>
          Edit job code yang terdaftar
        </Typography>
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(null)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='code'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextField
                  error={Boolean(error)}
                  fullWidth
                  label='Job Code'
                  variant='outlined'
                  placeholder='Masukkkan Job Code'
                  value={value}
                  onChange={e => onChange((e.target as HTMLInputElement).value)}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='description'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextField
                  error={Boolean(error)}
                  fullWidth
                  label='Deskripsi'
                  variant='outlined'
                  placeholder='Masukkkan Deskripsi'
                  value={value}
                  onChange={e => onChange((e.target as HTMLInputElement).value)}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='usage'
              render={({ field: { onChange, value }, fieldState: { error } }) => (
                <TextField
                  error={Boolean(error)}
                  fullWidth
                  label='Usage'
                  variant='outlined'
                  placeholder='Masukkan Usage'
                  value={value}
                  onChange={e => onChange((e.target as HTMLInputElement).value)}
                />
              )}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='newLifeRecognition'
              render={({ field: { onChange, value }, fieldState: { error } }) => {
                return (
                  <FormControl fullWidth>
                    <InputLabel>New Life Recognition</InputLabel>
                    <Select
                      // error={Boolean(error)}
                      id='numbering-divider'
                      label='New Life Recognition'
                      value={value}
                      onChange={e => onChange((e.target.value as string) === 'true')}
                      placeholder='Pilih New Life Recognition'
                      inputProps={{
                        className: 'bg-white dark:bg-inherit'
                      }}
                      MenuProps={{
                        PaperProps: {
                          className: 'max-h-[200px] overflow-y-auto'
                        }
                      }}
                    >
                      {[true, false].map((code, idx) => (
                        <MenuItem key={idx} value={String(code)}>
                          <ListItemText primary={code ? 'Ya' : 'Tidak'} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <Controller
              control={control}
              name='isUseStock'
              render={({ field: { onChange, value }, fieldState: { error } }) => {
                return (
                  <FormControl fullWidth>
                    <InputLabel>Pakai Stok</InputLabel>
                    <Select
                      // error={Boolean(error)}
                      id='numbering-divider'
                      label='Pakai Stok'
                      value={value}
                      onChange={e => onChange((e.target.value as string) === 'true')}
                      placeholder='Pilih Pakai Stok'
                      inputProps={{
                        className: 'bg-white dark:bg-inherit'
                      }}
                      MenuProps={{
                        PaperProps: {
                          className: 'max-h-[200px] overflow-y-auto'
                        }
                      }}
                    >
                      {[true, false].map((code, idx) => (
                        <MenuItem key={idx} value={String(code)}>
                          <ListItemText primary={code ? 'Ya' : 'Tidak'} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )
              }}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        <Button
          onClick={() => setOpen(null)}
          variant='outlined'
          /*disabled={isLoading}*/ className='is-full sm:is-auto'
        >
          BATAL
        </Button>
        <LoadingButton
          startIcon={<></>}
          loading={loadingEditJobCode}
          loadingPosition='start'
          variant='contained'
          onClick={handleSubmit(onSubmitHandler)}
          className='px-8 is-full !ml-0 sm:is-auto'
        >
          SIMPAN PERUBAHAN
        </LoadingButton>
      </DialogActions>
    </Dialog>
  )
}

export default EditCodeComponentDialog
