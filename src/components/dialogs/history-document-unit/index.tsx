// MUI Imports

import Dialog, { DialogProps } from '@mui/material/Dialog'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import { IconButton, Typography } from '@mui/material'
import { MrItemType } from '@/types/mrTypes'
import CompanyQueryMethods, { SITE_LIST_QUERY_KEY } from '@/api/services/company/query'
import { useQuery } from '@tanstack/react-query'
import { defaultListData } from '@/api/queryClient'
import { ListResponse } from '@/types/api'
import { SiteType } from '@/types/companyTypes'
import {
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getPaginationRowModel,
  getSortedRowModel
} from '@tanstack/react-table'
import { useMemo } from 'react'
import { useReactTable } from '@tanstack/react-table'
import { docTableColumns, getLabel } from './config'
import Table from '@/components/table'

type StockDetailDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  type?: string
  unitId: string
}

const HistoryDocumentUnit = ({ open, setOpen, type, unitId }: StockDetailDialogProps) => {
  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  const { data: unitLogs } = useQuery({
    enabled: !!type,
    queryKey: ['UNIT_LOG_LIST_QUERY_KEY', unitId, type],
    queryFn: () => CompanyQueryMethods.getDocumentUnitLog(unitId, type)
  })

  const tableOptions = useMemo(
    () => ({
      data: unitLogs?.items ?? [],
      columns: docTableColumns({
        type
      }),
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFacetedRowModel: getFacetedRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      getFacetedMinMaxValues: getFacetedMinMaxValues()
    }),
    [unitLogs?.items, type]
  )

  const table = useReactTable<any>(tableOptions)

  return (
    <Dialog open={open} onClose={handleClose} fullWidth maxWidth='md' scroll='body'>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        Riwayat {getLabel(type)}
        <Typography align='center'>Lihat Riwayat perubahan data {getLabel(type)} untuk Unit ini</Typography>
      </DialogTitle>
      <DialogContent className='overflow-visible pbs-0 p-8'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <div className='flex flex-col gap-4'>
          <div className='rounded-[8px] shadow-md'>
            <Table
              table={table}
              emptyLabel={
                <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                  <Typography>Belum ada riwayat</Typography>
                </td>
              }
            />
          </div>
        </div>
        {/* <div className='flex flex-col gap-4'>
          <Typography>Ketersediaan Stok</Typography>
          {mrItem?.item?.stocks?.map(stock => (
            <div className='flex justify-between w-full'>
              <Typography>{siteList.find(site => site.id === stock.siteId)?.name ?? 'Site Tidak Ditemukan'}</Typography>
              <Typography>
                {stock.stock} {mrItem?.item?.smallUnit}
              </Typography>
            </div>
          ))}
        </div> */}
      </DialogContent>
    </Dialog>
  )
}

export default HistoryDocumentUnit
