import truncateString from '@/core/utils/truncate'
import { UnitLogDocument } from '@/types/companyTypes'
import { downloadFile } from '@/utils/downloadFile'
import { extractNameFromUrl } from '@/utils/string'
import { Typography } from '@mui/material'
import { createColumnHelper } from '@tanstack/react-table'
import { formatDate } from 'date-fns'
import { id } from 'date-fns/locale'

const columnHelper = createColumnHelper<UnitLogDocument>()

type RowActionType = {
  detail?: (log: UnitLogDocument) => void
  type: string
}

export const getLabel = (type: string) => {
  switch (type) {
    case 'BPKB':
    case 'STNK':
    case 'KIR':
    case 'SIO':
      return type
    default:
      return 'Asuransi'
  }
}

export const docTableColumns = (rowAction: RowActionType) => [
  ...(rowAction.type.includes('INSURANCE')
    ? [
        columnHelper.accessor('name', {
          header: '<PERSON>a <PERSON>ura<PERSON>i',
          cell: ({ row }) => <Typography>{row.original.name}</Typography>
        })
      ]
    : []),
  columnHelper.accessor('url', {
    header: 'DOKUMEN ' + getLabel(rowAction.type),
    cell: ({ row }) => (
      <Typography
        sx={{ cursor: 'pointer' }}
        onClick={() => downloadFile(row.original.url, row.original.name)}
        color='primary'
      >
        {truncateString(extractNameFromUrl(row.original.url), 15)}
      </Typography>
    )
  }),
  columnHelper.accessor('effectiveDate', {
    header: 'TGL MULAI BERLAKU',
    cell: ({ row }) =>
      !!row.original.effectiveDate
        ? formatDate(new Date(row.original.effectiveDate), 'dd/MM/yyyy', { locale: id })
        : '-'
  }),
  columnHelper.accessor('expirationDate', {
    header: 'TGL HABIS BERLAKU',
    cell: ({ row }) =>
      !!row.original.expirationDate
        ? formatDate(new Date(row.original.expirationDate), 'dd/MM/yyyy', { locale: id })
        : '-'
  }),
  columnHelper.accessor('renewalDate', {
    header: 'TGL DIPERBARUI',
    cell: ({ row }) =>
      !!row.original.renewalDate ? formatDate(new Date(row.original.renewalDate), 'dd/MM/yyyy', { locale: id }) : '-'
  })
]
