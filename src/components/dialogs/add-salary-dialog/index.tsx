import { useAddCategory, useUpdateCategory } from '@/api/services/company/mutation'
import { useItem } from '@/pages/company-data/item/context/ItemContext'
import { zodResolver } from '@hookform/resolvers/zod'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Autocomplete,
  Box,
  Button,
  colors,
  debounce,
  Dialog,
  DialogActions,
  DialogContent,
  DialogProps,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography
} from '@mui/material'
import { useEffect, useState } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { toast } from 'react-toastify'
import { object, string, TypeOf, z } from 'zod'
import { SalaryType } from '@/types/salaryTypes'
import { usePayrollContext } from '@/pages/accounting/payroll/context/PayrollContext'
import truncateString from '@/core/utils/truncate'
import { useQuery } from '@tanstack/react-query'
import AccountsQueryMethods, { ACCOUNT_LIST_QUERY_KEY } from '@/api/services/account/query'
import { AccountType } from '@/types/accountTypes'
import { useCreateSalary, useUpdateSalary } from '@/api/services/salaries/mutation'
import { SalaryPayload } from '@/types/payload'

type AvailableCategoryType = 'Gaji' | 'Tunjangan'

type AddSalaryDialogProps = {
  open: boolean
  setOpen: (open: boolean) => void
  salaryData?: SalaryType
  type?: AvailableCategoryType
  readonly?: boolean
}

const addCategorySchema = z.object({
  name: z.string({ message: 'Wajib diisi' }),
  salaryTypeId: z.number({ message: 'Wajib diisi' }),
  accountId: z.string({ message: 'Wajib diisi' }),
  paymentFrequency: z.string({ message: 'Wajib diisi' })
})
type AddCategoryInput = Required<TypeOf<typeof addCategorySchema>>

const AddSalaryDialog = ({
  open,
  setOpen,
  type = 'Gaji',
  salaryData: salaryData,
  readonly = false
}: AddSalaryDialogProps) => {
  const [accountQuery, setAccountQuery] = useState<string>('')

  const { salaryTypeList, fetchSalaryList, handleDeleteRow, handleDetailEdit } = usePayrollContext()
  const { control, handleSubmit, reset } = useForm<AddCategoryInput>({
    resolver: zodResolver(addCategorySchema)
  })

  const { mutate: addMutate, isLoading: addCategoryLoading } = useCreateSalary()
  const { mutate: updateCategoryMutate, isLoading: updateCategoryLoading } = useUpdateSalary()

  const isLoading = addCategoryLoading || updateCategoryLoading

  const onSubmitHandler: SubmitHandler<AddCategoryInput> = (inputValues: AddCategoryInput) => {
    if (salaryData) {
      updateCategoryMutate(
        {
          ...inputValues,
          id: salaryData.id,
          paymentFrequency: inputValues.paymentFrequency as SalaryPayload['paymentFrequency']
        },
        {
          onSuccess: () => {
            toast.success('Data kategori berhasil diubah')
            fetchSalaryList()
            setOpen(false)
          }
        }
      )
    } else {
      addMutate(
        {
          ...inputValues,
          paymentFrequency: inputValues.paymentFrequency as SalaryPayload['paymentFrequency']
        },
        {
          onSuccess: () => {
            toast.success('Data kategori berhasil ditambahkan')
            fetchSalaryList()
            setOpen(false)
          }
        }
      )
    }
  }

  const [importDialogOpen, setImportDialogOpen] = useState(false)

  const { data: accountTypelist, remove: removeAccountTypeList } = useQuery({
    enabled: !!accountQuery,
    queryKey: [ACCOUNT_LIST_QUERY_KEY, accountQuery],
    queryFn: async () =>
      (
        await AccountsQueryMethods.getAccountList({
          page: 1,
          limit: Number.MAX_SAFE_INTEGER,
          search: accountQuery,
          level: 1,
          accountTypeCodes: ['EXPENSE', 'OTHER_EXPENSE'].join(',')
        })
      ).items,
    placeholderData: []
  })

  const handleImportSubmit = () => {
    setImportDialogOpen(false)
  }

  const handleClose: DialogProps['onClose'] = (_, reason) => {
    if (reason && (reason === 'backdropClick' || reason === 'escapeKeyDown')) return
    setOpen(false)
  }

  let label: string
  switch (type) {
    case 'Gaji':
      label = 'Gaji / Tunjangan'
      break
    case 'Tunjangan':
      label = 'Gaji / Tunjangan'
      break
    default:
      label = ''
  }

  useEffect(() => {
    if (salaryData) {
      if (salaryData.account?.name) setAccountQuery(salaryData.account.name)
      reset(salaryData)
    }
  }, [salaryData])

  return (
    <Dialog open={open} maxWidth='sm' onClose={handleClose}>
      <DialogTitle variant='h4' className='flex flex-col gap-2 text-center sm:pbs-8 sm:pbe-6 sm:px-12'>
        {readonly ? 'Kategori Gaji / Tunjangan' : salaryData ? 'Ubah Kategori' : 'Tambah Kategori'}
        {readonly ? (
          <Typography component='span' className='flex flex-col text-center'>
            Lihat detil kategori gaji/tunjangan
          </Typography>
        ) : (
          !salaryData && (
            <Typography component='span' className='flex flex-col text-center'>
              Tambahkan kategori {label}
            </Typography>
          )
        )}
      </DialogTitle>
      <DialogContent className='pbs-0 sm:pbe-16 sm:px-12 !py-4'>
        <IconButton onClick={() => setOpen(false)} className='absolute block-start-4 inline-end-4'>
          <i className='ri-close-line text-textSecondary' />
        </IconButton>
        <Grid container spacing={5}>
          {!readonly ? (
            <>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='name'
                  render={({ field, fieldState: { error } }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label='Nama'
                      variant='outlined'
                      placeholder='Contoh: Gaji Pokok'
                      disabled={isLoading}
                      InputLabelProps={salaryData ? { shrink: !!salaryData?.name } : undefined}
                      {...(!!error && { error: true, helperText: error?.message })}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='salaryTypeId'
                  render={({ field, fieldState: { error } }) => (
                    <FormControl fullWidth>
                      <InputLabel id='salary-type'>Tipe Gaji / Tunjangan</InputLabel>
                      <Select
                        {...field}
                        labelId='salary-type'
                        label='Tipe Gaji / Tunjangan'
                        required
                        variant='outlined'
                        placeholder='Contoh: Gaji Pokok'
                        disabled={isLoading}
                        {...(!!error && { error: true })}
                      >
                        {salaryTypeList.map(salaryType => (
                          <MenuItem key={salaryType.id} value={salaryType.id}>
                            {salaryType.name}
                          </MenuItem>
                        ))}
                      </Select>
                      {!!error && <FormHelperText error>{error.message}</FormHelperText>}
                    </FormControl>
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='accountId'
                  render={({ field: { onChange, value }, fieldState: { error } }) => (
                    <Autocomplete
                      key={JSON.stringify(value)}
                      selectOnFocus
                      clearOnBlur
                      handleHomeEndKeys
                      freeSolo
                      value={accountTypelist?.find(acc => acc.id === value) || null}
                      getOptionLabel={option => (option as AccountType).name}
                      options={accountTypelist}
                      disabled={isLoading}
                      onInputChange={debounce((e, newValue, reason) => {
                        if (reason === 'input') {
                          setAccountQuery(newValue)
                        }
                      }, 700)}
                      onChange={(e, newValue) => {
                        if (newValue) {
                          removeAccountTypeList()
                          onChange((newValue as AccountType).id)
                        } else {
                          onChange(null)
                        }
                      }}
                      renderInput={params => (
                        <TextField
                          {...params}
                          label='Akun Beban'
                          placeholder='Cari Akun Beban'
                          {...(!!error && { error: true, helperText: error.message })}
                        />
                      )}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12}>
                <Controller
                  control={control}
                  name='paymentFrequency'
                  render={({ field, fieldState: { error } }) => (
                    <FormControl fullWidth>
                      <InputLabel id='payment-frequency'>Pembayaran</InputLabel>
                      <Select
                        {...field}
                        labelId='payment-frequency'
                        label='Pembayaran'
                        required
                        variant='outlined'
                        disabled={isLoading}
                        error={!!error}
                      >
                        <MenuItem value='MONTHLY'>Bulanan</MenuItem>
                        <MenuItem value='OTHER'>Tidak Bulanan</MenuItem>
                      </Select>
                      {!!error && <FormHelperText error>{error.message}</FormHelperText>}
                    </FormControl>
                  )}
                />
              </Grid>
            </>
          ) : (
            <>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Nama Kategori</small>
                  <Typography>{salaryData?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Tipe Gaji / Tunjangan</small>
                  <Typography>{salaryData?.salaryType?.name}</Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Akun Beban</small>
                  <Typography>
                    {truncateString(`[${salaryData?.account?.code}] ${salaryData?.account?.name}`, 25)}
                  </Typography>
                </div>
              </Grid>
              <Grid item xs={12}>
                <div className='flex flex-col gap-1'>
                  <small>Pembayaran</small>
                  <Typography>{salaryData?.paymentFrequency}</Typography>
                </div>
              </Grid>
            </>
          )}
        </Grid>
      </DialogContent>
      <DialogActions className='gap-2 sm:gap-6 max-sm:flex-col max-sm:items-center justify-center pbs-0 sm:pbe-10 sm:px-16'>
        {readonly ? (
          <>
            <Button onClick={() => handleDeleteRow(salaryData.id)} variant='outlined' color='error'>
              Hapus Kategori
            </Button>
            <Button onClick={handleDetailEdit} variant='outlined'>
              Edit Kategori
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => setOpen(false)}
              variant='outlined'
              disabled={isLoading}
              className='is-full sm:is-auto'
            >
              BATALKAN
            </Button>
            <LoadingButton
              startIcon={<></>}
              loading={isLoading}
              loadingPosition='start'
              variant='contained'
              onClick={handleSubmit(onSubmitHandler, console.error)}
              className='px-8 is-full !ml-0 sm:is-auto'
            >
              {salaryData ? 'UBAH DATA' : 'TAMBAHKAN'}
            </LoadingButton>
          </>
        )}
      </DialogActions>
    </Dialog>
  )
}

export default AddSalaryDialog
