// React Imports
import { createContext, useCallback, useContext, useMemo, useState } from 'react'

// Type Imports
import type { ChildrenType } from '../types'
import ConfirmDialog from '@/components/dialogs/confirm-dialog'
import { DefaultApprovalScope } from '@/pages/user/default-approval/config/enum'
import { useAuth } from '@/contexts/AuthContext'
import dictionary from '@/data/dictionaries/id.json'
import { MenuDataType } from '@/types/menuTypes'

export type MenuState = {
  width?: number
  collapsedWidth?: number
  isCollapsed?: boolean
  isHovered?: boolean
  isToggled?: boolean
  isScrollWithContent?: boolean
  isBreakpointReached?: boolean
  isPopoutWhenCollapsed?: boolean
  collapsing?: boolean // for internal use only
  expanding?: boolean // for internal use only
  transitionDuration?: number
}

export type ConfirmDialogType = {
  open: boolean
  title: string
  content: string
  confirmText: string
  confirmColor?: string
  onConfirm: () => void
  onCancel?: () => void
}

export type MenuContextProps = MenuState & {
  updateVerticalNavState: (values: MenuState) => void
  collapseVerticalNav: (value?: MenuState['isCollapsed']) => void
  hoverVerticalNav: (value?: MenuState['isHovered']) => void
  toggleVerticalNav: (value?: MenuState['isToggled']) => void
  confirmState: ConfirmDialogType
  setConfirmState: React.Dispatch<React.SetStateAction<ConfirmDialogType>>
  menuData: MenuDataType[]
}

const MenuContext = createContext({} as MenuContextProps)

export const useMenu = () => {
  return useContext(MenuContext)
}

export const MenuProvider = ({ children }: ChildrenType) => {
  const { accountPermissions, offlinePermissions, approvalCounts } = useAuth()
  const {
    mrCount,
    prCount,
    mtCount,
    mbCount,
    mgOutCount,
    smCount,
    rmaCount,
    srCount,
    preReleaseCount,
    sreqCount,
    pwCount,
    poCount,
    soCount
  } = approvalCounts
  const [confirmState, setConfirmState] = useState<ConfirmDialogType>({
    open: false,
    title: '',
    content: '',
    confirmText: '',
    confirmColor: '',
    onConfirm: () => {},
    onCancel: () => {}
  })
  // States
  const [verticalNavState, setVerticalNavState] = useState<MenuState>()

  // Hooks
  const updateVerticalNavState = useCallback((values: Partial<MenuState>) => {
    setVerticalNavState(prevState => ({
      ...prevState,
      ...values,
      collapsing: values.isCollapsed === true,
      expanding: values.isCollapsed === false
    }))
  }, [])

  const collapseVerticalNav = useCallback((value?: boolean) => {
    setVerticalNavState(prevState => ({
      ...prevState,
      isHovered: value !== undefined && false,
      isCollapsed: value !== undefined ? Boolean(value) : !Boolean(prevState?.isCollapsed),
      collapsing: value === true,
      expanding: value !== true
    }))
  }, [])

  const hoverVerticalNav = useCallback((value?: boolean) => {
    setVerticalNavState(prevState => ({
      ...prevState,
      isHovered: value !== undefined ? Boolean(value) : !Boolean(prevState?.isHovered)
    }))
  }, [])

  const toggleVerticalNav = useCallback((value?: boolean) => {
    setVerticalNavState(prevState => ({
      ...prevState,
      isToggled: value !== undefined ? Boolean(value) : !Boolean(prevState?.isToggled)
    }))
  }, [])

  const getFilteredMenu = (permissions: string[], menu: MenuDataType, isParent?: boolean) =>
    !!accountPermissions?.find(permission =>
      permissions.includes(isParent ? permission.split('.')?.[0] ?? '' : permission)
    )
      ? [menu]
      : !!offlinePermissions?.find(permission =>
            permissions.includes(isParent ? permission.split('.')?.[0] ?? '' : permission)
          )
        ? [menu]
        : []

  const menuData =
    (accountPermissions?.length || offlinePermissions?.length) > 0
      ? [
          {
            id: 'approvals-section',
            isSection: true,
            label: 'Menu Persetujuan',
            children: [
              ...getFilteredMenu(['default-approval.write'], {
                id: 'default-approval',
                label: dictionary['navigation'].defaultApproval,
                icon: 'ri-user-line',
                href: `/user/default-approval`
              }),
              {
                id: 'approvals-section',
                label: dictionary['navigation'].persetujuan,
                suffix:
                  mrCount > 0 ||
                  prCount > 0 ||
                  mtCount > 0 ||
                  mbCount > 0 ||
                  mgOutCount > 0 ||
                  smCount > 0 ||
                  srCount > 0 ||
                  rmaCount > 0 ? (
                    <i className='red-dot-icon size-2 text-error' />
                  ) : null,
                icon: 'approvals-icon',
                children: [
                  ...getFilteredMenu(['material-request.approve'], {
                    id: 'mr-approval',
                    label: dictionary['navigation'].altMaterialRequest,
                    href: `/mr/approval`,
                    suffix:
                      mrCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {mrCount > 9 ? '9+' : mrCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['purchase-requisition.approve'], {
                    id: 'pr-approval',
                    label: dictionary['navigation'].altpersetujuanPp,
                    href: `/pr/approval`,
                    suffix:
                      prCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {prCount > 9 ? '9+' : prCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['material-transfer.approve'], {
                    id: 'mt-approval',
                    label: dictionary['navigation'].materialTransfer,
                    href: '/mt/approval',
                    suffix:
                      mtCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {mtCount > 9 ? '9+' : mtCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['material-transfer.approve'], {
                    id: 'mb-approval',
                    label: dictionary['navigation'].materialBorrow,
                    href: '/mb/approval',
                    suffix:
                      mbCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {mbCount > 9 ? '9+' : mbCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['outgoing-material.approve'], {
                    id: 'req-out',
                    label: dictionary['navigation'].barangKeluar,
                    href: '/mg/out/approval',
                    suffix:
                      mgOutCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {mgOutCount > 9 ? '9+' : mgOutCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['stock-movement.approve'], {
                    id: 'approve-stock-movement',
                    label: dictionary['navigation'].pindahBarang,
                    href: '/mg/sm/approval',
                    suffix:
                      smCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {smCount > 9 ? '9+' : smCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['stock-return.approve'], {
                    id: 'stock-approval',
                    label: 'Pengembalian Stok',
                    href: '/sr/approval',
                    suffix:
                      srCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {srCount > 9 ? '9+' : srCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['purchase-order.approve'], {
                    id: 'po-approval',
                    label: 'Purchase Order',
                    href: `/po/approval`,
                    suffix:
                      poCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {poCount > 9 ? '9+' : poCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['service-order.approve'], {
                    id: 'so-approval',
                    label: 'Service Order',
                    href: `/service-order/approval`,
                    suffix:
                      soCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {soCount > 9 ? '9+' : soCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['rma.approve'], {
                    id: 'rma-approval',
                    label: 'RMA',
                    href: '/rma/approval',
                    suffix:
                      rmaCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {rmaCount > 9 ? '9+' : rmaCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['work-order.approve-pre-release'], {
                    id: 'approval-pre-release-list',
                    label: 'Pre-Release',
                    href: '/wo/approval-pre-releases',
                    suffix:
                      preReleaseCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {preReleaseCount > 9 ? '9+' : preReleaseCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['service-requisition.approve'], {
                    id: 'service-request-approval',
                    label: 'Service Request',
                    href: '/service-request/approval',
                    suffix:
                      sreqCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {sreqCount > 9 ? '9+' : sreqCount}
                        </div>
                      ) : null
                  }),
                  ...getFilteredMenu(['part-swap.update', 'part-swap.approve'], {
                    id: 'part-swap-approval',
                    label: 'Part Swap',
                    href: '/part-swap/approval',
                    suffix:
                      pwCount > 0 ? (
                        <div className='bg-error text-white rounded-xl p-1 px-2 text-xxs'>
                          {pwCount > 9 ? '9+' : pwCount}
                        </div>
                      ) : null
                  })
                ]
              }
            ]
          },
          {
            id: 'warehouse-section',
            isSection: true,
            label: dictionary['navigation'].warehouseManagement,
            children: [
              {
                id: 'warehouse',
                label: 'Dashboard',
                icon: 'ri-home-smile-line',
                href: `/dashboard/warehouse`
              },
              {
                id: 'material-goods',
                label: 'Material/Barang',
                icon: 'mdi-layers-triple-outline',
                children: [
                  ...getFilteredMenu(['item.write'], {
                    id: 'item-list',
                    label: 'List Barang',
                    href: '/company-data/assets/goods'
                  }),
                  ...getFilteredMenu(['category.write'], {
                    id: 'category-list-item',
                    label: 'Kategori Barang',
                    href: `/company-data/category/item`
                  }),
                  ...getFilteredMenu(['incoming-material.create'], {
                    id: 'mg-in',
                    label: 'Barang Masuk',
                    href: '/mg/in'
                  }),
                  ...getFilteredMenu(['outgoing-material.create'], {
                    id: 'mg-out',
                    label: 'Barang Keluar',
                    href: '/mg/out/request'
                  }),
                  ...getFilteredMenu(['outgoing-material.create'], {
                    id: 'mg-out',
                    label: 'Draft Barang Keluar',
                    href: '/mg/out/draft'
                  }),
                  {
                    id: 'stock-movement',
                    label: 'Pindah Barang',
                    children: [
                      ...getFilteredMenu(['stock-movement.create'], {
                        id: 'created-stock-movement',
                        label: 'Terbuat',
                        href: '/mg/sm/list'
                      }),
                      ...getFilteredMenu(['stock-movement.receive'], {
                        id: 'receive-stock-movement',
                        label: 'Penerimaan',
                        href: '/mg/sm/receive'
                      })
                    ]
                  }
                ]
              },
              ...getFilteredMenu(
                [DefaultApprovalScope.MaterialRequest],
                {
                  id: 'material-request',
                  label: dictionary['navigation'].materialRequest,
                  icon: 'ic-outline-file-upload',
                  children: [
                    ...getFilteredMenu(['material-request.create', 'material-request.update'], {
                      id: 'mr-list',
                      label: dictionary['navigation'].mrTerbuat,
                      href: `/mr/list`
                    }),
                    ...getFilteredMenu(['material-request.create'], {
                      id: 'mr-create',
                      label: dictionary['navigation'].buatMr,
                      href: `/mr/create`
                    }),
                    ...getFilteredMenu(['material-request.create', 'material-request.update'], {
                      id: 'mr-list',
                      label: 'Draft',
                      href: `/mr/draft`
                    })
                  ]
                },
                true
              ),
              ...getFilteredMenu(['purchase-requisition.create', 'material-transfer.create'], {
                id: 'mr-created',
                label: dictionary['navigation'].mrMasuk,
                icon: 'ri-folder-download-line',
                href: '/mr-in'
              }),
              ...getFilteredMenu(
                [DefaultApprovalScope.PurchaseRequisition],
                {
                  id: 'purchase-requisition',
                  label: dictionary['navigation'].permintaanPembelian,
                  icon: 'mdi-file-document-outline',
                  children: [
                    ...getFilteredMenu(['purchase-requisition.create', 'purchase-requisition.update'], {
                      id: 'pr-list',
                      label: dictionary['navigation'].ppTerbuat,
                      href: `/pr/list`
                    }),
                    ...getFilteredMenu(['purchase-requisition.create', 'purchase-requisition.update'], {
                      id: 'pr-drafts',
                      label: 'Draft',
                      href: `/pr/draft`
                    })
                  ]
                },
                true
              ),
              ...getFilteredMenu(
                [
                  'material-transfer.create',
                  'material-transfer.approve',
                  'material-transfer.receive',
                  'material-transfer.delivery'
                ],
                {
                  id: 'mt',
                  label: 'Material Transfer',
                  icon: 'solar-card-transfer-linear',
                  children: [
                    ...getFilteredMenu(['material-transfer.create'], {
                      id: 'mt-created',
                      label: 'Terbuat',
                      href: '/mt/created'
                    }),
                    ...getFilteredMenu(['material-transfer.delivery'], {
                      id: 'mt-list',
                      label: 'Permintaan Masuk',
                      href: '/mt/list'
                    }),
                    ...getFilteredMenu(['material-transfer.receive'], {
                      id: 'mt-receive',
                      label: 'Penerimaan',
                      href: '/mt/receive'
                    }),
                    ...getFilteredMenu(['material-transfer.create'], {
                      id: 'mt-draft',
                      label: 'Draft',
                      href: '/mt/draft'
                    })
                  ]
                }
              ),
              ...getFilteredMenu(
                [
                  'material-transfer.create',
                  'material-transfer.approve',
                  'material-transfer.receive',
                  'material-transfer.delivery'
                ],
                {
                  id: 'mb',
                  label: 'Material Borrow',
                  icon: 'streamline-arrow-infinite-loop',
                  children: [
                    ...getFilteredMenu(['material-transfer.create'], {
                      id: 'mb-created',
                      label: 'Terbuat',
                      href: '/mb/created'
                    }),
                    ...getFilteredMenu(['material-transfer.delivery'], {
                      id: 'mb-list',
                      label: dictionary['navigation'].incomingRequest,
                      href: '/mb/list'
                    }),
                    ...getFilteredMenu(['material-transfer.receive'], {
                      id: 'mb-receive',
                      label: 'Penerimaan',
                      href: '/mb/receive'
                    }),
                    ...getFilteredMenu(['material-transfer.delivery'], {
                      id: 'mb-return-list',
                      label: 'Pengembalian',
                      href: '/mb/return-list'
                    })
                  ]
                }
              ),
              {
                id: 'mg-stock',
                label: 'Stok',
                icon: 'stock-icon',
                children: [
                  ...getFilteredMenu(['stock.read'], {
                    id: 'mg-stock',
                    label: 'Stok Barang',
                    href: '/mg/stock'
                  }),
                  ...getFilteredMenu(['stock-opname.create'], {
                    id: 'mg-stock-opname',
                    label: 'Stok Opnam',
                    href: '/mg/so'
                  }),
                  ...getFilteredMenu(['stock-return.create'], {
                    id: 'stock-return',
                    label: 'Pengembalian Stok',
                    children: [
                      {
                        id: 'stock-return-request',
                        label: 'Pengajuan',
                        href: '/sr/request'
                      },
                      {
                        id: 'stock-return-draft',
                        label: 'Draft',
                        href: '/sr/draft'
                      }
                    ]
                  })
                ]
              }
            ]
          },
          {
            id: 'purchasing-section',
            isSection: true,
            label: dictionary['navigation'].purchasing,
            children: [
              {
                id: 'purchasing',
                label: 'Dashboard',
                icon: 'ri-home-smile-line',
                href: `/dashboard/purchasing`
              },
              {
                id: 'vendor-section',
                label: 'Vendor',
                icon: 'ri-store-2-line',
                children: [
                  ...getFilteredMenu(['vendor.write'], {
                    id: 'vendor-list',
                    label: 'List',
                    href: '/company-data/vendor'
                  }),
                  ...getFilteredMenu(['category.write'], {
                    id: 'category-list-vendor',
                    label: 'Kategori',
                    href: `/company-data/category/vendor`
                  })
                ]
              },
              ...getFilteredMenu(
                [DefaultApprovalScope.PurchaseOrder],
                {
                  id: 'purchase-order',
                  label: dictionary['navigation'].purchaseOrder,
                  icon: 'heroicons-document-arrow-down',
                  children: [
                    ...getFilteredMenu(['purchase-order.create', 'purchase-order.update'], {
                      id: 'pr-created',
                      label: dictionary['navigation'].incomingRequest,
                      href: `/po/pr-list`
                    }),
                    ...getFilteredMenu(['purchase-order.create', 'purchase-order.update'], {
                      id: 'po-list',
                      label: dictionary['navigation'].poTerbuat,
                      href: `/po/list`
                    }),
                    ...getFilteredMenu(['purchase-order.create', 'purchase-order.update'], {
                      id: 'po-list',
                      label: 'Draft',
                      href: `/po/draft`
                    })
                  ]
                },
                true
              ),
              ...getFilteredMenu(
                [DefaultApprovalScope.ServiceOrder],
                {
                  id: 'service-order',
                  label: 'Service Order',
                  icon: 'heroicons-document-arrow-down',
                  children: [
                    ...getFilteredMenu(['service-order.create', 'service-order.update'], {
                      id: 'sr-created',
                      label: dictionary['navigation'].incomingRequest,
                      href: `/service-order/sr-list`
                    }),
                    ...getFilteredMenu(['service-order.create', 'service-order.update'], {
                      id: 'so-list',
                      label: 'Terbuat',
                      href: `/service-order/list`
                    })
                  ]
                },
                true
              ),
              ...getFilteredMenu(
                [DefaultApprovalScope.Rma],
                {
                  id: 'rma',
                  label: 'RMA',
                  icon: 'ic-rma-outline',
                  children: [
                    ...getFilteredMenu(['rma.create'], {
                      id: 'rma-list',
                      label: 'Terbuat',
                      href: '/rma/list'
                    }),
                    // ...getFilteredMenu(['rma.create'], {
                    //   id: 'request-rma',
                    //   label: 'Pengajuan',
                    //   href: '/rma/request'
                    // }),
                    ...getFilteredMenu(['rma.create'], {
                      id: 'draft-rma',
                      label: 'Draft',
                      href: '/rma/draft'
                    })
                  ]
                },
                true
              )
            ]
          },
          {
            id: 'repair-section',
            isSection: true,
            label: dictionary['navigation'].repairandmaintenance,
            children: [
              {
                id: 'workshop',
                label: 'Dashboard',
                icon: 'ri-home-smile-line',
                href: `/dashboard/workshop`
              },
              {
                id: 'unit-asset',
                label: dictionary['navigation'].unitAsset,
                icon: 'asset-icon',
                children: [
                  ...getFilteredMenu(['unit.write'], {
                    id: 'unit-asset-list',
                    label: 'List Unit',
                    // icon: 'ri-truck-line',
                    href: '/company-data/assets/unit'
                  }),
                  {
                    id: 'document-asset',
                    label: dictionary['navigation'].documentAsset,
                    href: '/company-data/assets/document'
                  },
                  {
                    id: 'insurance-asset',
                    label: dictionary['navigation'].insuranceAsset,
                    href: '/company-data/assets/insurance'
                  },
                  ...getFilteredMenu(['category.write'], {
                    id: 'category-list-unit',
                    label: 'Kategori',
                    href: `/company-data/category/unit`
                  })
                ]
              },
              ...getFilteredMenu(['code.write'], {
                id: 'code',
                label: 'Kode',
                icon: 'fluens-number-symbol-16',
                children: [
                  {
                    id: 'component',
                    label: 'Component',
                    href: '/rnm/component'
                  },
                  {
                    id: 'job-code',
                    label: 'Job Code',
                    href: '/rnm/job-code'
                  },
                  {
                    id: 'kode-modifier',
                    label: 'Kode Modifier',
                    href: '/rnm/modifier'
                  }
                ]
              }),
              {
                id: 'unit-section',
                label: 'Unit',
                icon: 'car-unit',
                href: '/rnm/unit'
              },
              {
                id: 'fr-section',
                label: 'Field Report',
                icon: 'paste-icon',
                children: [
                  {
                    id: 'created-fr',
                    label: 'Terbuat',
                    href: '/fr/created'
                  },
                  ...getFilteredMenu(['field-report.create', 'field-report.update'], {
                    id: 'create-fr',
                    label: 'Buat',
                    href: '/fr/new-fr'
                  })
                ]
              },
              {
                id: 'wo-section',
                label: 'Work Order',
                icon: 'wo-icon',
                children: [
                  ...getFilteredMenu(['work-order.create', 'work-order.update'], {
                    id: 'created-fr',
                    label: 'Report Masuk',
                    href: '/wo/list'
                  }),
                  ...getFilteredMenu(['work-order.update'], {
                    id: 'wo-list',
                    label: 'Terbuat',
                    href: '/wo/created'
                  })
                ]
              },
              {
                id: 'work-process-section',
                label: 'Work Process',
                icon: 'work-process',
                children: [
                  ...getFilteredMenu(['work-process.report'], {
                    id: 'work-process-in',
                    label: 'Masuk',
                    href: '/wp-in'
                  }),
                  ...getFilteredMenu(['work-process.create', 'work-process.update'], {
                    id: 'work-process',
                    label: 'Terbuat',
                    href: '/wp/list'
                  }),
                  ...getFilteredMenu(['work-process.create', 'work-process.update'], {
                    id: 'created-wp',
                    label: 'Buat',
                    href: '/wp/create'
                  }),
                  ...getFilteredMenu(['work-process.update'], {
                    id: 'wp-review',
                    label: 'Review',
                    href: '/wp/review'
                  })
                ]
              },
              {
                id: 'pre-release-section',
                label: 'Pre-Release',
                icon: 'pre-release-icon',
                children: [
                  ...getFilteredMenu(['work-order.write-pre-release-tmp'], {
                    id: 'pre-release-format',
                    label: 'Format',
                    href: '/wo/format-pre-release'
                  }),
                  ...getFilteredMenu(['work-order.write-pre-release-tmp'], {
                    id: 'pre-release-created',
                    label: 'Terbuat',
                    href: '/wo/pre-release-created'
                  }),
                  ...getFilteredMenu(['work-order.write-pre-release-tmp'], {
                    id: 'pre-release-list',
                    label: 'Pengajuan',
                    href: '/wo/pre-releases'
                  }),
                  {
                    id: 'unit-taking',
                    label: 'Pengambilan Unit',
                    href: '/wo/unit-taking'
                  }
                ]
              },
              {
                id: 'service-request-section',
                label: 'Service Request',
                icon: 'sr-icon',
                children: [
                  ...getFilteredMenu(['service-requisition.create', 'service-requisition.update'], {
                    id: 'service-request-list',
                    label: 'Terbuat',
                    href: '/service-request/list'
                  })
                ]
              },
              {
                id: 'part-swap-section',
                label: 'Part Swap',
                icon: 'part-swap-icon',
                children: [
                  ...getFilteredMenu(['part-swap.create', 'part-swap.update'], {
                    id: 'part-swap-list',
                    label: 'Terbuat',
                    href: '/part-swap/list'
                  })
                ]
              }
            ]
          },
          {
            id: 'accounting-section',
            isSection: true,
            label: 'Accounting',
            children: [
              {
                id: 'accounting',
                label: 'Dashboard',
                icon: 'ri-home-smile-line',
                href: `/dashboard/accounting`
              },
              {
                id: 'code-activa',
                label: 'Kode Aktiva',
                icon: 'asset-icon',
                href: '/company-data/assets/code-activa'
              },
              {
                id: 'data-accounting',
                label: 'Akun & Data',
                icon: 'ri-building-4-line',
                children: [
                  ...getFilteredMenu(['account.write'], {
                    id: 'accounts-section',
                    label: 'Perkiraan',
                    href: '/accounting/accounts'
                  }),
                  ...getFilteredMenu(['salary.write'], {
                    id: 'salary-section',
                    label: 'Gaji / Tunjangan',
                    href: '/accounting/salary'
                  }),
                  ...getFilteredMenu(['tax.write'], {
                    id: 'tax-section',
                    label: 'Pajak',
                    href: '/accounting/tax'
                  }),
                  ...getFilteredMenu(['currency.write'], {
                    id: 'currency-section',
                    label: 'Mata Uang',
                    href: '/accounting/currency'
                  }),
                  ...getFilteredMenu(['customer.write'], {
                    id: 'customer-section',
                    label: 'Customer/Pelanggan',
                    href: '/accounting/customer'
                  }),
                  ...getFilteredMenu(['carrier.write'], {
                    id: 'carrier-section',
                    label: 'Pengiriman',
                    href: '/accounting/carrier'
                  })
                ]
              }
            ]
          },
          {
            id: 'data-section',
            isSection: true,
            label: dictionary['navigation'].dataPerusahaan,
            children: [
              ...getFilteredMenu(['category.write'], {
                id: 'category-list',
                label: 'List Kategori',
                icon: 'tabler--category',
                children: []
              }),
              ...getFilteredMenu(['site.write'], {
                id: 'site-list',
                label: dictionary['navigation'].listSite,
                icon: 'ri-map-pin-line',
                href: '/company-data/site'
              }),
              ...getFilteredMenu(['project.write'], {
                id: 'project-list',
                label: 'Manajemen Proyek',
                href: '/company-data/projects',
                icon: 'projects-icon'
              }),
              ...getFilteredMenu(['department.write'], {
                id: 'department-list',
                label: dictionary['navigation'].listDepartemen,
                icon: 'ri-building-4-line',
                href: '/company-data/department'
              })
            ]
          },
          {
            id: 'purchasing-section',
            isSection: true,
            label: dictionary['navigation'].exportImport,
            children: [
              {
                id: 'data-export',
                label: 'List Ekspor Data',
                icon: 'tabler-file-export',
                href: '/data-export'
              },
              {
                id: 'data-import',
                label: 'List Impor Data',
                icon: 'tabler-file-import',
                href: '/data-import'
              }
            ]
          },

          {
            id: 'settings-section',
            isSection: true,
            label: dictionary['navigation'].settings,
            children: [
              {
                id: 'user',
                label: dictionary['navigation'].user,
                icon: 'ri-user-line',
                children: [
                  ...getFilteredMenu(['user.write'], {
                    id: 'user-list',
                    label: dictionary['navigation'].listUser,
                    href: `/user/list`
                  }),
                  ...getFilteredMenu(['permission-group.write'], {
                    id: 'role',
                    label: dictionary['navigation'].roleUser,
                    href: `/user/role`
                  })
                ]
              },
              ...getFilteredMenu(['number-format.write', 'number-format.write'], {
                id: 'numbering-settings',
                label: 'Penomoran',
                icon: 'lsicon--number-filled',
                href: '/setting/numbering'
              })
            ]
          }
        ]
      : ([] as MenuDataType[])

  const verticalNavProviderValue = useMemo(
    () => ({
      ...verticalNavState,
      updateVerticalNavState,
      collapseVerticalNav,
      hoverVerticalNav,
      toggleVerticalNav,
      confirmState,
      setConfirmState,
      menuData
    }),
    [
      verticalNavState,
      updateVerticalNavState,
      collapseVerticalNav,
      hoverVerticalNav,
      toggleVerticalNav,
      confirmState,
      setConfirmState,
      menuData
    ]
  )

  return (
    <MenuContext.Provider value={verticalNavProviderValue}>
      {children}
      {confirmState.open && (
        <ConfirmDialog
          title={confirmState.title}
          content={confirmState.content}
          confirmText={confirmState.confirmText}
          confirmColor={confirmState.confirmColor}
          open={confirmState.open}
          setOpen={open => setConfirmState(current => ({ ...current, open }))}
          onConfirm={confirmState.onConfirm}
          onCancel={confirmState.onCancel}
        />
      )}
    </MenuContext.Provider>
  )
}

export default MenuContext
