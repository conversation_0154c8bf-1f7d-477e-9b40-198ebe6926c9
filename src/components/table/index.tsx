/* eslint-disable import/named */
// Style Imports
import classNames from 'classnames'

import type { Table as TableType } from '@tanstack/react-table'
import { flexRender } from '@tanstack/react-table'

import { TablePagination } from '@mui/material'

import tableStyles from '@/core/styles/table.module.css'
import { ReactNode, useEffect } from 'react'

type DataTableType = {
  table: TableType<any>
  emptyLabel?: ReactNode
  onRowsPerPageChange?: (pageSize: number) => void
  onPageChange?: (pageIndex: number) => void
  disablePagination?: boolean
  headerColor?: 'default' | 'green'
  className?: string
  containerClassName?: string
  stickyHeader?: boolean
}

function Table({
  table,
  emptyLabel,
  onPageChange,
  onRowsPerPageChange,
  disablePagination,
  headerColor,
  className,
  containerClassName,
  stickyHeader
}: DataTableType) {
  let headerCssClasses = ''
  switch (headerColor) {
    case 'green':
      headerCssClasses = '!bg-[#DBF7E8]'
      break
    default:
      break
  }
  return (
    <div className='flex flex-col'>
      <div className={classNames('overflow-x-auto', containerClassName)}>
        <table className={tableStyles.table}>
          <thead
            style={{
              borderBlockEnd: '1px solid var(--border-color)'
            }}
          >
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th
                    className={headerCssClasses}
                    key={header.id}
                    style={{ width: header.getSize(), ...(stickyHeader && { position: 'sticky', zIndex: 1, top: 0 }) }}
                  >
                    {header.isPlaceholder ? null : (
                      <>
                        <div
                          className={classNames({
                            'flex items-center': header.column.getIsSorted(),
                            'cursor-pointer select-none': header.column.getCanSort()
                          })}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(header.column.columnDef.header, header.getContext())}
                          {{
                            asc: <i className='ri-arrow-up-s-line text-xl' />,
                            desc: <i className='ri-arrow-down-s-line text-xl' />
                          }[header.column.getIsSorted() as 'asc' | 'desc'] ?? null}
                        </div>
                      </>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          {table.getFilteredRowModel().rows.length === 0 ? (
            <tbody>
              <tr>
                {emptyLabel ?? (
                  <td colSpan={table.getVisibleFlatColumns().length} className='text-center h-60'>
                    Belum ada data
                  </td>
                )}
              </tr>
            </tbody>
          ) : (
            <tbody>
              {table
                .getRowModel()
                .rows.slice(0, table.getState().pagination.pageSize)
                .map(row => {
                  return (
                    <tr
                      key={row.id}
                      className={classNames(
                        row.original.isRead ? 'bg-gray-100 dark:bg-inherit' : 'bg-transparent dark:bg-inherit',
                        className
                      )}
                    >
                      {row.getVisibleCells().map(cell => (
                        <td key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</td>
                      ))}
                    </tr>
                  )
                })}
            </tbody>
          )}
        </table>
      </div>
      {table.getFilteredRowModel().rows.length > 0 ? (
        <TablePagination
          hidden={disablePagination}
          rowsPerPageOptions={[5, 10, 25, 50, 100]}
          labelRowsPerPage='Data per halaman'
          labelDisplayedRows={info => `${info.from} - ${info.to} dari ${info.count} data`}
          component='div'
          className='border-bs'
          count={table.getRowCount()}
          rowsPerPage={table.getState().pagination.pageSize}
          page={table.getState().pagination.pageIndex}
          slotProps={{
            select: {
              inputProps: { 'aria-label': 'rows per page' }
            }
          }}
          onPageChange={(_, page) => {
            table.setPageIndex(page)
            onPageChange?.(page + 1)
          }}
          onRowsPerPageChange={e => {
            table.setPageSize(Number((e.target as HTMLInputElement).value))
            onRowsPerPageChange?.(Number((e.target as HTMLInputElement).value))
          }}
        />
      ) : null}
    </div>
  )
}

export default Table
