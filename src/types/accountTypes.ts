import { ListParams } from './payload'

export type AccountParams = {
  accountTypeIds?: string[]
  companyId?: string
  status?: 'ACTIVE' | 'INACTIVE'
} & ListParams

export type AccountType = {
  id: string
  companyId: string
  parentCompanyId: string
  accountTypeId: number
  parentId: string
  code: string
  name: string
  level: number
  balance: number
  note: string
  status: string
  balanceAt: string
  createdAt: string
  updatedAt: string
  parent: string
}

export type AccountMasterType = {
  id: number
  companyId: string
  parentCompanyId: string
  code: string
  name: string
  createdAt: string
  updatedAt: string
}
