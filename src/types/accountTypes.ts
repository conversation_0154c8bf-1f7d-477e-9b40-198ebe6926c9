import { ReconciliationType } from '@/pages/cash-bank/reconsiliation/config/types'
import { CodeNameType } from './common'
import { ListParams } from './payload'
import { ProjectType } from './projectTypes'
import { UserOutlineType } from './userTypes'
import { DepartmentType, SiteType } from './companyTypes'

export type AccountParams = {
  accountTypeIds?: string
  accountTypeCodes?: string
  companyId?: string
  level?: number
  status?: 'ACTIVE' | 'INACTIVE'
} & ListParams

export type JournalLineParams = {
  accountId: string
  isReconciliated?: boolean
  bankReconciliationId?: string
} & ListParams

export type AccountType = {
  id: string
  companyId: string
  parentCompanyId: string
  accountTypeId: number
  accountType?: AccountMasterType
  parentId: string
  code: string
  name: string
  level: number
  balance: number
  note: string
  status: string
  balanceAt: string
  createdAt: string
  updatedAt: string
  parent: CodeNameType
}

export type AccountMasterType = {
  id: number
  companyId: string
  parentCompanyId: string
  code: string
  name: string
  createdAt: string
  updatedAt: string
}

export type DivisionType = {
  id: string
  code: string
  name: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  createdAt: string
  updatedAt: string
}

export type GeneralLedgerLineType = {
  id: number
  journalId: string
  accountId: string
  debit: number
  credit: number
  description: string
  createdAt: string
  updatedAt: string
  createdBy: string
  account: AccountType
}

export type GeneralLedgerType = {
  id: string
  companyId: string
  parentCompanyId: string
  number: string
  type: string
  description: string
  totalDebit: number
  totalCredit: number
  transactionDate: string
  reference: string
  refDocId: string
  refDocNumber: string
  refDocType: string
  siteId: string
  projectId: string
  createdAt: string
  updatedAt: string
  createdBy: string
  lines: GeneralLedgerLineType[]
  createdByUser: UserOutlineType
}

export type JournalLineType = {
  id: number
  journalId: string
  accountId: string
  debit: number
  credit: number
  description: string
  accountBalance: number
  voucherNumber: string
  bankReconciliationId: string
  bankReconciliationNote: string
  createdAt: string
  updatedAt: string
  createdBy: string
  account: AccountType
  journal: {
    id: string
    companyId: string
    parentCompanyId: string
    number: string
    type: string
    description: string
    linesCount: number
    totalDebit: number
    totalCredit: number
    transactionDate: string
    status: string
    refDocType: string
    refDocId: string
    refDocNumber: string
    departmentId: string
    siteId: string
    projectId: string
    createdAt: string
    updatedAt: string
    createdBy: string
    lines: string
    department: DepartmentType
    site: SiteType
    project: ProjectType
    createdByUser: UserOutlineType
  }
  bankReconciliation: ReconciliationType
}

export type PaymentTermsType = {
  id: string
  name: string
  discountDays: number
  discountPercent: number
  dueDays: number
  remarks: string
  isDefault: boolean
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
}
