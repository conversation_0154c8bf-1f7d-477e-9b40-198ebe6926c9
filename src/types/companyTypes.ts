import { array, coerce, object, string } from 'zod'
import { UserOutlineType, UserType } from './userTypes'
import { CodeNameType } from './common'
import { StockType } from './mgTypes'

export const CompanySiteType = {
  WAREHOUSE: 'WAREHOUSE',
  WORKSHOP: 'WORKSHOP'
}

export type SiteType = CodeNameType & {
  address?: string
  email?: string
  phoneNumber?: string
  faxNumber?: string
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
}

export type DepartmentType = CodeNameType & {
  companyId: string
  subCompanyId: string
  createdAt: string
  updatedAt: string
  company?: CompanyType
  subCompany?: SubCompanyType
}

export interface CompanyType {
  id: string
  code?: string
  name: string
  status: string
  parentId: string
  createdAt: string
  updatedAt: string
  parent: string
  subsidiaries: string[]
}

export interface SubCompanyType {
  id: string
  code: string
  name: string
  status: string
  parentId: string
  createdAt: string
  updatedAt: string
  parent: CompanyType
  subsidiaries: string[]
}

export interface Site {
  id: string
  code: string
  name: string
  type?: 'WAREHOUSE' | 'WORKSHOP'
  [key: string]: any
}

export interface CategoryType {
  id: string
  type: string
  code: string
  name: string
  parentId: string
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
  parent?: string
}

export type CategoryPayload = {
  id?: string
  type: string
  code: string
  name: string
  parentId?: string
  companyId?: string
  children?: CategoryChildrenType[]
}

export type CategoryChildrenType = {
  code: string
  name: string
}

export interface ItemType {
  id: string
  number: string
  vendorNumber: string
  parentCode?: string
  name: string
  description: string
  brandName: string
  largeUnit: string
  smallUnit: string
  largeUnitQuantity: number
  categoryId: string
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
  category: CategoryType
  images: ImageType[]
  createdByUser: UserType
  stock?: number
  stocks?: StockType[]
  vendorId?: string
  taxType?: string
  taxPercentage?: number
  pricePerUnit?: number
  discountType?: string
  discountValue?: number
  isDiscountAfterTax?: boolean
  vendor?: VendorType
  [key: string]: any
}

export interface ImageType {
  id?: number
  uploadId?: string
  url?: string
  content?: string
  fileName?: string
}

export interface VendorType {
  id: string
  code: string
  name: string
  address: string
  email: string
  phoneNumber: string
  faxNumber: string
  taxplayerNumber: string
  categoryId: string
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
  paymentTerms: string
  paymentDueDays: number
  category: CategoryType
  createdByUser: UserType
  addresses: VendorAdressesType[]
}

export type VendorAdressesType = {
  id: string
  vendorId: string
  address: string
  phoneNumber: string
  faxNumber: string
  picName: string
  picPhoneNumber: string
  isDefault: boolean
  createdAt: string // ISO date string
  updatedAt: string // ISO date string
}

export enum DocumentType {
  BPKB = 'BPKB',
  STNK = 'STNK',
  KIR = 'KIR',
  SIO = 'SIO',
  INSURANCE_FIRST = 'INSURANCE_FIRST',
  INSURANCE_SECOND = 'INSURANCE_SECOND',
  INSURANCE_THIRD = 'INSURANCE_THIRD'
}

export interface DocumentUnitType {
  id: number
  unitId: string
  uploadId: string
  type: DocumentType
  name: string
  isActive: boolean
  url: string
  mimeType: string
  effectiveDate: string
  expirationDate: string
  renewalDate: string
  createdAt: string
  updatedAt: string
  unit: UnitType
}

export interface AssetImage {
  id: number
  unitId: string
  uploadId: string
  url: string
  createdAt: string
  updatedAt: string
}

export interface AssetType {
  id: string
  companyId: string
  parentCompanyId: string
  code: string
  name: string
  note: string
  status: string
}

export interface UnitType {
  id: string
  number: string
  asset: AssetType
  assetId: string
  condition: string
  equipmentType: string
  plateNumber: string
  engineNumber: string
  specification: string
  smType: string
  description: string
  brandName: string
  type: string
  ownerName: string
  hullNumber: string
  categoryId: string
  subCategoryId: string
  companyId: string
  documents: DocumentUnitType[]
  site: CodeNameType
  purchaseDate: string
  images: AssetImage[]
  parentCompanyId: string
  createdAt: string
  updatedAt: string
  category: CategoryType
  subCategory: CategoryType
  createdByUser: UserType
  status: string
  km?: number
  hm?: number
}

export type UnitLogType = {
  id: number
  unitId: string
  userId: string
  description: string
  kmBefore: number
  kmAfter: number
  kmDifference: number
  hmBefore: number
  hmAfter: number
  hmDifference: number
  createdAt: string
  user: UserOutlineType
}

export type UnitLogDocument = {
  id: number
  unitId: string
  uploadId: string
  type: string
  name: string
  isActive: true
  url: string
  mimeType: string
  effectiveDate: string
  expirationDate: string
  renewalDate: string
  createdAt: string
  updatedAt: string
  unit: string
}

export type ImageItemType = {
  content: string
  fileName?: string
  uploadId?: string
}

export const itemSchema = object({
  id: string().optional().nullable(),
  number: string().optional().nullable(),
  parentCode: string().optional().nullable(),
  vendorNumber: string().optional().nullable(),
  name: string().optional().nullable(),
  brandName: string().optional().nullable(),
  largeUnit: string().optional().nullable(),
  smallUnit: string().optional().nullable(),
  largeUnitQuantity: coerce.number().optional().nullable(),
  categoryId: string().nullable().optional(),
  category: object({
    id: string().optional().nullable(),
    type: string().optional().nullable(),
    code: string().optional().nullable(),
    name: string().optional().nullable(),
    parentId: string().optional().nullable(),
    companyId: string().optional().nullable(),
    parentCompanyId: string().optional().nullable(),
    createdAt: string().optional().nullable(),
    updatedAt: string().optional().nullable()
  })
    .nullable()
    .optional(),
  images: array(
    object({
      id: coerce.number().optional().nullable(),
      uploadId: string().optional().nullable(),
      url: string().optional().nullable()
    })
  )
    .optional()
    .nullable(),
  createdByUser: object({
    id: string().optional().nullable(),
    name: string().optional().nullable(),
    email: string().optional().nullable(),
    role: string().optional().nullable(),
    createdAt: string().optional().nullable(),
    updatedAt: string().optional().nullable()
  })
    .optional()
    .nullable(),
  stock: coerce.number().optional().nullable(),
  stocks: array(
    object({
      id: coerce.number().optional().nullable(),
      note: string().optional().nullable(),
      siteId: string().optional().nullable(),
      stock: coerce.number().optional().nullable()
    })
  )
    .optional()
    .nullable()
})

export const unitSchema = object({
  id: string().optional().nullable(),
  number: string().optional().nullable(),
  brandName: string().optional().nullable(),
  type: string().optional().nullable(),
  hullNumber: string().optional().nullable(),
  categoryId: string().optional().nullable(),
  subCategoryId: string().optional().nullable(),
  category: object({
    id: string().optional().nullable(),
    type: string().optional().nullable(),
    code: string().optional().nullable(),
    name: string().optional().nullable(),
    parentId: string().optional().nullable()
  })
    .optional()
    .nullable(),
  subCategory: object({
    id: string().optional().nullable(),
    type: string().optional().nullable(),
    code: string().optional().nullable(),
    name: string().optional().nullable(),
    parentId: string().optional().nullable()
  })
    .optional()
    .nullable(),
  createdByUser: object({
    id: string().optional().nullable(),
    name: string().optional().nullable(),
    role: string().optional().nullable(),
    createdAt: string().optional().nullable(),
    updatedAt: string().optional().nullable()
  }).optional()
})

export enum CarrierStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE'
}

export type CarrierType = {
  id: string
  name: string
  status: string
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
}
