import { GenericResponse } from './api'
import { WarehouseDataType } from './appTypes'
import { ItemType } from './companyTypes'

export type PurchaseTableColumns = {
  item: ItemType
  totalPurchase: number
  percentPurchase: number
  percentReceived: number
  received: number
  notReceived: number
  quantityUnit: string
  id: string
}

export type PurchaseDocsTable = {
  number: string
  document: WarehouseDataType
  orderQty: number
  unit: string
  received: number
  notReceived: number
  id: string
}

export type PurchaseSummaryType = {
  purchase: number
  received: number
  nonReceived: number
}

export type MgInStatisticsChartType = {
  id: string
  label: string
  value: number
}

export type StatisticParams = {
  endDate: string
  startDate: string
  siteIds?: string
  departmentId?: string
  format?: string
  period?: string
}

export interface MgInStatistics extends GenericResponse {
  totalPurchase: number
  totalReceived: number
  totalRemaining: number
}
export interface MgOutStatistics extends GenericResponse {
  totalOutgoing: number
}

export interface PurchaseOrderStatisticsType {
  id: string
  label: string
  totalPurchase: number
  totalReceived: number
  totalRemaining: number
}

export interface OutgoingStatisticsType {
  id: string
  label: string
  value: number
}
export interface PurchaseDocumentListType {
  id: string
  number: string
  receivedQuantity: number
  remainingQuantity: number
  smallQuantity: number
}

export interface OutgoingDocumentListType {
  id: string
  number: string
  quantity: number
  quantityUnit: string
  isLargeQuantity: number
  largeUnitQuantity: number
  smallQuantity: number
  stock: number
  outQuantity: number
}

export type PurchasingSummaryValuesType = {
  id: string
  label: string
  value: number
}

export type PurchasingSummaryType = {
  totalPurchase: number
  totalReceived: number
  totalRemaining: number
  updatedAt: string
}

export type PurchaseByItemType = {
  id: string
  label: string
  totalPurchase: number
  totalReceived: number
  totalRemaining: number
}
