import { ListParams } from './payload'

export type CodeType = {
  id: number
  companyId: string
  parentCompanyId: string
  type: string
  code: string
  description: string
  usage: string
  newLifeRecognition: boolean
  isUseStock: boolean
  createdAt: string
  updatedAt: string
  [key: string]: any
}

type CodeTypeParams = 'FAMILY' | 'JOB' | 'COMPONENT' | 'MODIFIER'

export type CodeParams = ListParams & {
  type: CodeTypeParams
}

export type CodeDto = {
  code: string
  description: string
  usage: string
  newLifeRecognition: boolean
  isUseStock?: boolean
  codeFamilyIds: number[]
}

export type CodePayload = {
  type: CodeTypeParams
  codes: Array<CodeDto>
  companyId: string
}
