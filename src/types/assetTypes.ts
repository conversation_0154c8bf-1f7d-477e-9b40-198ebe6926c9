import { ImageType, UnitType } from './companyTypes'
import { CurrenciesType } from './currenciesTypes'
import { UserOutlineType } from './userTypes'

// Helper type for ownerCompany
export type CompanyOutlineType = {
  id?: string
  code?: string
  name?: string
}

// Helper type for ownerSite, to match the example exactly
export type AssetSiteType = {
  id?: string
  code?: string
  name?: string
  type?: string
}

// Helper type for picDepartment
export type DepartmentOutlineType = {
  id?: string
  code?: string
  name?: string
}

// Helper type for the various account fields
export type AssetAccountType = {
  id?: string
  companyId?: string
  parentCompanyId?: string
  accountTypeId?: number
  parentId?: string
  code?: string
  name?: string
  level?: number
  balance?: number
  note?: string
  status?: string
  balanceAt?: string
  parent?: string
  accountType?: {
    id?: number
    companyId?: string
    parentCompanyId?: string
    code?: string
    name?: string
  }
}

export type AssetType = {
  id?: string
  companyId?: string
  parentCompanyId?: string
  ownerCompanyId?: string
  ownerSiteId?: string
  picId?: string
  picDepartmentId?: string
  currencyId?: string
  assetAccountId?: string
  depreciationAccountId?: string
  expenseAccountId?: string
  parentId?: string
  code?: string
  name?: string
  type?: string
  subType?: string
  note?: string
  baseValue?: number
  exchangeRate?: number
  lcValue?: number
  depreciationMethod?: string
  status?: string
  purchasedDate?: string
  usedDate?: string
  ownerCompany?: CompanyOutlineType
  ownerSite?: AssetSiteType
  pic?: UserOutlineType
  picDepartment?: DepartmentOutlineType
  currency?: CurrenciesType
  assetAccount?: AssetAccountType
  depreciationAccount?: AssetAccountType
  expenseAccount?: AssetAccountType
  createdByUser?: UserOutlineType
  createdAt?: string
  updatedAt?: string
  isParent?: boolean
  unit?: UnitType
  parent?: Omit<AssetType, 'parent'>
  children?: Omit<AssetType, 'children'>[]
  images?: ImageType[]
}

// This was part of the original request
export type AssetPayload = {
  code?: string
  name?: string
  ownerCompanyId?: string
  ownerSiteId?: string
  picId?: string
  type?: string
  subType?: string
  unitId?: string
  purchasedDate?: string
  usedDate?: string
  currencyId?: string
  baseValue?: number
  exchangeRate?: number
  depreciationMethod?: string
  note?: string
  assetAccountId?: string
  depreciationAccountId?: string
  expenseAccountId?: string
  parentId?: string
  images?: {
    uploadId?: string
  }[]
}

export type AssetMobilizationPayload = {
  siteId?: string
  picId?: string
}

export type AssetMobilization = {
  id?: number
  assetId?: string
  previousSiteId?: string
  currentSiteId?: string
  previousPicId?: string
  currentPicId?: string
  createdAt?: string
  previousSite?: AssetSiteType
  currentSite?: AssetSiteType
  previousPic?: UserOutlineType
  currentPic?: UserOutlineType
}

// Asset Dispose Types
export type AssetDisposePayload = {
  reason?: string
  disposalDate?: string
  disposalValue?: number
  note?: string
}

export type AssetDisposeType = {
  id?: string
  assetId?: string
  reason?: string
  disposalDate?: string
  disposalValue?: number
  note?: string
  createdAt?: string
  createdByUser?: UserOutlineType
}

// Asset Value Log Types
export type AssetValueLogType = {
  id?: number
  assetId?: string
  previousValue?: number
  currentValue?: number
  changeReason?: string
  changeDate?: string
  note?: string
  createdAt?: string
  createdByUser?: UserOutlineType
  currency?: CurrenciesType
  type?: string
}

export type AssetValueLogDetailType = {
  id?: number
  assetId?: string
  previousValue?: number
  currentValue?: number
  changeReason?: string
  changeDate?: string
  note?: string
  createdAt?: string
  updatedAt?: string
  createdByUser?: UserOutlineType
  currency?: CurrenciesType
  asset?: AssetType
}
