import { PoType } from '@/pages/purchase-order/config/types'
import { WarehouseItemType } from './appTypes'
import { DepartmentType, ItemType, SiteType, UnitType, VendorType } from './companyTypes'
import { ApproverType, UserOutlineType } from './userTypes'
import { ListParams, UserIdPayload } from './payload'

export enum PurchaseInvoiceStatus {
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED',
  CANCEL_REQUESTED = 'CANCEL_REQUESTED',
  CLOSED = 'CLOSED'
}

export enum PurchaseInvoiceApprovalStatus {
  PENDING = 'PENDING',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export enum PurchaseInvoiceLogType {
  ACTIVITY = 'ACTIVITY',
  ITEM_FLOW = 'ITEM_FLOW'
}

export enum PurchaseInvoiceLogStatus {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCEL_REQUESTED = 'CANCEL_REQUESTED',
  CANCEL_APPROVED = 'CANCEL_APPROVED',
  CANCEL_REJECTED = 'CANCEL_REJECTED',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
  ITEM_UPDATED = 'ITEM_UPDATED',
  APPROVAL_APPROVED = 'APPROVAL_APPROVED',
  APPROVAL_REJECTED = 'APPROVAL_REJECTED',
  APPROVAL_UPDATED = 'APPROVAL_UPDATED'
}

export enum PurchaseInvoiceTaxType {
  NON_TAX = 'NON_TAX',
  INCLUDE_TAX = 'INCLUDE_TAX',
  EXCLUDE_TAX = 'EXCLUDE_TAX'
}

export enum PurchaseInvoiceDiscountType {
  PERCENTAGE = 'PERCENTAGE',
  FLAT = 'FLAT'
}

export enum PurchaseInvoicePaymentMethod {
  CASH = 'CASH',
  TRANSFER = 'TRANSFER',
  CHECK = 'CHECK',
  CREDIT = 'CREDIT'
}

export type PurchaseInvoiceCurrency = {
  id: string
  code: string
  name: string
  symbol: string
  isDefault: boolean
  companyId: string
  parentCompanyId: string
  createdAt: string
  updatedAt: string
}

export type PurchaseInvoiceItem = {
  id: number
  purchaseInvoiceId: string
  itemId: string
  incomingMaterialItemId?: number
  quantity: number
  quantityUnit: string
  isLargeUnit: boolean
  largeUnitQuantity: number
  smallQuantity: number
  note?: string
  pricePerUnit: number
  subtotalPrice: number
  taxType: string
  taxPercentage: number
  taxAmount: number
  discountType?: string
  discountValue?: number
  discountAmount?: number
  isDiscountAfterTax?: boolean
  subtotalDiscount?: number
  totalPrice: number
  lcTotalPrice: number
  createdAt: string
  updatedAt: string
  deletedAt?: string
  createdBy?: string
  updatedBy?: string
  deletedBy?: string
  item?: ItemType
  unit?: UnitType
  incomingMaterialItem?: WarehouseItemType
}

export type PurchaseInvoiceItemPayload = {
  id?: number
  itemId?: string
  incomingMaterialItemId?: number
  quantity?: number
  quantityUnit?: string
  unitId?: string
  note?: string
  pricePerUnit?: number
  taxType?: PurchaseInvoiceTaxType
  taxPercentage?: number
  discountType?: PurchaseInvoiceDiscountType
  discountValue?: number
  isDiscountAfterTax?: boolean
  largeUnitQuantity?: number
}

export type PurchaseInvoiceOrder = {
  id: number
  purchaseInvoiceId: string
  purchaseOrderId: string
  incomingMaterialId: string
  appliedPurchaseInvoiceOrderId: string | null
  subTotalAmount: number
  downPaymentAmount: number
  totalAmount: number
  lcTotalAmount: number
  itemsCount: number
  isDownPayment: boolean
  purchaseOrder: {
    id: string
    number: string
    lcGrandTotal: number
  }
  incomingMaterial: {
    id: string
    number: string
  }
  items: {
    id: number
    pricePerUnit: number
    quantity: number
    totalAmount: number
    item: ItemType
  }[]
}

export type PurchaseInvoiceExpense = {
  accountId: string
  amount: number
  note?: string
  account?: {
    code?: string
    name?: string
    id?: string
  }
}

export type PurchaseInvoiceApproval = {
  id: number
  purchaseInvoiceId: string
  userId: string
  status: PurchaseInvoiceApprovalStatus
  note?: string
  isRead: boolean
  readAt?: string
  approvedAt?: string
  rejectedAt?: string
  createdAt: string
  updatedAt: string
  user: UserOutlineType
} & ApproverType

export type PurchaseInvoiceLog = {
  id: number
  type: PurchaseInvoiceLogType
  status: PurchaseInvoiceLogStatus
  changes?: string
  attachmentUrl?: string
  attachmentMimeType?: string
  purchaseInvoiceId: string
  userId: string
  purchaseInvoiceItemId?: number
  createdAt: string
  user: UserOutlineType
  purchaseInvoiceItem?: PurchaseInvoiceItem
}

export type PurchaseInvoice = {
  id: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  projectId?: string
  vendorId: string
  currencyId: string
  number: string
  vendorNumber?: string
  vendorName?: string
  subTotalAmount: number
  otherAmount: number
  discountType: PurchaseInvoiceDiscountType
  discountValue: number
  discountAmount: number
  totalAmount: number
  lcTotalAmount: number
  status: PurchaseInvoiceStatus
  note?: string
  exchangeRate: number
  approvalsCount: number
  documentUrl?: string
  documentMimeType?: string
  isGeneralPurchase: boolean
  isDownPayment: boolean
  paymentTerms: string
  paymentDueDays: number
  paymentDueDate?: string
  invoiceDate: string
  createdAt: string
  updatedAt: string
  approvedAt?: string
  paidAt?: string
  approvals: PurchaseInvoiceApproval[]
  orders: PurchaseInvoiceOrder[]
  otherExpense: PurchaseInvoiceExpense[]
  currency: PurchaseInvoiceCurrency
  vendor: VendorType
  department: DepartmentType
  site: SiteType
  createdByUser: UserOutlineType
}

export type PurchaseInvoicePayload = {
  piId?: string
  vendorId: string
  paymentTerms: string
  paymentDueDays: number
  vendorNumber?: string
  vendorName?: string | null
  invoiceDate: string
  note?: string
  isDownPayment: boolean
  currencyId: string
  exchangeRate: number
  documentUploadId?: string
  isGeneralPurchase: boolean
  approvals: {
    userId: string
  }[]
  discountType: PurchaseInvoiceDiscountType
  discountValue: number
  orders: PurchaseInvoiceOrder[]
  otherExpenses?: PurchaseInvoiceExpense[]
  departmentId: string | null
  siteId: string | null
  projectId: string | null
}

export type PurchaseInvoiceParams = {
  endDate?: string
  startDate?: string
  isClosed?: boolean
  status?: PurchaseInvoiceStatus
  vendorId?: string
  purchaseOrderId?: string
  invoiceNumber?: string
  isDownPayment?: boolean
} & ListParams

export type PurchaseInvoiceApprovalPayload = {
  piId?: string
  approvalId?: number
  userId?: string
  note?: string
  status?: PurchaseInvoiceApprovalStatus
  isRead?: boolean
}

export type CancelPurchaseInvoicePayload = {
  piId: string
  cancelationType: string
  approvals: UserIdPayload[]
  cancelationProofUploadId: string
  cancelationNote: string
}

export type PurchaseInvoiceDownPayment = {
  id: string
  purchaseInvoiceId: string
  purchaseOrderId: string
  incomingMaterialId?: string
  appliedPurchaseInvoiceOrderId?: string
  subTotalAmount: number
  downPaymentAmount: number
  totalAmount: number
  lcTotalAmount: number
  itemsCount: number
  isDownPayment: boolean
  purchaseInvoice: {
    id: string
    number: string
    invoiceDate: string
  }
}

// DTO Type for form handling
export type PurchaseInvoiceDtoType = {
  // Vendor Information
  vendorId: string

  // Invoice Details
  invoiceNumber: string
  invoiceDate: string

  // Payment Terms
  paymentMethod: string
  paymentDueDate?: string

  // Down Payment
  isDownPayment: boolean

  // Memo
  note?: string

  // Purchase Order (optional)
  purchaseOrderId?: string

  // Currency and Exchange Rate
  currencyId: string
  exchangeRate: number
}
