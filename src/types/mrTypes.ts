import { WarehouseDataType, WarehouseItemType } from './appTypes'
import { DepartmentType, ImageType, ItemType, SiteType, UnitType } from './companyTypes'
import { ImagePayload, ListParams, UserIdPayload } from './payload'
import { ApproverType, UserType } from './userTypes'
import { WorkOrderType, WoSegmentType } from './woTypes'

export enum MrPrStatus {
  NCREATED = 'NCREATED',
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED'
}

export enum MrStatus {
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED',
  CLOSED = 'CLOSED',
  CANCEL_REQUESTED = 'CANCEL_REQUESTED'
}

export enum MrUserStatus {
  PENDING = 'PENDING',
  WAITING = 'WAITING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

export enum MrLogTypeStatus {
  CREATED = 'CREATED',
  ITEM_UPDATED = 'ITEM_UPDATED',
  UPDATED = 'UPDATED',
  CLOSED = 'CLOSED',
  CANCELED = 'CANCELED',
  APPROVAL_APPROVED = 'APPROVAL_APPROVED',
  APPROVAL_REJECTED = 'APPROVAL_REJECTED',
  APPROVAL_UPDATED = 'APPROVAL_UPDATED',
  ITEM_RECEIVED = 'ITEM_RECEIVED',
  ITEM_OUT = 'ITEM_OUT'
}

export type MrType = {
  purchaseRequisitionStatus: string
  items: MrItemType[]
  outgoingMaterialsCount: number
  itemsStock: number
  createdDocumentsCount?: number
  workOrderSegment: WoSegmentType & { workOrder: WorkOrderType }
} & WarehouseDataType

export type MrItemPayload = {
  id?: number
  itemId?: string
  quantity?: number
  quantityUnit?: string
  unitId?: string
  unitKm?: number
  unitHm?: number
  note?: string
  largeUnitQuantity?: number
  images?: ImagePayload[]
}

export type MrPayload = {
  mrId?: string
  items?: MrItemPayload[]
  approvals?: UserIdPayload[]
  siteId?: string
  projectId?: string
  note?: string
  unitId?: string
  unitKm?: number
  unitHm?: number
}

export type MrParams = {
  endDate?: string
  startDate?: string
  isClosed?: boolean
  status?: MrStatus
  projectId?: string
  isInStock?: boolean
  priority?: string
  workOrderSegmentId?: string
  workOrderId?: string
} & ListParams

export type MrApprovalPayload = {
  mrId?: string
  approvalId?: number
  userId?: string
  note?: string
  status?: MrUserStatus
  isRead?: boolean
}

export type MrLogType = {
  id: number
  type: string
  status: string
  changes: string
  attachmentUrl: string
  attachmentMimeType: string
  materialRequestId: string
  userId: string
  materialRequestItemId: number
  createdAt: string
  user: UserType
  materialRequestItem: MrItemType
}

export type MrItemType = {
  materialRequestId?: string
  recordedStock?: number
  receipts?: WarehouseItemType[]
} & WarehouseItemType

export type MrDetailType = {
  id: string
  number: string
  itemsCount: number
  approvalsCount: number
  note: string
  cancelationNote: any
  status: string
  purchaseRequisitionStatus: string
  createdDocumentsCount?: number
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  isClosed: boolean
  closedAt: any
  createdBy: string
  closedBy: any
  createdAt: string
  updatedAt: string
  items: MrItemType[]
  department: DepartmentType
  site: SiteType
  createdByUser: UserType
  approvals: ApproverType[]
}

export type CancelMrPayload = {
  mrId: string
  cancelationNote: string
}
