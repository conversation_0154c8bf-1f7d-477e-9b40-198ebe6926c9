import { List } from 'postcss/lib/list'
import { AccountsRelatedPayload, CarrierStatus, DocumentType } from './companyTypes'

export type LoginPayload = {
  companyCode: string
  username: string
  password: string
  clientId?: string
}

export type CheckRefCodePayload = {
  referralCode?: string
}

export type RequestOtpPayload = {
  email: string
} & CheckRefCodePayload

export type VerifyOtpPayload = {
  otp: string
} & RequestOtpPayload

export type RegisterPayload = {
  key: string
  username: string
  password: string
  retypePassword: string
}

export type AuthState = {
  [key: string]: any
  companyCode?: string
  referralCode?: string | null
  key?: string | null
  email?: string | null
  token?: string | null
  refreshToken?: string | null
  authenticated?: boolean
  registered?: boolean
  keyCreatedAt?: string
}

export type AssetLoaderState = {
  open: boolean
  onComplete?: () => void
}

export type ResetPasswordPayload = {
  companyCode: string
  username: string
  password: string
  verificationCode: string
}

export interface PromptModalProps {
  open: boolean
  message?: string
  onClose?: () => void
  onConfirm?: () => void
}

export type ListParams = {
  limit?: number
  page?: number
  search?: string
  status?: string
  userStatus?: string
  subCompanyId?: string
  departmentId?: string
  companyId?: string
  type?: string
  parentId?: string
  siteId?: string
  startDate?: string
  endDate?: string
  siteIds?: string
  priority?: string
  parentCode?: string
  unitId?: string
  materialRequestItemId?: number
  isApproved?: boolean
  types?: string
  sort?: string[]
  isParent?: boolean
}

export type PermissionParams = {
  isHidden?: boolean
} & ListParams

export type UserParams = {
  hasPermission?: string
  permissionGroupId?: string
  departmentId?: string
} & ListParams

export type UserPayload = {
  userId?: string
  fullName: string
  email: string
  departmentId: string
  permissionGroupId: string
  title?: string
  phoneNumber?: string
  role: string
  siteIds: string[]
  status: string
  divisionId?: string
}

export type RolePayload = {
  roleId?: string
  name: string
  description?: string
  permissionIds: string[]
}

export type ApproverParams = {
  scope?: string
  siteId?: string
} & ListParams

export type ItemParams = {
  itemId?: string
  vendorId?: string
  categoryId?: string
  vendorNumber?: string
  number?: string
  isInStock?: boolean
} & ListParams

export type DocumentParams = {
  startDate?: string
  endDate?: string
} & ListParams & {
    types: string[]
  }

export type DefaultApprovalPayload = {
  scope: string
  siteId: string
  departmentId: string
  userIds: string[]
  thresholds?: number[]
}

export type ItemPayload = {
  itemId?: string
  number: string
  vendorNumber: string
  name: string
  description?: string
  brandName: string
  largeUnit: string
  smallUnit: string
  largeUnitQuantity: number
  images?: ImagePayload[]
  categoryId: string
  stockId?: number
  stockNote?: string
  vendorId?: string
  taxType?: string
  taxPercentage?: number
  pricePerUnit?: number
  discountType?: string
  discountValue?: number
  isDiscountAfterTax?: boolean
  accounts?: AccountsRelatedPayload
}

export interface ImagePayload {
  uploadId?: string
}

export interface UploadPayload {
  fieldName: string
  scope: 'public-image' | 'profile-picture' | 'public-document'
  file: string
  fileName: string
}

export type VendorPayload = {
  vendorId?: string
  code: string
  name: string
  address: string
  email?: string
  phoneNumber: string
  faxNumber?: string
  taxplayerNumber: string
  categoryId: string
}

export type VendorAddressPayload = {
  address: string
  phoneNumber: string
  faxNumber: string
  picName: string
  picPhoneNumber: string
  isDefault: boolean
}

export type DocumentPayload = {
  type: string
  name: string
  content?: string
  effectiveDate: string
  expirationDate: string
  renewalDate: string
} & ImagePayload

export type UnitPayload = {
  unitId?: string
  number: string
  description?: string
  brandName: string
  type: string
  hullNumber: string
  categoryId: string
  subCategoryId: string
  documents?: DocumentPayload[]
  images?: ImagePayload[]
}

export type SitePayload = {
  siteId?: string
  code: string
  name: string
  address: string
  email?: string
  phoneNumber?: string
  faxNumber?: string
  companyId?: string
  type?: string
}

export type DepartmentPayload = {
  departmentId?: string
  code: string
  name: string
  companyId?: string
}

export interface UserIdPayload {
  userId: string
}

export type SalaryPayload = {
  name: string
  salaryTypeId: number
  accountId: string
  paymentFrequency: 'MONTHLY' | 'OTHER'
}

export type AccountPayload = {
  code: string
  name: string
  accountTypeId: number
  parentId: string
  balance: number
  balanceAt: string
  note: string
}

export type TaxPayload = {
  name: string
  taxTypeId: number
  taxSubTypeId: number
  inputTaxAccountId: string
  outputTaxAccountId: string
  percentage: number
}

export type CustomerPayload = {
  name: string
  address: string
  email: string
  phoneNumber: string
  faxNumber: string
  taxplayerNumber: string
  picName: string
  picPhoneNumber: string
}

export type ProjectPayload = {
  code: string
  name: string
  siteId: string
}

export type CarrierPayload = {
  name: string
  status: CarrierStatus
  companyId: string
}

export type DivisionPayload = {
  departmentId: string
  divisions: {
    code: string
    name: string
  }[]
  companyId: string
}

export type UpdateDivisionPayload = {
  code: string
  name: string
}

export type CompanyPayload = {
  companyId?: string
  code?: string
  name: string
  status: string
  parentId?: string
}

export type GeneralPurchaseInvoicePayload = {
  invoiceDate: string
  invoices: { uploadId: string; url?: string; mimeType?: string }[]
}

export type StuffReqItemPayload = {
  workOrderSegmentId: string
  itemId: string
  stuffRequestItemId?: number
  stuffRequestId?: string
  serialNumber?: string
  quantity: number
  returnQuantity?: number
  quantityUnit: string
  largeUnitQuantity?: number
  note?: string
}

export type StuffReqApprovalPayload = {
  userId: string
}

export type StuffReqPayload = {
  workProcessId: string
  type: 'TAKE' | 'RETURN'
  items: StuffReqItemPayload[]
  approvals: UserIdPayload[]
  note?: string
}

export enum GeneralLedgerType {
  GENERAL = 'GENERAL',
  SALES_TRANSACTION = 'SALES_TRANSACTION',
  PURCHASE_TRANSACTION = 'PURCHASE_TRANSACTION',
  CASH_RECEIPT = 'CASH_RECEIPT',
  CASH_PAYMENT = 'CASH_PAYMENT',
  ADJUSTING_ENTRIES = 'ADJUSTING_ENTRIES',
  OTHER_TRANSACTION = 'OTHER_TRANSACTION'
}

export type GeneralLedgerLineType = 'DEBIT' | 'CREDIT'

export type GeneralLedgerPayload = {
  type: GeneralLedgerType
  description: string
  transactionDate: string
  lines: {
    accountId: string
    type: GeneralLedgerLineType
    amount: number
    description: string
  }[]
  siteId?: string
  projectId?: string
}

export type CreatePaymentTermsPayload = {
  name: string
  discountDays: number
  discountPercent: number
  dueDays: number
  remarks: string
  isDefault: boolean
  companyId?: string
}
