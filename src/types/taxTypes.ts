import { ListParams } from './payload'

export type TaxType = {
  id: string
  companyId: string
  parentCompanyId: string
  taxTypeId: number
  taxSubTypeId: number
  inputTaxAccountId: string
  outputTaxAccountId: string
  name: string
  percentage: number
  status: string
  taxType: TaxTypeMaster
  taxSubType: TaxTypeMaster
  inputTaxAccount: TaxAccountType
  outputTaxAccount: TaxAccountType
}

export type TaxTypeMaster = {
  id: number
  companyId: string
  parentCompanyId: string
  parentId: string
  code: string
  name: string
}

export type TaxAccountType = {
  id: string
  companyId: string
  parentCompanyId: string
  accountTypeId: number
  parentId: string
  code: string
  name: string
  level: number
  balance: number
  note: string
  status: string
  balanceAt: string
  parent: string
}

export type TaxTypeParams = {
  taxSubTypeIds?: string
  taxTypeIds?: string
  status?: 'ACTIVE' | 'INACTIVE'
} & ListParams
