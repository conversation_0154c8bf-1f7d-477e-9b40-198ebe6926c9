import { CodeNameType } from './common'
import { ListParams } from './payload'
import { UserOutlineType } from './userTypes'

export enum ProjectStatus {
  ACTIVE = 'ACTIVE',
  FINISHED = 'FINISHED'
}

export type ProjectType = {
  id: string
  companyId: string
  parentCompanyId: string
  siteId: string
  code: string
  name: string
  status: string
  materialRequestsCount: number
  purchaseOrdersCount: number
  inboundDocumentsCount: number
  outboundDocumentsCount: number
  requirementAmount: number
  inboundAmount: number
  outboundAmount: number
  isEditable: boolean
  createdAt: string
  updatedAt: string
  finishedAt: string
  createdBy: string
  site: CodeNameType
  createdByUser: UserOutlineType
}

export type ProjectParams = {
  status?: ProjectStatus
} & ListParams
