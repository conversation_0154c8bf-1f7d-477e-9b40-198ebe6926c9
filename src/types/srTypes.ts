import { CodeNameType } from './common'
import { ImItem } from './mgTypes'
import { MrType, MrUserStatus } from './mrTypes'
import { ImagePayload, ListParams, UserIdPayload } from './payload'
import { ApproverType, UserOutlineType } from './userTypes'

export enum SrStatus {
  PROCESSED = 'PROCESSED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  CANCELED = 'CANCELED'
}

export type SrItemPayload = {
  materialRequestItemId: number
  isActive?: boolean
  materialRequestItemReceiptId?: number
  quantity?: number
  quantityUnit?: string
  note?: string
  largeUnitQuantity?: number
  receipts?: SrItemReceiptPayload[]
}

export type SrItemReceiptPayload = {
  materialRequestItemReceiptId?: number
  quantity?: number
  quantityUnit?: string
  largeUnitQuantity?: number
  note?: string
  isActive?: boolean
}

export type SrPayload = {
  materialRequestId: string
  items: SrItemPayload[]
  approvals: UserIdPayload[]
  note?: string
  hasBeenOut?: boolean
}

export type SrType = {
  id: string
  number: string
  itemsCount: number
  approvalsCount: number
  cancelationNote: string
  status: string
  companyId: string
  parentCompanyId: string
  departmentId: string
  siteId: string
  createdAt: string
  updatedAt: string
  createdBy: string
  materialRequest: MrType
  items: ImItem[] //to be checked
  approvals: ApproverType[] //to be checked
  department: CodeNameType
  site: CodeNameType
  hasBeenOut?: boolean
  createdByUser: UserOutlineType
}

export type SrApprovalPayload = {
  stockReturnId?: string
  approvalId?: number
  userId?: string
  note?: string
  status?: MrUserStatus
  isRead?: boolean
}

export type SrParams = ListParams & {
  endDate?: string
  startDate?: string
  status?: SrStatus
  userStatus?: MrUserStatus
  departmentId?: string
  siteId?: string
  materialRequestId?: string
}

export type SrItem = {
  itemId: string
  serialNumberId?: number
  quantity: number
  quantityUnit: string
  largeUnitQuantity?: number
  note?: string
  images?: ImagePayload[]
}

export type SrRmOPayload = {
  type: 'INTERNAL' | 'EXTERNAL' | 'VENDOR'
  workOrderSegmentId: string
  items: SrItem[]
  approvals: ApproverType[]
  note: string
  priority: number
  documentNumber: string
  documentUploadId: string
  documentNote: string
  vendorId: string
  siteId?: string
  originSiteId: string
}
