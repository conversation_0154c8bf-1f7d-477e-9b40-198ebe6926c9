import { ListParams } from './payload'

export type SalaryStatus = 'ACTIVE' | 'INACTIVE'
export type SalaryParams = {
  salaryTypeIds?: string
  status?: SalaryStatus
} & ListParams

export type SalaryType = {
  id: string
  companyId: string
  parentCompanyId: string
  salaryTypeId: number
  accountId: string
  name: string
  paymentFrequency: string
  status: string
  salaryType: {
    id: number
    companyId: string
    parentCompanyId: string
    code: string
    name: string
  }
  account: {
    id: string
    companyId: string
    parentCompanyId: string
    accountTypeId: number
    parentId: string
    code: string
    name: string
    level: number
    balance: number
    note: string
    status: string
    balanceAt: string // If this is an ISO date string, you might keep it as a string.
    parent: string
  }
}

export type SalaryTypeMaster = {
  id: number
  companyId: string
  parentCompanyId: string
  code: string
  name: string
  createdAt: string
  updatedAt: string
}
