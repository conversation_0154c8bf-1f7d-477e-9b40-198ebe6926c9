import { ApiResponse } from '@/types/api'
import { GeneralPurchaseType } from '@/types/appTypes'
import { GeneralPurchaseInvoicePayload } from '@/types/payload'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import GeneralPurchaseService from './service'

export const useUploadInvoice = (): UseMutationResult<
  ApiResponse<GeneralPurchaseType>,
  Error,
  GeneralPurchaseInvoicePayload & { id: string },
  void
> => {
  return useMutation({
    mutationFn: ({ id, ...payload }: GeneralPurchaseInvoicePayload & { id: string }) =>
      GeneralPurchaseService.uploadInvoice({ id, ...payload })
  })
}
