import request from '@/api/request'
import { ApiResponse } from '@/types/api'
import { GeneralPurchaseType } from '@/types/appTypes'
import { GeneralPurchaseInvoicePayload } from '@/types/payload'

const BASE_URL_GENERAL_PURCHASE = 'general-purchases'

export default class GeneralPurchaseService {
  public static readonly uploadInvoice = ({
    id,
    ...payload
  }: GeneralPurchaseInvoicePayload & { id: string }): Promise<ApiResponse<GeneralPurchaseType>> => {
    return request({
      url: `${BASE_URL_GENERAL_PURCHASE}/${id}/upload-invoices`,
      instance: 'CORE',
      method: 'PATCH',
      data: payload
    })
  }
}
