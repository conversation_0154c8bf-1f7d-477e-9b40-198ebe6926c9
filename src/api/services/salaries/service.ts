import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { ListParams, SalaryPayload } from '@/types/payload'
import { SalaryParams, SalaryType, SalaryTypeMaster } from '@/types/salaryTypes'

export const BASE_URL_SALARIES = 'salaries'

export default class SalaryService {
  public static readonly getSalaryList = (params?: SalaryParams): Promise<ApiResponse<ListResponse<SalaryType>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_SALARIES}`,
      instance: 'CORE',
      params
    })
  }

  public static readonly getTypeSalary = (
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<SalaryTypeMaster>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_SALARIES}/master-data/types`,
      instance: 'CORE',
      params
    })
  }

  public static readonly createSalary = (payload: SalaryPayload): Promise<ApiResponse<SalaryType>> => {
    return request({
      method: 'POST',
      url: `${BASE_URL_SALARIES}`,
      instance: 'CORE',
      data: payload
    })
  }

  public static readonly updateSalary = (payload: { id: string } & SalaryPayload): Promise<ApiResponse<SalaryType>> => {
    return request({
      method: 'PATCH',
      url: `${BASE_URL_SALARIES}/${payload.id}`,
      instance: 'CORE',
      data: payload
    })
  }

  public static readonly deleteSalary = (id: string): Promise<ApiResponse<any>> => {
    return request({
      method: 'DELETE',
      url: `${BASE_URL_SALARIES}/${id}`,
      instance: 'CORE'
    })
  }
}
