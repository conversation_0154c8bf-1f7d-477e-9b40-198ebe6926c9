import { ApiResponse } from '@/types/api'
import { SalaryPayload } from '@/types/payload'
import { SalaryType } from '@/types/salaryTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import SalaryService from './service'

export const useCreateSalary = (): UseMutationResult<ApiResponse<SalaryType>, Error, SalaryPayload, void> => {
  return useMutation({
    mutationFn: (payload: SalaryPayload) => SalaryService.createSalary(payload)
  })
}

export const useUpdateSalary = (): UseMutationResult<
  ApiResponse<SalaryType>,
  Error,
  { id: string } & SalaryPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: { id: string } & SalaryPayload) => SalaryService.updateSalary(payload)
  })
}

export const useDeleteSalary = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => SalaryService.deleteSalary(id)
  })
}
