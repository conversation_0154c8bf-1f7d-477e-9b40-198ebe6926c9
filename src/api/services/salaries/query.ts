import { ListResponse } from '@/types/api'
import { SalaryParams, SalaryType, SalaryTypeMaster } from '@/types/salaryTypes'
import SalaryService from './service'
import { ListParams } from '@/types/payload'

export const SALARY_TYPE_QUERY_KEY = 'SALARY_TYPE_QUERY_KEY'
export const SALARY_LIST_QUERY_KEY = 'SALARY_LIST_QUERY_KEY'

export default class SalaryQueryMethods {
  public static readonly getSalaryTypeList = async (params?: ListParams): Promise<ListResponse<SalaryTypeMaster>> => {
    return (await SalaryService.getTypeSalary(params)).data
  }

  public static readonly getSalaryList = async (params?: SalaryParams): Promise<ListResponse<SalaryType>> => {
    return (await SalaryService.getSalaryList(params)).data
  }
}
