import { PaymentParams, PaymentType } from '@/pages/cash-bank/payments/config/types'
import { ListResponse } from '@/types/api'
import CashBankService from './service'
import { ApprovalsCountType } from '@/types/appTypes'
import { CashReceiptParams, CashReceiptType } from '@/pages/cash-bank/receipt/config/types'
import { ReconciliationType } from '@/pages/cash-bank/reconsiliation/config/types'
import { ListParams } from '@/types/payload'

export const PAYMENT_QUERY_LIST_KEY = 'PAYMENT_QUERY_LIST_KEY'
export const PAYMENT_QUERY_KEY = 'PAYMENT_QUERY_KEY'
export const RECEIPT_QUERY_LIST_KEY = 'RECEIPT_QUERY_LIST_KEY'
export const RECEIPT_QUERY_KEY = 'RECEIPT_QUERY_KEY'
export const RECONCILIATION_LIST_QUERY_KEY = 'RECONCILIATION_LIST_QUERY_KEY'
export const RECONCILIATION_QUERY_KEY = 'RECONCILIATION_QUERY_KEY'

export default class CashBankQueryMethods {
  public static getPaymentsList = async (params: PaymentParams): Promise<ListResponse<PaymentType>> => {
    return (await CashBankService.getPaymentsList(params)).data
  }

  public static getPayment = async (id: string): Promise<PaymentType> => {
    return (await CashBankService.getPayment(id)).data
  }

  public static getPaymentToMe = async (params: PaymentParams): Promise<ListResponse<PaymentType>> => {
    return (await CashBankService.getPaymentToMe(params)).data
  }

  public static getCountApprovals = async (): Promise<ApprovalsCountType> => {
    return (await CashBankService.getCountApprovals()).data
  }

  public static getCashReceiptList = async (params: CashReceiptParams): Promise<ListResponse<CashReceiptType>> => {
    return (await CashBankService.getCashReceiptList(params)).data
  }

  public static getCashReceiptToMe = async (params: CashReceiptParams): Promise<ListResponse<CashReceiptType>> => {
    return (await CashBankService.getCashReceiptToMe(params)).data
  }

  public static getCashReceipt = async (id: string): Promise<CashReceiptType> => {
    return (await CashBankService.getCashReceipt(id)).data
  }

  public static getCashReceiptCount = async (): Promise<ApprovalsCountType> => {
    return (await CashBankService.getCountCashReceipt()).data
  }

  public static getReconciliationList = async (params: ListParams): Promise<ListResponse<ReconciliationType>> => {
    return (await CashBankService.getReconciliationList(params)).data
  }

  public static getReconciliation = async (id: string): Promise<ReconciliationType> => {
    return (await CashBankService.getReconciliation(id)).data
  }
}
