import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import CompanyDataService from './service'
import {
  CarrierType,
  CategoryPayload,
  CategoryType,
  DepartmentType,
  ItemType,
  SiteType,
  UnitType,
  VendorAdressesType,
  VendorType
} from '@/types/companyTypes'
import {
  DepartmentPayload,
  ItemPayload,
  SitePayload,
  UnitPayload,
  VendorPayload,
  VendorAddressPayload,
  CustomerPayload,
  ProjectPayload,
  CarrierPayload
} from '@/types/payload'
import { CurrenciesPayload, CurrenciesType } from '@/types/currenciesTypes'
import { CustomerType } from '@/types/customerTypes'
import { ProjectType } from '@/types/projectTypes'

export const useAddItem = (): UseMutationResult<ApiResponse<ItemType>, Error, ItemPayload, void> => {
  return useMutation({
    mutationFn: (payload: ItemPayload) => {
      return CompanyDataService.addItem(payload)
    }
  })
}

export const useUpdateItem = (): UseMutationResult<ApiResponse<ItemType>, Error, ItemPayload, void> => {
  return useMutation({
    mutationFn: (payload: ItemPayload) => {
      return CompanyDataService.updateItem(payload)
    }
  })
}

export const useDeleteItem = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (itemId: string) => {
      return CompanyDataService.deleteItem(itemId)
    }
  })
}

export const useAddVendor = (): UseMutationResult<ApiResponse<VendorType>, Error, VendorPayload, void> => {
  return useMutation({
    mutationFn: (payload: VendorPayload) => {
      return CompanyDataService.addVendor(payload)
    }
  })
}

export const useUpdateVendor = (): UseMutationResult<ApiResponse<VendorType>, Error, VendorPayload, void> => {
  return useMutation({
    mutationFn: (payload: VendorPayload) => {
      return CompanyDataService.updateVendor(payload)
    }
  })
}

export const useDeleteVendor = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (vendorId: string) => {
      return CompanyDataService.deleteVendor(vendorId)
    }
  })
}

export const useAddVendorAdresses = (): UseMutationResult<
  ApiResponse<VendorAdressesType>,
  Error,
  VendorAddressPayload & { vendorId: string },
  void
> => {
  return useMutation({
    mutationFn: ({ vendorId, ...payload }: VendorAddressPayload & { vendorId: string }) => {
      return CompanyDataService.addVendorAdresses(vendorId, payload)
    }
  })
}

export const useDeleteVendorAddress = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  { vendorId: string; addressId: string },
  void
> => {
  return useMutation({
    mutationFn: ({ vendorId, addressId }: { vendorId: string; addressId: string }) => {
      return CompanyDataService.deleteVendorAddresses(vendorId, addressId)
    }
  })
}

export const useUpdateVendorAddress = (): UseMutationResult<
  ApiResponse<VendorAdressesType>,
  Error,
  VendorAddressPayload & { vendorId: string; addressId: string },
  void
> => {
  return useMutation({
    mutationFn: ({
      vendorId,
      addressId,
      ...payload
    }: VendorAddressPayload & { vendorId: string; addressId: string }) => {
      return CompanyDataService.updateVendorAddress(vendorId, addressId, payload)
    }
  })
}

export const useAddUnit = (): UseMutationResult<ApiResponse<UnitType>, Error, UnitPayload, void> => {
  return useMutation({
    mutationFn: (payload: UnitPayload) => {
      return CompanyDataService.addUnit(payload)
    }
  })
}

export const useUpdateUnit = (): UseMutationResult<ApiResponse<UnitType>, Error, UnitPayload, void> => {
  return useMutation({
    mutationFn: (payload: UnitPayload) => {
      return CompanyDataService.updateUnit(payload)
    }
  })
}

export const useDeleteUnit = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (unitId: string) => {
      return CompanyDataService.deleteUnit(unitId)
    }
  })
}

export const useAddSite = (): UseMutationResult<ApiResponse<SiteType>, Error, SitePayload, void> => {
  return useMutation({
    mutationFn: (payload: SitePayload) => {
      return CompanyDataService.addSite(payload)
    }
  })
}

export const useUpdateSite = (): UseMutationResult<ApiResponse<SiteType>, Error, SitePayload, void> => {
  return useMutation({
    mutationFn: (payload: SitePayload) => {
      return CompanyDataService.updateSite(payload)
    }
  })
}

export const useDeleteSite = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (siteId: string) => {
      return CompanyDataService.deleteSite(siteId)
    }
  })
}

export const useAddDepartment = (): UseMutationResult<ApiResponse<DepartmentType>, Error, DepartmentPayload, void> => {
  return useMutation({
    mutationFn: (payload: DepartmentPayload) => {
      return CompanyDataService.addDepartment(payload)
    }
  })
}

export const useUpdateDepartment = (): UseMutationResult<
  ApiResponse<DepartmentType>,
  Error,
  DepartmentPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: DepartmentPayload) => {
      return CompanyDataService.updateDepartment(payload)
    }
  })
}

export const useDeleteDepartment = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (departmentId: string) => {
      return CompanyDataService.deleteDepartment(departmentId)
    }
  })
}

export const useAddCategory = (): UseMutationResult<ApiResponse<CategoryType>, Error, CategoryPayload, void> => {
  return useMutation({
    mutationFn: (payload: CategoryPayload) => {
      return CompanyDataService.addCategory(payload)
    }
  })
}

export const useUpdateCategory = (): UseMutationResult<ApiResponse<CategoryType>, Error, CategoryPayload, void> => {
  return useMutation({
    mutationFn: (payload: CategoryPayload) => {
      return CompanyDataService.updateCategory(payload)
    }
  })
}

export const useDeleteCategory = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => {
      return CompanyDataService.deleteCategory(id)
    }
  })
}

export const useAddCurrency = (): UseMutationResult<ApiResponse<CurrenciesType>, Error, CurrenciesPayload, void> => {
  return useMutation({
    mutationFn: (data: CurrenciesPayload) => CompanyDataService.addCurrency(data)
  })
}

export const useUpdateCurrency = (): UseMutationResult<
  ApiResponse<CurrenciesType>,
  Error,
  { id: string } & CurrenciesPayload,
  void
> => {
  return useMutation({
    mutationFn: (data: { id: string } & CurrenciesPayload) => CompanyDataService.updateCurrency(data)
  })
}

export const useDeleteCurrency = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => CompanyDataService.deleteCurrency(id)
  })
}

export const useAddCustomer = (): UseMutationResult<ApiResponse<CustomerType>, Error, CustomerPayload, void> => {
  return useMutation({
    mutationFn: (payload: CustomerPayload) => {
      return CompanyDataService.addCustomer(payload)
    }
  })
}

export const useUpdateCustomer = (): UseMutationResult<
  ApiResponse<CustomerType>,
  Error,
  { id: string } & CustomerPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: { id: string } & CustomerPayload) => {
      return CompanyDataService.updateCustomer(payload)
    }
  })
}

export const useDeleteCustomer = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => {
      return CompanyDataService.deleteCustomer(id)
    }
  })
}

export const useCreateProject = (): UseMutationResult<ApiResponse<ProjectType>, Error, ProjectPayload, void> => {
  return useMutation({
    mutationFn: (payload: ProjectPayload) => {
      return CompanyDataService.createProject(payload)
    }
  })
}

export const useUpdateProject = (): UseMutationResult<
  ApiResponse<ProjectType>,
  Error,
  { id: string } & ProjectPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: { id: string } & ProjectPayload) => {
      const { id, ...rest } = payload
      return CompanyDataService.updateProject(id, rest)
    }
  })
}

export const useDeleteProject = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => {
      return CompanyDataService.deleteProject(id)
    }
  })
}

export const useFinishProject = (): UseMutationResult<ApiResponse<ProjectType>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => {
      return CompanyDataService.finishProject(id)
    }
  })
}

export const useAddCarrier = (): UseMutationResult<ApiResponse<CarrierType>, Error, CarrierPayload, void> => {
  return useMutation({
    mutationFn: (payload: CarrierPayload) => {
      return CompanyDataService.addCarrier(payload)
    }
  })
}

export const useUpdateCarrier = (): UseMutationResult<
  ApiResponse<CarrierType>,
  Error,
  CarrierPayload & { id: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: CarrierPayload & { id: string }) => {
      return CompanyDataService.updateCarrier(payload)
    }
  })
}

export const useDeleteCarrier = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => {
      return CompanyDataService.deleteCarrier(id)
    }
  })
}
