import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import {
  CarrierType,
  CategoryPayload,
  CategoryType,
  DepartmentType,
  DocumentUnitType,
  ItemType,
  SiteType,
  UnitLogDocument,
  UnitLogType,
  UnitType,
  VendorAdressesType,
  VendorType
} from '@/types/companyTypes'
import { CurrenciesPayload, CurrenciesType } from '@/types/currenciesTypes'
import { CustomerType } from '@/types/customerTypes'
import {
  DepartmentPayload,
  DocumentParams,
  ItemParams,
  ItemPayload,
  ListParams,
  SitePayload,
  UnitPayload,
  VendorPayload,
  VendorAddressPayload,
  CustomerPayload,
  ProjectPayload,
  CarrierPayload
} from '@/types/payload'
import { ProjectParams, ProjectType } from '@/types/projectTypes'
import { SerialNumberType, SnParams } from '@/types/serialNumber'

export const BASE_DEPARTMENT_URL = 'departments'
export const BASE_SITE_URL = 'sites'
export const BASE_CATEGORY_URL = 'categories'
export const BASE_ITEMS_URL = 'items'
export const BASE_VENDORS_URL = 'vendors'
export const BASE_UNITS_URL = 'units'
export const BASE_SN_URL = 'serial-numbers'
export const BASE_CURRENCIES_URL = 'currencies'
export const BASE_CUSTOMERS_URL = 'customers'
export const BASE_PROJECT_URL = 'projects'
export const BASE_CARRIER_URL = 'carriers'

export default class CompanyDataService {
  public static readonly getCategoryList = (params?: ListParams): Promise<ApiResponse<ListResponse<CategoryType>>> => {
    return request({
      url: `${BASE_CATEGORY_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getCategory = (id: string): Promise<ApiResponse<CategoryType>> => {
    return request({
      url: `${BASE_CATEGORY_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly addCategory = (payload: CategoryPayload): Promise<ApiResponse<CategoryType>> => {
    return request({
      url: `${BASE_CATEGORY_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateCategory = (payload: CategoryPayload): Promise<ApiResponse<CategoryType>> => {
    return request({
      url: `${BASE_CATEGORY_URL}/${payload.id}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deleteCategory = (id: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_CATEGORY_URL}/${id}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getItemList = (params?: ItemParams): Promise<ApiResponse<ListResponse<ItemType>>> => {
    return request({
      url: `${BASE_ITEMS_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addItem = (payload: ItemPayload): Promise<ApiResponse<ItemType>> => {
    return request({
      url: `${BASE_ITEMS_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateItem = (payload: ItemPayload): Promise<ApiResponse<ItemType>> => {
    return request({
      url: `${BASE_ITEMS_URL}/${payload.itemId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly getItem = (id: string): Promise<ApiResponse<ItemType>> => {
    return request({
      url: `${BASE_ITEMS_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly deleteItem = (itemId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_ITEMS_URL}/${itemId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getVendorList = (params?: ListParams): Promise<ApiResponse<ListResponse<VendorType>>> => {
    return request({
      url: `${BASE_VENDORS_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addVendor = (payload: VendorPayload): Promise<ApiResponse<VendorType>> => {
    return request({
      url: `${BASE_VENDORS_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateVendor = (payload: VendorPayload): Promise<ApiResponse<VendorType>> => {
    return request({
      url: `${BASE_VENDORS_URL}/${payload.vendorId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly getVendor = (id: string): Promise<ApiResponse<VendorType>> => {
    return request({
      url: `${BASE_VENDORS_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly deleteVendor = (vendorId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_VENDORS_URL}/${vendorId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly addVendorAdresses = (
    vendorId: string,
    payload: VendorAddressPayload
  ): Promise<ApiResponse<VendorAdressesType>> => {
    return request({
      url: `${BASE_VENDORS_URL}/${vendorId}/addresses`,
      method: 'post',
      instance: 'CORE',
      data: payload
    })
  }

  public static readonly getVendorAddress = (
    vendorId: string,
    addressId: string
  ): Promise<ApiResponse<VendorAdressesType>> => {
    return request({
      url: `${BASE_VENDORS_URL}/${vendorId}/addresses/${addressId}`,
      method: 'get',
      instance: 'CORE'
    })
  }
  public static readonly deleteVendorAddresses = (vendorId: string, addressId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_VENDORS_URL}/${vendorId}/addresses/${addressId}`,
      method: 'delete',
      instance: 'CORE'
    })
  }

  public static readonly updateVendorAddress = (
    vendorId: string,
    addressId: string,
    payload: VendorAddressPayload
  ): Promise<ApiResponse<VendorAdressesType>> => {
    return request({
      url: `${BASE_VENDORS_URL}/${vendorId}/addresses/${addressId}`,
      method: 'patch',
      instance: 'CORE',
      data: payload
    })
  }

  public static readonly getUnitList = (params?: ListParams): Promise<ApiResponse<ListResponse<UnitType>>> => {
    return request({
      url: `${BASE_UNITS_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly addUnit = (payload: UnitPayload): Promise<ApiResponse<UnitType>> => {
    return request({
      url: `${BASE_UNITS_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateUnit = (payload: UnitPayload): Promise<ApiResponse<UnitType>> => {
    return request({
      url: `${BASE_UNITS_URL}/${payload.unitId}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly getUnit = (id: string): Promise<ApiResponse<UnitType>> => {
    return request({
      url: `${BASE_UNITS_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly deleteUnit = (unitId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_UNITS_URL}/${unitId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getUnitLogs = (unitId: string): Promise<ApiResponse<ListResponse<UnitLogType>>> => {
    return request({
      url: `${BASE_UNITS_URL}/${unitId}/logs`,
      instance: 'CORE',
      method: 'get',
      params: { page: 1, limit: Number.MAX_SAFE_INTEGER }
    })
  }

  public static readonly getDocumentUnitLog = (
    unitId: string,
    documentType: string
  ): Promise<ApiResponse<ListResponse<UnitLogDocument>>> => {
    return request({
      url: `${BASE_UNITS_URL}/${unitId}/documents`,
      instance: 'CORE',
      method: 'get',
      params: { page: 1, limit: Number.MAX_SAFE_INTEGER, type: documentType }
    })
  }

  public static readonly getDocumentList = (
    params?: DocumentParams
  ): Promise<ApiResponse<ListResponse<DocumentUnitType>>> => {
    return request({
      url: `${BASE_UNITS_URL}/documents`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getSiteList = (params?: ListParams): Promise<ApiResponse<ListResponse<SiteType>>> => {
    return request({
      url: `${BASE_SITE_URL}`,
      method: 'get',
      instance: 'ACCOUNT',
      params
    })
  }

  public static readonly addSite = (payload: SitePayload): Promise<ApiResponse<SiteType>> => {
    return request({
      url: `${BASE_SITE_URL}`,
      method: 'post',
      instance: 'ACCOUNT',
      data: payload
    })
  }

  public static readonly updateSite = (payload: SitePayload): Promise<ApiResponse<SiteType>> => {
    return request({
      url: `${BASE_SITE_URL}/${payload.siteId}`,
      method: 'patch',
      instance: 'ACCOUNT',
      data: payload
    })
  }

  public static readonly getSite = (id: string): Promise<ApiResponse<SiteType>> => {
    return request({
      url: `${BASE_SITE_URL}/${id}`,
      instance: 'ACCOUNT',
      method: 'get'
    })
  }

  public static readonly deleteSite = (SiteId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_SITE_URL}/${SiteId}`,
      instance: 'ACCOUNT',
      method: 'delete'
    })
  }

  public static readonly getDepartmentList = (
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<DepartmentType>>> => {
    return request({
      url: `${BASE_DEPARTMENT_URL}`,
      method: 'get',
      instance: 'ACCOUNT',
      params
    })
  }

  public static readonly addDepartment = (payload: DepartmentPayload): Promise<ApiResponse<DepartmentType>> => {
    return request({
      url: `${BASE_DEPARTMENT_URL}`,
      method: 'post',
      instance: 'ACCOUNT',
      data: payload
    })
  }

  public static readonly updateDepartment = (payload: DepartmentPayload): Promise<ApiResponse<DepartmentType>> => {
    return request({
      url: `${BASE_DEPARTMENT_URL}/${payload.departmentId}`,
      method: 'patch',
      instance: 'ACCOUNT',
      data: payload
    })
  }

  public static readonly getDepartment = (id: string): Promise<ApiResponse<DepartmentType>> => {
    return request({
      url: `${BASE_DEPARTMENT_URL}/${id}`,
      method: 'get',
      instance: 'ACCOUNT'
    })
  }

  public static readonly deleteDepartment = (DepartmentId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_DEPARTMENT_URL}/${DepartmentId}`,
      method: 'delete',
      instance: 'ACCOUNT'
    })
  }

  public static readonly getSerialNumberList = (
    params?: SnParams
  ): Promise<ApiResponse<ListResponse<SerialNumberType>>> => {
    return request({
      url: `${BASE_SN_URL}`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static readonly getCurrenciesList = (
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<CurrenciesType>>> => {
    return request({
      method: 'get',
      url: `${BASE_CURRENCIES_URL}`,
      instance: 'CORE',
      params
    })
  }

  public static readonly addCurrency = (payload: CurrenciesPayload): Promise<ApiResponse<CurrenciesType>> => {
    return request({
      url: `${BASE_CURRENCIES_URL}`,
      method: 'post',
      instance: 'CORE',
      data: payload
    })
  }

  public static readonly updateCurrency = (
    payload: { id: string } & CurrenciesPayload
  ): Promise<ApiResponse<CurrenciesType>> => {
    const { id, ...rest } = payload
    return request({
      url: `${BASE_CURRENCIES_URL}/${id}`,
      method: 'patch',
      instance: 'CORE',
      data: rest
    })
  }

  public static readonly deleteCurrency = (id: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_CURRENCIES_URL}/${id}`,
      method: 'delete',
      instance: 'CORE'
    })
  }

  public static readonly getCustomerList = (params?: ListParams): Promise<ApiResponse<ListResponse<CustomerType>>> => {
    return request({
      url: `${BASE_CUSTOMERS_URL}`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static readonly addCustomer = (payload: CustomerPayload): Promise<ApiResponse<CustomerType>> => {
    return request({
      url: `${BASE_CUSTOMERS_URL}`,
      method: 'post',
      instance: 'CORE',
      data: payload
    })
  }

  public static readonly updateCustomer = (
    payload: { id: string } & CustomerPayload
  ): Promise<ApiResponse<CustomerType>> => {
    const { id, ...rest } = payload
    return request({
      url: `${BASE_CUSTOMERS_URL}/${payload.id}`,
      method: 'patch',
      instance: 'CORE',
      data: rest
    })
  }

  public static readonly getCustomer = (id: string): Promise<ApiResponse<CustomerType>> => {
    return request({
      url: `${BASE_CUSTOMERS_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly deleteCustomer = (customerId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_CUSTOMERS_URL}/${customerId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly getProjectList = (params?: ProjectParams): Promise<ApiResponse<ListResponse<ProjectType>>> => {
    return request({
      url: `${BASE_PROJECT_URL}`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static readonly getProject = (projectId: string): Promise<ApiResponse<ProjectType>> => {
    return request({
      url: `${BASE_PROJECT_URL}/${projectId}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly createProject = (project: ProjectPayload): Promise<ApiResponse<ProjectType>> => {
    return request({
      url: `${BASE_PROJECT_URL}`,
      instance: 'CORE',
      method: 'post',
      data: project
    })
  }

  public static readonly updateProject = (
    projectId: string,
    project: ProjectPayload
  ): Promise<ApiResponse<ProjectType>> => {
    return request({
      url: `${BASE_PROJECT_URL}/${projectId}`,
      instance: 'CORE',
      method: 'patch',
      data: project
    })
  }

  public static readonly deleteProject = (projectId: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_PROJECT_URL}/${projectId}`,
      instance: 'CORE',
      method: 'delete'
    })
  }

  public static readonly finishProject = (projectId: string): Promise<ApiResponse<ProjectType>> => {
    return request({
      url: `${BASE_PROJECT_URL}/${projectId}/finish`,
      instance: 'CORE',
      method: 'patch'
    })
  }

  public static readonly getCarrierList = (params?: ListParams): Promise<ApiResponse<ListResponse<CarrierType>>> => {
    return request({
      url: `${BASE_CARRIER_URL}`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static readonly getCarrier = (id: string): Promise<ApiResponse<CarrierType>> => {
    return request({
      url: `${BASE_CARRIER_URL}/${id}`,
      instance: 'CORE',
      method: 'get'
    })
  }

  public static readonly addCarrier = (payload: CarrierPayload): Promise<ApiResponse<CarrierType>> => {
    return request({
      url: `${BASE_CARRIER_URL}`,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly updateCarrier = (
    payload: CarrierPayload & { id: string }
  ): Promise<ApiResponse<CarrierType>> => {
    return request({
      url: `${BASE_CARRIER_URL}/${payload.id}`,
      instance: 'CORE',
      method: 'patch',
      data: payload
    })
  }

  public static readonly deleteCarrier = (id: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_CARRIER_URL}/${id}`,
      instance: 'CORE',
      method: 'delete'
    })
  }
}
