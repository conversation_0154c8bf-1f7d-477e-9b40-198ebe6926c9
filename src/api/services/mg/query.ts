import MgService from './service'
import { ApiResponse, ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { ItemParams, ItemPayload, ListParams } from '@/types/payload'
import { ImParams, ImType, MgOutType, StockLogType, StockType } from '@/types/mgTypes'
import { ApprovalsCountType } from '@/types/appTypes'

export const MG_IM_QUERY_KEY = 'MG_IM_QUERY_KEY'
export const MG_IM_LIST_QUERY_KEY = 'MG_IM_LIST_QUERY_KEY'
export const STOCK_LOG_LIST_QUERY_KEY = 'STOCK_LOG_LIST_QUERY_KEY'
export const MG_OUT_QUERY_KEY = 'MG_OUT_QUERY_KEY'
export const MG_OUT_LIST_QUERY_KEY = 'MG_OUT_LIST_QUERY_KEY'
export const MG_OUT_TO_ME_LIST_QUERY_KEY = 'MG_OUT_TO_ME_LIST_QUERY_KEY'
export const STOCK_DETAILS_QUERY_KEY = 'STOCK_DETAILS_QUERY_KEY'

export default class MgQueryMethods {
  public static readonly getIm = async (id: string): Promise<ImType> => {
    const res = await MgService.getIncomingMaterial(id)
    return res?.data
  }

  public static readonly getImList = async (params?: ImParams): Promise<ImType[]> => {
    const res = await MgService.getIncomingMaterialList(params)
    return res?.data?.items ?? []
  }

  public static readonly getStockLogList = async (params: ItemParams): Promise<StockLogType[]> => {
    const res = await MgService.getStockLogList(params)
    return res?.data?.items ?? []
  }

  public static readonly getStockDetails = async (params: ItemParams): Promise<StockType[]> => {
    const res = await MgService.getStockDetails(params)
    return res?.data?.items ?? []
  }

  public static readonly getMgOut = async (id: string): Promise<MgOutType> => {
    const res = await MgService.getOutcomingMaterial(id)
    return res?.data
  }

  public static readonly getMgOutList = async (params?: ImParams): Promise<ListResponse<MgOutType>> => {
    const res = await MgService.getOutcomingMaterialList(params)
    return res?.data
  }

  public static readonly getToMeMgOutList = async (params?: ImParams): Promise<ListResponse<MgOutType>> => {
    const res = await MgService.getToMeOutcomingMaterialList(params)
    return res?.data ?? defaultListData
  }

  public static readonly getCountMgOutApprovals = async (): Promise<ApprovalsCountType> => {
    const res = await MgService.getCountMgOutApprovals()
    return res?.data
  }
}
