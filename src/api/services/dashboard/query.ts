import {
  MgInStatistics,
  MgInStatisticsChartType,
  MgOutStatistics,
  OutgoingDocumentListType,
  OutgoingStatisticsType,
  PurchaseByItemType,
  PurchaseDocumentListType,
  PurchaseOrderStatisticsType,
  PurchasingSummaryType,
  PurchasingSummaryValuesType,
  StatisticParams
} from '@/types/dashboardTypes'
import DashboardService from './service'
import { ApiResponse, GenericListResponse, ListResponse } from '@/types/api'
import { BarDataType, LineDataType } from '@/components/chart/RawChart'
import { defaultListData } from '@/api/queryClient'

export const PO_MG_IN_QUERY_KEY = 'PO_MG_IN_QUERY_KEY'
export const PO_MG_OUT_QUERY_KEY = 'PO_MG_OUT_QUERY_KEY'

export default class DashboardQueryMethods {
  public static readonly getPurchaseOrdersInCategories = async (
    params?: StatisticParams
  ): Promise<MgInStatisticsChartType[]> => {
    const res = await DashboardService.getPurchaseOrdersInCategories(params)
    return res?.data?.items ?? []
  }

  public static readonly getPurchaseOrdersInSummary = async (params?: StatisticParams): Promise<MgInStatistics> => {
    const res = await DashboardService.getPurchaseOrdersInSummary(params)
    return res?.data
  }

  public static readonly getPurchaseOrdersByItem = async (
    params?: StatisticParams
  ): Promise<GenericListResponse<PurchaseOrderStatisticsType>> => {
    const res = await DashboardService.getPurchaseOrdersByItem(params)
    return res?.data
  }

  public static readonly getPurchaseOrdersValueByItem = async (
    params?: StatisticParams
  ): Promise<GenericListResponse<PurchaseOrderStatisticsType>> => {
    const res = await DashboardService.getPurchaseOrdersValuesByItem(params)
    return res?.data
  }

  public static readonly getPurchaseOrdersOut = async (params?: StatisticParams): Promise<MgOutStatistics> => {
    const res = await DashboardService.getPurchaseOrdersOut(params)
    return res?.data
  }

  public static readonly getPurchaseOrdersOutValue = async (params?: StatisticParams): Promise<MgOutStatistics> => {
    const res = await DashboardService.getPurchaseOrdersOutValue(params)
    return res?.data
  }

  public static readonly getMaterialsInBar = async (params?: StatisticParams): Promise<BarDataType[]> => {
    const res = await DashboardService.getMaterialsInBar(params)
    return res?.data?.items ?? []
  }
  public static readonly getMaterialsInLine = async (params?: StatisticParams): Promise<LineDataType[]> => {
    const res = await DashboardService.getMaterialsInLine(params)
    return res?.data?.items ?? []
  }
  public static readonly getMaterialsValueInBar = async (params?: StatisticParams): Promise<BarDataType[]> => {
    const res = await DashboardService.getMaterialsValueInBar(params)
    return res?.data?.items ?? []
  }
  public static readonly getMaterialsValueInLine = async (params?: StatisticParams): Promise<LineDataType[]> => {
    const res = await DashboardService.getMaterialsValueInLine(params)
    return res?.data?.items ?? []
  }

  public static readonly getMaterialsOutBar = async (params?: StatisticParams): Promise<BarDataType[]> => {
    const res = await DashboardService.getMaterialsOutBar(params)
    return res?.data?.items ?? []
  }

  public static readonly getMaterialsOutLine = async (params?: StatisticParams): Promise<LineDataType[]> => {
    const res = await DashboardService.getMaterialsOutLine(params)
    return res?.data?.items ?? []
  }
  public static readonly getMaterialsOutValueBar = async (params?: StatisticParams): Promise<BarDataType[]> => {
    const res = await DashboardService.getMaterialsOutValueBar(params)
    return res?.data?.items ?? []
  }

  public static readonly getMaterialsOutValueLine = async (params?: StatisticParams): Promise<LineDataType[]> => {
    const res = await DashboardService.getMaterialsOutValueLine(params)
    return res?.data?.items ?? []
  }

  public static readonly getDetailQuantityByDocuments = async (
    params?: StatisticParams & { itemId: string }
  ): Promise<GenericListResponse<PurchaseDocumentListType>> => {
    const res = await DashboardService.getDetailQuantityByDocuments(params)
    return res?.data
  }

  public static readonly getOutgoingMaterialsByItem = async (
    params?: StatisticParams
  ): Promise<GenericListResponse<OutgoingStatisticsType>> => {
    const res = await DashboardService.getOutgoingMaterialsByItem(params)
    return res?.data
  }
  public static readonly getOutgoingMaterialsByValueItem = async (
    params?: StatisticParams
  ): Promise<GenericListResponse<OutgoingStatisticsType>> => {
    const res = await DashboardService.getOutgoingMaterialsByValueItem(params)
    return res?.data
  }

  public static readonly getDetailOutgoingQuantityByDocuments = async (
    params?: StatisticParams & { itemId: string }
  ): Promise<GenericListResponse<OutgoingDocumentListType>> => {
    const res = await DashboardService.getDetailOutgoingQuantityByDocuments(params)
    return res?.data
  }

  public static readonly getPurchaseSummary = async (params: StatisticParams): Promise<PurchasingSummaryType> => {
    const res = await DashboardService.getPurchasingSummary(params)
    return res?.data
  }

  public static readonly getPurchaseByCategory = async (
    params: StatisticParams
  ): Promise<GenericListResponse<PurchasingSummaryValuesType>> => {
    const res = await DashboardService.getPurchasingSummaryByCategory(params)
    return res?.data
  }

  public static readonly getPurchaseByItems = async (
    params: StatisticParams
  ): Promise<GenericListResponse<PurchaseByItemType>> => {
    const res = await DashboardService.getPurchasingByItems(params)
    return res?.data
  }
}
