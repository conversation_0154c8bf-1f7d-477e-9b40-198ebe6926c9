import { ApiResponse, GenericListResponse, ListResponse } from '@/types/api'
import {
  MgInStatistics,
  MgInStatisticsChartType,
  MgOutStatistics,
  OutgoingDocumentListType,
  OutgoingStatisticsType,
  PurchaseByItemType,
  PurchaseDocumentListType,
  PurchaseOrderStatisticsType,
  PurchasingSummaryType,
  PurchasingSummaryValuesType,
  StatisticParams
} from '@/types/dashboardTypes'
import request from '@/api/request'
import { BarDataType, LineDataType } from '@/components/chart/RawChart'

const PO_MG_IN_STATS = 'purchase-orders/statistics'
const PO_MG_OUT_STATS = 'outgoing-materials/statistics'
const MG_IN_STATS = 'incoming-materials/statistics'
const MG_OUT_STATS = 'material-requests/statistics'

export default class DashboardService {
  public static async getPurchaseOrdersInCategories(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<MgInStatisticsChartType>>> {
    return request({
      url: `${PO_MG_IN_STATS}/sum-of-quantity-by-category`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static async getPurchaseOrdersInSummary(params: StatisticParams): Promise<ApiResponse<MgInStatistics>> {
    return request({
      url: `${PO_MG_IN_STATS}/sum-of-quantities`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static async getPurchaseOrdersByItem(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<PurchaseOrderStatisticsType>>> {
    return request({
      url: `${PO_MG_IN_STATS}/sum-of-quantities-by-item`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static async getPurchaseOrdersValuesByItem(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<PurchaseOrderStatisticsType>>> {
    return request({
      url: `${PO_MG_IN_STATS}/sum-of-values-by-item`,
      instance: 'CORE',
      method: 'get',
      params
    })
  }

  public static async getPurchaseOrdersOut(params: StatisticParams): Promise<ApiResponse<MgOutStatistics>> {
    return request({
      url: `${PO_MG_OUT_STATS}/sum-of-quantity`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static async getPurchaseOrdersOutValue(params: StatisticParams): Promise<ApiResponse<MgOutStatistics>> {
    return request({
      url: `${PO_MG_OUT_STATS}/sum-of-value`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static async getMaterialsInBar(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<BarDataType>>> {
    return request({
      url: `${MG_IN_STATS}/sum-of-quantity-by-category-date`,
      method: 'get',
      instance: 'CORE',
      params: { ...params, format: 'bar' }
    })
  }
  public static async getMaterialsInLine(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<LineDataType>>> {
    return request({
      url: `${MG_IN_STATS}/sum-of-quantity-by-category-date`,
      method: 'get',
      instance: 'CORE',
      params: { ...params, format: 'line' }
    })
  }
  public static async getMaterialsValueInBar(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<BarDataType>>> {
    return request({
      url: `${MG_IN_STATS}/sum-of-value-by-category-date`,
      method: 'get',
      instance: 'CORE',
      params: { ...params, format: 'bar' }
    })
  }
  public static async getMaterialsValueInLine(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<LineDataType>>> {
    return request({
      url: `${MG_IN_STATS}/sum-of-value-by-category-date`,
      method: 'get',
      instance: 'CORE',
      params: { ...params, format: 'line' }
    })
  }

  public static async getMaterialsOutBar(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<BarDataType>>> {
    return request({
      url: `${PO_MG_OUT_STATS}/sum-of-quantity-by-category-date`,
      method: 'get',
      instance: 'CORE',
      params: { ...params, format: 'bar' }
    })
  }

  public static async getMaterialsOutLine(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<LineDataType>>> {
    return request({
      url: `${PO_MG_OUT_STATS}/sum-of-quantity-by-category-date`,
      method: 'get',
      instance: 'CORE',
      params: { ...params, format: 'line' }
    })
  }
  public static async getMaterialsOutValueBar(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<BarDataType>>> {
    return request({
      url: `${PO_MG_OUT_STATS}/sum-of-value-by-category-date`,
      method: 'get',
      instance: 'CORE',
      params: { ...params, format: 'bar' }
    })
  }

  public static async getMaterialsOutValueLine(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<LineDataType>>> {
    return request({
      url: `${PO_MG_OUT_STATS}/sum-of-value-by-category-date`,
      method: 'get',
      instance: 'CORE',
      params: { ...params, format: 'line' }
    })
  }

  public static async getDetailQuantityByDocuments(
    params: StatisticParams & { itemId: string }
  ): Promise<ApiResponse<GenericListResponse<PurchaseDocumentListType>>> {
    return request({
      url: `${PO_MG_IN_STATS}/quantities-by-documents`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static async getOutgoingMaterialsByItem(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<OutgoingStatisticsType>>> {
    return request({
      url: `${PO_MG_OUT_STATS}/sum-of-quantity-by-item`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }
  public static async getOutgoingMaterialsByValueItem(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<OutgoingStatisticsType>>> {
    return request({
      url: `${PO_MG_OUT_STATS}/sum-of-value-by-item`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static async getDetailOutgoingQuantityByDocuments(
    params: StatisticParams & { itemId: string }
  ): Promise<ApiResponse<GenericListResponse<OutgoingDocumentListType>>> {
    return request({
      url: `${MG_OUT_STATS}/quantities-by-documents`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static async getPurchasingSummaryByCategory(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<PurchasingSummaryValuesType>>> {
    return request({
      url: `${PO_MG_IN_STATS}/sum-of-value-by-category`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static async getPurchasingSummary(params: StatisticParams): Promise<ApiResponse<PurchasingSummaryType>> {
    return request({
      url: `${PO_MG_IN_STATS}/sum-of-values`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }

  public static async getPurchasingByItems(
    params: StatisticParams
  ): Promise<ApiResponse<GenericListResponse<PurchaseByItemType>>> {
    return request({
      url: `${PO_MG_IN_STATS}/sum-of-values-by-item`,
      method: 'get',
      instance: 'CORE',
      params
    })
  }
}
