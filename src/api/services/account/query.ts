import { ListResponse } from '@/types/api'
import { Sal<PERSON><PERSON>ara<PERSON>, SalaryType, SalaryTypeMaster } from '@/types/salaryTypes'
import AccountsService from './service'
import {
  AccountMasterType,
  AccountParams,
  AccountType,
  DivisionType,
  GeneralLedgerType,
  JournalLineParams,
  JournalLineType,
  PaymentTermsType
} from '@/types/accountTypes'
import { ListParams } from '@/types/payload'

export const ACCOUNT_LIST_QUERY_KEY = 'ACCOUNT_LIST_QUERY_KEY'
export const ACCOUNT_QUERY_KEY = 'ACCOUNT_QUERY_KEY'
export const ACCOUNT_TYPE_QUERY_KEY = 'ACCOUNT_TYPE_QUERY_KEY'
export const DIVISION_LIST_QUERY_KEY = 'DIVISION_LIST_QUERY_KEY'
export const GENERAL_LEDGER_LIST_QUERY_KEY = 'GENERAL_LEDGER_LIST_QUERY_KEY'
export const GENERAL_LEDGER_QUERY_KEY = 'GENERAL_LEDGER_QUERY_KEY'
export const JOURNAL_LINE_LIST_QUERY_KEY = 'JOURNAL_LINE_LIST_QUERY_KEY'
export const PAYMENT_TERMS_LIST_QUERY_KEY = 'PAYMENT_TERMS_LIST_QUERY_KEY'
export const PAYMENT_TERMS_QUERY_KEY = 'PAYMENT_TERMS_QUERY_KEY'

export default class AccountsQueryMethods {
  public static readonly getAccountList = async (params?: AccountParams): Promise<ListResponse<AccountType>> => {
    return (await AccountsService.getAccountList(params)).data
  }

  public static readonly getAccountTypeList = async (params?: ListParams): Promise<ListResponse<AccountMasterType>> => {
    return (await AccountsService.getAccountTypeList(params)).data
  }

  public static readonly getAccount = async (id: string): Promise<AccountType> => {
    return (await AccountsService.getAccount(id)).data
  }
  public static readonly getDivisionList = async (params?: ListParams): Promise<ListResponse<DivisionType>> => {
    return (await AccountsService.getDivisionList(params)).data
  }

  public static readonly getDivision = async (id: string): Promise<DivisionType> => {
    return (await AccountsService.getDivision(id)).data
  }

  public static readonly getGeneralLedgerList = async (
    params?: ListParams
  ): Promise<ListResponse<GeneralLedgerType>> => {
    return (await AccountsService.getGeneralLedgerList(params)).data
  }

  public static readonly getGeneralLedger = async (id: string): Promise<GeneralLedgerType> => {
    return (await AccountsService.getGeneralLedger(id)).data
  }

  public static readonly getJournalLines = async (
    params?: JournalLineParams
  ): Promise<ListResponse<JournalLineType>> => {
    return (await AccountsService.getJournalLines(params)).data
  }

  public static readonly getPaymentTermsList = async (params?: ListParams): Promise<ListResponse<PaymentTermsType>> => {
    return (await AccountsService.getPaymentTermsList(params)).data
  }

  public static readonly getPaymentTerms = async (id: string): Promise<PaymentTermsType> => {
    return (await AccountsService.getPaymentTerms(id)).data
  }
}
