import { ListResponse } from '@/types/api'
import { Salary<PERSON>arams, SalaryType, SalaryTypeMaster } from '@/types/salaryTypes'
import AccountsService from './service'
import { AccountMasterType, AccountParams, AccountType } from '@/types/accountTypes'
import { ListParams } from '@/types/payload'

export const ACCOUNT_LIST_QUERY_KEY = 'ACCOUNT_LIST_QUERY_KEY'
export const ACCOUNT_QUERY_KEY = 'ACCOUNT_QUERY_KEY'
export const ACCOUNT_TYPE_QUERY_KEY = 'ACCOUNT_TYPE_QUERY_KEY'

export default class AccountsQueryMethods {
  public static readonly getAccoutList = async (params?: AccountParams): Promise<ListResponse<AccountType>> => {
    return (await AccountsService.getAccountList(params)).data
  }

  public static readonly getAccountTypeList = async (params?: ListParams): Promise<ListResponse<AccountMasterType>> => {
    return (await AccountsService.getAccountTypeList(params)).data
  }

  public static readonly getAccout = async (id: string): Promise<AccountType> => {
    return (await AccountsService.getAccount(id)).data
  }
}
