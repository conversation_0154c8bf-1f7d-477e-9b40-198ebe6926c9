import request from '@/api/request'
import { AccountMasterType, AccountParams, AccountType } from '@/types/accountTypes'
import { ApiResponse, ListResponse } from '@/types/api'
import { AccountPayload, ListParams } from '@/types/payload'
import { SalaryParams, SalaryType, SalaryTypeMaster } from '@/types/salaryTypes'

export const BASE_URL_ACCOUNTS = 'accounts'

export default class AccountsService {
  public static readonly createAccount = (data: AccountPayload): Promise<ApiResponse<AccountType>> => {
    return request({
      method: 'POST',
      url: `${BASE_URL_ACCOUNTS}`,
      instance: 'CORE',
      data
    })
  }

  public static readonly updateAccount = (data: { id: string } & AccountPayload): Promise<ApiResponse<AccountType>> => {
    const { id, ...rest } = data
    return request({
      method: 'patch',
      url: `${BASE_URL_ACCOUNTS}/${id}`,
      instance: 'CORE',
      data: rest
    })
  }

  public static readonly deleteAccount = (id: string): Promise<ApiResponse<any>> => {
    return request({
      method: 'DELETE',
      url: `${BASE_URL_ACCOUNTS}/${id}`,
      instance: 'CORE'
    })
  }

  public static readonly getAccountList = (params?: AccountParams): Promise<ApiResponse<ListResponse<AccountType>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_ACCOUNTS}`,
      instance: 'CORE',
      params
    })
  }

  public static readonly getAccount = (id: string): Promise<ApiResponse<AccountType>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_ACCOUNTS}/${id}`,
      instance: 'CORE'
    })
  }

  public static readonly getAccountTypeList = (
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<AccountMasterType>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_ACCOUNTS}/master-data/types`,
      instance: 'CORE',
      params
    })
  }
}
