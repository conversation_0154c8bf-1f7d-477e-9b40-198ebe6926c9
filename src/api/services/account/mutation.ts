import { ApiResponse } from '@/types/api'
import { AccountPayload } from '@/types/payload'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import AccountsService from './service'
import { AccountType } from '@/types/accountTypes'

export const useCreateAccount = (): UseMutationResult<ApiResponse<AccountType>, Error, AccountPayload, void> => {
  return useMutation({
    mutationFn: (data: AccountPayload) => AccountsService.createAccount(data)
  })
}

export const useUpdateAccount = (): UseMutationResult<
  ApiResponse<AccountType>,
  Error,
  { id: string } & AccountPayload,
  void
> => {
  return useMutation({
    mutationFn: (data: { id: string } & AccountPayload) => AccountsService.updateAccount(data)
  })
}

export const useDeleteAccount = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => AccountsService.deleteAccount(id)
  })
}
