import { ApiResponse } from '@/types/api'
import {
  AccountPayload,
  CreatePaymentTermsPayload,
  DivisionPayload,
  GeneralLedgerPayload,
  UpdateDivisionPayload
} from '@/types/payload'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import AccountsService from './service'
import { AccountType, DivisionType, GeneralLedgerType, PaymentTermsType } from '@/types/accountTypes'

export const useCreateAccount = (): UseMutationResult<ApiResponse<AccountType>, Error, AccountPayload, void> => {
  return useMutation({
    mutationFn: (data: AccountPayload) => AccountsService.createAccount(data)
  })
}

export const useUpdateAccount = (): UseMutationResult<
  ApiResponse<AccountType>,
  Error,
  { id: string } & AccountPayload,
  void
> => {
  return useMutation({
    mutationFn: (data: { id: string } & AccountPayload) => AccountsService.updateAccount(data)
  })
}

export const useDeleteAccount = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => AccountsService.deleteAccount(id)
  })
}

export const useAddDivision = (): UseMutationResult<ApiResponse<DivisionType>, Error, DivisionPayload, void> => {
  return useMutation({
    mutationFn: (data: DivisionPayload) => AccountsService.addDivision(data)
  })
}

export const useUpdateDivision = (): UseMutationResult<
  ApiResponse<DivisionType>,
  Error,
  { id: string } & UpdateDivisionPayload,
  void
> => {
  return useMutation({
    mutationFn: (data: { id: string } & UpdateDivisionPayload) => AccountsService.updateDivision(data)
  })
}

export const useDeleteDivision = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => AccountsService.deleteDivision(id)
  })
}

export const useCreateGeneralLedger = (): UseMutationResult<
  ApiResponse<GeneralLedgerType>,
  Error,
  GeneralLedgerPayload,
  void
> => {
  return useMutation({
    mutationFn: (data: GeneralLedgerPayload) => AccountsService.createGeneralLedger(data)
  })
}

export const useCreatePaymentTerms = (): UseMutationResult<
  ApiResponse<PaymentTermsType>,
  Error,
  CreatePaymentTermsPayload,
  void
> => {
  return useMutation({
    mutationFn: (data: CreatePaymentTermsPayload) => AccountsService.createPaymentTerms(data)
  })
}

export const useUpdatePaymentTerms = (): UseMutationResult<
  ApiResponse<PaymentTermsType>,
  Error,
  { id: string } & CreatePaymentTermsPayload,
  void
> => {
  return useMutation({
    mutationFn: (data: { id: string } & CreatePaymentTermsPayload) => AccountsService.updatePaymentTerms(data)
  })
}

export const useDeletePaymentTerms = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => AccountsService.deletePaymentTerms(id)
  })
}
