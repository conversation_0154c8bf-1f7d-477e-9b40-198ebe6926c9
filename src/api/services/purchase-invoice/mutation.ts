import { ApiResponse } from '@/types/api'
import { UseMutationResult, useMutation } from '@tanstack/react-query'
import PurchaseInvoiceService from './service'
import {
  PurchaseInvoice,
  PurchaseInvoicePayload,
  PurchaseInvoiceApprovalPayload,
  CancelPurchaseInvoicePayload
} from '@/types/purchaseInvoiceTypes'

// Create Purchase Invoice
export const useCreatePurchaseInvoice = (): UseMutationResult<
  ApiResponse<PurchaseInvoice>,
  Error,
  PurchaseInvoicePayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: PurchaseInvoicePayload) => {
      return PurchaseInvoiceService.createPurchaseInvoice(payload)
    }
  })
}

// Update Purchase Invoice
export const useUpdatePurchaseInvoice = (): UseMutationResult<
  ApiResponse<PurchaseInvoice>,
  Error,
  PurchaseInvoicePayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: PurchaseInvoicePayload) => {
      return PurchaseInvoiceService.updatePurchaseInvoice(payload)
    }
  })
}

// Delete Purchase Invoice
export const useDeletePurchaseInvoice = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (piId: string) => {
      return PurchaseInvoiceService.deletePurchaseInvoice(piId)
    }
  })
}

// Update Purchase Invoice Approval
export const useUpdatePurchaseInvoiceApproval = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  PurchaseInvoiceApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: PurchaseInvoiceApprovalPayload) => {
      return PurchaseInvoiceService.updatePurchaseInvoiceApproval(payload)
    }
  })
}

// Update Purchase Invoice Approval Read Status
export const useUpdatePurchaseInvoiceApprovalRead = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  PurchaseInvoiceApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: PurchaseInvoiceApprovalPayload) => {
      return PurchaseInvoiceService.updatePurchaseInvoiceApprovalRead(payload)
    }
  })
}

// Update Purchase Invoice Approval Status
export const useUpdatePurchaseInvoiceApprovalStatus = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  PurchaseInvoiceApprovalPayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: PurchaseInvoiceApprovalPayload) => {
      return PurchaseInvoiceService.updatePurchaseInvoiceApprovalStatus(payload)
    }
  })
}

// Cancel Purchase Invoice
export const useCancelPurchaseInvoice = (): UseMutationResult<
  ApiResponse<any>,
  Error,
  CancelPurchaseInvoicePayload,
  void
> => {
  return useMutation({
    mutationFn: (payload: CancelPurchaseInvoicePayload) => {
      return PurchaseInvoiceService.cancelPurchaseInvoice(payload)
    }
  })
}

// Close Purchase Invoice
export const useClosePurchaseInvoice = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (piId: string) => {
      return PurchaseInvoiceService.closePurchaseInvoice(piId)
    }
  })
}
