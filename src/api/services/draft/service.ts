import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { DraftParams, DraftPayload, DraftType } from '@/types/draftsTypes'

const BASE_DRAFT = 'drafts'

export const DRAFT_QUERY_LIST_KEY = 'DRAFT_QUERY_LIST_KEY'
export const DRAFT_QUERY_KEY = 'DRAFT_QUERY_KEY'

export default class DraftServices {
  public static readonly createDraft = (payload: DraftPayload): Promise<ApiResponse<DraftType>> => {
    return request({
      url: BASE_DRAFT,
      instance: 'CORE',
      method: 'post',
      data: payload
    })
  }

  public static readonly getDrafts = (params: DraftParams): Promise<ApiResponse<ListResponse<DraftType>>> => {
    return request({
      url: BASE_DRAFT,
      instance: 'CORE',
      method: 'GET',
      params
    })
  }

  public static readonly getDraftById = (id: string): Promise<ApiResponse<DraftType>> => {
    return request({
      url: `${BASE_DRAFT}/${id}`,
      instance: 'CORE',
      method: 'GET'
    })
  }

  public static readonly deleteDraft = (id: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_DRAFT}/${id}`,
      instance: 'CORE',
      method: 'DELETE'
    })
  }

  public static readonly updateDraft = (payload: {
    draftId: string
    payload: string
    siteId: string
  }): Promise<ApiResponse<DraftType>> => {
    return request({
      url: `${BASE_DRAFT}/${payload.draftId}`,
      instance: 'CORE',
      method: 'PATCH',
      data: {
        payload: payload.payload,
        siteId: payload.siteId
      }
    })
  }
}
