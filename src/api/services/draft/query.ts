import { ApiResponse, ListResponse } from '@/types/api'
import { DraftParams, DraftType } from '@/types/draftsTypes'
import DraftServices from './service'

export default class DraftQueryMethods {
  public static getDrafts = async (params: DraftParams): Promise<ListResponse<DraftType>> => {
    return (await DraftServices.getDrafts(params)).data
  }
  public static getOneDraft = async (id: string): Promise<DraftType> => {
    return (await DraftServices.getDraftById(id)).data
  }
}
