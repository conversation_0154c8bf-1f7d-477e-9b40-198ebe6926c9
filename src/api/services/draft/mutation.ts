import { DraftPayload, DraftType } from '@/types/draftsTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import DraftServices from './service'
import { ApiResponse } from '@/types/api'

export const useCreateDraft = (): UseMutationResult<ApiResponse<DraftType>, Error, DraftPayload, void> => {
  return useMutation({
    mutationFn: (payload: DraftPayload) => DraftServices.createDraft(payload)
  })
}

export const useUpdateDraft = (): UseMutationResult<
  ApiResponse<DraftType>,
  Error,
  { draftId: string; payload: string; siteId: string },
  void
> => {
  return useMutation({
    mutationFn: (payload: { draftId: string; payload: string; siteId: string }) => DraftServices.updateDraft(payload)
  })
}

export const useDeleteDraft = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => DraftServices.deleteDraft(id)
  })
}
