import { ListParams } from '@/types/payload'
import AssetManagementService from './services'

export default class AssetManagementQueryMethods {
  public static readonly getAssets = async (params: ListParams) => {
    const res = await AssetManagementService.getAssets(params)
    return res.data
  }

  public static readonly getAsset = async (id: string) => {
    const res = await AssetManagementService.getAsset(id)
    return res.data
  }

  // Asset Mobilization Query Methods
  public static readonly getPaginatedMobilization = async (id: string, params: ListParams) => {
    const res = await AssetManagementService.getPaginatedMobilization(id, params)
    return res.data
  }

  // Asset Value Log Query Methods
  public static readonly getPaginatedValueLogs = async (id: string, params: ListParams) => {
    const res = await AssetManagementService.getPaginatedValueLogs(id, params)
    return res.data
  }

  public static readonly getValueLogDetail = async (assetId: string, logId: string) => {
    const res = await AssetManagementService.getValueLogDetail(assetId, logId)
    return res.data
  }
}
