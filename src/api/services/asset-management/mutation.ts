import { AssetDtoType, AssetType } from '@/types/assetTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import AssetManagementService from './services'
import { ApiResponse } from '@/types/api'

export const useCreateAsset = (): UseMutationResult<ApiResponse<AssetType>, Error, AssetDtoType, void> => {
  return useMutation({
    mutationFn: (data: AssetDtoType) => {
      return AssetManagementService.createAsset(data)
    }
  })
}

export const useUpdateAsset = (): UseMutationResult<
  ApiResponse<AssetType>,
  Error,
  AssetDtoType & { id: string },
  void
> => {
  return useMutation({
    mutationFn: (data: AssetDtoType & { id: string }) => {
      return AssetManagementService.updateAsset(data.id, data)
    }
  })
}

export const useDeleteAsset = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => {
      return AssetManagementService.deleteAsset(id)
    }
  })
}
