import {
  AssetPayload,
  AssetType,
  AssetMobilizationPayload,
  AssetMobilization,
  AssetDisposePayload,
  AssetDisposeType
} from '@/types/assetTypes'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import AssetManagementService from './services'
import { ApiResponse } from '@/types/api'

export const useCreateAsset = (): UseMutationResult<ApiResponse<AssetType>, Error, AssetPayload, void> => {
  return useMutation({
    mutationFn: (data: AssetPayload) => {
      return AssetManagementService.createAsset(data)
    }
  })
}

export const useUpdateAsset = (): UseMutationResult<
  ApiResponse<AssetType>,
  Error,
  AssetPayload & { id: string },
  void
> => {
  return useMutation({
    mutationFn: (data: AssetPayload & { id: string }) => {
      return AssetManagementService.updateAsset(data.id, data)
    }
  })
}

export const useDeleteAsset = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => {
      return AssetManagementService.deleteAsset(id)
    }
  })
}

// Asset Dispose Mutation
export const useDisposeAsset = (): UseMutationResult<ApiResponse<AssetDisposeType>, Error, { id: string }, void> => {
  return useMutation({
    mutationFn: ({ id }: { id: string }) => {
      return AssetManagementService.disposeAsset(id)
    }
  })
}

// Asset Mobilization Mutation
export const useMobilizeAsset = (): UseMutationResult<
  ApiResponse<AssetMobilization>,
  Error,
  { id: string; data: AssetMobilizationPayload },
  void
> => {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: AssetMobilizationPayload }) => {
      return AssetManagementService.mobilizeAsset(id, data)
    }
  })
}
