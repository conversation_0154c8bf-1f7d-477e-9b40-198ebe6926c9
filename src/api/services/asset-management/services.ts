import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import { AssetDtoType, AssetType } from '@/types/assetTypes'
import { ListParams } from '@/types/payload'

export const CODE_ACTIVA_QUERY_LIST_KEY = 'CODE_ACTIVA_QUERY_LIST_KEY'

const BASE_URL = 'assets'

export default class AssetManagementService {
  public static readonly getAssets = (params: ListParams): Promise<ApiResponse<ListResponse<AssetType>>> => {
    return request({
      url: `${BASE_URL}`,
      instance: 'CORE',
      method: 'GET',
      params
    })
  }

  public static readonly createAsset = (data: AssetDtoType): Promise<ApiResponse<AssetType>> => {
    return request({
      url: `${BASE_URL}`,
      instance: 'CORE',
      method: 'POST',
      data
    })
  }

  public static readonly updateAsset = (id: string, data: AssetDtoType): Promise<ApiResponse<AssetType>> => {
    return request({
      url: `${BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'PATCH',
      data
    })
  }

  public static readonly deleteAsset = (id: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'DELETE'
    })
  }
}
