import request from '@/api/request'
import { ApiResponse, ListResponse } from '@/types/api'
import {
  AssetPayload,
  AssetType,
  AssetMobilizationPayload,
  AssetMobilization,
  AssetDisposePayload,
  AssetDisposeType,
  AssetValueLogType,
  AssetValueLogDetailType
} from '@/types/assetTypes'
import { ListParams } from '@/types/payload'

export const ASSET_QUERY_LIST_KEY = 'ASSET_QUERY_LIST_KEY'
export const ASSET_QUERY_DETAIL_KEY = 'ASSET_QUERY_DETAIL_KEY'
export const ASSET_MOBILIZATION_LIST_KEY = 'ASSET_MOBILIZATION_LIST_KEY'
export const ASSET_VALUE_LOGS_LIST_KEY = 'ASSET_VALUE_LOGS_LIST_KEY'
export const ASSET_VALUE_LOG_DETAIL_KEY = 'ASSET_VALUE_LOG_DETAIL_KEY'

const BASE_URL = 'assets'

export default class AssetManagementService {
  public static readonly getAssets = (params: ListParams): Promise<ApiResponse<ListResponse<AssetType>>> => {
    return request({
      url: `${BASE_URL}`,
      instance: 'CORE',
      method: 'GET',
      params
    })
  }

  public static readonly createAsset = (data: AssetPayload): Promise<ApiResponse<AssetType>> => {
    return request({
      url: `${BASE_URL}`,
      instance: 'CORE',
      method: 'POST',
      data
    })
  }

  public static readonly updateAsset = (id: string, data: AssetPayload): Promise<ApiResponse<AssetType>> => {
    return request({
      url: `${BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'PATCH',
      data
    })
  }

  public static readonly getAsset = (id: string): Promise<ApiResponse<AssetType>> => {
    return request({
      url: `${BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'GET'
    })
  }

  public static readonly deleteAsset = (id: string): Promise<ApiResponse<any>> => {
    return request({
      url: `${BASE_URL}/${id}`,
      instance: 'CORE',
      method: 'DELETE'
    })
  }

  // Asset Dispose Methods
  public static readonly disposeAsset = (id: string): Promise<ApiResponse<AssetDisposeType>> => {
    return request({
      url: `${BASE_URL}/${id}/dispose`,
      instance: 'CORE',
      method: 'PATCH'
    })
  }

  // Asset Mobilization Methods
  public static readonly getPaginatedMobilization = (
    id: string,
    params: ListParams
  ): Promise<ApiResponse<ListResponse<AssetMobilization>>> => {
    return request({
      url: `${BASE_URL}/${id}/mobilizations`,
      instance: 'CORE',
      method: 'GET',
      params
    })
  }

  public static readonly mobilizeAsset = (
    id: string,
    data: AssetMobilizationPayload
  ): Promise<ApiResponse<AssetMobilization>> => {
    return request({
      url: `${BASE_URL}/${id}/mobilizations`,
      instance: 'CORE',
      method: 'POST',
      data
    })
  }

  // Asset Value Log Methods
  public static readonly getPaginatedValueLogs = (
    id: string,
    params: ListParams
  ): Promise<ApiResponse<ListResponse<AssetValueLogType>>> => {
    return request({
      url: `${BASE_URL}/${id}/value-logs`,
      instance: 'CORE',
      method: 'GET',
      params
    })
  }

  public static readonly getValueLogDetail = (
    assetId: string,
    logId: string
  ): Promise<ApiResponse<AssetValueLogDetailType>> => {
    return request({
      url: `${BASE_URL}/${assetId}/value-logs/${logId}`,
      instance: 'CORE',
      method: 'GET'
    })
  }
}
