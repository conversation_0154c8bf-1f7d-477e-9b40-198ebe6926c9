import { ApproverType, PermissionType, RoleType, UserType } from '@/types/userTypes'
import UsersService from './service'
import { ApproverParams, ListParams, PermissionParams, UserParams } from '@/types/payload'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'

export const USER_QUERY_KEY = 'USER_QUERY_KEY'
export const USER_LIST_QUERY_KEY = 'USER_LIST_QUERY_KEY'
export const ROLE_QUERY_KEY = 'ROLE_QUERY_KEY'
export const ROLE_LIST_QUERY_KEY = 'ROLE_LIST_QUERY_KEY'
export const PERMISSION_LIST_QUERY_KEY = 'PERMISSION_LIST_QUERY_KEY'
export const ACCOUNT_PERMISSIONS_QUERY_KEY = 'ACCOUNT_PERMISSIONS_QUERY_KEY'
export const DEFAULT_APPROVER_QUERY_KEY = 'DEFAULT_APPROVER_QUERY_KEY'
export const APPROVER_USER_LIST_QUERY_KEY = 'APPROVER_USER_LIST_QUERY_KEY'

export default class UserQueryMethods {
  public static readonly getUser = async (id: string): Promise<UserType> => {
    const res = await UsersService.getUser(id)

    return res?.data
  }

  public static readonly getUserList = async (params?: UserParams): Promise<ListResponse<UserType>> => {
    const res = await UsersService.getUserList(params)
    return res?.data ?? defaultListData
  }

  public static readonly getRole = async (id: string): Promise<RoleType> => {
    const res = await UsersService.getRole(id)

    return res?.data
  }

  public static readonly getRoleList = async (params?: ListParams): Promise<ListResponse<RoleType>> => {
    const res = await UsersService.getRoleList(params)
    return res?.data ?? defaultListData
  }

  public static readonly getPermissionList = async (
    params?: PermissionParams
  ): Promise<ListResponse<PermissionType>> => {
    const res = await UsersService.getPermissionList(params)
    return res?.data ?? defaultListData
  }

  public static readonly getPermissionsByRole = async (roleId: string): Promise<string[]> => {
    const res = await UsersService.getPermissionsByRole(roleId)
    return res?.data ?? []
  }

  public static readonly getDefaultApproverList = async (params?: ApproverParams): Promise<ApproverType[]> => {
    const { data } = await UsersService.getDefaultApproverList(params)
    return data?.items ?? []
  }
}
