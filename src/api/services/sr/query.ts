import SrService from './service'
import { ListResponse } from '@/types/api'
import { defaultListData } from '@/api/queryClient'
import { SrParams, SrType } from '@/types/srTypes'
import { ApprovalsCountType } from '@/types/appTypes'

export const SR_QUERY_KEY = 'SR_QUERY_KEY'
export const SR_LIST_QUERY_KEY = 'SR_LIST_QUERY_KEY'
export const SR_TO_ME_LIST_QUERY_KEY = 'SR_TO_ME_LIST_QUERY_KEY'

export default class SrQueryMethods {
  public static readonly getSr = async (id: string): Promise<SrType> => {
    const { data } = await SrService.getSr(id)
    return data
  }

  public static readonly getSrList = async (params?: SrParams): Promise<SrType[]> => {
    const res = await SrService.getSrList(params)
    return res?.data?.items ?? []
  }

  public static readonly getToMeSrList = async (params?: SrParams): Promise<ListResponse<SrType>> => {
    const res = await SrService.getToMeSrList(params)
    return res.data ?? defaultListData
  }

  public static readonly getCountApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await SrService.getApprovalsCount()
    return data
  }
}
