import { ApiResponse } from '@/types/api'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import TaskService from './service'
import { TaxType } from '@/types/taxTypes'
import { TaxPayload } from '@/types/payload'

export const useCreateTax = (): UseMutationResult<ApiResponse<TaxType>, Error, TaxPayload, void> => {
  return useMutation({
    mutationFn: (data: TaxPayload) => TaskService.createTax(data)
  })
}

export const useUpdateTax = (): UseMutationResult<ApiResponse<TaxType>, Error, { id: string } & TaxPayload, void> => {
  return useMutation({
    mutationFn: (data: { id: string } & TaxPayload) => TaskService.updateTax(data)
  })
}

export const useDeleteTax = (): UseMutationResult<ApiResponse<any>, Error, string, void> => {
  return useMutation({
    mutationFn: (id: string) => TaskService.deleteTax(id)
  })
}
