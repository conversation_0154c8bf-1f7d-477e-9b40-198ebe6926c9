import request from '@/api/request'
import { AccountMasterType } from '@/types/accountTypes'
import { ApiResponse, ListResponse } from '@/types/api'
import { ListParams, TaxPayload } from '@/types/payload'
import { TaxType, TaxTypeParams } from '@/types/taxTypes'

const BASE_URL_TAX = 'taxes'

export default class TaxService {
  public static readonly getTaxList = (params?: TaxTypeParams): Promise<ApiResponse<ListResponse<TaxType>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_TAX}`,
      instance: 'CORE',
      params
    })
  }

  public static readonly getTaxTypeList = (
    params?: ListParams
  ): Promise<ApiResponse<ListResponse<AccountMasterType>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL_TAX}/master-data/types`,
      instance: 'CORE',
      params
    })
  }

  public static readonly createTax = (data: TaxPayload): Promise<ApiResponse<TaxType>> => {
    return request({
      method: 'POST',
      url: `${BASE_URL_TAX}`,
      instance: 'CORE',
      data
    })
  }

  public static readonly updateTax = (data: { id: string } & TaxPayload): Promise<ApiResponse<TaxType>> => {
    const { id, ...rest } = data
    return request({
      method: 'patch',
      url: `${BASE_URL_TAX}/${id}`,
      instance: 'CORE',
      data: rest
    })
  }

  public static readonly deleteTax = (id: string): Promise<ApiResponse<any>> => {
    return request({
      method: 'DELETE',
      url: `${BASE_URL_TAX}/${id}`,
      instance: 'CORE'
    })
  }
}
