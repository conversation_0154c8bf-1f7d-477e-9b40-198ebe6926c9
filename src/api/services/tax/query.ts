import { ListResponse } from '@/types/api'
import { TaxType, TaxTypeParams } from '@/types/taxTypes'
import TaxService from './service'
import { ListParams } from '@/types/payload'
import { AccountMasterType } from '@/types/accountTypes'

export const TAX_LIST_QUERY_KEY = 'TAX_LIST_QUERY_KEY'
export const TAX_TYPE_QUERY_KEY = 'TAX_TYPE_QUERY_KEY'

export default class TaxQueryMethods {
  public static readonly getTaxList = async (params?: TaxTypeParams): Promise<ListResponse<TaxType>> => {
    return (await TaxService.getTaxList(params)).data
  }

  public static readonly getTaxTypeList = async (params?: ListParams): Promise<ListResponse<AccountMasterType>> => {
    return (await TaxService.getTaxTypeList(params)).data
  }
}
