import { CodeParams } from '@/types/codes'
import RnMService from './service'
import { FrParams } from '@/types/frTypes'
import { WoParams } from '@/types/woTypes'
import { ListParams } from '@/types/payload'
import { MrParams } from '@/types/mrTypes'

export default class RnMQueryMethods {
  public static readonly getCodeList = async (params: CodeParams) => {
    const res = await RnMService.getCodeList(params)
    return res?.data
  }
  public static readonly getFrList = async (params: FrParams) => {
    const res = await RnMService.getFrList(params)
    return res?.data
  }

  public static readonly getFrDetail = async (frId: string) => {
    const res = await RnMService.getFrDetail(frId)
    return res?.data
  }

  public static readonly getFrDetailLogs = async (frId: string) => {
    const res = await RnMService.getFrLogs(frId)
    return res?.data
  }

  public static readonly getWoList = async (params: WoParams) => {
    const res = await RnMService.getWolist(params)
    return res?.data
  }

  public static readonly getWoDetail = async (id: string) => {
    const res = await RnMService.getWoDetail(id)
    return res?.data
  }

  public static readonly getWoDetailLogs = async (id: string, params?: ListParams) => {
    const res = await RnMService.getWoDetailLogs(id, params)
    return res?.data
  }

  public static readonly getWoSegments = async (id: string, params?: ListParams) => {
    const res = await RnMService.getWoSegments(id, params)
    return res?.data
  }

  public static readonly getWoSegment = async (woId: string, segmentId: string) => {
    const res = await RnMService.getWoSegment(woId, segmentId)
    return res?.data
  }

  public static readonly getSrList = async (params?: MrParams) => {
    const res = await RnMService.getSrList(params)
    return res?.data
  }
}
