import { CreateSalesInvoicePayload, SalesInvoice } from '@/pages/accounting/selling/invoice/config/types'
import { useMutation, UseMutationResult } from '@tanstack/react-query'
import SellingInvoiceService from './service'
import { ApiResponse } from '@/types/api'

export const useCreateSellingInvoice = (): UseMutationResult<
  ApiResponse<SalesInvoice>,
  Error,
  CreateSalesInvoicePayload,
  void
> => {
  return useMutation({
    mutationFn: payload => SellingInvoiceService.createSellingInvoice(payload)
  })
}

export const useUpdateSellingInvoiceApprovalStatus = (): UseMutationResult<
  ApiResponse<SalesInvoice>,
  Error,
  { id: string; approvalId: number; status: string },
  void
> => {
  return useMutation({
    mutationFn: payload => SellingInvoiceService.updateSellingInvoiceApprovalStatus(payload)
  })
}

export const useReadSellingInvoice = (): UseMutationResult<
  ApiResponse<SalesInvoice>,
  Error,
  { id: string; approvalId: number; isRead: boolean },
  void
> => {
  return useMutation({
    mutationFn: payload => SellingInvoiceService.readSellingInvoice(payload)
  })
}
