import { ListResponse } from '@/types/api'
import SellingInvoiceService from './service'
import { SalesInvoice, SalesInvoiceLog, SalesInvoiceParams } from '@/pages/accounting/selling/invoice/config/types'
import { ApprovalsCountType } from '@/types/appTypes'

export const SELLING_INVOICE_QUERY_KEY = 'SELLING_INVOICE_QUERY_KEY'
export const SELLING_INVOICE_LIST_QUERY_KEY = 'SELLING_INVOICE_LIST_QUERY_KEY'

export default class SellingInvoiceQueryMethods {
  // Get Selling Invoice Detail
  public static readonly getSellingInvoice = async (id: string): Promise<SalesInvoice> => {
    const { data } = await SellingInvoiceService.getSellingInvoiceDetail(id)
    return data
  }

  // Get Selling Invoice List
  public static readonly getSellingInvoiceList = async (
    params: SalesInvoiceParams
  ): Promise<ListResponse<SalesInvoice>> => {
    const { data } = await SellingInvoiceService.getSellingInvoiceList(params)
    return data
  }

  public static readonly getSellingInvoiceToMe = async (
    params?: SalesInvoiceParams
  ): Promise<ListResponse<SalesInvoice>> => {
    const { data } = await SellingInvoiceService.getSellingInvoiceToMe(params)
    return data
  }

  public static readonly getSellingInvoiceLogs = async (id: string): Promise<ListResponse<SalesInvoiceLog>> => {
    const { data } = await SellingInvoiceService.getSellingInvoiceLogs(id)
    return data
  }

  public static readonly getCountsApprovals = async (): Promise<ApprovalsCountType> => {
    const { data } = await SellingInvoiceService.getCountApprovals()
    return data
  }
}
