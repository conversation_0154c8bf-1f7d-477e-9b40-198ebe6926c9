import request from '@/api/request'
import {
  CreateSalesInvoicePayload,
  SalesInvoice,
  SalesInvoiceLog,
  SalesInvoiceParams
} from '@/pages/accounting/selling/invoice/config/types'
import { ApiResponse, ListResponse } from '@/types/api'
import { ApprovalsCountType } from '@/types/appTypes'

const BASE_URL = 'sales-invoices'

export default class SellingInvoiceService {
  public static readonly getSellingInvoiceList = (
    params: SalesInvoiceParams
  ): Promise<ApiResponse<ListResponse<SalesInvoice>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL}`,
      instance: 'CORE',
      params
    })
  }

  public static readonly getSellingInvoiceToMe = (
    params?: SalesInvoiceParams
  ): Promise<ApiResponse<ListResponse<SalesInvoice>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL}/to-me`,
      instance: 'CORE',
      params
    })
  }

  public static readonly getSellingInvoiceDetail = (id: string): Promise<ApiResponse<SalesInvoice>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL}/${id}`,
      instance: 'CORE'
    })
  }

  public static readonly getSellingInvoiceLogs = (id: string): Promise<ApiResponse<ListResponse<SalesInvoiceLog>>> => {
    return request({
      method: 'GET',
      url: `${BASE_URL}/${id}/logs`,
      instance: 'CORE',
      params: { limit: Number.MAX_SAFE_INTEGER }
    })
  }

  public static readonly createSellingInvoice = (
    payload: CreateSalesInvoicePayload
  ): Promise<ApiResponse<SalesInvoice>> => {
    return request({
      method: 'POST',
      url: `${BASE_URL}`,
      instance: 'CORE',
      data: payload
    })
  }

  public static readonly getCountApprovals = (): Promise<ApiResponse<ApprovalsCountType>> => {
    return request({
      method: 'get',
      url: `${BASE_URL}/to-me/waitings-count`,
      instance: 'CORE',
      params: { status: 'APPROVED' }
    })
  }

  public static readonly readSellingInvoice = ({
    id,
    isRead,
    approvalId
  }: {
    id: string
    isRead: boolean
    approvalId: number
  }): Promise<ApiResponse<SalesInvoice>> => {
    return request({
      method: 'PATCH',
      url: `${BASE_URL}/${id}/approvals/${approvalId}/read`,
      instance: 'CORE',
      data: {
        isRead
      }
    })
  }

  public static readonly updateSellingInvoiceApprovalStatus = ({
    id,
    approvalId,
    status
  }: {
    id: string
    approvalId: number
    status: string
  }): Promise<ApiResponse<SalesInvoice>> => {
    return request({
      method: 'PATCH',
      url: `${BASE_URL}/${id}/approvals/${approvalId}/status`,
      instance: 'CORE',
      data: {
        status
      }
    })
  }
}
