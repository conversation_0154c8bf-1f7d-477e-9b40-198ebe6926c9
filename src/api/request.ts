import axios, { Axi<PERSON><PERSON><PERSON>r, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import localforage from 'localforage'
import Cookies from 'js-cookie'
import { toast } from 'react-toastify'
import * as Sentry from '@sentry/react'
import { ACCOUNTS_STORAGE_KEY, AUTH_STATE_KEY, AUTH_STORAGE_KEY } from '@/contexts/AuthContext'
import { AuthState } from '@/types/payload'
import { StatusCodes } from '@/utils/status-codes'
import { getFromCookies, saveToCookies } from '@/utils/storage'
import AuthService, { BASE_AUTH_URL } from './services/auth/service'
import { objectsEqual } from '@/utils/helper'

export const UNSTABLE_NETWORK_MESSAGE = 'unstable-network'

export type ServiceInstance = 'ACCOUNT' | 'CORE' | 'FILE' | 'MESSAGE'

export const axiosClient = axios.create({
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json'
  },
  maxContentLength: Infinity,
  maxBodyLength: Infinity
})

type AxiosRequestConfigExtend = {
  instance?: ServiceInstance
} & AxiosRequestConfig

const request = async (options: AxiosRequestConfigExtend, returnAxiosResponse: boolean = false) => {
  let baseURL = import.meta.env.VITE_BASE_API_URL

  switch (options.instance) {
    case 'ACCOUNT':
      baseURL = import.meta.env.VITE_ACCOUNT_BASE_API_URL
      break
    case 'CORE':
      baseURL = import.meta.env.VITE_CORE_BASE_API_URL
      break
    case 'FILE':
      baseURL = import.meta.env.VITE_FILE_BASE_API_URL
      break
    case 'MESSAGE':
      baseURL = import.meta.env.VITE_MESSAGE_BASE_API_URL
      break
    default:
      break
  }

  const onSuccess = (response: AxiosResponse) => {
    const { data } = response
    if (returnAxiosResponse) {
      return response
    }
    return data
  }

  const onError = function (error: AxiosError) {
    return Promise.reject({
      message: error.message,
      code: error.code,
      response: error.response
    })
  }

  return axiosClient({ baseURL, ...options })
    .then(onSuccess)
    .catch(onError)
}

axiosClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const activeMail = localStorage.getItem(AUTH_STORAGE_KEY)?.replaceAll('"', '')
    const authState = getFromCookies<AuthState>(!activeMail ? AUTH_STORAGE_KEY : `${AUTH_STORAGE_KEY}_${activeMail}`)
    if (authState?.token) {
      config.headers.Authorization = `Bearer ${authState?.token}`
    }
    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

let isRefreshing = false
let pendingRequests = []

async function handleRequest(originalConfig, error) {
  if (!originalConfig['axios-retry']) {
    if (error.response.status === StatusCodes.UNAUTHORIZED) {
      if (originalConfig?.url === `${BASE_AUTH_URL}/refresh-token`) {
        localforage.clear()
        localStorage.clear()
        Object.keys(Cookies.get()).forEach(key => Cookies.remove(key))
        return (window.location.href = '/auth/login')
      }
      const activeMail = localStorage.getItem(AUTH_STORAGE_KEY)?.replaceAll('"', '')
      let authState = getFromCookies<AuthState>(!activeMail ? AUTH_STORAGE_KEY : `${AUTH_STORAGE_KEY}_${activeMail}`)
      if (!isRefreshing || (!authState?.refreshToken && activeMail)) {
        isRefreshing = true

        try {
          const activeMail = localStorage.getItem(AUTH_STORAGE_KEY)?.replaceAll('"', '')
          let authState = getFromCookies<AuthState>(
            !activeMail ? AUTH_STORAGE_KEY : `${AUTH_STORAGE_KEY}_${activeMail}`
          )

          if (!authState) {
            authState = getFromCookies<AuthState>(AUTH_STORAGE_KEY)
          }

          if (authState?.refreshToken) {
            axiosClient({
              baseURL: import.meta.env.VITE_ACCOUNT_BASE_API_URL,
              url: `${BASE_AUTH_URL}/refresh-token`,
              instance: 'ACCOUNT',
              method: 'post',
              data: {
                refreshToken: authState.refreshToken
              }
            } as AxiosRequestConfigExtend)
              .then(({ data: { data } }) => {
                if (data) {
                  saveToCookies<AuthState>(`${AUTH_STORAGE_KEY}_${activeMail}`, {
                    ...authState,
                    token: data.accessToken,
                    refreshToken: data.refreshToken
                  })
                  saveToCookies<AuthState>(`${AUTH_STORAGE_KEY}`, {
                    ...authState,
                    token: data.accessToken,
                    refreshToken: data.refreshToken
                  })
                  axiosClient.defaults.headers.common.Authorization = `Bearer ${data.accessToken}`

                  pendingRequests.forEach(callback => callback(data.accessToken))
                  pendingRequests = []
                } else {
                  return Promise.reject(new Error('Token expired'))
                }
              })
              .catch(error => {
                const accounts = localStorage.getItem(ACCOUNTS_STORAGE_KEY)
                const deepCloneAccounts = JSON.parse(accounts)
                delete deepCloneAccounts[activeMail]

                const remainKeys = Object.keys(deepCloneAccounts)

                if (remainKeys.length === 0 || objectsEqual(deepCloneAccounts, {})) {
                  localforage.clear()
                  localStorage.clear()
                  Object.keys(Cookies.get()).forEach(key => Cookies.remove(key))
                  window.location.href = '/auth/login'
                } else {
                  localStorage.setItem(ACCOUNTS_STORAGE_KEY, JSON.stringify(deepCloneAccounts))
                  localStorage.setItem(AUTH_STORAGE_KEY, remainKeys[0])
                  const activeMailAuthState = getFromCookies<AuthState>(`${AUTH_STORAGE_KEY}_${activeMail}`)
                  if (activeMailAuthState) {
                    Cookies.remove(`${AUTH_STORAGE_KEY}_${activeMail}`)
                  } else {
                    localforage.clear()
                    localStorage.clear()
                    Object.keys(Cookies.get()).forEach(key => Cookies.remove(key))
                    window.location.href = '/auth/login'
                  }
                  toast.error('Session Expired', { toastId: 'session-expired' })
                  setTimeout(() => {
                    window.location.href = '/'
                  }, 1000)
                }

                saveToCookies<AuthState>(AUTH_STATE_KEY, { authenticated: false, registered: false })
              })
              .finally(() => {
                isRefreshing = false
              })
          } else {
            const accounts = localStorage.getItem(ACCOUNTS_STORAGE_KEY)
            const deepCloneAccounts = JSON.parse(accounts)
            delete deepCloneAccounts[activeMail]

            const remainKeys = Object.keys(deepCloneAccounts)

            if (remainKeys.length === 0 || objectsEqual(deepCloneAccounts, {})) {
              localforage.clear()
              localStorage.clear()
              Object.keys(Cookies.get()).forEach(key => Cookies.remove(key))
              window.location.href = '/auth/login'
            } else {
              localStorage.setItem(ACCOUNTS_STORAGE_KEY, JSON.stringify(deepCloneAccounts))
              localStorage.setItem(AUTH_STORAGE_KEY, remainKeys[0])
              const activeMailAuthState = getFromCookies<AuthState>(`${AUTH_STORAGE_KEY}_${activeMail}`)
              if (activeMailAuthState) {
                Cookies.remove(`${AUTH_STORAGE_KEY}_${activeMail}`)
              } else {
                localforage.clear()
                localStorage.clear()
                Object.keys(Cookies.get()).forEach(key => Cookies.remove(key))
                window.location.href = '/auth/login'
              }
              toast.error('Session Expired', { toastId: 'session-expired' })
              setTimeout(() => {
                window.location.href = '/'
              }, 1000)
            }
          }
          return Promise.reject(error)
        } catch (err) {
          Sentry.captureException(err)
          return Promise.reject(err)
        }
      }

      return new Promise((resolve, reject) => {
        pendingRequests.push(newAccessToken => {
          if (newAccessToken) {
            originalConfig.headers.Authorization = `Bearer ${newAccessToken}`
            resolve(axiosClient(originalConfig))
          } else {
            reject(error)
          }
        })
      })
    }

    if (
      window.location.pathname !== '/' &&
      (error.response.status === StatusCodes.SERVICE_UNAVAILABLE || error.response.status === StatusCodes.BAD_GATEWAY)
    ) {
      return axiosClient(originalConfig)
    }

    if (error.response.status === StatusCodes.FORBIDDEN && error.response.data) {
      return Promise.reject(error.response.data)
    }
    if (error.response.status !== StatusCodes.OK) {
      return Promise.reject(error?.response?.data ?? error)
    }
  }
  Sentry.captureException(error)
}

axiosClient.interceptors.response.use(
  (res: AxiosResponse) => {
    return res
  },
  async (error: AxiosError) => {
    return handleRequest(error.config, error)
  }
)

export default request
