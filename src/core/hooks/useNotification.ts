import { useEffect, useState } from 'react'

type NotificationPermissionStatus = 'ALLOW' | 'REJECT' | 'ASK'

const useNotificationPermission = (): NotificationPermissionStatus => {
  const [permissionStatus, setPermissionStatus] = useState<NotificationPermissionStatus>('ASK')

  useEffect(() => {
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        setPermissionStatus('ALLOW')
      } else if (Notification.permission === 'denied') {
        setPermissionStatus('REJECT')
      } else {
        setPermissionStatus('ASK')
      }
    } else {
      console.log('This browser does not support notifications.')
      setPermissionStatus('REJECT')
    }
  }, [])

  return permissionStatus
}

export default useNotificationPermission
