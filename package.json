{"name": "equalindo-webapp", "version": "0.0.10", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "preview": "vite preview", "prepare": "husky"}, "lint-staged": {"**/*": ["prettier --write --ignore-unknown"]}, "dependencies": {"@ag-media/react-pdf-table": "^2.0.3", "@emotion/react": "11.11.4", "@emotion/styled": "11.11.5", "@floating-ui/react": "0.26.19", "@hookform/resolvers": "3.7.0", "@mui/lab": "5.0.0-alpha.170", "@mui/material": "5.15.21", "@nivo/bar": "*", "@nivo/line": "*", "@nivo/pie": "*", "@react-pdf/renderer": "3.4.5", "@react-pdf/stylesheet": "^6.1.0", "@sentry/react": "^9.1.0", "@sentry/vite-plugin": "^3.1.2", "@tanstack/match-sorter-utils": "8.15.1", "@tanstack/react-query": "4.36.1", "@tanstack/react-query-devtools": "4.36.1", "@tanstack/react-table": "8.19.2", "axios": "^1.6.8", "axios-retry": "^4.5.0", "browser-image-compression": "^2.0.2", "classnames": "2.5.1", "cmdk": "1.0.0", "date-fns": "3.6.0", "file-saver": "^2.0.5", "input-otp": "1.2.4", "js-cookie": "^3.0.5", "keen-slider": "6.8.6", "localforage": "^1.10.0", "qrcode": "^1.5.4", "react": "18.3.1", "react-cache-buster": "^0.1.8", "react-datepicker": "7.5.0", "react-dom": "18.3.1", "react-dropzone": "14.2.3", "react-error-boundary": "^4.0.13", "react-helmet-async": "^2.0.4", "react-hook-form": "7.52.1", "react-number-format": "^5.4.2", "react-onesignal": "^3.1.1", "react-perfect-scrollbar": "1.5.8", "react-photo-view": "^1.2.6", "react-router-dom": "^6.27.0", "react-toastify": "10.0.5", "react-use": "17.5.0", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "use-file-picker": "^2.1.2", "uuidv7": "^1.0.2", "zod": "^3.22.4"}, "devDependencies": {"@iconify/json": "2.2.225", "@iconify/tools": "4.0.4", "@iconify/types": "2.0.0", "@iconify/utils": "2.1.25", "@types/css-modules": "^1.0.5", "@types/node": "^20.14.9", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@typescript-eslint/eslint-plugin": "7.15.0", "@typescript-eslint/parser": "7.15.0", "@vite-pwa/assets-generator": "^0.2.6", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "10.4.19", "dotenv-cli": "7.4.2", "eslint": "8.57.0", "eslint-config-next": "^14.2.14", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.6.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^9.0.11", "lint-staged": "^15.2.2", "postcss": "8.4.39", "postcss-styled-syntax": "0.6.4", "prettier": "3.3.2", "prettier-plugin-tailwindcss": "^0.5.12", "stylelint": "16.6.1", "stylelint-use-logical-spec": "5.0.1", "stylis": "4.3.2", "stylis-plugin-rtl": "2.1.1", "tailwindcss": "3.4.4", "tailwindcss-logical": "3.0.1", "tsx": "4.16.2", "typescript": "5.5.3", "vite": "^5.1.6", "vite-plugin-pwa": "^0.20.5"}, "msw": {"workerDirectory": ["public"]}}